﻿<style>
    /* إخفاء الجدول العادي في الشاشات الصغيرة */
    .regular-table { display: block; }
    .mobile-cards { display: none; }
    
    @@media (max-width: 768px) {
        .regular-table { display: none; }
        .mobile-cards { display: block; }
    }

    /* تصميم البطاقات المصغرة */
    .mobile-cards { padding: 0 2px; }
    .mobile-card { 
        border-radius: 6px; 
        transition: all 0.2s ease;
        border: 1px solid #e0e0e0;
        margin-bottom: 6px;
    }
    .mobile-card:hover { 
        transform: translateY(-1px); 
        box-shadow: 0 2px 8px rgba(0,0,0,0.1); 
    }
    
    /* تصغير المحتوى */
    .compact-content { padding: 8px !important; }
    .client-header { 
        display: flex; 
        justify-content: space-between; 
        align-items: center; 
        margin-bottom: 6px; 
    }
    .client-number { 
        font-size: 0.7rem; 
        padding: 1px 6px; 
        min-height: 20px;
    }
    .client-actions { display: flex; gap: 2px; }
    .client-name { 
        font-size: 0.9rem; 
        font-weight: 600; 
        margin-bottom: 3px; 
        line-height: 1.2;
    }
    .client-info { 
        display: flex; 
        align-items: center; 
        gap: 4px; 
        font-size: 0.8rem;
        margin-bottom: 4px;
    }
    .info-icon { color: #1976d2; }
    .info-label { color: #666; font-weight: 500; }
    .status-chip { 
        font-size: 0.7rem; 
        padding: 1px 4px; 
        min-height: 18px;
    }
</style>