﻿@page "/post/{Id:int}"

@using System.Text.RegularExpressions
@using System.IO
@using Microsoft.JSInterop
@using MudBlazor

@inject IJSRuntime JSRuntime
@inject ISnackbar _Snackbar

<PageTitle>@(article?.Title ?? "تحميل...") | @(_companyInfo?.Name ?? "شركتنا")</PageTitle>

<style>
    .absolute-fill {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
</style>

@if (isLoading)
{
    <div class="d-flex justify-center my-16">
        <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
    </div>
}
else if (article == null)
{
    <MudContainer MaxWidth="MaxWidth.Large" Class="py-16">
        <MudAlert Severity="Severity.Error" Class="my-8">
            عذراً، لم يتم العثور على المقال المطلوب.
        </MudAlert>
        <MudButton Variant="Variant.Outlined" Color="Color.Primary" Href="/">
            العودة إلى الصفحة الرئيسية
        </MudButton>
    </MudContainer>
}
else
{
    <!-- Hero Section with Article Image or Carousel -->
    <div class="article-hero-section">
        @if (carouselImages.Count > 0)
        {
            <div class="position-relative" style="height: 250px;">
                <MudCarousel Class="mud-width-full" Style="height:250px;" ShowArrows="true" ShowDelimiters="true" AutoCycle="true" TData="string">
                    @foreach (var imageUrl in carouselImages)
                    {
                        <MudCarouselItem Transition="Transition.Slide">
                               <MudImage Src="@imageUrl"  ObjectFit="ObjectFit.Fill" Width="100" Height="100" Class="absolute-fill" />
                            @* <div class="position-relative" style="background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('@imageUrl') no-repeat center center; background-size: cover; height: 500px;"> *@
                            <div class="position-relative" style="background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)),  no-repeat center center; background-size: cover; height: 500px;">
                                <div class="d-flex flex-column justify-end h-100 pb-8">
                                    <MudContainer>
                                        <MudChip T="string" Color="Color.Primary" Size="Size.Small" Class="mb-2">@article.CategoryName</MudChip>
                                        <MudText Typo="Typo.h2" Color="Color.Surface" Class="article-title">@article.Title</MudText>
                                        <MudText Typo="Typo.subtitle1" Color="Color.Surface" Class="mt-2 article-intro">@article.Introduction</MudText>
                                        <div class="d-flex align-center mt-4 article-meta">
                                            <MudIcon Icon="@Icons.Material.Filled.CalendarToday" Color="Color.Surface" Size="Size.Small" Class="mr-2" />
                                            <MudText Typo="Typo.body2" Color="Color.Surface">@article.PublishDate.ToString("yyyy-MM-dd")</MudText>
                                            <MudDivider Vertical="true" FlexItem="true" Class="mx-4" />
                                            <MudIcon Icon="@Icons.Material.Filled.Person" Color="Color.Surface" Size="Size.Small" Class="mr-2" />
                                            <MudText Typo="Typo.body2" Color="Color.Surface">@article.ClientName</MudText>
                                        </div>
                                    </MudContainer>
                                </div>
                            </div>
                        </MudCarouselItem>
                    }
                </MudCarousel>
            </div>
        }
        else
        {
            <!-- Fallback to single image if no carousel images -->
            <div class="position-relative" style="background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('@article.ImageUrl') no-repeat center center; background-size: cover; height: 500px;">
                <div class="d-flex flex-column justify-end h-100 pb-8">
                    <MudContainer>
                        <MudChip T="string" Color="Color.Primary" Size="Size.Small" Class="mb-2">@article.CategoryName</MudChip>
                        <MudText Typo="Typo.h2" Color="Color.Surface" Class="article-title">@article.Title</MudText>
                        <MudText Typo="Typo.subtitle1" Color="Color.Surface" Class="mt-2 article-intro">@article.Introduction</MudText>
                        <div class="d-flex align-center mt-4 article-meta">
                            <MudIcon Icon="@Icons.Material.Filled.CalendarToday" Color="Color.Surface" Size="Size.Small" Class="mr-2" />
                            <MudText Typo="Typo.body2" Color="Color.Surface">@article.PublishDate.ToString("yyyy-MM-dd")</MudText>
                            <MudDivider Vertical="true" FlexItem="true" Class="mx-4" />
                            <MudIcon Icon="@Icons.Material.Filled.Person" Color="Color.Surface" Size="Size.Small" Class="mr-2" />
                            <MudText Typo="Typo.body2" Color="Color.Surface">@article.ClientName</MudText>
                        </div>
                    </MudContainer>
                </div>
            </div>
        }
    </div>

    <MudContainer MaxWidth="MaxWidth.Large" Class="py-8">
        <MudGrid>
            <MudItem xs="12" md="8">
                <!-- Client Information Card -->
                <MudCard Elevation="3" Class="mb-6 client-card">
                    <MudCardHeader>
                        <CardHeaderAvatar>
                            <MudAvatar Color="Color.Primary" Size="Size.Large">@(article.ClientName?.Substring(0, 1) ?? "C")</MudAvatar>
                        </CardHeaderAvatar>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h5" Class="client-name">@article.ClientName</MudText>
                            <MudText Typo="Typo.subtitle2" Class="client-label">العميل</MudText>
                        </CardHeaderContent>
                        <CardHeaderActions>
                            <MudChip T="string" Icon="@Icons.Material.Filled.Star" Color="Color.Secondary" Size="Size.Small">عميل مميز</MudChip>
                        </CardHeaderActions>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudText Typo="Typo.body1" Class="mb-2">
                            <MudIcon Icon="@Icons.Material.Filled.Info" Class="mr-2" Size="Size.Small" />
                            <span class="font-weight-bold">معلومات عن العميل:</span>
                        </MudText>
                        <MudText Typo="Typo.body2" Class="mr-6 mb-3">
                            عميل مميز لدينا يتعاون معنا في العديد من المشاريع المتميزة.
                        </MudText>

                        <MudDivider Class="my-3" />

                        <div class="d-flex flex-wrap gap-2">
                            <MudButton Variant="Variant.Outlined"
                                      Color="Color.Primary"
                                      Size="Size.Small"
                                      StartIcon="@Icons.Material.Filled.ContactPage">
                                عرض الملف الكامل
                            </MudButton>
                            <MudButton Variant="Variant.Outlined"
                                      Color="Color.Secondary"
                                      Size="Size.Small"
                                      StartIcon="@Icons.Material.Filled.Article">
                                مقالات العميل
                            </MudButton>
                        </div>
                    </MudCardContent>
                </MudCard>

                <!-- Article Content -->
                <MudPaper Elevation="3" Class="pa-6 mb-6 article-main-content">
                    <!-- Article Header -->
                    <div class="d-flex justify-space-between align-center mb-4">
                        <MudText Typo="Typo.h4" Class="article-main-title">@article.Title</MudText>
                        <MudChip T="string" Icon="@Icons.Material.Filled.Category" Color="Color.Primary" Size="Size.Small">@article.CategoryName</MudChip>
                    </div>

                    <!-- Introduction Section -->
                    <MudCard Elevation="0" Class="mb-6 introduction-card">
                        <MudCardHeader>
                            <CardHeaderAvatar>
                                <MudIcon Icon="@Icons.Material.Filled.Description" Color="Color.Primary" />
                            </CardHeaderAvatar>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h5" Class="article-section-title">المقدمة</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudText Typo="Typo.body1" Class="article-introduction">
                                @article.Introduction
                            </MudText>
                        </MudCardContent>
                    </MudCard>

                    <MudDivider Class="my-6" />

                    <!-- Video Section - Only show if VideoUrl exists -->
                    @if (!string.IsNullOrEmpty(article.VideoUrl))
                    {
                        <MudCard Elevation="0" Class="mb-6 video-card">
                            <MudCardHeader>
                                <CardHeaderAvatar>
                                    <MudIcon Icon="@Icons.Material.Filled.VideoLibrary" Color="Color.Primary" />
                                </CardHeaderAvatar>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h5" Class="article-section-title">الفيديو</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <div class="video-container mb-4">
                                    <video controls width="100%" style="max-height: 500px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                                        <source src="@article.VideoUrl" type="video/mp4" />
                                        متصفحك لا يدعم تشغيل الفيديو.
                                    </video>
                                </div>
                            </MudCardContent>
                        </MudCard>
                        <MudDivider Class="my-6" />
                    }

                    <!-- Article Content -->
                    <MudCard Elevation="0" Class="mb-6 content-card">
                        <MudCardHeader>
                            <CardHeaderAvatar>
                                <MudIcon Icon="@Icons.Material.Filled.Article" Color="Color.Primary" />
                            </CardHeaderAvatar>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h5" Class="article-section-title">المحتوى</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <div class="article-content">
                                @((MarkupString)FormatContent(article.Content))
                            </div>
                        </MudCardContent>
                    </MudCard>

                    <!-- Image Carousel/Slider (inside content) -->
                    @if (carouselImages.Count > 1)
                    {
                        <MudDivider Class="my-6" />

                        <MudCard Elevation="0" Class="mb-6 gallery-card">
                            <MudCardHeader>
                                <CardHeaderAvatar>
                                    <MudIcon Icon="@Icons.Material.Filled.PhotoLibrary" Color="Color.Primary" />
                                </CardHeaderAvatar>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h5" Class="article-section-title">معرض الصور</MudText>
                                </CardHeaderContent>
                                <CardHeaderActions>
                                    <MudText Typo="Typo.caption">@carouselImages.Count صورة</MudText>
                                </CardHeaderActions>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudPaper Elevation="0" Class="mt-2 pa-0">
                                    <MudCarousel Class="mud-width-full"
                                               Style="height:400px; border-radius: 8px; overflow: hidden;"
                                               ShowArrows="true"
                                               ShowDelimiters="true"
                                               AutoCycle="true"
                                               TData="string">
                                        @foreach (var imageUrl in carouselImages)
                                        {
                                            <MudCarouselItem Transition="Transition.Slide">
                                                <div class="position-relative h-100">
                                                    <img src="@imageUrl" alt="صورة المقال" style="width: 100%; height: 100%; object-fit: cover;" />
                                                    <div class="image-overlay d-flex justify-center align-center">
                                                        <MudIconButton Icon="@Icons.Material.Filled.ZoomIn"
                                                                     Color="Color.Surface"
                                                                     Size="Size.Large"
                                                                     Class="image-zoom-button" />
                                                    </div>
                                                </div>
                                            </MudCarouselItem>
                                        }
                                    </MudCarousel>
                                </MudPaper>

                                <!-- Thumbnails -->
                                <div class="d-flex flex-wrap justify-center gap-2 mt-4">
                                    @for (int i = 0; i < Math.Min(carouselImages.Count, 5); i++)
                                    {
                                        <MudAvatar Image="@carouselImages[i]"
                                                 Size="Size.Medium"
                                                 Square="true"
                                                 Class="thumbnail-image" />
                                    }
                                    @if (carouselImages.Count > 5)
                                    {
                                        <MudAvatar Color="Color.Primary"
                                                 Size="Size.Medium"
                                                 Square="true"
                                                 Class="thumbnail-image">
                                            +@(carouselImages.Count - 5)
                                        </MudAvatar>
                                    }
                                </div>
                            </MudCardContent>
                        </MudCard>
                    }
                    else if (!string.IsNullOrEmpty(article.ImageCarousel) && article.ImageCarousel != article.ImageUrl)
                    {
                        <MudDivider Class="my-6" />

                        <MudCard Elevation="0" Class="mb-6 gallery-card">
                            <MudCardHeader>
                                <CardHeaderAvatar>
                                    <MudIcon Icon="@Icons.Material.Filled.Photo" Color="Color.Primary" />
                                </CardHeaderAvatar>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h5" Class="article-section-title">صورة إضافية</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <div class="position-relative">
                                    <img src="@article.ImageCarousel" alt="صورة المقال" class="rounded" style="width: 100%; border-radius: 8px;" />
                                    <div class="image-overlay d-flex justify-center align-center">
                                        <MudIconButton Icon="@Icons.Material.Filled.ZoomIn"
                                                     Color="Color.Surface"
                                                     Size="Size.Large"
                                                     Class="image-zoom-button" />
                                    </div>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    }

                    <!-- Share Buttons -->
                    <MudDivider Class="my-6" />

                    <MudCard Elevation="0" Class="mb-6 share-card">
                        <MudCardHeader>
                            <CardHeaderAvatar>
                                <MudIcon Icon="@Icons.Material.Filled.Share" Color="Color.Primary" />
                            </CardHeaderAvatar>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h5" Class="article-section-title">مشاركة المقال</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudText Typo="Typo.body2" Class="mb-4">شارك هذا المقال مع أصدقائك على وسائل التواصل الاجتماعي</MudText>

                            <div class="d-flex flex-wrap gap-3 share-buttons">
                                <MudButton StartIcon="@Icons.Custom.Brands.Facebook"
                                         Color="Color.Primary"
                                         Variant="Variant.Filled"
                                         Class="share-button facebook-button">
                                    فيسبوك
                                </MudButton>
                                <MudButton StartIcon="@Icons.Custom.Brands.Twitter"
                                         Color="Color.Info"
                                         Variant="Variant.Filled"
                                         Class="share-button twitter-button">
                                    تويتر
                                </MudButton>
                                <MudButton StartIcon="@Icons.Custom.Brands.WhatsApp"
                                         Color="Color.Success"
                                         Variant="Variant.Filled"
                                         Class="share-button whatsapp-button">
                                    واتساب
                                </MudButton>
                                <MudButton StartIcon="@Icons.Material.Filled.Link"
                                         Color="Color.Default"
                                         Variant="Variant.Filled"
                                         Class="share-button link-button">
                                    نسخ الرابط
                                </MudButton>
                            </div>

                            <MudTextField @bind-Value="shareUrl"
                                        Label="رابط المقال"
                                        ReadOnly="true"
                                        Adornment="Adornment.End"
                                        AdornmentIcon="@Icons.Material.Filled.ContentCopy"
                                        OnAdornmentClick="CopyShareUrl"
                                        Class="mt-4" />
                        </MudCardContent>
                    </MudCard>
                </MudPaper>
            </MudItem>

            <MudItem xs="12" md="4">
                <!-- Sidebar -->
                <MudPaper Elevation="3" Class="pa-6 sidebar-content">
                    <MudText Typo="Typo.h5" Class="mb-4 sidebar-title">معلومات المقال</MudText>

                    <MudList T="string" Clickable="false">
                        <MudListItem T="string" Icon="@Icons.Material.Filled.Category">
                            <div>
                                <MudText Typo="Typo.subtitle2">التصنيف</MudText>
                                <MudText Typo="Typo.body1">@article.CategoryName</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem T="string" Icon="@Icons.Material.Filled.CalendarToday">
                            <div>
                                <MudText Typo="Typo.subtitle2">تاريخ النشر</MudText>
                                <MudText Typo="Typo.body1">@article.PublishDate.ToString("yyyy-MM-dd")</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem T="string" Icon="@Icons.Material.Filled.Person">
                            <div>
                                <MudText Typo="Typo.subtitle2">العميل</MudText>
                                <MudText Typo="Typo.body1">@article.ClientName</MudText>
                            </div>
                        </MudListItem>
                    </MudList>

                    <MudDivider Class="my-4" />

                    <MudText Typo="Typo.h5" Class="mb-4 mt-6 sidebar-title">مقالات ذات صلة</MudText>

                    @if (relatedArticles.Count > 0)
                    {
                        <MudList T="ArticleDto">
                            @foreach (var relatedArticle in relatedArticles)
                            {
                                <MudListItem T="ArticleDto" Icon="@Icons.Material.Filled.Article" OnClick="@(() => NavigateToArticle(relatedArticle.Id))">
                                    <div>
                                        <MudText Typo="Typo.body1">@relatedArticle.Title</MudText>
                                        <MudText Typo="Typo.caption">@relatedArticle.PublishDate.ToString("yyyy-MM-dd")</MudText>
                                    </div>
                                </MudListItem>
                            }
                        </MudList>
                    }
                    else
                    {
                        <MudAlert Severity="Severity.Info" Class="mt-3">لا توجد مقالات ذات صلة حاليًا.</MudAlert>
                    }
                </MudPaper>
            </MudItem>
        </MudGrid>
    </MudContainer>
}

@code {
    [Parameter]
    public int Id { get; set; }

    private ArticleDto? article;
    private CompanyInfo? _companyInfo;
    private bool isLoading = true;
    private List<ArticleDto> relatedArticles = new List<ArticleDto>();
    private List<string> carouselImages = new List<string>();
    private string shareUrl = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Load company info
            try
            {
                _companyInfo = await Http.GetFromJsonAsync<CompanyInfo>("/api/CompanyInfo/1");
            }
            catch
            {
                // Default company info if API fails
                _companyInfo = new CompanyInfo
                {
                    Name = "شركتنا",
                    Vision = "أن نكون الشركة الرائدة في مجال عملنا",
                    Mission = "توفير خدمات عالية الجودة لعملائنا",
                    AboutUs = "نحن شركة رائدة في مجال تكنولوجيا المعلومات.",
                    Phone = "+966123456789",
                    Email = "<EMAIL>",
                    Address = "الرياض، المملكة العربية السعودية"
                };
            }

            await LoadArticle();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading article: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (Id > 0)
        {
            await LoadArticle();
        }
    }

    private async Task LoadArticle()
    {
        try
        {
            isLoading = true;

            // Load the article
            article = await Http.GetFromJsonAsync<ArticleDto>($"api/Article/{Id}");

            if (article != null)
            {
                // Load related articles (same category)
                await LoadRelatedArticles();

                // Load carousel images
                await LoadCarouselImages();

                // Set share URL
                var baseUrl = _Navigation.BaseUri;
                shareUrl = $"{baseUrl}post/{Id}";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading article: {ex.Message}");
            article = null;
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task CopyShareUrl()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", shareUrl);
            _Snackbar.Add("تم نسخ الرابط بنجاح", Severity.Success);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error copying URL: {ex.Message}");
            _Snackbar.Add("حدث خطأ أثناء نسخ الرابط", Severity.Error);
        }
    }

    private async Task LoadCarouselImages()
    {
        try
        {
            // Clear existing images
            carouselImages.Clear();

            if (article == null || string.IsNullOrEmpty(article.ImageCarousel))
                return;

            // Add the main carousel image first
            carouselImages.Add(article.ImageCarousel);

            // Extract article ID from the image path
            // Example: /Sider/article_123_guid.jpg
            string fileName = Path.GetFileName(article.ImageCarousel);

            if (fileName.StartsWith("article_"))
            {
                // Extract article ID from the file name
                int underscoreIndex = fileName.IndexOf('_');
                int secondUnderscoreIndex = fileName.IndexOf('_', underscoreIndex + 1);

                if (underscoreIndex > 0 && secondUnderscoreIndex > underscoreIndex)
                {
                    string articleIdStr = fileName.Substring(underscoreIndex + 1, secondUnderscoreIndex - underscoreIndex - 1);

                    if (int.TryParse(articleIdStr, out int articleId) && articleId > 0)
                    {
                        // Get all slider images for this article
                        try
                        {
                            var sliderImages = await Http.GetFromJsonAsync<List<string>>($"api/Article/{articleId}/slider-images");

                            if (sliderImages != null && sliderImages.Count > 0)
                            {
                                // Replace the list with the complete list from the server
                                carouselImages = sliderImages;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error loading slider images: {ex.Message}");
                            // Keep using just the main carousel image
                        }
                    }
                }
            }

            // If we couldn't get additional images, at least use the main image and the article image
            if (carouselImages.Count <= 1 && !string.IsNullOrEmpty(article.ImageUrl))
            {
                carouselImages.Add(article.ImageUrl);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in LoadCarouselImages: {ex.Message}");

            // Fallback: use just the main carousel image if available
            carouselImages.Clear();
            if (article != null && !string.IsNullOrEmpty(article.ImageCarousel))
            {
                carouselImages.Add(article.ImageCarousel);
            }
        }
    }

    private async Task LoadRelatedArticles()
    {
        try
        {
            // Get all articles
            var allArticles = await Http.GetFromJsonAsync<List<ArticleDto>>("api/Article");

            // Filter related articles (same category, excluding current article)
            relatedArticles = allArticles
                .Where(a => a.CategoryId == article.CategoryId && a.Id != article.Id)
                .Take(3)
                .ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading related articles: {ex.Message}");
        }
    }

    private void NavigateToArticle(int articleId)
    {
        _Navigation.NavigateTo($"/post/{articleId}");
    }

    private string FormatContent(string content)
    {
        if (string.IsNullOrEmpty(content))
            return string.Empty;

        // Remove any existing video tags from the content
        // This is for backward compatibility with articles that have videos embedded in content
        var videoRegex = new Regex(@"<div class=""video-container"">.*?</div>", RegexOptions.Singleline);
        content = videoRegex.Replace(content, string.Empty);

        // Add paragraph tags if needed
        if (!content.Contains("<p>"))
        {
            content = "<p>" + content.Replace("\n\n", "</p><p>").Replace("\r\n\r\n", "</p><p>") + "</p>";
        }

        return content;
    }
}

<style>
    /* Hero Section */
    .article-hero-section {
        margin-bottom: 2rem;
    }

    .article-title {
        font-weight: 700;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    }

    .article-intro {
        max-width: 800px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .article-meta {
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    /* Main Content */
    .article-main-content {
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .article-main-content:hover {
        box-shadow: 0 6px 16px rgba(0,0,0,0.15);
    }

    .article-section-title {
        color: var(--mud-palette-primary);
        font-weight: 600;
        border-right: 4px solid var(--mud-palette-primary);
        padding-right: 12px;
    }

    .article-content {
        line-height: 1.8;
        font-size: 1.1rem;
    }

    .article-content p {
        margin-bottom: 1.5rem;
    }

    .article-introduction {
        font-size: 1.2rem;
        line-height: 1.8;
        color: var(--mud-palette-text-primary);
        background-color: rgba(0,0,0,0.02);
        padding: 1rem;
        border-radius: 4px;
        border-right: 3px solid var(--mud-palette-primary-lighten);
    }

    /* Client Card */
    .client-card {
        border-radius: 8px;
        transition: all 0.3s ease;
        border-top: 4px solid var(--mud-palette-primary);
    }

    .client-card:hover {
        box-shadow: 0 6px 16px rgba(0,0,0,0.15);
    }

    .client-name {
        font-weight: 600;
        color: var(--mud-palette-primary);
    }

    .client-label {
        color: var(--mud-palette-text-secondary);
        font-size: 0.9rem;
    }

    /* Sidebar */
    .sidebar-content {
        border-radius: 8px;
        height: 100%;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .sidebar-content:hover {
        box-shadow: 0 6px 16px rgba(0,0,0,0.15);
    }

    .sidebar-title {
        color: var(--mud-palette-primary);
        font-weight: 600;
        border-right: 4px solid var(--mud-palette-primary);
        padding-right: 12px;
    }

    /* Video Container */
    .video-container {
        position: relative;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        height: 0;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .video-container video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 8px;
    }

    /* Gallery and Images */
    .gallery-card {
        border-radius: 8px;
        overflow: hidden;
    }

    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.4);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .position-relative:hover .image-overlay {
        opacity: 1;
    }

    .image-zoom-button {
        transform: scale(0.8);
        transition: transform 0.3s ease;
    }

    .position-relative:hover .image-zoom-button {
        transform: scale(1);
    }

    .thumbnail-image {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .thumbnail-image:hover {
        transform: translateY(-3px);
        border: 2px solid var(--mud-palette-primary);
    }

    /* Article Main Title */
    .article-main-title {
        color: var(--mud-palette-primary-darken);
        font-weight: 700;
        border-right: 4px solid var(--mud-palette-primary);
        padding-right: 12px;
    }

    /* Introduction Card */
    .introduction-card {
        background-color: rgba(0,0,0,0.02);
        border-radius: 8px;
    }

    /* Content Card */
    .content-card {
        background-color: white;
        border-radius: 8px;
    }

    /* Share Card */
    .share-card {
        background-color: rgba(0,0,0,0.02);
        border-radius: 8px;
    }

    .share-button {
        transition: all 0.3s ease;
        border-radius: 20px;
        min-width: 120px;
    }

    .share-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .facebook-button:hover {
        background-color: #1877f2 !important;
    }

    .twitter-button:hover {
        background-color: #1da1f2 !important;
    }

    .whatsapp-button:hover {
        background-color: #25d366 !important;
    }

    .link-button:hover {
        background-color: #424242 !important;
        color: white;
    }

    /* Responsive Adjustments */
    @@media (max-width: 600px) {
        .article-title {
            font-size: 1.5rem;
        }

        .article-intro {
            font-size: 1rem;
        }

        .article-content {
            font-size: 1rem;
        }

        .article-main-title {
            font-size: 1.4rem;
        }
    }
</style>
