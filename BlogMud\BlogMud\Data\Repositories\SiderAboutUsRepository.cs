using BlogMud.Data;
using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;
using Microsoft.EntityFrameworkCore;

namespace BlogMud.Data.Repositories
{
    /// <summary>
    /// تنفيذ مستودع بيانات الشرائح المتحركة لصفحة "من نحن"
    /// </summary>
    public class SiderAboutUsRepository : Repository<SiderAboutUs>, ISiderAboutUsRepository
    {
        public SiderAboutUsRepository(ApplicationDbContext context) : base(context)
        {
        }

        /// <summary>
        /// الحصول على الشرائح النشطة مرتبة حسب ترتيب العرض
        /// </summary>
        /// <returns>قائمة بالشرائح النشطة</returns>
        public async Task<IEnumerable<SiderAboutUs>> GetActiveSlideshowsAsync()
        {
            return await GetAllAsync(
                filter: s => s.IsActive,
                orderBy: q => q.OrderBy(s => s.DisplayOrder).ThenBy(s => s.CreatedAt)
            );
        }

        /// <summary>
        /// الحصول على أول شريحة نشطة
        /// </summary>
        /// <returns>أول شريحة نشطة أو null إذا لم توجد</returns>
        public async Task<SiderAboutUs?> GetFirstActiveSlideshowAsync()
        {
            return await GetFirstOrDefaultAsync(
                filter: s => s.IsActive,
                includeProperties: ""
            );
        }

        /// <summary>
        /// الحصول على جميع الشرائح مرتبة حسب ترتيب العرض
        /// </summary>
        /// <returns>قائمة بجميع الشرائح</returns>
        public async Task<IEnumerable<SiderAboutUs>> GetAllOrderedAsync()
        {
            return await GetAllAsync(
                orderBy: q => q.OrderBy(s => s.DisplayOrder).ThenBy(s => s.CreatedAt)
            );
        }
    }
}
