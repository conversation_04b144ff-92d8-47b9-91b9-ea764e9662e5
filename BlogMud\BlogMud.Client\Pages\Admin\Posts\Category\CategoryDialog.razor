

<MudDialog>
    <DialogContent>
        <MudForm @ref="form" @bind-IsValid="@success">
            <MudTextField @bind-Value="categoryDto.Name" Label="اسم القسم" Required="true"
                RequiredError="يج<PERSON> إدخال اسم القسم" />
            <MudTextField @bind-Value="categoryDto.Description" Label="وصف القسم" Lines="3" Class="mt-3" />
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">إلغاء</MudButton>
        <MudButton Color="Color.Primary" OnClick="Submit" Disabled="@(!success)">حفظ</MudButton>
    </DialogActions>
</MudDialog>

@code {
    #region الخصائص والمتغيرات

    /// <summary>
    /// مثيل مربع الحوار MudDialog الذي يتم تمريره من خلال CascadingParameter
    /// </summary>
    /// <remarks>
    /// يستخدم للتحكم في إغلاق مربع الحوار وإرجاع النتائج
    /// </remarks>
    [CascadingParameter] public required IMudDialogInstance MudDialog { get; set; }

    /// <summary>
    /// بيانات القسم التي يتم تمريرها إلى مربع الحوار عند التعديل
    /// </summary>
    /// <remarks>
    /// إذا كانت القيمة null، فهذا يعني أننا نقوم بإنشاء قسم جديد
    /// </remarks>
    [Parameter] public CategoryDto? CategoryDto { get; set; }

    /// <summary>
    /// نموذج بيانات القسم الذي يتم تعديله أو إنشاؤه
    /// </summary>
    private CategoryDto categoryDto = new CategoryDto();

    /// <summary>
    /// مؤشر على صحة النموذج
    /// </summary>
    private bool success;

    /// <summary>
    /// مرجع إلى نموذج MudForm المستخدم للتحقق من صحة البيانات
    /// </summary>
    private MudForm? form;
    #endregion

    #region دوال دورة الحياة

    /// <summary>
    /// تهيئة مكونات الصفحة عند بدء التشغيل
    /// </summary>
    /// <remarks>
    /// إذا كان هناك قسم موجود (تعديل)، يتم نسخ بياناته إلى النموذج الحالي
    /// </remarks>
    protected override void OnInitialized()
    {
        if (CategoryDto != null)
        {
            categoryDto = new CategoryDto
            {
                Id = CategoryDto.Id,
                Name = CategoryDto.Name,
                Description = CategoryDto.Description,
                ArticleCount = CategoryDto.ArticleCount
            };
        }
    }
    #endregion

    #region الأحداث والتفاعلات

    /// <summary>
    /// إلغاء العملية وإغلاق مربع الحوار
    /// </summary>
    private void Cancel()
    {
        MudDialog.Cancel();
    }

    /// <summary>
    /// حفظ بيانات القسم
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بالتحقق من صحة النموذج أولاً، ثم إرسال البيانات إلى الخادم
    /// إذا كان معرف القسم أكبر من صفر، يتم تحديث قسم موجود، وإلا يتم إنشاء قسم جديد
    /// </remarks>
    private async Task Submit()
    {
        // التحقق من صحة النموذج
        if (form != null)
            await form.Validate();
        if (!success) return;

        try
        {
            HttpResponseMessage response;

            // تحديد ما إذا كان تحديث أو إنشاء جديد
            if (categoryDto.Id > 0)
            {
                // تحديث قسم موجود
                response = await Http.PutAsJsonAsync($"api/Categories/{categoryDto.Id}", categoryDto);
            }
            else
            {
                // إنشاء قسم جديد
                response = await Http.PostAsJsonAsync("api/Categories", categoryDto);
            }

            // معالجة الاستجابة
            if (response.IsSuccessStatusCode)
            {
                _Snackbar.Add("تم حفظ القسم بنجاح", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _Snackbar.Add($"فشل في حفظ القسم: {errorContent}", Severity.Error);
                Console.WriteLine($"Error saving category: {errorContent}");
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء حفظ القسم: {ex.Message}", Severity.Error);
        }
    }
    #endregion
}
