3@page "/admin/sidertMoveServices"
@attribute [Authorize(Roles = "Admin")]
@layout BlogMud.Client.Layout.AdminLayout

@using BlogMud.Shared.DTOs
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Authorization

<PageTitle>إدارة الشرائح المتحركة والصور المتحركة</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-6">
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h4">إدارة الشرائح المتحركة والصور المتحركة</MudText>
        <MudButton Variant="Variant.Filled"
                   Color="Color.Primary"
                   StartIcon="@Icons.Material.Filled.Add"
                   OnClick="OpenCreateDialog">
            إضافة شريحة متحركة جديدة
        </MudButton>
    </div>

    <!-- معاينة الشرائح المتحركة النشطة -->
    <MudGrid Class="mb-6">
        <MudItem xs="12">
            <MudPaper Class="pa-4 mb-4">
                <MudText Typo="Typo.h6" Class="mb-3">معاينة الشرائح المتحركة النشطة</MudText>

                @if (_sidertMoveServices.Any(s => s.IsActive))
                {
                    <MudCarousel Class="mud-width-full" Style="height:350px;" ShowArrows="@arrows" ShowBullets="@bullets"
                                 EnableSwipeGesture="@enableSwipeGesture" AutoCycle="@autocycle" TData="object">
                        @foreach (var slideshow in _sidertMoveServices.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder))
                        {
                            @if (slideshow.Images.Any())
                            {
                                @foreach (var imageUrl in slideshow.Images)
                                {
                                    <MudCarouselItem Transition="transition">
                                        <div class="d-flex flex-column" style="height:100%">
                                            <img src="@imageUrl" style="max-height: 280px; object-fit: contain;" alt="@slideshow.Title" />
                                            <div class="mt-2 text-center">
                                                <MudText Typo="Typo.subtitle1">@slideshow.Title</MudText>
                                                @if (!string.IsNullOrEmpty(slideshow.Description))
                                                {
                                                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                                                        @(slideshow.Description.Length > 100 ?
                                                          slideshow.Description.Substring(0, 100) + "..." :
                                                          slideshow.Description)
                                                    </MudText>
                                                }
                                                @if (!string.IsNullOrEmpty(slideshow.LinkUrl) && !string.IsNullOrEmpty(slideshow.LinkText))
                                                {
                                                    <MudButton Variant="Variant.Outlined"
                                                              Color="Color.Primary"
                                                              Size="Size.Small"
                                                              Class="mt-2"
                                                              Href="@slideshow.LinkUrl"
                                                              Target="_blank">
                                                        @slideshow.LinkText
                                                    </MudButton>
                                                }
                                            </div>
                                        </div>
                                    </MudCarouselItem>
                                }
                            }
                            else
                            {
                                <MudCarouselItem Transition="transition">
                                    <div class="d-flex flex-column align-center justify-center" style="height:100%">
                                        <MudIcon Icon="@Icons.Material.Filled.Image" Size="Size.Large" Color="Color.Secondary" />
                                        <MudText Typo="Typo.subtitle1" Class="mt-2">@slideshow.Title</MudText>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">لا توجد صور</MudText>
                                    </div>
                                </MudCarouselItem>
                            }
                        }
                    </MudCarousel>

                    <!-- إعدادات المعاينة -->
                    <div class="d-flex flex-wrap gap-4 mt-4">
                        <MudSelect @bind-Value="transition" Label="نوع الانتقال" Class="flex-grow-0" Style="min-width: 150px;">
                            <MudSelectItem Value="@Transition.Fade">تلاشي</MudSelectItem>
                            <MudSelectItem Value="@Transition.Slide">انزلاق</MudSelectItem>
                            <MudSelectItem Value="@Transition.None">بدون انتقال</MudSelectItem>
                        </MudSelect>
                        <MudSwitch @bind-Value="arrows" Color="Color.Primary" Label="إظهار الأسهم" />
                        <MudSwitch @bind-Value="bullets" Color="Color.Primary" Label="إظهار النقاط" />
                        <MudSwitch @bind-Value="enableSwipeGesture" Color="Color.Primary" Label="تمكين السحب" />
                        <MudSwitch @bind-Value="autocycle" Color="Color.Primary" Label="التشغيل التلقائي" />
                    </div>
                }
                else
                {
                    <MudAlert Severity="Severity.Info">لا توجد شرائح متحركة نشطة للعرض</MudAlert>
                }
            </MudPaper>
        </MudItem>
    </MudGrid>

    <!-- شريط البحث والفلترة -->
    <MudPaper Class="pa-4 mb-4">
        <MudGrid>
            <MudItem xs="12" md="6">
                <MudTextField @bind-Value="_searchString"
                             Label="البحث في الشرائح المتحركة"
                             Adornment="Adornment.Start"
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             IconSize="Size.Medium"
                             Class="mt-0" />
            </MudItem>
            <MudItem xs="12" md="6" Class="d-flex align-center justify-end">
                <MudText Typo="Typo.body2" Color="Color.Secondary">
                    إجمالي الشرائح: @_sidertMoveServices.Count | النشطة: @_sidertMoveServices.Count(s => s.IsActive) | غير النشطة: @_sidertMoveServices.Count(s => !s.IsActive)
                </MudText>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- جدول الشرائح المتحركة -->
    <MudPaper Class="pa-4">
        <MudTable Items="@_sidertMoveServices" Loading="@_loading" Filter="FilterFunc" Dense="true" Hover="true" Bordered="true">
            <HeaderContent>
                <MudTh>المعرف</MudTh>
                <MudTh>العنوان</MudTh>
                <MudTh>الوصف</MudTh>
                <MudTh>عدد الصور</MudTh>
                <MudTh>الترتيب</MudTh>
                <MudTh>المدة (ثانية)</MudTh>
                <MudTh>نشط</MudTh>
                <MudTh>تاريخ الإنشاء</MudTh>
                <MudTh>الإجراءات</MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="ت">@(_sidertMoveServices.IndexOf(context) + 1)</MudTd>
                <MudTd>@context.Title</MudTd>
                <MudTd>@(context.Description?.Length > 30 ? context.Description.Substring(0, 30) + "..." : context.Description)</MudTd>
                <MudTd>
                    <MudChip T="string" Color="Color.Primary" Size="Size.Small">
                        @context.Images.Count صورة
                    </MudChip>
                </MudTd>
                <MudTd>@context.DisplayOrder</MudTd>
                <MudTd>@context.Duration</MudTd>
                <MudTd>
                    <MudIcon Icon="@(context.IsActive ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)"
                             Color="@(context.IsActive ? Color.Success : Color.Error)" />
                </MudTd>
                <MudTd>@context.CreatedAt.ToString("yyyy-MM-dd")</MudTd>
                <MudTd>
                    <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                   Color="Color.Info"
                                   OnClick="@(() => OpenDetailsDialog(context))"
                                   Title="عرض التفاصيل" />
                    <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                   Color="Color.Primary"
                                   OnClick="@(() => OpenEditDialog(context))"
                                   Title="تعديل" />
                    <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                   Color="Color.Error"
                                   OnClick="@(() => DeleteSidertMoveService(context.Id))"
                                   Title="حذف" />
                </MudTd>
            </RowTemplate>
            <NoRecordsContent>
                <MudText>لا توجد شرائح متحركة</MudText>
            </NoRecordsContent>
            <LoadingContent>
                <MudText>جاري التحميل...</MudText>
            </LoadingContent>
            <PagerContent>
                <MudTablePager />
            </PagerContent>
        </MudTable>
    </MudPaper>
</MudContainer>

