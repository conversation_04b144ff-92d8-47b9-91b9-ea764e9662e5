using BlogMud.Client.Pages;
using BlogMud.Components;
using BlogMud.Components.Account;
using BlogMud.Data;
using BlogMud.Data.Repositories;
using BlogMud.Services;
using BlogMud.Shared.Mapping;
using BlogMud.Shared.Repositories;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MudBlazor.Services;
using AutoMapper;

var builder = WebApplication.CreateBuilder(args);

// Add MudBlazor services
builder.Services.AddMudServices();

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddInteractiveWebAssemblyComponents();

// Add controllers for API endpoints
builder.Services.AddControllers();

builder.Services.AddCascadingAuthenticationState();
builder.Services.AddScoped<IdentityUserAccessor>();
builder.Services.AddScoped<IdentityRedirectManager>();
builder.Services.AddScoped<AuthenticationStateProvider, PersistingRevalidatingAuthenticationStateProvider>();

// استخدام AddIdentity سيقوم بإضافة خدمات المصادقة، لذا لا حاجة لإضافة AddAuthentication بشكل منفصل
//Server
//DefaultConnection
var connectionString = builder.Configuration.GetConnectionString("Server") ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString));
builder.Services.AddDatabaseDeveloperPageExceptionFilter();

builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
{
    // Require email confirmation for account activation
    options.SignIn.RequireConfirmedAccount = true;

    // Relaxed password requirements for testing
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireUppercase = false; // Made optional
    options.Password.RequireNonAlphanumeric = false; // Made optional
    options.Password.RequiredLength = 6; // Reduced from 8

    // User settings
    options.User.RequireUniqueEmail = true;
    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
})
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddSignInManager()
    .AddDefaultTokenProviders();

// تكوين خيارات cookie
builder.Services.ConfigureApplicationCookie(options =>
{
    options.Cookie.HttpOnly = true;
    options.ExpireTimeSpan = TimeSpan.FromHours(24);
    options.SlidingExpiration = true;
    // توجيه المستخدم إلى صفحة تسجيل الدخول المخصصة
    options.LoginPath = "/Account/Login";
    // ملاحظة: خيار RegisterPath غير متوفر في CookieAuthenticationOptions
    // يتم التعامل مع مسار التسجيل عبر تكوين نقاط نهاية Identity
});

// Configure email settings
builder.Services.Configure<BlogMud.Services.EmailSettings>(
    builder.Configuration.GetSection("EmailSettings"));

// Register email and notification services
builder.Services.AddScoped<BlogMud.Services.EmailService>();

// Use real email service if SMTP credentials are configured, otherwise use development service
if (builder.Environment.IsDevelopment())
{
    // Check if real SMTP credentials are configured
    var emailSettings = builder.Configuration.GetSection("EmailSettings");
    var password = emailSettings["Password"];

    if (!string.IsNullOrEmpty(password) && password != "your-gmail-app-password-here")
    {
        // Use real email service with SMTP
        builder.Services.AddScoped<IEmailSender<ApplicationUser>, BlogMud.Services.EmailService>();
    }
    else
    {
        // Use development email service (simulation)
        builder.Services.AddScoped<IEmailSender<ApplicationUser>, BlogMud.Services.DevelopmentEmailService>();
    }
}
else
{
    builder.Services.AddScoped<IEmailSender<ApplicationUser>, BlogMud.Services.EmailService>();
}
builder.Services.AddScoped<BlogMud.Services.INotificationService, BlogMud.Services.NotificationService>();
builder.Services.AddScoped<BlogMud.Services.INewsletterService, BlogMud.Services.NewsletterService>();

// Add AutoMapper
var mapperConfig = new MapperConfiguration(mc =>
{
    mc.AddProfile(new MappingProfile());
});
builder.Services.AddSingleton(mapperConfig.CreateMapper());

// Add Repository and UnitOfWork
builder.Services.AddScoped(typeof(BlogMud.Shared.Repositories.IRepository<>), typeof(Repository<>));
builder.Services.AddScoped(typeof(BlogMud.Shared.Repositories.IGRepository<>), typeof(GRepository<>));
builder.Services.AddScoped<BlogMud.Shared.Repositories.IUnitOfWork, UnitOfWork>();

// Add specific repositories
builder.Services.AddScoped<BlogMud.Data.Repositories.IAboutUsRepository, BlogMud.Data.Repositories.AboutUsRepository>();
builder.Services.AddScoped<BlogMud.Data.Repositories.ISiderAboutUsRepository, BlogMud.Data.Repositories.SiderAboutUsRepository>();

// Add File Service
builder.Services.AddScoped<IFileService, FileService>();

// Add HttpClient for WebAssembly components
builder.Services.AddScoped(sp =>
{
    var baseAddress = builder.Configuration["BaseAddress"];
    if (string.IsNullOrEmpty(baseAddress))
    {
        baseAddress = "https://localhost:7009";
    }
    return new HttpClient { BaseAddress = new Uri(baseAddress) };
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
    app.UseMigrationsEndPoint();
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseAntiforgery();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode()
    .AddAdditionalAssemblies(typeof(BlogMud.Client._Imports).Assembly);

// تخصيص نقاط نهاية Identity لاستخدام الصفحات المخصصة
app.MapAdditionalIdentityEndpoints();

// Add controllers with API endpoints
app.MapControllers();



// Initialize the database with the admin user and roles
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        await DbInitializer.Initialize(services, app.Configuration);
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while seeding the database.");
    }
}

app.Run();
