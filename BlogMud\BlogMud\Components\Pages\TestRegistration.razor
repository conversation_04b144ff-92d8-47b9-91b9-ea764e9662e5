@page "/test-registration"
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore
@using BlogMud.Data
@using BlogMud.Services
@inject UserManager<ApplicationUser> UserManager
@inject RoleManager<IdentityRole> RoleManager
@inject ILogger<TestRegistration> Logger

<PageTitle>اختبار التسجيل</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium" Class="mt-8">
    <MudPaper Elevation="3" Class="pa-8">
        <MudText Typo="Typo.h4" Align="Align.Center" GutterBottom="true" Class="mb-6">
            اختبار نظام التسجيل
        </MudText>

        <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="TestDatabaseConnection" Class="mb-4">
            اختبار الاتصال بقاعدة البيانات
        </MudButton>

        <MudButton Variant="Variant.Filled" Color="Color.Secondary" OnClick="TestRoleCreation" Class="mb-4 ml-2">
            اختبار إنشاء الأدوار
        </MudButton>

        <MudButton Variant="Variant.Filled" Color="Color.Success" OnClick="TestUserCreation" Class="mb-4 ml-2">
            اختبار إنشاء المستخدم
        </MudButton>

        <MudDivider Class="my-4" />

        <MudText Typo="Typo.h6" Class="mb-4">نتائج الاختبار:</MudText>

        @if (testResults.Any())
        {
            <MudList T="string">
                @foreach (var result in testResults)
                {
                    <MudListItem>
                        <MudText Color="@(result.IsSuccess ? Color.Success : Color.Error)">
                            @result.Message
                        </MudText>
                    </MudListItem>
                }
            </MudList>
        }

        <MudDivider Class="my-4" />

        <MudText Typo="Typo.h6" Class="mb-4">إحصائيات المستخدمين:</MudText>
        <MudText>عدد المستخدمين المسجلين: @userCount</MudText>
        <MudText>عدد المستخدمين المفعلين: @activeUserCount</MudText>
        <MudText>عدد المستخدمين المؤكدين: @confirmedUserCount</MudText>

        <MudButton Variant="Variant.Outlined" Color="Color.Info" OnClick="RefreshStats" Class="mt-4">
            تحديث الإحصائيات
        </MudButton>
    </MudPaper>
</MudContainer>

@code {
    private List<TestResult> testResults = new();
    private int userCount = 0;
    private int activeUserCount = 0;
    private int confirmedUserCount = 0;

    protected override async Task OnInitializedAsync()
    {
        await RefreshStats();
    }

    private async Task TestDatabaseConnection()
    {
        try
        {
            var users = await UserManager.Users.Take(1).ToListAsync();
            testResults.Add(new TestResult { IsSuccess = true, Message = "✅ الاتصال بقاعدة البيانات ناجح" });
        }
        catch (Exception ex)
        {
            testResults.Add(new TestResult { IsSuccess = false, Message = $"❌ فشل الاتصال بقاعدة البيانات: {ex.Message}" });
        }
        StateHasChanged();
    }

    private async Task TestRoleCreation()
    {
        try
        {
            if (!await RoleManager.RoleExistsAsync("User"))
            {
                var result = await RoleManager.CreateAsync(new IdentityRole("User"));
                if (result.Succeeded)
                {
                    testResults.Add(new TestResult { IsSuccess = true, Message = "✅ تم إنشاء دور 'User' بنجاح" });
                }
                else
                {
                    testResults.Add(new TestResult { IsSuccess = false, Message = $"❌ فشل إنشاء دور 'User': {string.Join(", ", result.Errors.Select(e => e.Description))}" });
                }
            }
            else
            {
                testResults.Add(new TestResult { IsSuccess = true, Message = "✅ دور 'User' موجود بالفعل" });
            }
        }
        catch (Exception ex)
        {
            testResults.Add(new TestResult { IsSuccess = false, Message = $"❌ خطأ في اختبار الأدوار: {ex.Message}" });
        }
        StateHasChanged();
    }

    private async Task TestUserCreation()
    {
        try
        {
            var testEmail = $"test{DateTime.Now.Ticks}@test.com";
            var testUser = new ApplicationUser
            {
                UserName = testEmail,
                Email = testEmail,
                FullName = "مستخدم تجريبي",
                CreatedAt = DateTime.Now,
                IsActive = true,
                EmailConfirmed = true // For testing purposes
            };

            var result = await UserManager.CreateAsync(testUser, "TestPassword123!");

            if (result.Succeeded)
            {
                // Assign role
                if (await RoleManager.RoleExistsAsync("User"))
                {
                    await UserManager.AddToRoleAsync(testUser, "User");
                }

                testResults.Add(new TestResult { IsSuccess = true, Message = $"✅ تم إنشاء المستخدم التجريبي بنجاح: {testEmail}" });
                await RefreshStats();
            }
            else
            {
                testResults.Add(new TestResult { IsSuccess = false, Message = $"❌ فشل إنشاء المستخدم التجريبي: {string.Join(", ", result.Errors.Select(e => e.Description))}" });
            }
        }
        catch (Exception ex)
        {
            testResults.Add(new TestResult { IsSuccess = false, Message = $"❌ خطأ في إنشاء المستخدم التجريبي: {ex.Message}" });
        }
        StateHasChanged();
    }

    private async Task RefreshStats()
    {
        try
        {
            var users = await UserManager.Users.ToListAsync();
            userCount = users.Count;
            activeUserCount = users.Count(u => u.IsActive);
            confirmedUserCount = users.Count(u => u.EmailConfirmed);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to refresh user statistics");
        }
        StateHasChanged();
    }

    private class TestResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
