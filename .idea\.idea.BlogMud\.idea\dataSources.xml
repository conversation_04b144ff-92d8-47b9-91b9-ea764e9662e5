<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="MudBlog@localhost" uuid="c70e2571-1192-467a-9935-fb0c980538d1">
      <driver-ref>sqlserver.jb</driver-ref>
      <synchronize>true</synchronize>
      <configured-by-url>true</configured-by-url>
      <jdbc-driver>com.jetbrains.jdbc.sqlserver.SqlServerDriver</jdbc-driver>
      <jdbc-url>Server=localhost;Database=MudBlog;User Id=sa;Password=*********;MultipleActiveResultSets=true;Encrypt=False;TrustServerCertificate=True;</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>