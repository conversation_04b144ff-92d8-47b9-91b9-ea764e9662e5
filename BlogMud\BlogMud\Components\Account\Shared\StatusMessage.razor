﻿@if (!string.IsNullOrEmpty(DisplayMessage))
{
    var severity = DisplayMessage.StartsWith("Error") ? Severity.Error : Severity.Success;

    <MudAlert Variant="Variant.Outlined" Severity="@severity">@DisplayMessage</MudAlert>
}

@code {
    private string? messageFromCookie;

    [Parameter]
    public string? Message { get; set; }

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    private string? DisplayMessage => Message ?? messageFromCookie;

    protected override void OnInitialized()
    {
        if (HttpContext?.Request != null)
        {
            messageFromCookie = HttpContext.Request.Cookies[IdentityRedirectManager.StatusCookieName];

            if (messageFromCookie is not null)
            {
                HttpContext.Response.Cookies.Delete(IdentityRedirectManager.StatusCookieName);
            }
        }
    }
}
