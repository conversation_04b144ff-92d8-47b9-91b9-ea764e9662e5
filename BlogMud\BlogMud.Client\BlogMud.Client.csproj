<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <NoDefaultLaunchSettingsFile>true</NoDefaultLaunchSettingsFile>
    <StaticWebAssetProjectMode>Default</StaticWebAssetProjectMode>
    <Platforms>AnyCPU;x86</Platforms>
  </PropertyGroup>

  <!-- مشروع WebAssembly لا يحتاج إعدادات x86 خاصة -->

  <ItemGroup>
    <Compile Remove="Pages\Posts\**" />
    <Content Remove="Pages\Posts\**" />
    <EmbeddedResource Remove="Pages\Posts\**" />
    <None Remove="Pages\Posts\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Pages\Admin\Posts\AnimatedImg\AnimatedGifEditDialog.razor" />
    <Content Remove="Pages\Admin\Posts\AnimatedImg\AnimatedGifEditForm.razor" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="FluentValidation" Version="12.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.*" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" Version="8.*" />
    <PackageReference Include="MudBlazor" Version="8.8.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BlogMud.Shared\BlogMud.Shared.csproj" />
  </ItemGroup>

</Project>
