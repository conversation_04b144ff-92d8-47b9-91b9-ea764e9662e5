<style>
    /* SidertMoveServices Component - Beautiful & Elegant Styles */

    /* Container Styling */
    .sidert-move-container {
        margin-top: 2rem;
        padding: 0 1rem;
    }

    /* Page Header Section */
    .page-header-section {
        margin-bottom: 2rem;
    }

    .page-header-card {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        border-radius: 20px;
        padding: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(149, 55, 53, 0.3);
    }

    .page-header-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .header-content {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 1;
    }

    .header-icon-wrapper {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .header-icon {
        font-size: 2rem;
        color: white;
    }

    .header-title {
        color: white;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .header-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 400;
        line-height: 1.6;
    }

    .header-stats {
        display: flex;
        gap: 2rem;
        position: relative;
        z-index: 1;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        padding: 1rem 1.5rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
    }

    .stat-icon {
        font-size: 1.5rem;
        color: rgba(255, 255, 255, 0.9);
    }

    .stat-icon.active {
        color: #4caf50;
    }

    .stat-number {
        color: white;
        font-weight: 700;
        margin: 0;
    }

    .stat-label {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
    }

    /* Preview Section */
    .preview-section {
        margin-bottom: 2rem;
    }

    .preview-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .preview-card:hover {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(149, 55, 53, 0.1);
    }

    .preview-title-wrapper {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .preview-icon {
        color: #953735;
        font-size: 1.5rem;
    }

    .preview-title {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
    }

    .carousel-controls {
        display: flex;
        gap: 0.5rem;
    }

    .carousel-container {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        background: #f8f9fa;
        position: relative;
    }

    .carousel-container .mud-carousel {
        height: 400px;
        width: 100%;
    }

    .enhanced-carousel {
        height: 400px;
        border-radius: 16px;
        width: 100%;
    }

    .enhanced-carousel .mud-carousel-item {
        height: 400px;
        width: 100%;
    }

    .carousel-item {
        position: relative;
        height: 400px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .slide-content {
        position: relative;
        height: 100%;
        width: 100%;
        display: block;
    }

    .slide-image-wrapper {
        position: relative;
        height: 100%;
        width: 100%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .slide-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        transition: transform 0.3s ease;
    }

    .slide-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 40%;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        pointer-events: none;
        z-index: 1;
    }

    .slide-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 1.5rem 2rem;
        color: white;
        text-align: center;
        z-index: 2;
    }

    .slide-title {
        color: white;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    .slide-description {
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .slide-link-btn {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        font-weight: 600;
    }

    .slide-link-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }

    .no-active-slides {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .no-slides-icon {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }

    .no-slides-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .no-slides-subtitle {
        color: #6c757d;
    }

    /* Controls Section */
    .controls-section {
        margin-bottom: 2rem;
    }

    .controls-card {
        background: white;
        border-radius: 20px;
        padding: 1.5rem 2rem;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .controls-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 2rem;
    }

    .controls-left {
        flex: 0 0 auto;
    }

    .controls-right {
        flex: 1;
        display: flex;
        justify-content: flex-end;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
    }

    .primary-action-btn {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: none;
        box-shadow: 0 4px 16px rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .primary-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 24px rgba(149, 55, 53, 0.4);
    }

    .secondary-action-btn {
        border: 2px solid #953735;
        color: #953735;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: none;
        transition: all 0.3s ease;
    }

    .secondary-action-btn:hover {
        background: rgba(149, 55, 53, 0.1);
        transform: translateY(-2px);
    }

    .search-wrapper {
        position: relative;
        max-width: 400px;
        width: 100%;
    }

    .enhanced-search-field {
        border-radius: 12px;
    }

    .enhanced-search-field .mud-input-control {
        border-radius: 12px;
        border: 2px solid rgba(149, 55, 53, 0.2);
        transition: all 0.3s ease;
    }

    .enhanced-search-field .mud-input-control:hover {
        border-color: rgba(149, 55, 53, 0.4);
    }

    .enhanced-search-field .mud-input-control.mud-input-control-focused {
        border-color: #953735;
        box-shadow: 0 0 0 3px rgba(149, 55, 53, 0.1);
    }

    .search-clear-btn {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
    }

    .button-text-short {
        display: none;
    }

    .button-text-full {
        display: inline;
    }

    /* Loading Section */
    .loading-section {
        margin: 3rem 0;
    }

    .loading-card {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        text-align: center;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .loading-text {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
    }

    .loading-subtitle {
        color: #6c757d;
        margin: 0;
    }

    /* Empty State Section */
    .empty-state-section {
        margin: 3rem 0;
    }

    .empty-state-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 20px;
        padding: 4rem 2rem;
        text-align: center;
        border: 2px dashed rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .empty-state-card:hover {
        border-color: rgba(149, 55, 53, 0.5);
        transform: translateY(-2px);
    }

    .empty-state-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .empty-state-icon-wrapper {
        background: rgba(149, 55, 53, 0.1);
        border-radius: 50%;
        padding: 2rem;
        margin-bottom: 1rem;
    }

    .empty-state-icon {
        font-size: 4rem;
        color: #953735;
    }

    .empty-state-title {
        color: #2c3e50;
        font-weight: 700;
        margin: 0;
    }

    .empty-state-subtitle {
        color: #6c757d;
        max-width: 400px;
        line-height: 1.6;
        margin: 0;
    }

    .empty-state-action-btn {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        border-radius: 12px;
        padding: 1rem 2rem;
        font-weight: 600;
        text-transform: none;
        box-shadow: 0 4px 16px rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .empty-state-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 24px rgba(149, 55, 53, 0.4);
    }

    /* Table Enhancements */
    .regular-table {
        display: block;
    }

    .regular-table .mud-table {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .regular-table .mud-table-head {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
    }

    .regular-table .mud-table-head .mud-table-cell {
        color: white;
        font-weight: 600;
        border-bottom: none;
    }

    .regular-table .mud-table-row:hover {
        background: rgba(149, 55, 53, 0.05);
    }

    .mud-table-cell:first-child {
        font-weight: 600;
        color: #953735;
        text-align: center;
        width: 60px;
        min-width: 60px;
    }

    .mobile-cards {
        display: none;
    }

    /* Enhanced Mobile Cards - Redesigned */
    .mobile-cards {
        display: none;
        gap: 1.5rem;
    }

    .mobile-card {
        background: #ffffff !important;
        border-radius: 20px;
        transition: all 0.3s ease;
        border: 1px solid rgba(149, 55, 53, 0.15);
        box-shadow: 0 4px 20px rgba(149, 55, 53, 0.1);
        overflow: hidden;
        position: relative;
        margin-bottom: 1.5rem;
    }

    .mobile-card .mud-card {
        background: #ffffff !important;
    }

    .mobile-card .mud-card-content {
        background: #ffffff !important;
    }

    .mobile-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        z-index: 1;
    }

    .mobile-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 32px rgba(149, 55, 53, 0.2);
        border-color: rgba(149, 55, 53, 0.3);
    }

    .compact-content {
        padding: 1.5rem !important;
        background: #ffffff !important;
        position: relative;
        z-index: 2;
    }

    /* Card Header */
    .gif-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(149, 55, 53, 0.15);
        background: #ffffff;
    }

    .gif-number {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        color: white;
        font-weight: 700;
        min-width: 45px;
        height: 45px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(149, 55, 53, 0.3);
        font-size: 1.1rem;
    }

    .gif-actions {
        display: flex;
        gap: 0.5rem;
    }

    .action-btn {
        border-radius: 10px;
        transition: all 0.3s ease;
        width: 40px;
        height: 40px;
    }

    .action-btn:hover {
        transform: scale(1.1);
    }

    /* Title Section */
    .gif-title {
        color: #2c3e50;
        font-weight: 700;
        line-height: 1.4;
        word-break: break-word;
        margin: 0 0 1rem 0;
        flex: 1;
    }

    /* Info Items */
    .gif-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        direction: rtl;
        margin-bottom: 0.75rem;
    }

    .info-icon {
        color: #953735;
        font-size: 1rem;
    }

    .info-label {
        color: #6c757d;
        font-weight: 500;
        margin: 0;
        font-size: 0.85rem;
    }

    .status-chip {
        flex-shrink: 0;
        font-weight: 600;
    }

    /* Responsive Design - Mobile First */

    /* عرض البطاقات في الشاشات الصغيرة */
    @@media (max-width: 768px) {
        .regular-table {
            display: none;
        }

        .mobile-cards {
            display: block;
        }

        .page-header-card {
            padding: 1.5rem;
            border-radius: 16px;
        }

        .header-content {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .header-stats {
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .stat-item {
            flex: 1;
            min-width: 140px;
        }

        .preview-card,
        .controls-card {
            padding: 1.5rem;
            border-radius: 16px;
        }

        .enhanced-carousel {
            height: 300px;
        }

        .enhanced-carousel .mud-carousel {
            height: 300px;
        }

        .carousel-item {
            height: 300px;
        }

        .slide-image-wrapper {
            height: 300px;
        }

        .slide-image {
            max-height: 300px;
        }

        .controls-container {
            flex-direction: column;
            gap: 1.5rem;
        }

        .controls-left,
        .controls-right {
            width: 100%;
        }

        .action-buttons {
            justify-content: center;
        }

        .search-wrapper {
            max-width: none;
        }
    }

    /* تحسينات للشاشات الصغيرة جداً */
    @@media (max-width: 480px) {
        .sidert-move-container {
            padding: 0 0.5rem;
        }

        .page-header-card {
            padding: 1rem;
            margin: 0 0.5rem 1.5rem;
        }

        .header-title {
            font-size: 1.5rem;
        }

        .stat-item {
            padding: 0.75rem 1rem;
            min-width: 120px;
        }

        .button-text-short {
            display: inline;
        }

        .button-text-full {
            display: none;
        }

        .action-buttons {
            flex-direction: column;
            width: 100%;
        }

        .primary-action-btn,
        .secondary-action-btn {
            width: 100%;
            justify-content: center;
        }

        .compact-content {
            padding: 1rem !important;
        }

        .gif-header {
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
        }

        .gif-number {
            min-width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .action-btn {
            width: 36px;
            height: 36px;
        }

        .gif-title {
            font-size: 1.1rem;
        }

        .enhanced-carousel {
            height: 250px;
        }

        .enhanced-carousel .mud-carousel {
            height: 250px;
        }

        .carousel-item {
            height: 250px;
        }

        .slide-image-wrapper {
            height: 250px;
        }

        .slide-image {
            max-height: 250px;
        }

        .slide-info {
            padding: 1rem;
        }

        .preview-card,
        .controls-card,
        .loading-card,
        .empty-state-card {
            margin: 0 0.5rem 1.5rem;
        }
    }

    /* تحسينات للتابلت */
    @@media (min-width: 769px) and (max-width: 1024px) {
        .regular-table {
            display: none;
        }

        .mobile-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 1.5rem;
        }

        .controls-container {
            gap: 1.5rem;
        }

        .search-wrapper {
            max-width: 350px;
        }

        .enhanced-carousel {
            height: 350px;
        }

        .enhanced-carousel .mud-carousel {
            height: 350px;
        }

        .carousel-item {
            height: 350px;
        }

        .slide-image-wrapper {
            height: 350px;
        }

        .slide-image {
            max-height: 350px;
        }

        .page-header-card {
            padding: 2rem;
        }

        .header-stats {
            gap: 1.5rem;
        }

        .stat-item {
            padding: 1rem 1.5rem;
        }
    }

    /* Animations and Transitions */
    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @@keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }

    .page-header-section {
        animation: fadeInUp 0.6s ease-out;
    }

    .preview-section {
        animation: fadeInUp 0.6s ease-out 0.1s both;
    }

    .controls-section {
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }

    .mobile-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .mobile-card:nth-child(2n) {
        animation-delay: 0.1s;
    }

    .mobile-card:nth-child(2n+1) {
        animation-delay: 0.2s;
    }

    .stat-item:hover {
        animation: pulse 0.6s ease-in-out;
    }

    /* Carousel Enhancements */
    .carousel-container .mud-carousel-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        min-width: 48px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.45);
        color: #fff;
        transition: background 0.3s ease;
    }

    .carousel-container .mud-carousel-button:hover {
        background: rgba(0, 0, 0, 0.65);
    }

    .carousel-container .mud-carousel-button[data-side="Prev"],
    .carousel-container .mud-carousel-prev {
        left: 8px;
    }

    .carousel-container .mud-carousel-button[data-side="Next"],
    .carousel-container .mud-carousel-next {
        right: 8px;
    }

    .carousel-container .mud-carousel-button svg {
        font-size: 24px;
    }

    /* Mobile carousel optimisation */
    @@media (max-width: 768px) {
        .carousel-container .mud-carousel-button {
            width: 44px !important;
            height: 44px !important;
            min-width: 44px !important;
            background: rgba(149, 55, 53, 0.8) !important;
            border: 2px solid rgba(255, 255, 255, 0.9) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
        }

        .carousel-container .mud-carousel-button:hover {
            background: rgba(149, 55, 53, 0.95) !important;
            transform: scale(1.1) !important;
        }

        .carousel-container .mud-carousel-button svg {
            font-size: 22px !important;
            color: white !important;
        }

        .carousel-container .mud-carousel-button[data-side="Prev"],
        .carousel-container .mud-carousel-prev {
            left: 12px !important;
        }

        .carousel-container .mud-carousel-button[data-side="Next"],
        .carousel-container .mud-carousel-next {
            right: 12px !important;
        }
    }

    @@media (max-width: 480px) {
        .carousel-container .mud-carousel-button {
            width: 40px !important;
            height: 40px !important;
            min-width: 40px !important;
            background: rgba(149, 55, 53, 0.9) !important;
            border: 2px solid rgba(255, 255, 255, 1) !important;
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.4) !important;
        }

        .carousel-container .mud-carousel-button svg {
            font-size: 20px !important;
        }

        .carousel-container .mud-carousel-button[data-side="Prev"],
        .carousel-container .mud-carousel-prev {
            left: 8px !important;
        }

        .carousel-container .mud-carousel-button[data-side="Next"],
        .carousel-container .mud-carousel-next {
            right: 8px !important;
        }
    }

    /* RTL support: swap arrow positions for right-to-left layouts */
[dir="rtl"] .carousel-container .mud-carousel-button[data-side="Prev"],
[dir="rtl"] .carousel-container .mud-carousel-prev {
    right: 8px !important;
    left: auto !important;
}

[dir="rtl"] .carousel-container .mud-carousel-button[data-side="Next"],
[dir="rtl"] .carousel-container .mud-carousel-next {
    left: 8px !important;
    right: auto !important;
}

/* Force arrow visibility - fallback styles */
    .carousel-container .mud-carousel-button,
    .carousel-container .mud-carousel-prev,
    .carousel-container .mud-carousel-next {
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    /* Additional MudBlazor carousel arrow selectors */
    .enhanced-carousel .mud-button-root[aria-label*="Previous"],
    .enhanced-carousel .mud-button-root[aria-label*="Next"],
    .enhanced-carousel .mud-icon-button[aria-label*="Previous"],
    .enhanced-carousel .mud-icon-button[aria-label*="Next"] {
        position: absolute !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        z-index: 1000 !important;
        background: rgba(149, 55, 53, 0.8) !important;
        color: white !important;
        border: 2px solid rgba(255, 255, 255, 0.9) !important;
        border-radius: 50% !important;
        width: 48px !important;
        height: 48px !important;
        min-width: 48px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
    }

    .enhanced-carousel .mud-button-root[aria-label*="Previous"],
    .enhanced-carousel .mud-icon-button[aria-label*="Previous"] {
        left: 8px !important;
    }

    .enhanced-carousel .mud-button-root[aria-label*="Next"],
    .enhanced-carousel .mud-icon-button[aria-label*="Next"] {
        right: 8px !important;
    }

    /* Mobile specific arrow styles */
    @@media (max-width: 768px) {
        .enhanced-carousel .mud-button-root[aria-label*="Previous"],
        .enhanced-carousel .mud-button-root[aria-label*="Next"],
        .enhanced-carousel .mud-icon-button[aria-label*="Previous"],
        .enhanced-carousel .mud-icon-button[aria-label*="Next"] {
            width: 44px !important;
            height: 44px !important;
            min-width: 44px !important;
        }
    }

    @@media (max-width: 480px) {
        .enhanced-carousel .mud-button-root[aria-label*="Previous"],
        .enhanced-carousel .mud-button-root[aria-label*="Next"],
        .enhanced-carousel .mud-icon-button[aria-label*="Previous"],
        .enhanced-carousel .mud-icon-button[aria-label*="Next"] {
            width: 40px !important;
            height: 40px !important;
            min-width: 40px !important;
        }

        /* Force both arrows to be visible on very small screens */
        .carousel-container .mud-button-root[aria-label*="Previous"],
        .carousel-container .mud-icon-button[aria-label*="Previous"] {
            left: 8px !important;
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .carousel-container .mud-button-root[aria-label*="Next"],
        .carousel-container .mud-icon-button[aria-label*="Next"] {
            right: 8px !important;
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
    }

    /* RTL support for additional selectors */
    [dir="rtl"] .enhanced-carousel .mud-button-root[aria-label*="Previous"],
    [dir="rtl"] .enhanced-carousel .mud-icon-button[aria-label*="Previous"] {
        right: 8px !important;
        left: auto !important;
    }

    [dir="rtl"] .enhanced-carousel .mud-button-root[aria-label*="Next"],
    [dir="rtl"] .enhanced-carousel .mud-icon-button[aria-label*="Next"] {
        left: 8px !important;
        right: auto !important;
    }

    /* Enhanced carousel arrow visibility - comprehensive fix */
    .carousel-container .mud-carousel .mud-carousel-button,
    .carousel-container .mud-carousel .mud-button-root,
    .carousel-container .mud-carousel .mud-icon-button,
    .enhanced-carousel .mud-carousel-button,
    .enhanced-carousel .mud-button-root,
    .enhanced-carousel .mud-icon-button {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        pointer-events: auto !important;
        z-index: 1000 !important;
    }

    /* Ensure arrows are always on top and visible */
    .carousel-container {
        position: relative;
        z-index: 1;
        overflow: visible !important; /* Prevent clipping of arrows */
    }

    .carousel-container .mud-carousel {
        position: relative;
        z-index: 2;
        overflow: visible !important; /* Prevent clipping of arrows */
    }

    .carousel-container .mud-carousel-button,
    .carousel-container .mud-button-root[aria-label*="Previous"],
    .carousel-container .mud-button-root[aria-label*="Next"],
    .carousel-container .mud-icon-button[aria-label*="Previous"],
    .carousel-container .mud-icon-button[aria-label*="Next"] {
        z-index: 1000 !important;
        position: absolute !important;
    }

    /* Specific fix for left arrow positioning */
    .carousel-container .mud-button-root[aria-label*="Previous"],
    .carousel-container .mud-icon-button[aria-label*="Previous"],
    .enhanced-carousel .mud-button-root[aria-label*="Previous"],
    .enhanced-carousel .mud-icon-button[aria-label*="Previous"] {
        left: 12px !important;
        right: auto !important;
    }

    /* Specific fix for right arrow positioning */
    .carousel-container .mud-button-root[aria-label*="Next"],
    .carousel-container .mud-icon-button[aria-label*="Next"],
    .enhanced-carousel .mud-button-root[aria-label*="Next"],
    .enhanced-carousel .mud-icon-button[aria-label*="Next"] {
        right: 12px !important;
        left: auto !important;
    }

    /* Prevent any parent container from hiding arrows */
    .enhanced-carousel,
    .carousel-container,
    .preview-card {
        overflow: visible !important;
    }

    .enhanced-carousel .mud-carousel-content,
    .carousel-container .mud-carousel-content {
        overflow: visible !important;
    }
</style>
<script>
    // ضمان عرض أسهم الكاروسيل على الهاتف المحمول
    document.addEventListener('DOMContentLoaded', function() {
        function ensureCarouselArrowsVisible() {
            const carouselContainer = document.querySelector('.carousel-container');
            if (carouselContainer) {
                // البحث عن أزرار الأسهم بطرق مختلفة
                const arrowButtons = carouselContainer.querySelectorAll(
                    '.mud-carousel-button, .mud-carousel-prev, .mud-carousel-next, ' +
                    '[aria-label*="Previous"], [aria-label*="Next"], ' +
                    '.mud-button-root[aria-label*="Previous"], .mud-button-root[aria-label*="Next"], ' +
                    '.mud-icon-button[aria-label*="Previous"], .mud-icon-button[aria-label*="Next"]'
                );

                arrowButtons.forEach(button => {
                    button.style.display = 'flex';
                    button.style.visibility = 'visible';
                    button.style.opacity = '1';
                    button.style.pointerEvents = 'auto';
                    button.style.zIndex = '1000';
                    button.style.position = 'absolute';
                    button.style.top = '50%';
                    button.style.transform = 'translateY(-50%)';

                    // Apply enhanced styling
                    button.style.background = 'rgba(149, 55, 53, 0.8)';
                    button.style.color = 'white';
                    button.style.border = '2px solid rgba(255, 255, 255, 0.9)';
                    button.style.borderRadius = '50%';
                    button.style.width = '48px';
                    button.style.height = '48px';
                    button.style.minWidth = '48px';
                    button.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';
                });

                // إضافة كلاسات CSS مخصصة للأسهم
                const prevButtons = carouselContainer.querySelectorAll(
                    '.mud-carousel-prev, [aria-label*="Previous"], .mud-button-root[aria-label*="Previous"], .mud-icon-button[aria-label*="Previous"]'
                );
                const nextButtons = carouselContainer.querySelectorAll(
                    '.mud-carousel-next, [aria-label*="Next"], .mud-button-root[aria-label*="Next"], .mud-icon-button[aria-label*="Next"]'
                );

                // Mobile-specific positioning
                const isMobile = window.innerWidth <= 768;
                const leftPosition = isMobile ? '12px' : '8px';
                const rightPosition = isMobile ? '12px' : '8px';

                prevButtons.forEach(btn => {
                    btn.classList.add('custom-carousel-prev');
                    btn.style.left = leftPosition;
                    btn.style.right = 'auto';
                    btn.style.position = 'absolute';
                    btn.style.top = '50%';
                    btn.style.transform = 'translateY(-50%)';

                    // Mobile-specific styling
                    if (isMobile) {
                        btn.style.width = '44px';
                        btn.style.height = '44px';
                        btn.style.minWidth = '44px';
                    }
                });

                nextButtons.forEach(btn => {
                    btn.classList.add('custom-carousel-next');
                    btn.style.right = rightPosition;
                    btn.style.left = 'auto';
                    btn.style.position = 'absolute';
                    btn.style.top = '50%';
                    btn.style.transform = 'translateY(-50%)';

                    // Mobile-specific styling
                    if (isMobile) {
                        btn.style.width = '44px';
                        btn.style.height = '44px';
                        btn.style.minWidth = '44px';
                    }
                });
            }
        }

        // تشغيل الدالة عند تحميل الصفحة
        ensureCarouselArrowsVisible();

        // تشغيل الدالة عند تغيير حجم الشاشة
        window.addEventListener('resize', ensureCarouselArrowsVisible);

        // تشغيل الدالة بعد تحديث المحتوى (للتأكد من عمل Blazor)
        setTimeout(ensureCarouselArrowsVisible, 500);
        setTimeout(ensureCarouselArrowsVisible, 1000);
        setTimeout(ensureCarouselArrowsVisible, 2000);
        setTimeout(ensureCarouselArrowsVisible, 3000);

        // مراقبة التغييرات في DOM للكاروسيل
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    setTimeout(ensureCarouselArrowsVisible, 100);
                }
            });
        });

        const carouselContainer = document.querySelector('.carousel-container');
        if (carouselContainer) {
            observer.observe(carouselContainer, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class', 'style']
            });
        }

        // Additional event listeners for better coverage
        window.addEventListener('load', ensureCarouselArrowsVisible);
        document.addEventListener('DOMContentLoaded', ensureCarouselArrowsVisible);

        // Listen for Blazor component updates
        if (window.Blazor) {
            window.Blazor.addEventListener('enhancedload', ensureCarouselArrowsVisible);
        }

        // Specific fix for left arrow visibility issue
        function fixLeftArrowVisibility() {
            const carouselContainers = document.querySelectorAll('.carousel-container, .enhanced-carousel');

            carouselContainers.forEach(container => {
                // Find all possible left arrow selectors
                const leftArrows = container.querySelectorAll(
                    '.mud-carousel-prev, ' +
                    '[aria-label*="Previous"], ' +
                    '.mud-button-root[aria-label*="Previous"], ' +
                    '.mud-icon-button[aria-label*="Previous"], ' +
                    '.custom-carousel-prev'
                );

                leftArrows.forEach(arrow => {
                    // Force visibility and positioning
                    arrow.style.display = 'flex !important';
                    arrow.style.visibility = 'visible !important';
                    arrow.style.opacity = '1 !important';
                    arrow.style.pointerEvents = 'auto !important';
                    arrow.style.position = 'absolute !important';
                    arrow.style.left = '12px !important';
                    arrow.style.right = 'auto !important';
                    arrow.style.top = '50% !important';
                    arrow.style.transform = 'translateY(-50%) !important';
                    arrow.style.zIndex = '1001 !important';

                    // Ensure it's not hidden by parent elements
                    let parent = arrow.parentElement;
                    while (parent && parent !== container) {
                        if (parent.style.overflow === 'hidden') {
                            parent.style.overflow = 'visible';
                        }
                        parent = parent.parentElement;
                    }
                });
            });
        }

        // Run the left arrow fix multiple times
        setTimeout(fixLeftArrowVisibility, 100);
        setTimeout(fixLeftArrowVisibility, 500);
        setTimeout(fixLeftArrowVisibility, 1000);
        setTimeout(fixLeftArrowVisibility, 2000);

        // Add to resize event
        window.addEventListener('resize', fixLeftArrowVisibility);
    });
</script>