@page "/contact"
@using FluentValidation
@using BlogMud.Shared.DTOs

<PageTitle>اتصل بنا | @(_companyInfo?.Name ?? "شركتنا")</PageTitle>

<!-- Hero Section -->
<div class="hero-section">
    <div class="d-flex flex-column justify-center align-center h-100">
        <MudContainer Class="text-center">
            <MudText Typo="Typo.h2" Color="Color.Surface">اتصل بنا</MudText>
            <MudText Typo="Typo.subtitle1" Color="Color.Surface">نسعد بتواصلكم معنا والإجابة على استفساراتكم</MudText>
        </MudContainer>
    </div>
</div>

<MudContainer MaxWidth="MaxWidth.Large" Class="contact-container">
    <MudGrid Class="contact-grid">
        <!-- Contact Form -->
        <MudItem xs="12" md="8">
            <MudCard Elevation="3" Class="contact-form-container">
                <MudText Typo="Typo.h4" Class="mb-4">أرسل رسالة</MudText>
                <MudText Typo="Typo.body1" Class="mb-6">يرجى ملء النموذج أدناه وسنقوم بالرد عليكم في أقرب وقت ممكن.</MudText>
                
                <MudForm @ref="_form" Model="@_contactMessage">
                    <MudGrid>
                        <MudItem xs="12" sm="6">
                            <MudTextField @bind-Value="_contactMessage.Name" Label="الاسم" Variant="Variant.Outlined" 
                                          Validation="@(new Func<string, IEnumerable<string>>(ValidateName))" 
                                          Required="true" RequiredError="الاسم مطلوب" />
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <MudTextField @bind-Value="_contactMessage.Email" Label="البريد الإلكتروني" Variant="Variant.Outlined" 
                                          Validation="@(new Func<string, IEnumerable<string>>(ValidateEmail))" 
                                          Required="true" RequiredError="البريد الإلكتروني مطلوب" />
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <MudTextField @bind-Value="_contactMessage.Phone" Label="رقم الهاتف" Variant="Variant.Outlined" 
                                          Validation="@(new Func<string, IEnumerable<string>>(ValidatePhone))" />
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <MudTextField @bind-Value="_contactMessage.Subject" Label="الموضوع" Variant="Variant.Outlined" 
                                          Required="true" RequiredError="الموضوع مطلوب" />
                        </MudItem>
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_contactMessage.Message" Label="الرسالة" Variant="Variant.Outlined"
                                          Lines="5" Required="true" RequiredError="الرسالة مطلوبة" />
                        </MudItem>
                        <MudItem xs="12">
                            <MudButton Variant="Variant.Filled" Color="Color.Primary" Size="Size.Large"
                                       OnClick="SubmitForm" Class="mt-4 submit-button-mobile"
                                       Disabled="@_isSubmitting">
                                @if (_isSubmitting)
                                {
                                    <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                                    <MudText Class="ms-2">جارِ الإرسال...</MudText>
                                }
                                else
                                {
                                    <MudText>إرسال</MudText>
                                }
                            </MudButton>
                        </MudItem>
                    </MudGrid>
                </MudForm>
            </MudCard>
        </MudItem>
        
        <!-- Contact Info -->
        <MudItem xs="12" md="4" Class="contact-info-section">
            <MudPaper Elevation="3" Class="contact-info-card">
                <MudText Typo="Typo.h5" Class="mb-6">معلومات التواصل</MudText>

                <div class="d-flex flex-column gap-4">
                    <div class="contact-info-item">
                        <MudIcon Icon="@Icons.Material.Filled.Place" Color="Color.Primary" />
                        <div>
                            <MudText Typo="Typo.subtitle1">العنوان</MudText>
                            <MudText Typo="Typo.body2">@(_companyInfo?.Address ?? "الرياض، المملكة العربية السعودية")</MudText>
                        </div>
                    </div>

                    <div class="contact-info-item">
                        <MudIcon Icon="@Icons.Material.Filled.Phone" Color="Color.Primary" />
                        <div>
                            <MudText Typo="Typo.subtitle1">الهاتف</MudText>
                            <MudText Typo="Typo.body2">@(_companyInfo?.Phone ?? "+966123456789")</MudText>
                        </div>
                    </div>

                    <div class="contact-info-item">
                        <MudIcon Icon="@Icons.Material.Filled.Email" Color="Color.Primary" />
                        <div>
                            <MudText Typo="Typo.subtitle1">البريد الإلكتروني</MudText>
                            <MudText Typo="Typo.body2">@(_companyInfo?.Email ?? "<EMAIL>")</MudText>
                        </div>
                    </div>

                    <MudDivider Class="my-4" />

                    <MudText Typo="Typo.subtitle1" Class="mb-2">تابعنا على</MudText>
                    <div class="social-icons">
                        @if (!string.IsNullOrEmpty(_companyInfo?.FacebookUrl))
                        {
                            <MudIconButton Icon="@Icons.Custom.Brands.Facebook" Color="Color.Primary" Href="@_companyInfo.FacebookUrl" Target="_blank" />
                        }
                        @if (!string.IsNullOrEmpty(_companyInfo?.TwitterUrl))
                        {
                            <MudIconButton Icon="@Icons.Custom.Brands.Twitter" Color="Color.Primary" Href="@_companyInfo.TwitterUrl" Target="_blank" />
                        }
                        @if (!string.IsNullOrEmpty(_companyInfo?.InstagramUrl))
                        {
                            <MudIconButton Icon="@Icons.Custom.Brands.Instagram" Color="Color.Primary" Href="@_companyInfo.InstagramUrl" Target="_blank" />
                        }
                        @if (!string.IsNullOrEmpty(_companyInfo?.LinkedInUrl))
                        {
                            <MudIconButton Icon="@Icons.Custom.Brands.LinkedIn" Color="Color.Primary" Href="@_companyInfo.LinkedInUrl" Target="_blank" />
                        }
                    </div>
                </div>
            </MudPaper>

            <MudPaper Elevation="3" Class="contact-info-card">
                <MudText Typo="Typo.h5" Class="mb-6">ساعات العمل</MudText>

                <div class="working-hours">
                    <div class="working-hours-item">
                        <MudText Typo="Typo.body1">الأحد - الخميس</MudText>
                        <MudText Typo="Typo.body1">8:00 ص - 5:00 م</MudText>
                    </div>
                    <div class="working-hours-item">
                        <MudText Typo="Typo.body1">الجمعة</MudText>
                        <MudText Typo="Typo.body1">مغلق</MudText>
                    </div>
                    <div class="working-hours-item">
                        <MudText Typo="Typo.body1">السبت</MudText>
                        <MudText Typo="Typo.body1">10:00 ص - 3:00 م</MudText>
                    </div>
                </div>
            </MudPaper>
        </MudItem>
    </MudGrid>
</MudContainer>

<!-- Map Section -->
<div class="map-container">
    <!-- In a real application, this would be a Google Maps embed -->
    <div id="map"></div>
</div>

@code {
    private CompanyInfo _companyInfo;
    private ContactMessageDto _contactMessage = new ContactMessageDto();
    private MudForm _form;
    private bool _isSubmitting = false;
    
    // Form validation
    private FluentValidation.IValidator _contactValidator = new ContactMessageValidator();
    
    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Intentar obtener datos de la empresa usando una API
            try
            {
                _companyInfo = await Http.GetFromJsonAsync<CompanyInfo>("/api/CompanyInfo/1");
            }
            catch
            {
                // Si no hay un endpoint para CompanyInfo, usar datos por defecto
                _companyInfo = new CompanyInfo
                {
                    Name = "شركتنا",
                    Vision = "أن نكون الشركة الرائدة في مجال عملنا",
                    Mission = "توفير خدمات عالية الجودة لعملائنا",
                    AboutUs = "نحن شركة رائدة في مجال تكنولوجيا المعلومات.",
                    Phone = "+966123456789",
                    Email = "<EMAIL>",
                    Address = "الرياض، المملكة العربية السعودية"
                };
            }
        }
        catch (Exception ex)
        {
            // Handle error if needed
            Console.WriteLine($"Error loading company info: {ex.Message}");
            
            // Usar datos por defecto en caso de error
            _companyInfo = new CompanyInfo
            {
                Name = "شركتنا",
                Vision = "أن نكون الشركة الرائدة في مجال عملنا",
                Mission = "توفير خدمات عالية الجودة لعملائنا",
                AboutUs = "نحن شركة رائدة في مجال تكنولوجيا المعلومات.",
                Phone = "+966123456789",
                Email = "<EMAIL>",
                Address = "الرياض، المملكة العربية السعودية"
            };
        }
        
        await base.OnInitializedAsync();
    }
    
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Initialize Google Maps
            await InitializeMap();
        }
        
        await base.OnAfterRenderAsync(firstRender);
    }
    
    private async Task InitializeMap()
    {
        try
        {
            // In a real application, you would use Google Maps JS API
            // Here we'll just indicate this would be where the map is initialized
            // await JSRuntime.InvokeVoidAsync("initContactMap", latitude, longitude);
            
            Console.WriteLine("Contact map would be initialized here");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error initializing map: {ex.Message}");
        }
    }
    
    private async Task SubmitForm()
    {
        await _form.Validate();
        
        if (_form.IsValid)
        {
            _isSubmitting = true;
            
            try
            {
                // Set submission date
                _contactMessage.SubmissionDate = DateTime.Now;
                
                // Send to API on server
                try
                {
                    var response = await Http.PostAsJsonAsync("/api/ContactMessages", _contactMessage);

                    if (response.IsSuccessStatusCode)
                    {
                        // Reset form
                        _contactMessage = new ContactMessageDto();
                        StateHasChanged();

                        // Show success message
                        _Snackbar.Add("تم إرسال رسالتك بنجاح! سنقوم بالرد عليك في أقرب وقت ممكن.", MudBlazor.Severity.Success);
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        _Snackbar.Add("حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.", MudBlazor.Severity.Error);
                    }
                }
                catch (Exception ex)
                {
                    _Snackbar.Add("حدث خطأ في الاتصال. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.", MudBlazor.Severity.Error);
                }
            }
            catch (Exception ex)
            {
                // Show error message
                _Snackbar.Add("حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقاً.", MudBlazor.Severity.Error);
            }
            finally
            {
                _isSubmitting = false;
                StateHasChanged();
            }
        }
    }
    
    private IEnumerable<string> ValidateName(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            yield return "الاسم مطلوب";
        else if (name.Length < 2)
            yield return "الاسم يجب أن يحتوي على حرفين على الأقل";
    }
    
    private IEnumerable<string> ValidateEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            yield return "البريد الإلكتروني مطلوب";
        else if (!email.Contains("@") || !email.Contains("."))
            yield return "البريد الإلكتروني غير صحيح";
    }
    
    private IEnumerable<string> ValidatePhone(string phone)
    {
        if (!string.IsNullOrWhiteSpace(phone) && phone.Length < 8)
            yield return "رقم الهاتف غير صحيح";
    }
    
    // Validator class
    public class ContactMessageValidator : FluentValidation.AbstractValidator<ContactMessageDto>
    {
        public ContactMessageValidator()
        {
            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("الاسم مطلوب")
                .MinimumLength(2).WithMessage("الاسم يجب أن يحتوي على حرفين على الأقل");

            RuleFor(x => x.Email)
                .NotEmpty().WithMessage("البريد الإلكتروني مطلوب")
                .EmailAddress().WithMessage("البريد الإلكتروني غير صحيح");

            RuleFor(x => x.Subject)
                .NotEmpty().WithMessage("الموضوع مطلوب");

            RuleFor(x => x.Message)
                .NotEmpty().WithMessage("الرسالة مطلوبة")
                .MinimumLength(10).WithMessage("الرسالة يجب أن تحتوي على 10 أحرف على الأقل");
        }
    }
}
