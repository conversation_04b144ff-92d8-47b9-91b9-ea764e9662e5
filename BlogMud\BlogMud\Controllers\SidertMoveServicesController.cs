using AutoMapper;
using BlogMud.Services;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlogMud.Controllers
{
    /// <summary>
    /// وحدة تحكم واجهة برمجة التطبيقات لإدارة الشرائح المتحركة والصور المتحركة
    /// </summary>
    /// <remarks>
    /// توفر هذه الوحدة واجهات برمجة تطبيقات RESTful للتعامل مع الشرائح المتحركة
    /// بما في ذلك عمليات الإنشاء والقراءة والتحديث والحذف
    /// </remarks>
    [Route("api/[controller]")]
    [ApiController]
    public class SidertMoveServicesController : ControllerBase
    {
        #region المتغيرات والمنشئ

        private readonly IUnitOfWork _unitOfWork;
        private readonly IRepository<SidertMoveServices> _sidertMoveServicesRepository;
        private readonly ILogger<SidertMoveServicesController> _logger;
        private readonly IMapper _mapper;
        private readonly IFileService _fileService;

        public SidertMoveServicesController(
            IUnitOfWork unitOfWork,
            ILogger<SidertMoveServicesController> logger,
            IMapper mapper,
            IFileService fileService)
        {
            _unitOfWork = unitOfWork;
            _sidertMoveServicesRepository = _unitOfWork.Repository<SidertMoveServices>();
            _logger = logger;
            _mapper = mapper;
            _fileService = fileService;
        }

        #endregion

        #region عمليات القراءة

        /// <summary>
        /// الحصول على جميع الشرائح المتحركة
        /// </summary>
        /// <returns>قائمة بجميع الشرائح المتحركة</returns>
        /// <response code="200">تم استرجاع قائمة الشرائح المتحركة بنجاح</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<SidertMoveServicesDto>>> GetAllSidertMoveServices()
        {
            try
            {
                _logger.LogInformation("Fetching all sidert move services");
                var sidertMoveServices = await _sidertMoveServicesRepository.GetAllAsync(
                    orderBy: q => q.OrderBy(s => s.DisplayOrder).ThenByDescending(s => s.CreatedAt)
                );
                var sidertMoveServicesDtos = _mapper.Map<IEnumerable<SidertMoveServicesDto>>(sidertMoveServices);
                return Ok(sidertMoveServicesDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching sidert move services");
                return StatusCode(500, "حدث خطأ أثناء استرجاع الشرائح المتحركة");
            }
        }

        /// <summary>
        /// الحصول على الشرائح المتحركة النشطة فقط
        /// </summary>
        /// <returns>قائمة بالشرائح المتحركة النشطة</returns>
        /// <response code="200">تم استرجاع قائمة الشرائح المتحركة النشطة بنجاح</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        [HttpGet("active")]
        public async Task<ActionResult<IEnumerable<SidertMoveServicesDto>>> GetActiveSidertMoveServices()
        {
            try
            {
                _logger.LogInformation("Fetching active sidert move services");
                var sidertMoveServices = await _sidertMoveServicesRepository.GetAllAsync(
                    filter: s => s.IsActive,
                    orderBy: q => q.OrderBy(s => s.DisplayOrder)
                );
                var sidertMoveServicesDtos = _mapper.Map<IEnumerable<SidertMoveServicesDto>>(sidertMoveServices);
                return Ok(sidertMoveServicesDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching active sidert move services");
                return StatusCode(500, "حدث خطأ أثناء استرجاع الشرائح المتحركة النشطة");
            }
        }

        /// <summary>
        /// الحصول على شريحة متحركة محددة بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف الشريحة المتحركة</param>
        /// <returns>بيانات الشريحة المتحركة</returns>
        /// <response code="200">تم العثور على الشريحة المتحركة</response>
        /// <response code="404">لم يتم العثور على الشريحة المتحركة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        [HttpGet("{id}")]
        public async Task<ActionResult<SidertMoveServicesDto>> GetSidertMoveService(int id)
        {
            try
            {
                _logger.LogInformation("Fetching sidert move service with ID: {SidertMoveServiceId}", id);
                var sidertMoveService = await _sidertMoveServicesRepository.GetByIdAsync(id);

                if (sidertMoveService == null)
                {
                    _logger.LogWarning("Sidert move service not found with ID: {SidertMoveServiceId}", id);
                    return NotFound();
                }

                var sidertMoveServiceDto = _mapper.Map<SidertMoveServicesDto>(sidertMoveService);
                return Ok(sidertMoveServiceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching sidert move service with ID: {SidertMoveServiceId}", id);
                return StatusCode(500, "حدث خطأ أثناء استرجاع الشريحة المتحركة");
            }
        }

        #endregion

        #region عمليات الإنشاء

        /// <summary>
        /// إنشاء شريحة متحركة جديدة
        /// </summary>
        /// <param name="sidertMoveServiceDto">بيانات الشريحة المتحركة الجديدة</param>
        /// <returns>بيانات الشريحة المتحركة المنشأة</returns>
        /// <response code="201">تم إنشاء الشريحة المتحركة بنجاح</response>
        /// <response code="400">بيانات غير صحيحة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<SidertMoveServicesDto>> CreateSidertMoveService(SidertMoveServicesDto sidertMoveServiceDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                _logger.LogInformation("Creating new sidert move service: {Title}", sidertMoveServiceDto.Title);

                var sidertMoveService = _mapper.Map<SidertMoveServices>(sidertMoveServiceDto);
                sidertMoveService.CreatedAt = DateTime.Now;
                sidertMoveService.LastModifiedAt = null;

                await _sidertMoveServicesRepository.AddAsync(sidertMoveService);
                await _unitOfWork.SaveAsync();

                var createdSidertMoveServiceDto = _mapper.Map<SidertMoveServicesDto>(sidertMoveService);

                _logger.LogInformation("Successfully created sidert move service with ID: {SidertMoveServiceId}", sidertMoveService.Id);
                return CreatedAtAction(nameof(GetSidertMoveService), new { id = sidertMoveService.Id }, createdSidertMoveServiceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating sidert move service");
                return StatusCode(500, "حدث خطأ أثناء إنشاء الشريحة المتحركة");
            }
        }

        #endregion

        #region عمليات التحديث

        /// <summary>
        /// تحديث شريحة متحركة موجودة
        /// </summary>
        /// <param name="id">معرف الشريحة المتحركة</param>
        /// <param name="sidertMoveServiceDto">بيانات الشريحة المتحركة المحدثة</param>
        /// <returns>بيانات الشريحة المتحركة المحدثة</returns>
        /// <response code="200">تم تحديث الشريحة المتحركة بنجاح</response>
        /// <response code="400">بيانات غير صحيحة</response>
        /// <response code="404">لم يتم العثور على الشريحة المتحركة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<SidertMoveServicesDto>> UpdateSidertMoveService(int id, SidertMoveServicesDto sidertMoveServiceDto)
        {
            try
            {
                if (id != sidertMoveServiceDto.Id)
                {
                    return BadRequest("معرف الشريحة المتحركة غير متطابق");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                _logger.LogInformation("Updating sidert move service with ID: {SidertMoveServiceId}", id);

                var existingSidertMoveService = await _sidertMoveServicesRepository.GetByIdAsync(id);
                if (existingSidertMoveService == null)
                {
                    _logger.LogWarning("Sidert move service not found with ID: {SidertMoveServiceId}", id);
                    return NotFound();
                }

                // حفظ الصور القديمة للحذف إذا تم تغييرها
                var oldImageUrls = existingSidertMoveService.ImageUrls;

                // تحديث البيانات
                _mapper.Map(sidertMoveServiceDto, existingSidertMoveService);
                existingSidertMoveService.LastModifiedAt = DateTime.Now;

                _sidertMoveServicesRepository.Update(existingSidertMoveService);
                await _unitOfWork.SaveAsync();

                // حذف الصور القديمة إذا تم تغييرها
                if (!string.IsNullOrEmpty(oldImageUrls) && oldImageUrls != existingSidertMoveService.ImageUrls)
                {
                    try
                    {
                        int deletedCount = _fileService.DeleteOldSidertMoveServiceImages(oldImageUrls, existingSidertMoveService.ImageUrls);
                        _logger.LogInformation("Deleted {DeletedCount} old image files for SidertMoveService ID: {SidertMoveServiceId}",
                            deletedCount, id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Could not delete old image files for SidertMoveService ID: {SidertMoveServiceId}", id);
                    }
                }

                var updatedSidertMoveServiceDto = _mapper.Map<SidertMoveServicesDto>(existingSidertMoveService);

                _logger.LogInformation("Successfully updated sidert move service with ID: {SidertMoveServiceId}", id);
                return Ok(updatedSidertMoveServiceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating sidert move service with ID: {SidertMoveServiceId}", id);
                return StatusCode(500, "حدث خطأ أثناء تحديث الشريحة المتحركة");
            }
        }

        #endregion

        #region عمليات الحذف

        /// <summary>
        /// حذف شريحة متحركة
        /// </summary>
        /// <param name="id">معرف الشريحة المتحركة</param>
        /// <returns>رسالة تأكيد الحذف</returns>
        /// <response code="200">تم حذف الشريحة المتحركة بنجاح</response>
        /// <response code="404">لم يتم العثور على الشريحة المتحركة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> DeleteSidertMoveService(int id)
        {
            try
            {
                _logger.LogInformation("Deleting sidert move service with ID: {SidertMoveServiceId}", id);

                var sidertMoveService = await _sidertMoveServicesRepository.GetByIdAsync(id);
                if (sidertMoveService == null)
                {
                    _logger.LogWarning("Sidert move service not found with ID: {SidertMoveServiceId}", id);
                    return NotFound();
                }

                // حذف ملفات الصور من المجلد إذا كانت موجودة
                if (!string.IsNullOrEmpty(sidertMoveService.ImageUrls))
                {
                    try
                    {
                        int deletedCount = _fileService.DeleteSidertMoveServiceImages(sidertMoveService.ImageUrls);
                        _logger.LogInformation("Deleted {DeletedCount} image files for SidertMoveService ID: {SidertMoveServiceId}",
                            deletedCount, id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error deleting image files for SidertMoveService ID: {SidertMoveServiceId}", id);
                        // نستمر في حذف السجل حتى لو فشل حذف الملفات
                    }
                }

                _sidertMoveServicesRepository.Remove(sidertMoveService);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Successfully deleted sidert move service with ID: {SidertMoveServiceId}", id);
                return Ok(new { message = "تم حذف الشريحة المتحركة بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting sidert move service with ID: {SidertMoveServiceId}", id);
                return StatusCode(500, "حدث خطأ أثناء حذف الشريحة المتحركة");
            }
        }

        #endregion
    }
}
