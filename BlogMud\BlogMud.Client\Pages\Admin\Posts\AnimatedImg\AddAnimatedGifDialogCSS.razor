﻿<style>
    /* AddAnimatedGifDialog Component Scoped Styles */

    .animated-gif-dialog {
        direction: rtl;
    }

    .animated-gif-dialog .mud-dialog-content {
        padding: 0 !important;
    }

    /* عنوان الحوار */
    .dialog-title-container {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem 0;
    }

    .dialog-title-icon {
        background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-primary-darken) 100%);
        color: white;
        border-radius: 50%;
        padding: 0.5rem;
        font-size: 1.5rem !important;
    }

    .dialog-title-text {
        color: var(--mud-palette-text-primary) !important;
        font-weight: 700 !important;
    }

    /* محتوى الحوار */
    .dialog-content-container {
        padding: 1rem;
    }

    .gif-form {
        width: 100%;
    }

    /* تبويبات الحوار */
    .animated-gif-dialog .mud-tabs {
        border-radius: 12px !important;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .animated-gif-dialog .mud-tabs-toolbar {
        background: linear-gradient(135deg, var(--mud-palette-background-grey) 0%, var(--mud-palette-surface) 100%) !important;
        border-bottom: 2px solid var(--mud-palette-divider) !important;
    }

    .animated-gif-dialog .mud-tab {
        font-weight: 600 !important;
        text-transform: none !important;
        color: var(--mud-palette-text-primary) !important;
        border-radius: 8px 8px 0 0 !important;
        margin: 0 2px !important;
    }

    .animated-gif-dialog .mud-tab.mud-tab-active {
        background: white !important;
        color: var(--mud-palette-primary) !important;
        border-bottom: 2px solid var(--mud-palette-primary) !important;
    }

    .animated-gif-dialog .mud-tab:hover:not(.mud-tab-active) {
        background: rgba(149, 55, 53, 0.1) !important;
        color: var(--mud-palette-primary) !important;
    }

    .animated-gif-dialog .mud-tabs-panels {
        background: white;
        min-height: 400px;
    }

    /* شبكة النماذج */
    .form-grid,
    .media-grid,
    .preview-grid {
        margin: 0 !important;
    }

    .form-field {
        margin-bottom: 1rem !important;
    }

    .form-field .mud-input-root {
        border-radius: 8px !important;
    }

    .form-field .mud-input-outlined {
        border-radius: 8px !important;
    }

    /* قسم الحالة */
    .form-section.status-section {
        border: 1px solid var(--mud-palette-divider);
        border-radius: 12px;
        padding: 1rem;
        background: var(--mud-palette-background-grey);
        margin-top: 1rem;
    }

    .status-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .status-icon {
        color: var(--mud-palette-primary);
        font-size: 1.25rem !important;
    }

    .status-title {
        color: var(--mud-palette-text-primary) !important;
        font-weight: 600 !important;
        margin: 0 !important;
    }

    .status-content {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .status-switch {
        margin: 0 !important;
    }

    .status-description {
        font-size: 0.875rem !important;
        line-height: 1.4 !important;
    }

    /* قسم رفع الصور */
    .upload-section.image-upload {
        border: 2px dashed var(--mud-palette-divider);
        border-radius: 12px;
        padding: 1.5rem;
        background: var(--mud-palette-background-grey);
        transition: all 0.3s ease;
    }

    .upload-section.image-upload:hover {
        border-color: var(--mud-palette-primary);
        background: rgba(149, 55, 53, 0.05);
    }

    .upload-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--mud-palette-divider);
    }

    .upload-icon {
        color: var(--mud-palette-primary);
        font-size: 1.5rem !important;
    }

    .upload-title {
        color: var(--mud-palette-text-primary) !important;
        font-weight: 600 !important;
        margin: 0 !important;
    }

    .upload-content {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .upload-button {
        border-radius: 8px !important;
        padding: 0.75rem 2rem !important;
        font-weight: 600 !important;
        text-transform: none !important;
        border: 2px solid var(--mud-palette-primary) !important;
        color: var(--mud-palette-primary) !important;
        transition: all 0.3s ease !important;
    }

    .upload-button:hover:not(:disabled) {
        background: var(--mud-palette-primary) !important;
        color: white !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(149, 55, 53, 0.3);
    }

    .upload-button:disabled {
        opacity: 0.6;
        transform: none !important;
        box-shadow: none !important;
    }

    .upload-progress {
        text-align: center;
    }

    .upload-status {
        color: var(--mud-palette-primary) !important;
        font-weight: 500 !important;
    }

    /* قسم الصور المرفوعة */
    .uploaded-image-section {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid var(--mud-palette-divider);
    }

    .image-title {
        color: var(--mud-palette-text-primary) !important;
        font-weight: 600 !important;
    }

    .image-card {
        border-radius: 8px !important;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .image-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
    }

    .image-preview {
        object-fit: cover;
        width: 100%;
    }

    .image-actions {
        padding: 0.5rem !important;
        justify-content: center;
        background: rgba(0, 0, 0, 0.05);
    }

    .delete-image-btn {
        color: var(--mud-palette-error) !important;
    }

    .delete-image-btn:hover {
        background: rgba(244, 67, 54, 0.1) !important;
    }

    /* قسم المعاينة */
    .preview-section {
        border: 1px solid var(--mud-palette-divider);
        border-radius: 12px;
        padding: 1.5rem;
        background: var(--mud-palette-background-grey);
    }

    .preview-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--mud-palette-divider);
    }

    .preview-icon {
        color: var(--mud-palette-primary);
        font-size: 1.5rem !important;
    }

    .preview-title {
        color: var(--mud-palette-text-primary) !important;
        font-weight: 600 !important;
        margin: 0 !important;
    }

    .preview-content {
        display: flex;
        justify-content: center;
    }

    .preview-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        max-width: 400px;
        width: 100%;
    }

    .preview-image-wrapper {
        position: relative;
        height: 250px;
        overflow: hidden;
    }

    .preview-image-full {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .preview-info {
        padding: 1rem;
    }

    .preview-title-text {
        color: var(--mud-palette-text-primary) !important;
        font-weight: 700 !important;
        margin-bottom: 0.5rem !important;
    }

    .preview-description {
        color: var(--mud-palette-text-secondary) !important;
        margin-bottom: 1rem !important;
        line-height: 1.5;
    }

    .preview-details {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    /* حالة عدم وجود معاينة */
    .no-preview-section {
        border: 2px dashed var(--mud-palette-divider);
        border-radius: 12px;
        padding: 3rem;
        text-align: center;
        background: var(--mud-palette-background-grey);
    }

    .no-preview-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .no-preview-icon {
        font-size: 4rem !important;
        color: var(--mud-palette-text-disabled);
    }

    .no-preview-title {
        color: var(--mud-palette-text-secondary) !important;
        font-weight: 600 !important;
        margin: 0 !important;
    }

    .no-preview-subtitle {
        color: var(--mud-palette-text-disabled) !important;
        margin: 0 !important;
        line-height: 1.5;
    }

    /* أزرار الحوار */
    .dialog-actions {
        padding: 1rem 0 0 0;
        border-top: 2px solid var(--mud-palette-background-grey);
        margin-top: 1rem;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    .cancel-button {
        border-radius: 8px !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        text-transform: none !important;
        color: #666 !important;
    }

    .cancel-button:hover {
        background: #f5f5f5 !important;
        color: #333 !important;
    }

    .submit-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border: none !important;
        border-radius: 8px !important;
        padding: 0.75rem 2rem !important;
        font-weight: 600 !important;
        text-transform: none !important;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
        transition: all 0.3s ease !important;
    }

    .submit-button:hover:not(:disabled) {
        transform: translateY(-1px) !important;
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6) !important;
    }

    .submit-button:disabled {
        opacity: 0.6 !important;
        transform: none !important;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2) !important;
    }

    /* تحسينات الاستجابة */
    @@media (max-width: 768px) {
        .animated-gif-dialog .mud-dialog-content {
            padding: 0.5rem !important;
        }

        .dialog-content-container {
            padding: 0.5rem;
        }

        .dialog-title-container {
            flex-direction: column;
            text-align: center;
            gap: 0.75rem;
        }

        .upload-section.image-upload {
            padding: 1rem;
        }

        .preview-card {
            max-width: 100%;
        }

        .preview-image-wrapper {
            height: 200px;
        }

        .action-buttons {
            flex-direction: column-reverse;
            gap: 0.75rem;
        }

        .cancel-button,
        .submit-button {
            width: 100%;
            justify-content: center;
        }

        .animated-gif-dialog .mud-tabs-toolbar {
            flex-wrap: wrap;
        }

        .animated-gif-dialog .mud-tab {
            min-width: auto;
            flex: 1;
        }
    }

    @@media (max-width: 480px) {
        .form-grid .mud-grid-item {
            padding: 0.25rem !important;
        }

        .media-grid .mud-grid-item {
            padding: 0.25rem !important;
        }

        .preview-grid .mud-grid-item {
            padding: 0.25rem !important;
        }

        .upload-section.image-upload {
            padding: 0.75rem;
        }

        .preview-section {
            padding: 1rem;
        }

        .no-preview-section {
            padding: 2rem 1rem;
        }
    }

    /* تأثيرات الحركة */
    .animated-gif-dialog {
        animation: slideInUp 0.3s ease-out;
    }

    @@keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* تحسينات للحقول المطلوبة */
    .animated-gif-dialog .mud-input-outlined.mud-input-error .mud-input-outlined-border {
        border-color: #f44336 !important;
        border-width: 2px !important;
    }

    .animated-gif-dialog .mud-input-helper-text.mud-input-error {
        color: #f44336 !important;
        font-weight: 500 !important;
    }

    /* تحسينات للمفاتيح */
    .animated-gif-dialog .mud-switch.mud-checked .mud-switch-track {
        background-color: rgba(102, 126, 234, 0.5) !important;
    }

    .animated-gif-dialog .mud-switch.mud-checked .mud-switch-thumb {
        background-color: #667eea !important;
    }

    /* تحسينات للنصوص */
    .animated-gif-dialog .mud-typography {
        line-height: 1.5;
    }

    .animated-gif-dialog .mud-input-label {
        font-weight: 500;
        color: #555;
    }

    /* تحسينات للتركيز */
    .animated-gif-dialog .mud-input-outlined:focus-within .mud-input-outlined-border {
        border-color: #667eea !important;
        border-width: 2px !important;
        box-shadow: 0 0 0 1px rgba(102, 126, 234, 0.2) !important;
    }
</style>