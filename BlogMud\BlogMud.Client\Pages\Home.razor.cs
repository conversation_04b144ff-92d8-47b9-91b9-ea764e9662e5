using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace BlogMud.Client.Pages;

public partial class Home : ComponentBase
{
    private CompanyInfo _companyInfo;
    private IEnumerable<Article> _latestArticles = new List<Article>();
    private IEnumerable<AnimatedGifDto> _animatedGifs = new List<AnimatedGifDto>();
    private List<ServiceDto> _services = new List<ServiceDto>();
    private bool _servicesLoading = true;

    /// <summary>
    /// بيانات صفحة "من نحن" المدارة من لوحة الإدارة
    /// </summary>
    private AboutUsDto? _aboutUsData;

    /// <summary>
    /// مؤشر على حالة تحميل بيانات "من نحن"
    /// </summary>
    private bool _aboutUsLoading = true;

    [Inject]
    private HttpClient Https { get; set; }

    private class SlideItem
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public string ImageUrl { get; set; }
        public string ButtonText { get; set; }
        public string ButtonLink { get; set; }
    }

    // Default slides to use as fallback if no animated GIFs are available
    private List<SlideItem> _defaultSlides = new List<SlideItem>
    {
        new SlideItem
        {
            Title = "أهلاً بكم في شركتنا",
            Description = "شركة رائدة في مجال [مجال الشركة] منذ أكثر من 10 سنوات",
            ImageUrl = "/images/slide1.jpg",
            ButtonText = "اعرف أكثر",
            ButtonLink = "/about"
        },
        new SlideItem
        {
            Title = "خدمات متميزة لعملائنا",
            Description = "نقدم مجموعة متنوعة من الخدمات المتميزة لتلبية احتياجاتكم",
            ImageUrl = "/images/slide2.jpg",
            ButtonText = "تصفح خدماتنا",
            ButtonLink = "/services"
        },
        new SlideItem
        {
            Title = "مشاريعنا الناجحة",
            Description = "اطلع على أحدث مشاريعنا وقصص النجاح مع عملائنا",
            ImageUrl = "/images/slide3.jpg",
            ButtonText = "مشاريعنا",
            ButtonLink = "/news"
        }
    };

    // Slides to display in the carousel
    private List<SlideItem> _slides = new List<SlideItem>();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Try to get data if HttpClient is available
            if (Https != null)
            {
                try
                {
                    _companyInfo = await Https.GetFromJsonAsync<CompanyInfo>("/api/CompanyInfo/1");
                }
                catch
                {
                    // If there's an error, use default data
                    SetDefaultCompanyInfo();
                }

                // Try to get articles using API
                try
                {
                    _latestArticles = await Https.GetFromJsonAsync<List<Article>>("/api/Article");
                    // Take only the latest 3 articles
                    _latestArticles = _latestArticles.OrderByDescending(a => a.PublishDate).Take(3).ToList();
                }
                catch
                {
                    // If there's an error, use sample data
                    SetDefaultArticles();
                }

                // Try to get services using API
                try
                {
                    await LoadServices();
                }
                catch
                {
                    // If there's an error, use empty services list
                    _services = new List<ServiceDto>();
                }
                finally
                {
                    _servicesLoading = false;
                }

                // Try to get AboutUs data using API
                try
                {
                    await LoadAboutUsData();
                }
                catch
                {
                    // If there's an error, use null (will fall back to default data)
                    _aboutUsData = null;
                }
                finally
                {
                    _aboutUsLoading = false;
                }

                // Try to get active animated GIFs for the slider
                try
                {
                    _animatedGifs = await Https.GetFromJsonAsync<List<AnimatedGifDto>>("/api/AnimatedGifs/active");

                    // If we have active animated GIFs, convert them to slide items
                    if (_animatedGifs != null && _animatedGifs.Any())
                    {
                        _slides = _animatedGifs
                            .OrderBy(g => g.DisplayOrder)
                            .Select(gif => new SlideItem
                            {
                                Title = gif.Title,
                                Description = gif.Description ?? "",
                                ImageUrl = gif.ImageUrl,
                                ButtonText = "اعرف أكثر", // Default button text
                                ButtonLink = "/about"     // Default button link
                            })
                            .ToList();
                    }
                    else
                    {
                        // If no animated GIFs are found, use default slides
                        _slides = _defaultSlides;
                    }
                }
                catch
                {
                    // If there's an error, use default slides
                    _slides = _defaultSlides;
                }
            }
            else
            {
                // If we can't get HttpClient, use default data
                SetDefaultCompanyInfo();
                SetDefaultArticles();
                _services = new List<ServiceDto>();
                _servicesLoading = false;
                _aboutUsData = null;
                _aboutUsLoading = false;
                _slides = _defaultSlides;
            }
        }
        catch (Exception)
        {
            // In case of any error, use default data
            SetDefaultCompanyInfo();
            SetDefaultArticles();
            _services = new List<ServiceDto>();
            _servicesLoading = false;
            _aboutUsData = null;
            _aboutUsLoading = false;
            _slides = _defaultSlides;
        }
    }

    private void SetDefaultCompanyInfo()
    {
        _companyInfo = new CompanyInfo
        {
            Name = "شركتنا",
            Vision = "أن نكون الشركة الرائدة في مجال عملنا",
            Mission = "توفير خدمات عالية الجودة لعملائنا",
            AboutUs = "نحن شركة رائدة في مجال تكنولوجيا المعلومات.",
            Phone = "+966123456789",
            Email = "<EMAIL>",
            Address = "الرياض، المملكة العربية السعودية"
        };
    }

    private void SetDefaultArticles()
    {
        _latestArticles = new List<Article>
        {
            new Article
            {
                Id = 1,
                Title = "مقال تجريبي 1",
                Content = "محتوى المقال التجريبي الأول لموقع الشركة، يحتوي على معلومات مهمة عن آخر الأخبار والتطورات.",
                ImageUrl = "/images/article1.jpg",
                PublishDate = DateTime.Now.AddDays(-1),
                // Category = "أخبار الشركة"
            },
            new Article
            {
                Id = 2,
                Title = "مقال تجريبي 2",
                Content = "محتوى المقال التجريبي الثاني لموقع الشركة، يحتوي على معلومات مهمة عن آخر المشاريع.",
                ImageUrl = "/images/article2.jpg",
                PublishDate = DateTime.Now.AddDays(-2),
                // Category = "مشاريع"
            },
            new Article
            {
                Id = 3,
                Title = "مقال تجريبي 3",
                Content = "محتوى المقال التجريبي الثالث لموقع الشركة، يحتوي على معلومات مهمة عن الخدمات الجديدة.",
                ImageUrl = "/images/article3.jpg",
                PublishDate = DateTime.Now.AddDays(-3),
                // Category = "خدمات"
            }
        };
    }

    private async Task LoadServices()
    {
        try
        {
            _services = await Https.GetFromJsonAsync<List<ServiceDto>>("api/Services") ?? new List<ServiceDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading services: {ex.Message}");
            _services = new List<ServiceDto>();
        }
    }

    /// <summary>
    /// تحميل بيانات صفحة "من نحن" المدارة من لوحة الإدارة
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    private async Task LoadAboutUsData()
    {
        try
        {
            _aboutUsLoading = true;
            StateHasChanged();

            // تحميل أول محتوى نشط لصفحة "من نحن" من واجهة برمجة التطبيقات
            _aboutUsData = await Https.GetFromJsonAsync<AboutUsDto>("api/AboutUs/first-active");
        }
        catch (Exception ex)
        {
            // في حالة حدوث خطأ، عرض null (سيتم استخدام البيانات الافتراضية)
            Console.WriteLine($"Error loading AboutUs data: {ex.Message}");
            _aboutUsData = null;
        }
        finally
        {
            _aboutUsLoading = false;
            StateHasChanged();
        }
    }

    private string GetArticleSummary(string content)
    {
        if (string.IsNullOrEmpty(content))
            return string.Empty;

        // Create a summary of the article (first 150 characters)
        var summary = content.Length > 150 ? content.Substring(0, 150) + "..." : content;
        return summary;
    }
}