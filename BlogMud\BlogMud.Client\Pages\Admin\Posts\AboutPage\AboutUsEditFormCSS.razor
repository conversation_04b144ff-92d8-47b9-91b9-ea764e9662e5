﻿
<style>
    /* AboutUsEditForm - Component Scoped Styles (Based on PostManagementDialog) */

    /* Dialog Container */
    .about-us-management-dialog {
        direction: rtl;
        font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Helvetica', '<PERSON>l', sans-serif;
    }

        .about-us-management-dialog .mud-dialog-content {
            padding: 0;
            max-height: 80vh;
            overflow-y: auto;
        }

    /* Dialog Title */
    .dialog-title-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem 0;
    }

    .dialog-title-icon {
        color: var(--mud-palette-primary);
        font-size: 1.5rem;
    }

    .dialog-title-text {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
        margin: 0;
    }

    /* Dialog Content */
    .dialog-content-container {
        padding: 1.5rem;
        background: var(--mud-palette-background-grey);
    }

    /* Form Container */
    .about-us-form {
        width: 100%;
    }

    .form-section-container {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    /* Form Sections */
    .form-section {
        background: var(--mud-palette-surface);
        border-radius: 8px;
        padding: 1.5rem;
        border: 1px solid var(--mud-palette-divider);
        transition: all 0.3s ease;
    }

        .form-section:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

    /* Section Titles */
    .section-title {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--mud-palette-divider);
    }

    .section-icon {
        color: var(--mud-palette-primary);
        font-size: 1.25rem;
    }

    .section-text {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
        margin: 0;
        flex-grow: 1;
    }

    /* Upload Sections */
    .upload-section {
        background: var(--mud-palette-background-grey);
        border: 2px dashed var(--mud-palette-divider);
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
    }

        .upload-section:hover {
            border-color: var(--mud-palette-primary);
            background: var(--mud-palette-primary-lighten-5);
        }

    .upload-header {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .upload-icon {
        color: var(--mud-palette-primary);
        font-size: 2rem;
    }

    .upload-text {
        text-align: center;
    }

    .upload-content {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    /* Upload Buttons */
    .upload-button {
        min-height: 48px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

    /* Image Preview */
    .image-preview {
        position: relative;
        display: inline-block;
        margin-top: 1rem;
    }

    .preview-image {
        max-width: 100%;
        max-height: 200px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

        .preview-image:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

    .image-caption {
        display: block;
        text-align: center;
        margin-top: 0.5rem;
        color: var(--mud-palette-text-secondary);
    }

    .delete-button {
        position: absolute;
        top: -8px;
        right: -8px;
        background: var(--mud-palette-error);
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        min-width: 24px;
        min-height: 24px;
    }

    /* Content Fields */
    .content-field {
        width: 100%;
        margin-bottom: 1rem;
    }

    /* Media Cards */
    .media-card {
        transition: all 0.3s ease;
        border-radius: 8px;
        overflow: hidden;
    }

        .media-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        align-items: center;
    }

    .cancel-button,
    .submit-button {
        min-height: 44px;
        min-width: 120px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

        .cancel-button:hover,
        .submit-button:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

    .action-icon-button {
        transition: all 0.3s ease;
    }

        .action-icon-button:hover {
            transform: scale(1.1);
        }

    /* Dialog Actions */
    .dialog-actions {
        padding: 1rem 1.5rem;
        background: var(--mud-palette-surface);
        border-top: 1px solid var(--mud-palette-divider);
    }

    /* Metadata Items */
    .metadata-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem;
        background: var(--mud-palette-background-grey);
        border-radius: 6px;
        margin-bottom: 0.5rem;
    }

    /* Production Items Preview */
    .production-items-preview {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    /* Responsive Design */
    @@media (max-width: 767px) {
        .dialog-content-container

    {
        padding: 1rem;
    }

    .form-section {
        padding: 1rem;
    }

    .section-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .upload-header {
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.75rem;
    }

    .cancel-button,
    .submit-button {
        width: 100%;
    }

    }

    @@media (min-width: 768px) and (max-width: 1023px) {
        .dialog-content-container

    {
        padding: 1.25rem;
    }

    .form-section {
        padding: 1.25rem;
    }

    }

    @@media (min-width: 1024px) {
        .about-us-management-dialog .mud-dialog-content

    {
        max-height: 85vh;
    }

    .dialog-content-container {
        padding: 2rem;
    }

    .form-section {
        padding: 2rem;
    }

    }

    /* RTL Specific Adjustments */
    [dir="rtl"] .dialog-title-container {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .section-title {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .upload-header {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .action-buttons {
        flex-direction: row-reverse;
    }

    /* High Contrast Mode Support */
    @@media (prefers-contrast: high) {
        .form-section, .upload-section, .media-card

    {
        border-width: 3px;
        border-style: solid;
    }

    .preview-image {
        border: 2px solid var(--mud-palette-text-primary);
    }

    }

    /* Reduced Motion Support */
    @@media (prefers-reduced-motion: reduce) {
        .form-section, .upload-button, .preview-image, .submit-button, .media-card

    {
        transition: none;
    }

    .upload-button:hover,
    .submit-button:hover:not(:disabled),
    .media-card:hover {
        transform: none;
    }

    }

    /* Smooth Animations */
    .form-section,
    .upload-section,
    .image-preview,
    .media-card {
        animation: fadeInUp 0.3s ease-out;
    }

    @@keyframes fadeInUp {
        from

    {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }

    }

    /* Improved Scrollbar for Dialog Content */
    .about-us-management-dialog .mud-dialog-content::-webkit-scrollbar {
        width: 8px;
    }

    .about-us-management-dialog .mud-dialog-content::-webkit-scrollbar-track {
        background: var(--mud-palette-background-grey);
        border-radius: 4px;
    }

    .about-us-management-dialog .mud-dialog-content::-webkit-scrollbar-thumb {
        background: var(--mud-palette-divider);
        border-radius: 4px;
    }

        .about-us-management-dialog .mud-dialog-content::-webkit-scrollbar-thumb:hover {
            background: var(--mud-palette-text-secondary);
        }
</style>
