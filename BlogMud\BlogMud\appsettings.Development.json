{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=MudBlog;User Id=sa;Password=*********;MultipleActiveResultSets=true;Encrypt=False;TrustServerCertificate=True;"}, "AdminAccount": {"Email": "<EMAIL>", "Password": "*********456", "FullName": "مدير النظام"}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SenderEmail": "<EMAIL>", "SenderName": "Blazor App2", "Username": "<EMAIL>", "Password": "shvn snjn yzon ngvf", "EnableSsl": true}}