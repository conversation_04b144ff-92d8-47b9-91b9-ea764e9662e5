﻿
<MudDialog Class="post-management-dialog" MaxWidth="MaxWidth.ExtraLarge" FullWidth="true">
    <TitleContent>
        <div class="dialog-title-container">
            <MudIcon Icon="@(article.Id > 0 ? Icons.Material.Filled.Edit : Icons.Material.Filled.Add)"
                     Class="dialog-title-icon" />
            <MudText Typo="Typo.h5" Class="dialog-title-text">
                @(article.Id > 0 ? "تعديل المقال" : "إضافة مقال جديد")
            </MudText>
        </div>
    </TitleContent>
    <DialogContent>
        <div class="dialog-content-container">
            <MudForm @ref="form" @bind-IsValid="@success" Class="post-form">

                <!-- Tabbed Interface -->
                <MudTabs Elevation="1" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6" @bind-ActivePanelIndex="activeTabIndex">

                    <!-- Tab 1: Basic Information Fields -->
                    <MudTabPanel Text="حقول المعلومات الأساسية" Icon="@Icons.Material.Filled.Info">
                        <MudGrid Spacing="3" Class="form-grid">
                            <MudItem xs="12">
                                <MudTextField @bind-Value="article.Title"
                                              Label="عنوان المقال"
                                              Required="true"
                                              RequiredError="يجب إدخال عنوان المقال"
                                              MaxLength="100"
                                              Counter="100"
                                              HelperText="الحد الأقصى 100 حرف"
                                              Variant="Variant.Outlined"
                                              Class="form-field"
                                              id="title-field" />
                            </MudItem>

                            <MudItem xs="12">
                                <MudTextField @bind-Value="article.Introduction"
                                              Label="مقدمة المقال"
                                              Required="true"
                                              RequiredError="يجب إدخال مقدمة المقال"
                                              MaxLength="200"
                                              Counter="200"
                                              HelperText="الحد الأقصى 200 حرف"
                                              Variant="Variant.Outlined"
                                              Lines="3"
                                              Class="form-field"
                                              id="introduction-field" />
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <MudAutocomplete T="CategoryDto"
                                                Label="القسم"
                                                @bind-Value="selectedCategory"
                                                SearchFunc="@SearchCategories"
                                                Variant="Variant.Outlined"
                                                ResetValueOnEmptyText="@resetValueOnEmptyText"
                                                CoerceValue="@coerceValue"
                                                SelectValueOnTab="@selectedOnTab"
                                                ToStringFunc="@(c => c == null ? "" : c.Name)"
                                                Class="form-field"
                                                Required="true"
                                                id="category-field" />
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <MudAutocomplete T="ClientDto"
                                                Label="العميل"
                                                @bind-Value="selectedClient"
                                                SearchFunc="@SearchClients"
                                                Variant="Variant.Outlined"
                                                ResetValueOnEmptyText="@resetValueOnEmptyText"
                                                CoerceValue="@coerceValue"
                                                SelectValueOnTab="@selectedOnTab"
                                                ToStringFunc="@(c => c == null ? "" : c.Name)"
                                                Class="form-field"
                                                Required="true"
                                                id="client-field" />
                            </MudItem>

                            <MudItem xs="12">
                                <MudTextField @bind-Value="article.Content"
                                              Label="محتوى المقال"
                                              Variant="Variant.Outlined"
                                              Lines="6"
                                              Required="true"
                                              Class="form-field content-field"
                                              id="content-field" />
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                    <!-- Tab 2: Media -->
                    <MudTabPanel Text="الوسائط" Icon="@Icons.Material.Filled.PhotoLibrary">
                        <MudGrid Spacing="3" Class="media-grid">
                            <!-- Main Article Image -->
                            <MudItem xs="12" md="4">
                                <MudPaper Class="upload-section main-image-upload" Elevation="0" id="main-image-field">
                                    <div class="upload-header">
                                        <MudIcon Icon="@Icons.Material.Filled.Image" Class="upload-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="upload-title">صورة المقال الرئيسية</MudText>
                                    </div>

                                    <div class="upload-content">
                                        <MudFileUpload T="IBrowserFile" FilesChanged="UploadFiles" Class="file-upload">
                                            <ActivatorContent>
                                                <MudButton Variant="Variant.Outlined"
                                                           Color="Color.Primary"
                                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                                           FullWidth="true"
                                                           Class="upload-button">
                                                    تحميل صورة رئيسية
                                                </MudButton>
                                            </ActivatorContent>
                                        </MudFileUpload>

                                        @if (!string.IsNullOrEmpty(article.ImageUrl))
                                        {
                                            <div class="image-preview">
                                                <MudImage Src="@article.ImageUrl"
                                                          Alt="صورة المقال"
                                                          Class="preview-image"
                                                          ObjectFit="ObjectFit.Cover" />
                                                <MudText Typo="Typo.caption" Class="image-caption">
                                                    @GetFileName(article.ImageUrl)
                                                </MudText>
                                            </div>
                                        }
                                    </div>
                                </MudPaper>
                            </MudItem>

                            <!-- Carousel Images -->
                            <MudItem xs="12" md="4">
                                <MudPaper Class="upload-section carousel-upload" Elevation="0">
                                    <div class="upload-header">
                                        <MudIcon Icon="@Icons.Material.Filled.ViewCarousel" Class="upload-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="upload-title">صور شرائح العرض</MudText>
                                    </div>

                                    <div class="upload-content">
                                        <MudFileUpload T="IReadOnlyList<IBrowserFile>"
                                                       Accept=".png, .jpg, .jpeg, .webp"
                                                       FilesChanged="UploadMultipleFiles"
                                                       MaximumFileCount="100"
                                                       Class="file-upload">
                                            <ActivatorContent>
                                                <MudButton Variant="Variant.Outlined"
                                                           Color="Color.Secondary"
                                                           StartIcon="@Icons.Material.Filled.PhotoLibrary"
                                                           FullWidth="true"
                                                           Class="upload-button">
                                                    تحميل شرائح متعددة
                                                </MudButton>
                                            </ActivatorContent>
                                        </MudFileUpload>

                                        @if (!string.IsNullOrEmpty(article.ImageCarousel))
                                        {
                                            <div class="image-preview">
                                                <MudImage Src="@article.ImageCarousel"
                                                          Alt="صورة الشرائح"
                                                          Class="preview-image"
                                                          ObjectFit="ObjectFit.Cover" />
                                                <div class="carousel-info">
                                                    @if (carouselImagesCount > 1)
                                                    {
                                                        <MudChip T="string" Color="Color.Success" Size="Size.Small" Class="image-count-chip">
                                                            @carouselImagesCount صورة
                                                        </MudChip>
                                                    }
                                                    <MudText Typo="Typo.caption" Class="image-caption">
                                                        @GetFileName(article.ImageCarousel)
                                                    </MudText>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </MudPaper>
                            </MudItem>

                            <!-- Video Upload -->
                            <MudItem xs="12" md="4">
                                <MudPaper Class="upload-section video-upload" Elevation="0">
                                    <div class="upload-header">
                                        <MudIcon Icon="@Icons.Material.Filled.VideoFile" Class="upload-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="upload-title">فيديو المقال</MudText>
                                    </div>

                                    <div class="upload-content">
                                        <MudFileUpload T="IBrowserFile"
                                                       Accept=".mp4,.avi,.mov,.wmv,.flv,.webm,.mkv,.m4v,.3gp,.ogv"
                                                       FilesChanged="UploadVideo"
                                                       Class="file-upload">
                                            <ActivatorContent>
                                                <MudButton Variant="Variant.Outlined"
                                                           Color="Color.Info"
                                                           StartIcon="@Icons.Material.Filled.VideoCall"
                                                           FullWidth="true"
                                                           Class="upload-button">
                                                    تحميل فيديو
                                                </MudButton>
                                            </ActivatorContent>
                                        </MudFileUpload>

                                        @if (hasVideo && !string.IsNullOrEmpty(videoUrl))
                                        {
                                            <div class="video-preview">
                                                <MudAlert Severity="Severity.Success" Class="video-success-alert">
                                                    تم تحميل الفيديو بنجاح
                                                </MudAlert>
                                                <div class="video-container">
                                                    <video controls class="preview-video">
                                                        <source src="@videoUrl" type="video/mp4">
                                                        المتصفح لا يدعم عرض الفيديو.
                                                    </video>
                                                </div>
                                                <MudText Typo="Typo.caption" Class="video-caption">
                                                    سيتم عرض الفيديو في صفحة المقال
                                                </MudText>
                                            </div>
                                        }
                                    </div>
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                </MudTabs>

                <!-- Notification Status Section -->
                @if (isSendingNotifications || notificationsSentCount > 0)
                {
                    <MudPaper Class="form-section notification-section" Elevation="1">
                        @if (isSendingNotifications)
                        {
                            <MudAlert Severity="Severity.Info" Class="notification-alert">
                                <div class="notification-content">
                                    <MudProgressCircular Color="Color.Primary" Size="Size.Small" Indeterminate="true" Class="notification-spinner" />
                                    <MudText Class="notification-text">جاري إرسال الإشعارات للمستخدمين...</MudText>
                                </div>
                            </MudAlert>
                        }
                        else if (notificationsSentCount > 0)
                        {
                            <MudAlert Severity="Severity.Success" Class="notification-alert">
                                <div class="notification-content">
                                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="notification-icon" />
                                    <MudText Class="notification-text">تم إرسال الإشعارات بنجاح إلى @notificationsSentCount مستخدم</MudText>
                                </div>
                            </MudAlert>
                        }
                    </MudPaper>
                }

            </MudForm>
        </div>
    </DialogContent>
    <DialogActions>
        <div class="dialog-actions">
            <div class="action-buttons">
                <MudButton Variant="Variant.Text"
                           Color="Color.Default"
                           OnClick="Cancel"
                           StartIcon="@Icons.Material.Filled.Cancel"
                           Class="cancel-button">
                    إلغاء
                </MudButton>
                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           OnClick="Submit"
                           Disabled="@(!success)"
                           StartIcon="@(article.Id > 0 ? Icons.Material.Filled.Save : Icons.Material.Filled.Add)"
                           Class="submit-button">
                    @(article.Id > 0 ? "حفظ التعديلات" : "إضافة المقال")
                </MudButton>
            </div>
        </div>
    </DialogActions>
</MudDialog>

<PostManagementDialogCSS />

<script>
    window.scrollToElement = (elementId) => {
        const element = document.getElementById(elementId);
        if (element) {
            // إضافة كلاس التمييز
            element.classList.add('highlighted-field');

            // التمرير السلس إلى العنصر
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'nearest'
            });

            // التركيز على العنصر إذا كان قابلاً للتركيز
            if (element.focus) {
                setTimeout(() => {
                    element.focus();
                }, 500);
            }

            // إزالة كلاس التمييز بعد 3 ثوانٍ
            setTimeout(() => {
                element.classList.remove('highlighted-field');
            }, 3000);
        }
    };
</script>


@code {
    #region الخصائص والمتغيرات

    /// <summary>
    /// مثيل مربع الحوار MudDialog الذي يتم تمريره من خلال CascadingParameter
    /// </summary>
    /// <remarks>
    /// يستخدم للتحكم في إغلاق مربع الحوار وإرجاع النتائج
    /// </remarks>
    [CascadingParameter] public required IMudDialogInstance MudDialog { get; set; }

    /// <summary>
    /// خدمة JavaScript Runtime للتفاعل مع JavaScript
    /// </summary>
    [Inject] private IJSRuntime JSRuntime { get; set; } = default!;

    /// <summary>
    /// بيانات المقال التي يتم تمريرها إلى مربع الحوار عند التعديل
    /// </summary>
    /// <remarks>
    /// إذا كانت القيمة null، فهذا يعني أننا نقوم بإنشاء مقال جديد
    /// </remarks>
    [Parameter] public ArticleDto? articleDto { get; set; }

    /// <summary>
    /// مؤشر على حالة إرسال الإشعارات
    /// </summary>
    private bool isSendingNotifications = false;

    /// <summary>
    /// عدد المستخدمين الذين تم إرسال الإشعارات إليهم
    /// </summary>
    private int notificationsSentCount = 0;


    /// <summary>
    /// نموذج بيانات المقال الذي يتم تعديله أو إنشاؤه
    /// </summary>
    private ArticleDto article = new ArticleDto();

    /// <summary>
    /// مؤشر على صحة النموذج
    /// </summary>
    private bool success;

    /// <summary>
    /// مرجع إلى نموذج MudForm المستخدم للتحقق من صحة البيانات
    /// </summary>
    private MudForm? form;

    /// <summary>
    /// قيمة النص المستخدمة في محرر النص
    /// </summary>
    public string TextValue { get; set; }

    // خصائص لمكونات الاختيار التلقائي
    /// <summary>
    /// القسم المحدد للمقال
    /// </summary>
    private CategoryDto selectedCategory;

    /// <summary>
    /// العميل المحدد للمقال
    /// </summary>
    private ClientDto selectedClient;

    /// <summary>
    /// خيار إعادة تعيين القيمة عند إفراغ النص
    /// </summary>
    private bool resetValueOnEmptyText;

    /// <summary>
    /// خيار إجبار النص
    /// </summary>
    private bool coerceText;

    /// <summary>
    /// خيار إجبار القيمة
    /// </summary>
    private bool coerceValue;

    /// <summary>
    /// خيار تحديد القيمة عند الضغط على Tab
    /// </summary>
    private bool selectedOnTab;

    // قوائم لتخزين الأقسام والعملاء
    /// <summary>
    /// قائمة الأقسام المتاحة
    /// </summary>
    private List<CategoryDto> categories = new List<CategoryDto>();

    /// <summary>
    /// قائمة العملاء المتاحة
    /// </summary>
    private List<ClientDto> clients = new List<ClientDto>();

    // معالجة الملفات
    /// <summary>
    /// قائمة الملفات التي تم تحميلها
    /// </summary>
    IList<IBrowserFile> _files = new List<IBrowserFile>();

    /// <summary>
    /// مؤشر على وجود فيديو
    /// </summary>
    private bool hasVideo = false;

    /// <summary>
    /// رابط الفيديو
    /// </summary>
    private string videoUrl = string.Empty;

    /// <summary>
    /// مؤشر على تضمين الفيديو في المحتوى (الافتراضي هو عدم التضمين)
    /// </summary>
    private bool includeVideoInContent = false;

    /// <summary>
    /// عدد صور عرض الشرائح
    /// </summary>
    private int carouselImagesCount = 0;

    /// <summary>
    /// معرف الجلسة المستخدم لتجميع صور عرض الشرائح المؤقتة
    /// </summary>
    private string sessionId = string.Empty;

    /// <summary>
    /// فهرس التبويب النشط (0 = المعلومات الأساسية، 1 = الوسائط)
    /// </summary>
    private int activeTabIndex = 0;
    #endregion

    #region دوال دورة الحياة

    /// <summary>
    /// تهيئة مكونات الصفحة عند بدء التشغيل
    /// </summary>
    /// <remarks>
    /// إذا كان هناك مقال موجود (تعديل)، يتم نسخ بياناته إلى النموذج الحالي
    /// كما يتم التحقق من وجود فيديو في المقال واستخراج رابط الفيديو إذا كان موجودًا
    /// </remarks>
    protected override void OnInitialized()
    {
        if (articleDto != null)
        {
            article = new ArticleDto
            {
                Id = articleDto.Id,
                Title = articleDto.Title,
                Introduction = articleDto.Introduction,
                Content = articleDto.Content,
                ImageUrl = articleDto.ImageUrl,
                ImageCarousel = articleDto.ImageCarousel,
                VideoUrl = articleDto.VideoUrl, // Include VideoUrl property
                ClientId = articleDto.ClientId,
                ClientName = articleDto.ClientName,
                CategoryId = articleDto.CategoryId,
                CategoryName = articleDto.CategoryName,
                PublishDate = articleDto.PublishDate,
                IsPublished = articleDto.IsPublished,
                CreatedAt = articleDto.CreatedAt,
                LastModifiedAt = articleDto.LastModifiedAt
            };

            // التحقق من وجود رابط فيديو في خاصية VideoUrl
            if (!string.IsNullOrEmpty(article.VideoUrl))
            {
                hasVideo = true;
                videoUrl = article.VideoUrl;
            }
            // للتوافق مع الإصدارات السابقة، نتحقق أيضًا من وجود فيديو في المحتوى
            else if (article.Content != null && article.Content.Contains("<video"))
            {
                hasVideo = true;

                // محاولة استخراج رابط الفيديو من المحتوى
                try
                {
                    int sourceIndex = article.Content.IndexOf("src=\"");
                    if (sourceIndex > 0)
                    {
                        sourceIndex += 5; // طول 'src="'
                        int endIndex = article.Content.IndexOf("\"", sourceIndex);
                        if (endIndex > sourceIndex)
                        {
                            videoUrl = article.Content.Substring(sourceIndex, endIndex - sourceIndex);
                            // تخزين الرابط المستخرج في خاصية VideoUrl
                            article.VideoUrl = videoUrl;

                            // إزالة HTML الفيديو من المحتوى
                            int videoContainerStart = article.Content.IndexOf("<div class=\"video-container\">");
                            if (videoContainerStart >= 0)
                            {
                                int videoContainerEnd = article.Content.IndexOf("</div>", videoContainerStart);
                                if (videoContainerEnd > videoContainerStart)
                                {
                                    // إضافة 6 لطول "</div>"
                                    videoContainerEnd += 6;

                                    // إزالة حاوية الفيديو من المحتوى
                                    string beforeVideo = article.Content.Substring(0, videoContainerStart);
                                    string afterVideo = videoContainerEnd < article.Content.Length ?
                                        article.Content.Substring(videoContainerEnd) : "";

                                    article.Content = beforeVideo + afterVideo;
                                }
                            }
                        }
                    }
                }
                catch
                {
                    // إذا حدث خطأ أثناء استخراج الرابط، نستمر فقط
                }
            }
        }
    }

    /// <summary>
    /// تهيئة مكونات الصفحة عند بدء التشغيل بشكل غير متزامن
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بتحميل قوائم الأقسام والعملاء من الخادم
    /// وتعيين القسم والعميل المحددين إذا كان المقال موجودًا
    /// وتعيين القيم الافتراضية إذا كان المقال جديدًا
    /// </remarks>
    protected override async Task OnInitializedAsync()
    {
        await LoadCategories();
        await LoadClients();

        // تعيين القسم والعميل المحددين إذا كان تعديل لمقال موجود
        if (articleDto != null)
        {
            if (articleDto.CategoryId > 0)
            {
                selectedCategory = categories.FirstOrDefault(c => c.Id == articleDto.CategoryId);
            }

            if (articleDto.ClientId > 0)
            {
                selectedClient = clients.FirstOrDefault(c => c.Id == articleDto.ClientId);
            }
        }

        // إذا كنا ننشئ مقالًا جديدًا، نقوم بتعيين القيم الافتراضية
        if (articleDto == null)
        {
            // تعيين القيم الافتراضية إذا لزم الأمر
            article.IsPublished = true;
            article.PublishDate = DateTime.Now;
            article.CreatedAt = DateTime.Now;
        }

        await base.OnInitializedAsync();
    }
    #endregion

    #region الأحداث والتفاعلات

    /// <summary>
    /// إلغاء العملية وإغلاق مربع الحوار
    /// </summary>
    private void Cancel()
    {
        MudDialog.Cancel();
    }

    /// <summary>
    /// حفظ بيانات المقال
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بالتحقق من صحة النموذج أولاً، ثم التحقق من الحقول المطلوبة
    /// ثم إرسال البيانات إلى الخادم
    /// إذا كان معرف المقال أكبر من صفر، يتم تحديث مقال موجود، وإلا يتم إنشاء مقال جديد
    /// إذا تم إنشاء مقال جديد وكان هناك صور شرائح مؤقتة، يتم تحديث معرف المقال لهذه الصور
    /// </remarks>
    private async Task Submit()
    {
        if (form != null)
            await form.Validate();
        if (!success) return;

        try
        {
            // التحقق من الحقول المطلوبة مع التمرير التلقائي
            if (string.IsNullOrEmpty(article.Title))
            {
                await ScrollToMissingField("title-field", 0, "يجب إدخال عنوان المقال");
                return;
            }

            // التحقق من طول العنوان
            if (article.Title.Length > 100)
            {
                await ScrollToMissingField("title-field", 0, "يجب أن لا يتجاوز عنوان المقال 100 حرف");
                return;
            }

            if (string.IsNullOrEmpty(article.Introduction))
            {
                await ScrollToMissingField("introduction-field", 0, "يجب إدخال مقدمة المقال");
                return;
            }

            // التحقق من طول المقدمة
            if (article.Introduction.Length > 200)
            {
                await ScrollToMissingField("introduction-field", 0, "يجب أن لا تتجاوز مقدمة المقال 200 حرف");
                return;
            }

            if (string.IsNullOrEmpty(article.Content))
            {
                await ScrollToMissingField("content-field", 0, "يجب إدخال محتوى المقال");
                return;
            }

            if (string.IsNullOrEmpty(article.ImageUrl))
            {
                await ScrollToMissingField("main-image-field", 1, "يجب إدخال صورة اساسية");
                return;
            }

            // تعيين معرفات القسم والعميل من العناصر المحددة
            if (selectedCategory != null)
            {
                article.CategoryId = selectedCategory.Id;
                article.CategoryName = selectedCategory.Name;
            }
            else
            {
                await ScrollToMissingField("category-field", 0, "يجب اختيار قسم للمقال");
                return;
            }

            if (selectedClient != null)
            {
                article.ClientId = selectedClient.Id;
                article.ClientName = selectedClient.Name;
            }
            else
            {
                await ScrollToMissingField("client-field", 0, "يجب اختيار عميل للمقال");
                return;
            }

            // تعيين قيم افتراضية للحقول المطلوبة إذا كانت مفقودة
            if (string.IsNullOrEmpty(article.ImageUrl))
            {
                article.ImageUrl = "/images/default-article.jpg";
            }

            if (string.IsNullOrEmpty(article.ImageCarousel))
            {
                article.ImageCarousel = "/images/default-carousel.jpg";
            }

            // لم نعد نضيف الفيديو إلى المحتوى، سيتم تخزينه بشكل منفصل في خاصية VideoUrl
            // وعرضه في صفحة المقال دون أن يكون جزءًا من المحتوى

            HttpResponseMessage response;

            // تحديد ما إذا كان تحديث أو إنشاء جديد
            if (article.Id > 0)
            {
                response = await Http.PutAsJsonAsync($"api/Article/{article.Id}", article);
            }
            else
            {
                response = await Http.PostAsJsonAsync("api/Article", article);
            }

            // معالجة الاستجابة
            if (response.IsSuccessStatusCode)
            {
                bool isNewPost = article.Id == 0;
                ArticleDto? createdArticle = null;

                // للمقالات الجديدة، نحصل على البيانات المُرجعة من الخادم
                // للمقالات المُحدثة، الخادم يُرجع NoContent (204) بدون محتوى JSON
                if (isNewPost)
                {
                    try
                    {
                        createdArticle = await response.Content.ReadFromJsonAsync<ArticleDto>();
                    }
                    catch (Exception jsonEx)
                    {
                        // في حالة فشل تحليل JSON للمقال الجديد، نسجل الخطأ
                        Console.WriteLine($"Error parsing created article JSON: {jsonEx.Message}");
                        var responseText = await response.Content.ReadAsStringAsync();
                        Console.WriteLine($"Response content: {responseText}");
                    }
                }

                // إذا كان مقالًا جديدًا، قم بتحديث صور عرض الشرائح بمعرف المقال
                if (isNewPost && !string.IsNullOrEmpty(sessionId))
                {
                    try
                    {
                        // الحصول على معرف المقال الذي تم إنشاؤه حديثًا
                        if (createdArticle != null && createdArticle.Id > 0)
                        {
                            // استدعاء نقطة نهاية لتحديث صور عرض الشرائح
                            await Http.PostAsync($"api/Upload/updateSessionImages?sessionId={sessionId}&articleId={createdArticle.Id}", null);
                        }
                    }
                    catch (Exception ex)
                    {
                        // تسجيل الخطأ فقط، لا تعرضه للمستخدم
                        Console.WriteLine($"Error updating carousel images: {ex.Message}");
                    }
                }

                _Snackbar.Add("تم حفظ المقال بنجاح", Severity.Success);

                // إرسال الإشعارات للمستخدمين إذا كان مقالًا جديدًا
                if (isNewPost && createdArticle != null)
                {
                    await SendNotificationsAsync(createdArticle);
                }

                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _Snackbar.Add($"فشل في حفظ المقال: {errorContent}", Severity.Error);
                Console.WriteLine($"Error saving article: {errorContent}");
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء حفظ المقال: {ex.Message}", Severity.Error);
        }
    }

    /// <summary>
    /// التمرير التلقائي إلى الحقل المفقود مع التمييز البصري
    /// </summary>
    /// <param name="fieldId">معرف الحقل المطلوب التمرير إليه</param>
    /// <param name="tabIndex">فهرس التبويب الذي يحتوي على الحقل</param>
    /// <param name="errorMessage">رسالة الخطأ المراد عرضها</param>
    private async Task ScrollToMissingField(string fieldId, int tabIndex, string errorMessage)
    {
        try
        {
            // التبديل إلى التبويب المناسب إذا لزم الأمر
            if (activeTabIndex != tabIndex)
            {
                activeTabIndex = tabIndex;
                StateHasChanged();
                await Task.Delay(200); // انتظار قصير لضمان تحديث التبويب
            }

            // انتظار إضافي لضمان تحديث DOM
            await Task.Delay(100);

            // التمرير إلى الحقل باستخدام JavaScript مع التمييز البصري
            await JSRuntime.InvokeVoidAsync("scrollToElement", fieldId);

            // عرض رسالة الخطأ
            _Snackbar.Add(errorMessage, Severity.Error);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in ScrollToMissingField: {ex.Message}");
            // في حالة فشل التمرير، عرض رسالة الخطأ فقط
            _Snackbar.Add(errorMessage, Severity.Error);
        }
    }

    /// <summary>
    /// إرسال الإشعارات للمستخدمين عند إنشاء مقال جديد
    /// </summary>
    /// <param name="createdArticle">المقال الذي تم إنشاؤه</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بإرسال إشعارات داخل التطبيق وإشعارات بريد إلكتروني لجميع المستخدمين المسجلين
    /// يتم التعامل مع الأخطاء بحيث لا تؤثر على عملية إنشاء المقال
    /// </remarks>
    private async Task SendNotificationsAsync(ArticleDto createdArticle)
    {
        try
        {
            isSendingNotifications = true;
            notificationsSentCount = 0;
            StateHasChanged(); // تحديث واجهة المستخدم لإظهار حالة الإرسال

            // إرسال طلب إلى الخادم لإرسال الإشعارات
            var response = await Http.PostAsJsonAsync("api/Notification/send-new-post-notification", createdArticle);

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<BlogMud.Shared.DTOs.NotificationResultDto>();
                if (result != null)
                {
                    notificationsSentCount = result.UserCount;
                    _Snackbar.Add($"تم إرسال الإشعارات بنجاح إلى {result.UserCount} مستخدم", Severity.Success);
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _Snackbar.Add($"فشل في إرسال الإشعارات: {errorContent}", Severity.Warning);
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء إرسال الإشعارات: {ex.Message}", Severity.Warning);
        }
        finally
        {
            isSendingNotifications = false;
            StateHasChanged(); // تحديث واجهة المستخدم لإخفاء حالة الإرسال
        }
    }
    #endregion

    #region طلبات البيانات

    /// <summary>
    /// تحميل قائمة الأقسام من الخادم
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بتحميل قائمة الأقسام من الخادم وتخزينها في المتغير categories
    /// في حالة حدوث خطأ، يتم عرض رسالة خطأ وتعيين قائمة فارغة
    /// </remarks>
    private async Task LoadCategories()
    {
        try
        {
            categories = await Http.GetFromJsonAsync<List<CategoryDto>>("api/Categories") ?? new List<CategoryDto>();
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء تحميل الأقسام: {ex.Message}", Severity.Error);
            categories = new List<CategoryDto>();
        }
    }

    /// <summary>
    /// تحميل قائمة العملاء من الخادم
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بتحميل قائمة العملاء من الخادم وتخزينها في المتغير clients
    /// في حالة حدوث خطأ، يتم عرض رسالة خطأ وتعيين قائمة فارغة
    /// </remarks>
    private async Task LoadClients()
    {
        try
        {
            clients = await Http.GetFromJsonAsync<List<ClientDto>>("api/Clients") ?? new List<ClientDto>();
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء تحميل العملاء: {ex.Message}", Severity.Error);
            clients = new List<ClientDto>();
        }
    }
    #endregion

    #region معالجة الملفات

    /// <summary>
    /// تحميل ملف صورة للمقال
    /// </summary>
    /// <param name="file">ملف الصورة المراد تحميله</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بتحميل ملف صورة إلى الخادم وتعيين رابط الصورة في المقال
    /// الحد الأقصى لحجم الملف هو 10 ميجابايت
    /// </remarks>
    private async Task UploadFiles(IBrowserFile file)
    {
        _files.Add(file);

        try
        {
            // إنشاء نموذج مع الملف
            var content = new MultipartFormDataContent();
            var fileContent = new StreamContent(file.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024)); // الحد الأقصى 10 ميجابايت
            content.Add(fileContent, "file", file.Name);

            // إضافة مسار الصورة القديمة للحذف
            if (!string.IsNullOrEmpty(article.ImageUrl))
            {
                content.Add(new StringContent(article.ImageUrl), "oldImageUrl");
            }

            // تحميل الملف إلى الخادم
            var response = await Http.PostAsync("api/Upload/image", content);

            if (response.IsSuccessStatusCode)
            {
                try
                {
                    var uploadResult = await response.Content.ReadFromJsonAsync<UploadResult>();
                    if (uploadResult != null && !string.IsNullOrEmpty(uploadResult.StoredFileName))
                    {
                        // تعيين رابط الصورة في المقال
                        article.ImageUrl = uploadResult.StoredFileName;
                        _Snackbar.Add("تم تحميل الصورة بنجاح", Severity.Success);
                    }
                    else
                    {
                        _Snackbar.Add("فشل في معالجة استجابة تحميل الصورة", Severity.Error);
                    }
                }
                catch (Exception jsonEx)
                {
                    // في حالة فشل تحليل JSON، نحاول قراءة الاستجابة كنص
                    var responseText = await response.Content.ReadAsStringAsync();
                    _Snackbar.Add($"خطأ في معالجة استجابة الخادم: {jsonEx.Message}. الاستجابة: {responseText}", Severity.Error);
                }
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                _Snackbar.Add($"فشل في تحميل الصورة: {error}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء تحميل الصورة: {ex.Message}", Severity.Error);
        }
    }

    /// <summary>
    /// تحميل مجموعة من ملفات الصور لعرض الشرائح
    /// </summary>
    /// <param name="files">قائمة ملفات الصور المراد تحميلها</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بتحميل مجموعة من ملفات الصور إلى الخادم لاستخدامها في عرض الشرائح
    /// يتم استخدام الملف الأول كصورة معاينة ويتم تحميل باقي الملفات في الخلفية
    /// يتم استخدام معرف المقال أو معرف جلسة مؤقت لتجميع الصور
    /// الحد الأقصى لحجم كل ملف هو 10 ميجابايت
    /// </remarks>
    private async Task UploadMultipleFiles(IReadOnlyList<IBrowserFile> files)
    {
        if (files == null || files.Count == 0)
            return;

        try
        {
            // نأخذ الملف الأول لعرضه كمعاينة
            var file = files[0];

            foreach (var currentFile in files)
            {
                _files.Add(currentFile);
            }

            // إنشاء معرف جلسة إذا لم يكن موجودًا
            if (string.IsNullOrEmpty(sessionId))
            {
                sessionId = Guid.NewGuid().ToString();
            }

            // تحميل الملف الأول للمعاينة
            var content = new MultipartFormDataContent();
            var fileContent = new StreamContent(file.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024)); // الحد الأقصى 10 ميجابايت
            content.Add(fileContent, "file", file.Name);

            // إضافة مسار الصورة القديمة للحذف
            if (!string.IsNullOrEmpty(article.ImageCarousel))
            {
                content.Add(new StringContent(article.ImageCarousel), "oldImageUrl");
            }

            // إضافة معرف المقال أو معرف الجلسة لتجميع الصور
            if (article.Id > 0)
            {
                content.Add(new StringContent(article.Id.ToString()), "articleId");

                // إذا كان هناك معرف مقال، نقوم بحذف جميع صور العرض المتحرك القديمة المرتبطة بهذا المقال
                try
                {
                    // استخدام نقطة نهاية API الجديدة لحذف صور الشرائح
                    var deleteResponse = await Http.DeleteAsync($"api/Upload/slider/{article.Id}");

                    if (deleteResponse.IsSuccessStatusCode)
                    {
                        try
                        {
                            var deleteResult = await deleteResponse.Content.ReadFromJsonAsync<DeleteResult>();
                            if (deleteResult != null)
                            {
                                _Snackbar.Add($"تم حذف {deleteResult.DeletedCount} صورة قديمة", Severity.Info);
                            }
                        }
                        catch (Exception jsonEx)
                        {
                            // في حالة فشل تحليل JSON، نحاول قراءة الاستجابة كنص
                            var responseText = await deleteResponse.Content.ReadAsStringAsync();
                            _Snackbar.Add($"خطأ في معالجة استجابة حذف الصور: {jsonEx.Message}. الاستجابة: {responseText}", Severity.Warning);
                        }
                    }
                    else
                    {
                        _Snackbar.Add("فشل في حذف صور الشرائح القديمة", Severity.Warning);
                        // نستمر في تحميل الصور الجديدة حتى لو فشل حذف الصور القديمة
                    }
                }
                catch (Exception ex)
                {
                    _Snackbar.Add($"خطأ أثناء حذف صور الشرائح القديمة: {ex.Message}", Severity.Error);
                    // نستمر في تحميل الصور الجديدة حتى لو فشل حذف الصور القديمة
                }
            }
            else
            {
                content.Add(new StringContent(sessionId), "sessionId");
            }

            // تحميل الملف إلى الخادم
            var response = await Http.PostAsync("api/Upload/slider", content);

            if (response.IsSuccessStatusCode)
            {
                try
                {
                    var uploadResult = await response.Content.ReadFromJsonAsync<UploadResult>();
                    if (uploadResult != null && !string.IsNullOrEmpty(uploadResult.StoredFileName))
                    {
                        // تعيين رابط صورة عرض الشرائح في المقال (الصورة الأولى للمعاينة)
                        article.ImageCarousel = uploadResult.StoredFileName;

                    // تحميل باقي الملفات في الخلفية
                    if (files.Count > 1)
                    {
                        // البدء من الملف الثاني (الفهرس 1)
                        for (int i = 1; i < files.Count; i++)
                        {
                            var additionalFile = files[i];
                            var additionalContent = new MultipartFormDataContent();
                            var additionalFileContent = new StreamContent(additionalFile.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024));
                            additionalContent.Add(additionalFileContent, "file", additionalFile.Name);

                            // إضافة معرف المقال أو معرف الجلسة لتجميع الصور
                            if (article.Id > 0)
                            {
                                additionalContent.Add(new StringContent(article.Id.ToString()), "articleId");
                            }
                            else
                            {
                                additionalContent.Add(new StringContent(sessionId), "sessionId");
                            }

                            // تحميل الملف الإضافي مع معالجة الأخطاء
                            try
                            {
                                var additionalResponse = await Http.PostAsync("api/Upload/slider", additionalContent);
                                if (!additionalResponse.IsSuccessStatusCode)
                                {
                                    var errorContent = await additionalResponse.Content.ReadAsStringAsync();
                                    _Snackbar.Add($"فشل في تحميل الصورة {i + 1}: {errorContent}", Severity.Warning);
                                }
                            }
                            catch (Exception ex)
                            {
                                _Snackbar.Add($"خطأ في تحميل الصورة {i + 1}: {ex.Message}", Severity.Warning);
                            }
                        }
                    }

                        carouselImagesCount = files.Count;
                        _Snackbar.Add($"تم تحميل {files.Count} صورة بنجاح", Severity.Success);
                    }
                    else
                    {
                        _Snackbar.Add("فشل في معالجة استجابة تحميل الصور", Severity.Error);
                    }
                }
                catch (Exception jsonEx)
                {
                    // في حالة فشل تحليل JSON، نحاول قراءة الاستجابة كنص
                    var responseText = await response.Content.ReadAsStringAsync();
                    _Snackbar.Add($"خطأ في معالجة استجابة الخادم: {jsonEx.Message}. الاستجابة: {responseText}", Severity.Error);
                }
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                _Snackbar.Add($"فشل في تحميل شرائح الصور: {error}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء تحميل شرائح الصور: {ex.Message}", Severity.Error);
        }
    }

    /// <summary>
    /// تحميل ملف صورة واحد لعرض الشرائح (للتوافق مع الكود الموجود)
    /// </summary>
    /// <param name="file">ملف الصورة المراد تحميله</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// هذه الدالة موجودة للحفاظ على التوافق مع الكود الموجود
    /// وهي تقوم بتحويل الملف الواحد إلى قائمة واستدعاء دالة UploadMultipleFiles
    /// </remarks>
    private async Task UploadFiles2(IBrowserFile file)
    {
        await UploadMultipleFiles(new List<IBrowserFile> { file });
    }

    /// <summary>
    /// تحميل ملف فيديو للمقال
    /// </summary>
    /// <param name="file">ملف الفيديو المراد تحميله</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بتحميل ملف فيديو إلى الخادم وتخزين رابط الفيديو في خاصية VideoUrl للمقال
    /// الحد الأقصى لحجم الملف هو 50 ميجابايت
    /// يتم التحقق من نوع الملف للتأكد من أنه ملف فيديو صالح
    /// </remarks>
    private async Task UploadVideo(IBrowserFile file)
    {
        _files.Add(file);

        try
        {
            // التحقق من نوع الملف - قبول ملفات الفيديو فقط
            var allowedVideoExtensions = new[] { ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv", ".m4v", ".3gp", ".ogv" };
            var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();

            if (!allowedVideoExtensions.Contains(fileExtension))
            {
                _Snackbar.Add("يُسمح فقط بملفات الفيديو (mp4, avi, mov, wmv, flv, webm, mkv, m4v, 3gp, ogv)", Severity.Error);
                return;
            }

            // التحقق من حجم الملف (الحد الأقصى 50 ميجابايت)
            const long maxFileSize = 50 * 1024 * 1024; // 50 MB
            if (file.Size > maxFileSize)
            {
                _Snackbar.Add("حجم الملف يتجاوز الحد الأقصى (50 ميجابايت)", Severity.Error);
                return;
            }

            // إنشاء نموذج مع الملف
            var content = new MultipartFormDataContent();
            var fileContent = new StreamContent(file.OpenReadStream(maxAllowedSize: maxFileSize));
            content.Add(fileContent, "file", file.Name);

            // إضافة مسار الفيديو القديم للحذف
            if (!string.IsNullOrEmpty(article.VideoUrl))
            {
                content.Add(new StringContent(article.VideoUrl), "oldVideoUrl");
            }

            // تحميل الملف إلى الخادم
            var response = await Http.PostAsync("api/Upload/video", content);

            if (response.IsSuccessStatusCode)
            {
                try
                {
                    var uploadResult = await response.Content.ReadFromJsonAsync<UploadResult>();
                    if (uploadResult != null && !string.IsNullOrEmpty(uploadResult.StoredFileName))
                    {
                        // تخزين رابط الفيديو في خاصية VideoUrl للمقال
                        videoUrl = uploadResult.StoredFileName;
                        article.VideoUrl = videoUrl; // تعيين خاصية VideoUrl
                        hasVideo = true;
                        _Snackbar.Add("تم تحميل الفيديو بنجاح", Severity.Success);
                    }
                    else
                    {
                        _Snackbar.Add("فشل في معالجة استجابة تحميل الفيديو", Severity.Error);
                    }
                }
                catch (Exception jsonEx)
                {
                    // في حالة فشل تحليل JSON، نحاول قراءة الاستجابة كنص
                    var responseText = await response.Content.ReadAsStringAsync();
                    _Snackbar.Add($"خطأ في معالجة استجابة الخادم: {jsonEx.Message}. الاستجابة: {responseText}", Severity.Error);
                }
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                _Snackbar.Add($"فشل في تحميل الفيديو: {error}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء تحميل الفيديو: {ex.Message}", Severity.Error);
        }
    }
    #endregion

    #region الفئات المساعدة

    /// <summary>
    /// فئة لمعالجة استجابة التحميل
    /// </summary>
    private class UploadResult
    {
        /// <summary>
        /// اسم الملف المخزن على الخادم
        /// </summary>
        public string StoredFileName { get; set; }

        /// <summary>
        /// اسم الملف الأصلي
        /// </summary>
        public string OriginalFileName { get; set; }

        /// <summary>
        /// حجم الملف
        /// </summary>
        public long Size { get; set; }
    }

    /// <summary>
    /// فئة لمعالجة استجابة حذف الملفات
    /// </summary>
    private class DeleteResult
    {
        /// <summary>
        /// عدد الملفات التي تم حذفها
        /// </summary>
        public int DeletedCount { get; set; }
    }


    #endregion

    #region البحث والتصفية

    /// <summary>
    /// دالة البحث للاختيار التلقائي للأقسام
    /// </summary>
    /// <param name="searchText">نص البحث</param>
    /// <param name="token">رمز الإلغاء</param>
    /// <returns>قائمة الأقسام المطابقة لنص البحث</returns>
    /// <remarks>
    /// إذا لم يتم تحميل الأقسام بعد، يتم تحميلها أولاً
    /// إذا كان نص البحث فارغًا، يتم إرجاع جميع الأقسام
    /// وإلا يتم تصفية الأقسام حسب الاسم الذي يحتوي على نص البحث
    /// </remarks>
    private async Task<IEnumerable<CategoryDto>> SearchCategories(string searchText, CancellationToken token)
    {
        // إذا لم يتم تحميل الأقسام بعد، قم بتحميلها
        if (categories.Count == 0)
        {
            await LoadCategories();
        }

        // إذا كان نص البحث فارغًا، أرجع جميع الأقسام
        if (string.IsNullOrEmpty(searchText))
            return categories;

        // تصفية الأقسام حسب الاسم الذي يحتوي على نص البحث
        return categories.Where(c => c.Name.Contains(searchText, StringComparison.InvariantCultureIgnoreCase));
    }

    /// <summary>
    /// دالة البحث للاختيار التلقائي للعملاء
    /// </summary>
    /// <param name="searchText">نص البحث</param>
    /// <param name="token">رمز الإلغاء</param>
    /// <returns>قائمة العملاء المطابقة لنص البحث</returns>
    /// <remarks>
    /// إذا لم يتم تحميل العملاء بعد، يتم تحميلهم أولاً
    /// إذا كان نص البحث فارغًا، يتم إرجاع جميع العملاء
    /// وإلا يتم تصفية العملاء حسب الاسم الذي يحتوي على نص البحث
    /// </remarks>
    private async Task<IEnumerable<ClientDto>> SearchClients(string searchText, CancellationToken token)
    {
        // إذا لم يتم تحميل العملاء بعد، قم بتحميلهم
        if (clients.Count == 0)
        {
            await LoadClients();
        }

        // إذا كان نص البحث فارغًا، أرجع جميع العملاء
        if (string.IsNullOrEmpty(searchText))
            return clients;

        // تصفية العملاء حسب الاسم الذي يحتوي على نص البحث
        return clients.Where(c => c.Name.Contains(searchText, StringComparison.InvariantCultureIgnoreCase));
    }
    #endregion

    #region Helper Methods

    /// <summary>
    /// استخراج اسم الملف من المسار الكامل
    /// </summary>
    /// <param name="filePath">مسار الملف</param>
    /// <returns>اسم الملف فقط</returns>
    private string GetFileName(string filePath)
    {
        if (string.IsNullOrEmpty(filePath))
            return string.Empty;

        try
        {
            // استخدام System.IO.Path.GetFileName
            return System.IO.Path.GetFileName(filePath);
        }
        catch
        {
            // في حالة حدوث خطأ، إرجاع المسار كما هو
            return filePath;
        }
    }

    #endregion

}
