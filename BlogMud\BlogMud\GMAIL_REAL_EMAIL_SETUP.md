# Gmail Real Email Setup Guide for BlogMud

## 🎯 Goal
Configure BlogMud to send real confirmation emails to `<EMAIL>` instead of simulated emails.

## ✅ Prerequisites
- [x] Gmail account: `<EMAIL>`
- [x] Two-factor authentication enabled on Gmail
- [x] BlogMud application running

## 📧 Step 1: Create Gmail App Password

### 1.1 Access Google Account Settings
1. Go to: https://myaccount.google.com/
2. Sign in with `<EMAIL>`

### 1.2 Navigate to Security
1. Click **"Security"** in the left sidebar
2. Scroll to **"Signing in to Google"** section

### 1.3 Generate App Password
1. Click **"App passwords"** (only visible if 2FA is enabled)
2. You may need to re-enter your password
3. Select **"Mail"** from the app dropdown
4. Select **"Other (Custom name)"** from device dropdown
5. Enter: **"BlogMud Email System"**
6. Click **"Generate"**

### 1.4 Copy the Password
- Google shows a 16-character password like: `abcd efgh ijkl mnop`
- **COPY THIS IMMEDIATELY** - you won't see it again!
- Keep it secure - treat it like a password

## 🔧 Step 2: Update BlogMud Configuration

### 2.1 Manual Method
1. Open `BlogMud/BlogMud/appsettings.Development.json`
2. Find the `EmailSettings` section
3. Replace `"your-gmail-app-password-here"` with your App Password
4. Example:
   ```json
   {
     "EmailSettings": {
       "SmtpServer": "smtp.gmail.com",
       "SmtpPort": 587,
       "SenderEmail": "<EMAIL>",
       "SenderName": "BlogMud System",
       "Username": "<EMAIL>",
       "Password": "abcd efgh ijkl mnop",
       "EnableSsl": true
     }
   }
   ```

### 2.2 PowerShell Script Method
Run this command in the BlogMud directory:
```powershell
.\update-gmail-password.ps1 -AppPassword "your-16-char-password"
```

## 🚀 Step 3: Restart Application

1. **Stop** the current application (Ctrl+C in terminal)
2. **Start** with HTTPS profile:
   ```bash
   dotnet run --launch-profile https
   ```
3. Application will run on: https://localhost:7009

## 🧪 Step 4: Test Email Configuration

### 4.1 Check Email Service Status
1. Go to: https://localhost:7009/dev/email-test
2. Verify the status shows:
   - ✅ **Real Email Service Active**
   - ✅ **Password: Configured**
   - ✅ **Service Status: Real Email Service**

### 4.2 Send Test Email
1. On the same page, use the test form
2. Email field should show: `<EMAIL>`
3. Enter your name: `Ahmed Franchy`
4. Click **"Send Test Email"**
5. Check your Gmail inbox for the test email

## 📬 Step 5: Test Full Registration Flow

### 5.1 Register New User
1. Go to: https://localhost:7009/Account/Register
2. Fill the form:
   - **Full Name**: Test User
   - **Email**: `<EMAIL>`
   - **Password**: TestPassword123!
   - **Confirm Password**: TestPassword123!
3. Click **"Register Account"**

### 5.2 Check Gmail Inbox
1. Check your Gmail inbox for confirmation email
2. Subject should be: **"تأكيد البريد الإلكتروني - BlogMud"**
3. Email should be in Arabic with RTL layout
4. Click the confirmation button in the email

### 5.3 Verify Account
1. Confirmation link should redirect to BlogMud
2. You should see success message
3. You can now login with the new account

## 🔍 Troubleshooting

### Issue: Still seeing "Development Email Service"
**Solution**: 
- Verify App Password is correctly set in `appsettings.Development.json`
- Restart the application
- Check https://localhost:7009/dev/email-test

### Issue: "Authentication failed" error
**Solutions**:
1. Verify 2FA is enabled on Gmail
2. Generate a new App Password
3. Ensure no spaces in the App Password
4. Try using the account password temporarily (less secure)

### Issue: Emails not arriving
**Check**:
1. Gmail Spam/Junk folder
2. Application logs for SMTP errors
3. Gmail account security settings
4. Try sending to a different email address

### Issue: SSL/TLS errors
**Solution**:
- Ensure `"EnableSsl": true` in configuration
- Port should be 587 for Gmail SMTP

## 📊 Verification Checklist

- [ ] Gmail App Password created
- [ ] `appsettings.Development.json` updated with real password
- [ ] Application restarted
- [ ] Email test page shows "Real Email Service"
- [ ] Test email sent successfully
- [ ] Test email received in Gmail inbox
- [ ] Registration confirmation email received
- [ ] Email confirmation link works
- [ ] User can login after confirmation

## 🔐 Security Notes

- **Never commit** App Passwords to version control
- **Use environment variables** in production
- **Regenerate** App Password if compromised
- **Disable** App Password when not needed

## 📞 Support

If you encounter issues:
1. Check application logs in terminal
2. Verify Gmail security settings
3. Test with https://localhost:7009/dev/email-test
4. Check https://localhost:7009/dev/emails for sent emails
