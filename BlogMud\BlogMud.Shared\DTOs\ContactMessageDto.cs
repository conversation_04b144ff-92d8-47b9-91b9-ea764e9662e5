using System;

namespace BlogMud.Shared.DTOs
{
    /// <summary>
    /// كائن نقل البيانات لرسائل الاتصال
    /// يستخدم لنقل بيانات رسائل الاتصال بين واجهة المستخدم والخادم
    /// </summary>
    public class ContactMessageDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime SubmissionDate { get; set; }
        public bool IsRead { get; set; }
        public bool IsResponded { get; set; }
    }
}
