using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    /// <summary>
    /// نموذج صفحة "من نحن" لإدارة محتوى الشركة
    /// </summary>
    public class AboutUs
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// شعار الشركة (مطلوب)
        /// </summary>
        [Required(ErrorMessage = "شعار الشركة مطلوب")]
        [StringLength(255, ErrorMessage = "مسار شعار الشركة طويل جداً")]
        public string CompanyLogoUrl { get; set; }

        /// <summary>
        /// نظرة عامة على الشركة / الوصف (مطلوب)
        /// </summary>
        [Required(ErrorMessage = "وصف الشركة مطلوب")]
        [StringLength(2000, ErrorMessage = "يجب أن يكون وصف الشركة أقل من {1} حرفًا")]
        public string CompanyDescription { get; set; }

        /// <summary>
        /// قدرات ومميزات الشركة (مطلوب)
        /// </summary>
        [Required(ErrorMessage = "قدرات الشركة مطلوبة")]
        [StringLength(2000, ErrorMessage = "يجب أن تكون قدرات الشركة أقل من {1} حرفًا")]
        public string CompanyCapabilities { get; set; }

        /// <summary>
        /// قائمة عناصر الطاقة الإنتاجية المرتبطة بهذا المحتوى
        /// </summary>
        public virtual ICollection<ProductionCapacityItem> ProductionCapacityItems { get; set; } = new List<ProductionCapacityItem>();

        /// <summary>
        /// ترتيب عرض المحتوى
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// حالة المحتوى (نشط أو غير نشط)
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ إنشاء المحتوى
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل للمحتوى
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }
    }
}
