<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x86</Platforms>
  </PropertyGroup>

  <!-- إعدادات خاصة لمنصة x86 -->
  <PropertyGroup Condition="'$(Platform)' == 'x86'">
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>

  <!-- إعدادات خاصة لـ win-x86 runtime -->
  <PropertyGroup Condition="'$(RuntimeIdentifier)' == 'win-x86'">
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.16" />
    <PackageReference Include="MudBlazor" Version="8.8.0" />
  </ItemGroup>

</Project>
