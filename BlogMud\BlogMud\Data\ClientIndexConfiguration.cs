using BlogMud.Shared.Models;
using Microsoft.EntityFrameworkCore;

// Alias to avoid namespace conflict
using ClientModel = BlogMud.Shared.Models.Client;

namespace BlogMud.Data
{
    public static class ClientIndexConfiguration
    {
        public static void ConfigureClientIndexes(this ModelBuilder modelBuilder)
        {
            // Add index on CreatedAt for better sorting performance
            modelBuilder.Entity<ClientModel>()
                .HasIndex(c => c.CreatedAt)
                .HasDatabaseName("IX_Clients_CreatedAt");

            // Add index on IsActive for better filtering performance
            modelBuilder.Entity<ClientModel>()
                .HasIndex(c => c.IsActive)
                .HasDatabaseName("IX_Clients_IsActive");

            // Add index on Name for better search performance
            modelBuilder.Entity<ClientModel>()
                .HasIndex(c => c.Name)
                .HasDatabaseName("IX_Clients_Name");

            // Add index on Email for better search performance
            modelBuilder.Entity<ClientModel>()
                .HasIndex(c => c.Email)
                .HasDatabaseName("IX_Clients_Email");
        }
    }
}
