﻿@page "/Account/RegisterConfirmation"

@using System.Text
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using BlogMud.Data

@inject UserManager<ApplicationUser> UserManager
@inject IEmailSender<ApplicationUser> EmailSender
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager

<PageTitle>Register confirmation</PageTitle>

<h1>Register confirmation</h1>

<StatusMessage Message="@statusMessage" />

@if (emailConfirmationLink is not null)
{
    <MudAlert Severity="Severity.Info" Class="mb-4">
        <MudText Typo="Typo.h6" Class="mb-2">تطوير - رابط التأكيد</MudText>
        <MudText Class="mb-2">في بيئة التطوير، يمكنك استخدام الرابط التالي لتأكيد حسابك:</MudText>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="@emailConfirmationLink" Class="mt-2">
            تأكيد الحساب
        </MudButton>
    </MudAlert>

    <MudAlert Severity="Severity.Warning" Class="mb-4">
        <MudText Class="mb-2">يمكنك أيضاً مراجعة جميع رسائل البريد الإلكتروني المرسلة في صفحة اختبار التطوير:</MudText>
        <MudButton Variant="Variant.Outlined" Color="Color.Warning" Href="/dev/emails" Target="_blank" Class="mt-2">
            عرض رسائل التطوير
        </MudButton>
    </MudAlert>
}
else
{
    <MudAlert Severity="Severity.Success" Class="mb-4">
        <MudText Typo="Typo.h6" Class="mb-2">تم إرسال رسالة التأكيد</MudText>
        <MudText>تم إرسال رسالة تأكيد إلى بريدك الإلكتروني. يرجى التحقق من صندوق الوارد والنقر على رابط التأكيد لتفعيل حسابك.</MudText>
    </MudAlert>
}

@code {
    private string? emailConfirmationLink;
    private string? statusMessage;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromQuery]
    private string? Email { get; set; }

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (Email is null)
        {
            RedirectManager.RedirectTo("");
        }

        var user = await UserManager.FindByEmailAsync(Email);
        if (user is null)
        {
            HttpContext.Response.StatusCode = StatusCodes.Status404NotFound;
            statusMessage = "Error finding user for unspecified email";
        }
        else if (EmailSender is IdentityNoOpEmailSender)
        {
            // Once you add a real email sender, you should remove this code that lets you confirm the account
            var userId = await UserManager.GetUserIdAsync(user);
            var code = await UserManager.GenerateEmailConfirmationTokenAsync(user);
            code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
            emailConfirmationLink = NavigationManager.GetUriWithQueryParameters(
                NavigationManager.ToAbsoluteUri("Account/ConfirmEmail").AbsoluteUri,
                new Dictionary<string, object?> { ["userId"] = userId, ["code"] = code, ["returnUrl"] = ReturnUrl });
        }
    }
}
