﻿@page "/admin/newsSiderMove"
@attribute [Authorize(Roles = "Admin")]
@layout BlogMud.Client.Layout.AdminLayout


<PageTitle>إدارة الشرائح المتحركة للأخبار</PageTitle>
<NewsSiderMoveCSS />
<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="news-slider-container">
    <!-- عنوان الصفحة المحسن -->
    <div class="page-header-section" data-aos="fade-down" data-aos-duration="800" data-aos-once="false">
        <MudPaper Class="page-header-card" Elevation="0">
            <div class="header-content" data-aos="fade-right" data-aos-delay="200" data-aos-duration="600">
                <div class="header-icon-wrapper" data-aos="zoom-in" data-aos-delay="400" data-aos-duration="500">
                    <MudIcon Icon="@Icons.Material.Filled.ViewCarousel" Class="header-icon" />
                </div>
                <div class="header-text">
                    <MudText Typo="Typo.h4" Class="header-title" data-aos="fade-up" data-aos-delay="300" data-aos-duration="600">إدارة الشرائح المتحركة للأخبار</MudText>
                    <MudText Typo="Typo.body1" Class="header-subtitle" data-aos="fade-up" data-aos-delay="500" data-aos-duration="600">
                        إدارة عروض الصور المتحركة التي تظهر في صفحة الأخبار بتصميم أنيق ومتجاوب
                    </MudText>
                </div>
            </div>
            <div class="header-stats" data-aos="fade-left" data-aos-delay="600" data-aos-duration="700">
                <div class="stat-item" data-aos="flip-left" data-aos-delay="700" data-aos-duration="500">
                    <MudIcon Icon="@Icons.Material.Filled.Slideshow" Class="stat-icon" />
                    <div class="stat-content">
                        <MudText Typo="Typo.h6" Class="stat-number">@_newsSiderMoves.Count</MudText>
                        <MudText Typo="Typo.caption" Class="stat-label">إجمالي الشرائح</MudText>
                    </div>
                </div>
                <div class="stat-item" data-aos="flip-right" data-aos-delay="900" data-aos-duration="500">
                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="stat-icon active" />
                    <div class="stat-content">
                        <MudText Typo="Typo.h6" Class="stat-number">@_newsSiderMoves.Count(s => s.IsActive)</MudText>
                        <MudText Typo="Typo.caption" Class="stat-label">الشرائح النشطة</MudText>
                    </div>
                </div>
            </div>
        </MudPaper>
    </div>

    <!-- معاينة الشرائح المتحركة المحسنة -->
    @if (_newsSiderMoves.Any())
    {
        <div class="preview-section" data-aos="fade-up" data-aos-delay="300" data-aos-duration="800" data-aos-once="false">
            <MudPaper Class="preview-card" Elevation="0">
                <div class="preview-header" data-aos="slide-down" data-aos-delay="400" data-aos-duration="600">
                    <div class="preview-title-wrapper" data-aos="fade-right" data-aos-delay="500" data-aos-duration="500">
                        <MudIcon Icon="@Icons.Material.Filled.Preview" Class="preview-icon" />
                        <MudText Typo="Typo.h6" Class="preview-title">معاينة الشرائح المتحركة</MudText>
                    </div>
                    <div class="carousel-controls" data-aos="fade-left" data-aos-delay="600" data-aos-duration="500">
                        <MudIconButton Icon="@(autocycle? Icons.Material.Filled.Pause : Icons.Material.Filled.PlayArrow)"
                                       Color="Color.Primary"
                                       Size="Size.Small"
                                       OnClick="() => autocycle = !autocycle"
                                       Title="@(autocycle ? "إيقاف التشغيل التلقائي" : "تشغيل تلقائي")" />
                        <MudIconButton Icon="@(arrows? Icons.Material.Filled.VisibilityOff : Icons.Material.Filled.Visibility)"
                                       Color="Color.Secondary"
                                       Size="Size.Small"
                                       OnClick="() => arrows = !arrows"
                                       Title="@(arrows ? "إخفاء الأسهم" : "إظهار الأسهم")" />
                    </div>
                </div>

                @if (_newsSiderMoves.Any(s => s.IsActive))
                {
                    <div class="carousel-container" data-aos="zoom-in" data-aos-delay="700" data-aos-duration="800">
                        <MudCarousel Class="enhanced-carousel"
                                     ShowArrows="@arrows"
                                     ShowBullets="@bullets"
                                     EnableSwipeGesture="@enableSwipeGesture"
                                     AutoCycle="@autocycle"
                                     TData="object"
                                     AutoCycleTime="TimeSpan.FromSeconds(4)"
                                     BulletsColor="Color.Primary">
                            @foreach (var slideshow in _newsSiderMoves.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder))
                            {
                                @if (slideshow.Images.Any())
                                {
                                    @foreach (var imageUrl in slideshow.Images)
                                    {
                                        <MudCarouselItem Transition="transition">
                                            <div class="carousel-item">
                                                <div class="slide-content">
                                                    <div class="slide-image-wrapper">
                                                        <img src="@imageUrl"
                                                             alt="@slideshow.Title"
                                                             class="slide-image"
                                                             @onclick="() => ShowImageModal(imageUrl, slideshow.Title)"
                                                             @onclick:stopPropagation="true"
                                                             title="انقر لعرض الصورة بحجم كامل" />
                                                        <div class="slide-overlay"></div>
                                                    </div>
                                                    <div class="slide-info">
                                                        <MudText Typo="Typo.h6" Class="slide-title">@slideshow.Title</MudText>
                                                        @if (!string.IsNullOrEmpty(slideshow.Description))
                                                        {
                                                            <MudText Typo="Typo.body2" Class="slide-description">@slideshow.Description</MudText>
                                                        }
                                                        @if (!string.IsNullOrEmpty(slideshow.LinkUrl) && !string.IsNullOrEmpty(slideshow.LinkText))
                                                        {
                                                            <MudButton Href="@slideshow.LinkUrl"
                                                                       Target="_blank"
                                                                       Color="Color.Primary"
                                                                       Variant="Variant.Filled"
                                                                       Size="Size.Small"
                                                                       StartIcon="@Icons.Material.Filled.OpenInNew"
                                                                       Class="slide-link-btn">
                                                                @slideshow.LinkText
                                                            </MudButton>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        </MudCarouselItem>
                                    }
                                }
                            }
                        </MudCarousel>
                    </div>
                }
                else
                {
                    <div class="no-active-slides" data-aos="fade-in" data-aos-delay="500" data-aos-duration="600">
                        <MudIcon Icon="@Icons.Material.Filled.Image" Class="no-slides-icon" data-aos="bounce" data-aos-delay="700" data-aos-duration="800" />
                        <MudText Typo="Typo.h6" Class="no-slides-title" data-aos="fade-up" data-aos-delay="800" data-aos-duration="500">لا توجد شرائح نشطة</MudText>
                        <MudText Typo="Typo.body2" Class="no-slides-subtitle" data-aos="fade-up" data-aos-delay="900" data-aos-duration="500">قم بتفعيل بعض الشرائح لرؤية المعاينة</MudText>
                    </div>
                }
            </MudPaper>
        </div>
    }

    <!-- أدوات التحكم المحسنة -->
    <div class="controls-section" data-aos="slide-up" data-aos-delay="400" data-aos-duration="700" data-aos-once="false">
        <MudPaper Class="controls-card" Elevation="0">
            <div class="controls-container">
                <div class="controls-left" data-aos="slide-right" data-aos-delay="500" data-aos-duration="600">
                    <div class="action-buttons">
                        <MudButton Color="Color.Primary"
                                   StartIcon="@Icons.Material.Filled.Add"
                                   OnClick="OpenCreateDialog"
                                   Variant="Variant.Filled"
                                   Size="Size.Large"
                                   Class="primary-action-btn"
                                   data-aos="zoom-in"
                                   data-aos-delay="600"
                                   data-aos-duration="500">
                            <span class="button-text-full">إضافة شريحة متحركة جديدة</span>
                            <span class="button-text-short">إضافة</span>
                        </MudButton>

                        <MudButton Color="Color.Secondary"
                                   StartIcon="@Icons.Material.Filled.Refresh"
                                   OnClick="LoadNewsSiderMoves"
                                   Variant="Variant.Outlined"
                                   Size="Size.Large"
                                   Class="secondary-action-btn"
                                   Loading="_loading"
                                   data-aos="zoom-in"
                                   data-aos-delay="700"
                                   data-aos-duration="500">
                            تحديث
                        </MudButton>
                    </div>
                </div>

                <div class="controls-right" data-aos="slide-left" data-aos-delay="600" data-aos-duration="600">
                    <!-- شريط البحث المحسن -->
                    <div class="search-wrapper" data-aos="fade-in" data-aos-delay="800" data-aos-duration="500">
                        <MudTextField @bind-Value="_searchString"
                                      Placeholder="البحث في الشرائح المتحركة..."
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Search"
                                      IconSize="Size.Medium"
                                      Variant="Variant.Outlined"
                                      Class="enhanced-search-field"
                                      Immediate="true"
                                      DebounceInterval="300">
                        </MudTextField>
                        @if (!string.IsNullOrEmpty(_searchString))
                        {
                            <MudIconButton Icon="@Icons.Material.Filled.Clear"
                                           Color="Color.Default"
                                           Size="Size.Small"
                                           OnClick="() => _searchString = string.Empty"
                                           Class="search-clear-btn"
                                           Title="مسح البحث" />
                        }
                    </div>
                </div>
            </div>
        </MudPaper>
    </div>
    @if (_loading)
    {
        <div class="loading-section" data-aos="fade-in" data-aos-duration="600">
            <MudPaper Class="loading-card" Elevation="0">
                <div class="loading-content">
                    <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" data-aos="zoom-in" data-aos-delay="200" data-aos-duration="500" />
                    <MudText Typo="Typo.h6" Class="loading-text" data-aos="fade-up" data-aos-delay="400" data-aos-duration="500">جاري تحميل الشرائح المتحركة...</MudText>
                    <MudText Typo="Typo.body2" Class="loading-subtitle" data-aos="fade-up" data-aos-delay="600" data-aos-duration="500">يرجى الانتظار</MudText>
                </div>
            </MudPaper>
        </div>
    }
    else if (_newsSiderMoves.Count == 0)
    {
        <div class="empty-state-section" data-aos="fade-up" data-aos-duration="800" data-aos-once="false">
            <MudPaper Class="empty-state-card" Elevation="0">
                <div class="empty-state-content">
                    <div class="empty-state-icon-wrapper" data-aos="bounce" data-aos-delay="300" data-aos-duration="800">
                        <MudIcon Icon="@Icons.Material.Filled.ViewCarousel" Class="empty-state-icon" />
                    </div>
                    <MudText Typo="Typo.h5" Class="empty-state-title" data-aos="fade-up" data-aos-delay="500" data-aos-duration="600">لا توجد شرائح متحركة</MudText>
                    <MudText Typo="Typo.body1" Class="empty-state-subtitle" data-aos="fade-up" data-aos-delay="700" data-aos-duration="600">
                        ابدأ بإضافة شرائح متحركة جديدة لعرضها في صفحة الأخبار
                    </MudText>
                    <MudButton Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Add"
                               OnClick="OpenCreateDialog"
                               Variant="Variant.Filled"
                               Size="Size.Large"
                               Class="empty-state-action-btn"
                               data-aos="zoom-in"
                               data-aos-delay="900"
                               data-aos-duration="500">
                        إضافة أول شريحة متحركة
                    </MudButton>
                </div>
            </MudPaper>
        </div>
    }
    else
    {
        <!-- الجدول العادي للشاشات الكبيرة -->
        <div class="regular-table" data-aos="fade-up" data-aos-delay="500" data-aos-duration="800" data-aos-once="false">
            <MudPaper Class="pa-4">
                <MudTable Items="@_newsSiderMoves" Loading="@_loading" Filter="FilterFunc" Dense="true" Hover="true" Bordered="true">
                    <HeaderContent>
                        <MudTh>ت</MudTh>
                        <MudTh>العنوان</MudTh>
                        @* <MudTh>الوصف</MudTh> *@
                        <MudTh>عدد الصور</MudTh>
                        <MudTh>الترتيب</MudTh>
                        <MudTh>المدة (ثانية)</MudTh>
                        <MudTh>نشط</MudTh>
                        <MudTh>تاريخ الإنشاء</MudTh>
                        <MudTh>الإجراءات</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="ت">@(_newsSiderMoves.IndexOf(context) + 1)</MudTd>
                        <MudTd>@(context.Title?.Length > 5 ? context.Title.Substring(0, 30) + "..." : context.Title)</MudTd>
                        @* <MudTd>@(context.Description?.Length > 30 ? context.Description.Substring(0, 30) + "..." : context.Description)</MudTd> *@
                        <MudTd>
                            <MudChip T="string" Color="Color.Primary" Size="Size.Small">
                                @context.Images.Count صورة
                            </MudChip>
                        </MudTd>
                        <MudTd>@context.DisplayOrder</MudTd>
                        <MudTd>@context.Duration</MudTd>
                        <MudTd>
                            <MudChip T="string" Color="@(context.IsActive? Color.Success: Color.Default)" Size="Size.Small">
                                @(context.IsActive ? "نشط" : "غير نشط")
                            </MudChip>
                        </MudTd>
                        <MudTd>@context.CreatedAt.ToString("yyyy/MM/dd")</MudTd>
                        <MudTd>
                            <div class="d-flex gap-1">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                               Color="Color.Primary"
                                               Size="Size.Small"
                                               OnClick="@(() => OpenEditDialog(context))"
                                               Title="تعديل" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                               Color="Color.Error"
                                               Size="Size.Small"
                                               OnClick="@(() => DeleteNewsSiderMove(context.Id))"
                                               Title="حذف" />
                            </div>
                        </MudTd>
                    </RowTemplate>
                    <PagerContent>
                        <MudTablePager PageSizeOptions="new int[] { 10, 25, 50, 100 }" />
                    </PagerContent>
                </MudTable>
            </MudPaper>
        </div>

        <!-- تخطيط البطاقات المحسن للشاشات الصغيرة -->
        <div class="mobile-cards" data-aos="fade-up" data-aos-delay="600" data-aos-duration="800" data-aos-once="false">
            @foreach (var newsSiderMove in _newsSiderMoves.Where(FilterFunc))
            {
                var cardIndex = _newsSiderMoves.Where(FilterFunc).ToList().IndexOf(newsSiderMove);
                var currentDelay = cardIndex * 100;
                <MudCard Class="mobile-card" Elevation="1" data-aos="flip-up" data-aos-delay="@(700 + currentDelay)" data-aos-duration="600">
                    <MudCardContent Class="compact-content">
                        <!-- رقم الشريحة والإجراءات -->
                        <div class="gif-header">
                            <MudChip T="string" Color="Color.Primary" Size="Size.Small" Class="gif-number">
                                @(_newsSiderMoves.IndexOf(newsSiderMove) + 1)
                            </MudChip>
                            <div class="gif-actions">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                               Color="Color.Primary"
                                               Size="Size.Small"
                                               OnClick="@(() => OpenEditDialog(newsSiderMove))"
                                               Title="تعديل" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                               Color="Color.Error"
                                               Size="Size.Small"
                                               OnClick="@(() => DeleteNewsSiderMove(newsSiderMove.Id))"
                                               Title="حذف" />
                            </div>
                        </div>

                        <!-- عنوان الشريحة -->
                        <div class="gif-title">
                            @(newsSiderMove.Title ?? "غير محدد")
                        </div>
                        <!-- معلومات الشريحة -->
                        @if (!string.IsNullOrEmpty(newsSiderMove.Description))
                        {
                            <div class="gif-info">
                                <MudIcon Icon="@Icons.Material.Filled.Description" Size="Size.Small" Class="info-icon" />
                                <span class="info-label">الوصف:</span>
                                <span>@(newsSiderMove.Description.Length > 30 ? newsSiderMove.Description.Substring(0, 30) + "..." : newsSiderMove.Description)</span>
                            </div>
                        }
                        <div class="gif-info">
                            <MudIcon Icon="@Icons.Material.Filled.Image" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">عدد الصور:</span>
                            <span>@newsSiderMove.Images.Count</span>
                        </div>
                        <div class="gif-info">
                            <MudIcon Icon="@Icons.Material.Filled.Sort" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">الترتيب:</span>
                            <span>@newsSiderMove.DisplayOrder</span>
                        </div>
                        <div class="gif-info">
                            <MudIcon Icon="@Icons.Material.Filled.Timer" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">المدة:</span>
                            <span>@newsSiderMove.Duration ثانية</span>
                        </div>
                        <div class="gif-info">
                            <MudIcon Icon="@Icons.Material.Filled.DateRange" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">تاريخ الإنشاء:</span>
                            <span>@newsSiderMove.CreatedAt.ToString("yyyy/MM/dd")</span>
                        </div>

                        <!-- حالة الشريحة -->
                        <div class="gif-info">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">الحالة:</span>
                            @if (newsSiderMove.IsActive)
                            {
                                <MudChip T="string" Color="Color.Success" Size="Size.Small" Class="status-chip">نشط</MudChip>
                            }
                            else
                            {
                                <MudChip T="string" Color="Color.Error" Size="Size.Small" Class="status-chip">غير نشط</MudChip>
                            }
                        </div>


                    </MudCardContent>
                </MudCard>
            }
        </div>
    }
</MudContainer>

<!-- مودال عرض الصورة بحجم كامل -->
@if (_showImageModal)
{
    <div class="image-modal-overlay" @onclick="HideImageModal" @onkeydown="@(async (e) => { if (e.Key == "Escape") HideImageModal(); })" tabindex="0" data-aos="fade-in" data-aos-duration="300">
        <button class="image-modal-close" @onclick="HideImageModal" @onclick:stopPropagation="true" title="إغلاق" data-aos="zoom-in" data-aos-delay="200" data-aos-duration="300">
            <MudIcon Icon="@Icons.Material.Filled.Close" />
        </button>
        <div class="image-modal-content-wrapper" @onclick:stopPropagation="true" data-aos="zoom-in" data-aos-delay="100" data-aos-duration="400">
            <img src="@_modalImageUrl"
                 alt="@_modalImageTitle"
                 class="image-modal-content" />
            <div class="image-modal-title" data-aos="fade-up" data-aos-delay="300" data-aos-duration="400">@_modalImageTitle</div>
        </div>
    </div>
}
