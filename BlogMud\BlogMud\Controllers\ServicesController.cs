using AutoMapper;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;
using BlogMud.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlogMud.Controllers
{
    /// <summary>
    /// وحدة التحكم بالخدمات - تدير عمليات إنشاء وقراءة وتحديث وحذف الخدمات
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ServicesController : ControllerBase
    {
        #region المتغيرات والمنشئ

        /// <summary>
        /// وحدة العمل المسؤولة عن إدارة المستودعات وحفظ التغييرات
        /// </summary>
        private readonly IUnitOfWork _unitOfWork;

        /// <summary>
        /// مستودع الخدمات المسؤول عن عمليات قراءة وكتابة بيانات الخدمات
        /// </summary>
        private readonly IRepository<Service> _serviceRepository;

        /// <summary>
        /// مسجل الأحداث لتسجيل معلومات التنفيذ والأخطاء
        /// </summary>
        private readonly ILogger<ServicesController> _logger;

        /// <summary>
        /// أداة التحويل بين كائنات النموذج وكائنات نقل البيانات
        /// </summary>
        private readonly IMapper _mapper;

        /// <summary>
        /// خدمة إدارة الملفات لحذف ورفع الملفات
        /// </summary>
        private readonly IFileService _fileService;

        /// <summary>
        /// منشئ وحدة التحكم بالخدمات
        /// </summary>
        /// <param name="unitOfWork">وحدة العمل</param>
        /// <param name="logger">مسجل الأحداث</param>
        /// <param name="mapper">أداة التحويل</param>
        /// <param name="fileService">خدمة إدارة الملفات</param>
        public ServicesController(IUnitOfWork unitOfWork, ILogger<ServicesController> logger, IMapper mapper, IFileService fileService)
        {
            _unitOfWork = unitOfWork;
            _serviceRepository = _unitOfWork.Repository<Service>();
            _logger = logger;
            _mapper = mapper;
            _fileService = fileService;
        }

        #endregion

        #region عمليات CRUD

        /// <summary>
        /// استرجاع جميع الخدمات
        /// </summary>
        /// <returns>قائمة بجميع الخدمات</returns>
        /// <response code="200">تم استرجاع الخدمات بنجاح</response>
        /// <response code="500">حدث خطأ أثناء استرجاع الخدمات</response>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ServiceDto>>> GetServices()
        {
            try
            {
                _logger.LogInformation("Fetching all Services");

                // استرجاع جميع الخدمات مرتبة حسب ترتيب العرض
                var services = await _serviceRepository.GetAllAsync(
                    orderBy: q => q.OrderBy(s => s.DisplayOrder).ThenBy(s => s.Title)
                );

                // تحويل كائنات النموذج إلى كائنات نقل البيانات
                var serviceDtos = _mapper.Map<IEnumerable<ServiceDto>>(services);

                _logger.LogInformation("Retrieved {Count} services", serviceDtos.Count());
                return Ok(serviceDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving services");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// استرجاع خدمة محددة بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف الخدمة</param>
        /// <returns>بيانات الخدمة المطلوبة</returns>
        /// <response code="200">تم استرجاع الخدمة بنجاح</response>
        /// <response code="404">الخدمة غير موجودة</response>
        /// <response code="500">حدث خطأ أثناء استرجاع الخدمة</response>
        [HttpGet("{id}")]
        public async Task<ActionResult<ServiceDto>> GetService(int id)
        {
            try
            {
                _logger.LogInformation("Fetching service with ID: {ServiceId}", id);

                var service = await _serviceRepository.GetByIdAsync(id);

                if (service == null)
                {
                    _logger.LogWarning("Service not found with ID: {ServiceId}", id);
                    return NotFound();
                }

                // تحويل كائن النموذج إلى كائن نقل البيانات
                var serviceDto = _mapper.Map<ServiceDto>(service);
                return Ok(serviceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving service with ID: {ServiceId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// استرجاع الخدمات النشطة فقط
        /// </summary>
        /// <returns>قائمة بالخدمات النشطة</returns>
        /// <response code="200">تم استرجاع الخدمات النشطة بنجاح</response>
        /// <response code="500">حدث خطأ أثناء استرجاع الخدمات النشطة</response>
        [HttpGet("active")]
        public async Task<ActionResult<IEnumerable<ServiceDto>>> GetActiveServices()
        {
            try
            {
                _logger.LogInformation("Fetching active services");

                // استرجاع الخدمات النشطة فقط مرتبة حسب ترتيب العرض
                var services = await _serviceRepository.GetAllAsync(
                    filter: s => s.IsActive,
                    orderBy: q => q.OrderBy(s => s.DisplayOrder).ThenBy(s => s.Title)
                );

                // تحويل كائنات النموذج إلى كائنات نقل البيانات
                var serviceDtos = _mapper.Map<IEnumerable<ServiceDto>>(services);

                _logger.LogInformation("Retrieved {Count} active services", serviceDtos.Count());
                return Ok(serviceDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active services");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث خدمة موجودة
        /// </summary>
        /// <param name="id">معرف الخدمة المراد تحديثها</param>
        /// <param name="serviceDto">بيانات الخدمة المحدثة</param>
        /// <returns>استجابة بدون محتوى في حالة نجاح التحديث</returns>
        /// <response code="204">تم تحديث الخدمة بنجاح</response>
        /// <response code="400">بيانات الخدمة غير صالحة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="404">الخدمة غير موجودة</response>
        /// <response code="500">حدث خطأ أثناء تحديث الخدمة</response>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> PutService(int id, ServiceDto serviceDto)
        {
            // التحقق من تطابق المعرف في المسار مع المعرف في البيانات
            if (id != serviceDto.Id)
            {
                return BadRequest("Service ID mismatch");
            }

            try
            {
                _logger.LogInformation("Updating service with ID: {ServiceId}", id);

                // التحقق أولاً من وجود الخدمة
                var existingService = await _serviceRepository.GetByIdAsync(id);
                if (existingService == null)
                {
                    _logger.LogWarning("Service not found with ID: {ServiceId}", id);
                    return NotFound();
                }

                // تعيين قيم افتراضية للحقول المطلوبة إذا كانت مفقودة
                if (string.IsNullOrEmpty(serviceDto.MainImageUrl))
                {
                    serviceDto.MainImageUrl = existingService.MainImageUrl ?? "/images/default-service.jpg";
                }

                // حذف الملفات القديمة قبل التحديث
                DeleteOldFilesOnUpdate(existingService, serviceDto);

                // تحديث الخدمة الموجودة بالقيم من كائن نقل البيانات
                _mapper.Map(serviceDto, existingService);
                existingService.LastModifiedAt = DateTime.Now;

                _serviceRepository.Update(existingService);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Service updated successfully with ID: {ServiceId}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating service with ID: {ServiceId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء خدمة جديدة
        /// </summary>
        /// <param name="serviceDto">بيانات الخدمة الجديدة</param>
        /// <returns>الخدمة التي تم إنشاؤها مع معرفها الجديد</returns>
        /// <response code="201">تم إنشاء الخدمة بنجاح</response>
        /// <response code="400">بيانات الخدمة غير صالحة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="500">حدث خطأ أثناء إنشاء الخدمة</response>
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ServiceDto>> PostService(ServiceDto serviceDto)
        {
            try
            {
                _logger.LogInformation("Creating new service: {ServiceTitle}", serviceDto.Title);

                // تعيين قيم افتراضية للحقول المطلوبة إذا كانت مفقودة
                if (string.IsNullOrEmpty(serviceDto.MainImageUrl))
                {
                    serviceDto.MainImageUrl = "/images/default-service.jpg";
                }

                // تحويل كائن نقل البيانات إلى كائن نموذج باستخدام AutoMapper
                var service = _mapper.Map<Service>(serviceDto);
                service.CreatedAt = DateTime.Now;

                await _serviceRepository.AddAsync(service);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Service created successfully with ID: {ServiceId}", service.Id);

                // تحويل الخدمة المنشأة مرة أخرى إلى كائن نقل بيانات باستخدام AutoMapper
                var createdServiceDto = _mapper.Map<ServiceDto>(service);

                return CreatedAtAction("GetService", new { id = createdServiceDto.Id }, createdServiceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating service: {ServiceTitle}", serviceDto.Title);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف خدمة بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف الخدمة المراد حذفها</param>
        /// <returns>استجابة بدون محتوى في حالة نجاح الحذف</returns>
        /// <response code="204">تم حذف الخدمة بنجاح</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="404">الخدمة غير موجودة</response>
        /// <response code="500">حدث خطأ أثناء حذف الخدمة</response>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteService(int id)
        {
            try
            {
                _logger.LogInformation("Deleting service with ID: {ServiceId}", id);

                // التحقق من وجود الخدمة
                var service = await _serviceRepository.GetByIdAsync(id);
                if (service == null)
                {
                    _logger.LogWarning("Service not found with ID: {ServiceId}", id);
                    return NotFound();
                }

                // حذف جميع الملفات المرتبطة بالخدمة قبل حذفها من قاعدة البيانات
                DeleteServiceFiles(service);

                // حذف الخدمة من قاعدة البيانات
                _serviceRepository.Remove(service);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Service and associated files deleted successfully with ID: {ServiceId}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting service with ID: {ServiceId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #endregion

        #region دوال مساعدة لإدارة الملفات

        /// <summary>
        /// حذف جميع الملفات المرتبطة بخدمة معينة
        /// </summary>
        /// <param name="service">الخدمة المراد حذف ملفاتها</param>
        private void DeleteServiceFiles(Service service)
        {
            try
            {
                _logger.LogInformation("Deleting files for service ID: {ServiceId}", service.Id);

                // حذف الصورة الرئيسية
                if (!string.IsNullOrEmpty(service.MainImageUrl))
                {
                    _fileService.DeleteFileIfExists(service.MainImageUrl, "OurServImg/img");
                    _logger.LogInformation("Deleted main image: {MainImageUrl}", service.MainImageUrl);
                }

                // حذف صور الدوار
                if (!string.IsNullOrEmpty(service.ImageCarousel))
                {
                    var carouselImages = service.ImageCarousel.Split(';', StringSplitOptions.RemoveEmptyEntries);
                    foreach (var imageUrl in carouselImages)
                    {
                        _fileService.DeleteFileIfExists(imageUrl.Trim(), "OurServImg/Sider");
                        _logger.LogInformation("Deleted carousel image: {ImageUrl}", imageUrl.Trim());
                    }
                }

                // حذف الفيديو
                if (!string.IsNullOrEmpty(service.VideoUrl))
                {
                    _fileService.DeleteFileIfExists(service.VideoUrl, "OurServImg/viedo");
                    _logger.LogInformation("Deleted video: {VideoUrl}", service.VideoUrl);
                }

                _logger.LogInformation("Successfully deleted all files for service ID: {ServiceId}", service.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting files for service ID: {ServiceId}", service.Id);
            }
        }

        /// <summary>
        /// حذف الملفات القديمة عند تحديث الخدمة
        /// </summary>
        /// <param name="oldService">الخدمة القديمة</param>
        /// <param name="newService">الخدمة الجديدة</param>
        private void DeleteOldFilesOnUpdate(Service oldService, ServiceDto newService)
        {
            try
            {
                _logger.LogInformation("Checking for old files to delete for service ID: {ServiceId}", oldService.Id);

                // حذف الصورة الرئيسية القديمة إذا تم تغييرها
                if (!string.IsNullOrEmpty(oldService.MainImageUrl) &&
                    oldService.MainImageUrl != newService.MainImageUrl)
                {
                    _fileService.DeleteFileIfExists(oldService.MainImageUrl, "OurServImg/img");
                    _logger.LogInformation("Deleted old main image: {OldMainImageUrl}", oldService.MainImageUrl);
                }

                // حذف صور الدوار القديمة إذا تم تغييرها
                if (!string.IsNullOrEmpty(oldService.ImageCarousel) &&
                    oldService.ImageCarousel != newService.ImageCarousel)
                {
                    var oldCarouselImages = oldService.ImageCarousel.Split(';', StringSplitOptions.RemoveEmptyEntries);
                    var newCarouselImages = string.IsNullOrEmpty(newService.ImageCarousel)
                        ? new string[0]
                        : newService.ImageCarousel.Split(';', StringSplitOptions.RemoveEmptyEntries);

                    // حذف الصور القديمة التي لم تعد موجودة في القائمة الجديدة
                    foreach (var oldImageUrl in oldCarouselImages)
                    {
                        var trimmedOldUrl = oldImageUrl.Trim();
                        if (!newCarouselImages.Any(newUrl => newUrl.Trim() == trimmedOldUrl))
                        {
                            _fileService.DeleteFileIfExists(trimmedOldUrl, "OurServImg/Sider");
                            _logger.LogInformation("Deleted old carousel image: {OldImageUrl}", trimmedOldUrl);
                        }
                    }
                }

                // حذف الفيديو القديم إذا تم تغييره
                if (!string.IsNullOrEmpty(oldService.VideoUrl) &&
                    oldService.VideoUrl != newService.VideoUrl)
                {
                    _fileService.DeleteFileIfExists(oldService.VideoUrl, "OurServImg/viedo");
                    _logger.LogInformation("Deleted old video: {OldVideoUrl}", oldService.VideoUrl);
                }

                _logger.LogInformation("Successfully processed old files for service ID: {ServiceId}", oldService.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting old files for service ID: {ServiceId}", oldService.Id);
            }
        }

        #endregion
    }
}