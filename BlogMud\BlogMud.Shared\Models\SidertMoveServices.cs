using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    /// <summary>
    /// نموذج الشرائح المتحركة لإدارة عروض الصور المتعددة
    /// نظام منفصل عن إدارة الخدمات - يدعم فقط الصور المتعددة للعرض
    /// </summary>
    public class SidertMoveServices
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// عنوان الشريحة أو العرض
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// وصف الشريحة أو العرض
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// روابط الصور المتعددة مفصولة بفاصلة منقوطة (;)
        /// مسار الحفظ: wwwroot/OurServImg/SiderMoveOurServicesImg/
        /// </summary>
        [Required]
        [StringLength(2000)]
        public string ImageUrls { get; set; } = string.Empty;

        /// <summary>
        /// مدة العرض بالثواني لكل صورة في الشريحة
        /// </summary>
        public int Duration { get; set; } = 5;

        /// <summary>
        /// ترتيب ظهور الشريحة في العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// حالة الشريحة (نشطة أو غير نشطة)
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ إنشاء الشريحة
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل للشريحة
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }

        /// <summary>
        /// رابط إضافي للانتقال عند النقر على الشريحة (اختياري)
        /// </summary>
        [StringLength(255)]
        public string? LinkUrl { get; set; }

        /// <summary>
        /// نص الرابط أو الزر
        /// </summary>
        [StringLength(50)]
        public string? LinkText { get; set; }

        /// <summary>
        /// ملاحظات إضافية للشريحة
        /// </summary>
        [StringLength(1000)]
        public string? Notes { get; set; }
    }
}
