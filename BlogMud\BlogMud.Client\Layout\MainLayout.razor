@inherits LayoutComponentBase
@using BlogMud.Shared.Enums
@using BlogMud.Shared.Models
@using System.Net.Http
@using System.Net.Http.J<PERSON>
@inject NavigationManager Navigation
@inject IServiceProvider ServiceProvider
<MudRTLProvider RightToLeft="true">
    <MudThemeProvider Theme="_theme" />
    <MudPopoverProvider />
    <MudDialogProvider />
    <MudSnackbarProvider />

    <MudLayout>


        <MudAppBar Elevation="1" Color="Color.Primary" Fixed="true" Dense="true">
            <MudContainer MaxWidth="MaxWidth.Large" Class="d-flex align-center justify-space-between py-1 px-4">
                <div class="d-flex align-center">
                    <MudLink Href="/" Underline="Underline.None">
                        <MudImage Src="@(_companyInfo?.LogoUrl ?? "/imeg/c61a6b2a-796c-4475-aef1-3efa083f38c4.jpg")"
                                  Alt="@(_companyInfo?.Name ?? "شركتنا")" Width="120" Height="40" ObjectFit="ObjectFit.Contain" />
                    </MudLink>
                </div>

                @* <MudSwitch @bind-Value="IsRTLon" ThumbIcon="@Icons.Custom.Brands.MudBlazor" ThumbIconColor="Color.Info">RTL</MudSwitch> *@

                <MudHidden Breakpoint="Breakpoint.SmAndDown" Invert="true">
                    <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start"
                                   OnClick="@((e) => DrawerToggle())" />
                </MudHidden>
                <MudHidden Breakpoint="Breakpoint.SmAndDown">
                    <div class="d-flex align-center">
                        <NavMenu Mode="NavMenuMode.Horizontal" />
                        <AuthorizeView>
                            <Authorized>
                                <MudMenu Icon="@Icons.Material.Filled.Person" Color="Color.Inherit"
                                         direction="Direction.Bottom" offsetY="true" Dense="true">
                                    <MudMenuItem OnClick="@(() => NavigateTo("/account/profile"))">
                                        الملف الشخصي
                                    </MudMenuItem>
                                    <MudMenuItem OnClick="@(() => NavigateTo("/admin/dashboard", true))">
                                        لوحة التحكم
                                    </MudMenuItem>
                                    <MudDivider />
                                    <MudMenuItem OnClick="@(() => NavigateTo("/Account/Logout", true))">
                                        تسجيل خروج
                                    </MudMenuItem>
                                </MudMenu>
                            </Authorized>
                            <NotAuthorized>
                                <MudButton Href="/Account/Login" Variant="Variant.Text" Color="Color.Inherit">
                                    تسجيل الدخول
                                </MudButton>
                            </NotAuthorized>
                        </AuthorizeView>
                    </div>
                </MudHidden>
            </MudContainer>
        </MudAppBar>

        <MudDrawer @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2" Variant="DrawerVariant.Temporary"
                   Anchor="Anchor.End">
            <MudDrawerHeader Class="d-flex align-center justify-center">
                <MudImage Src="@(_companyInfo?.LogoUrl ?? "/imeg/c61a6b2a-796c-4475-aef1-3efa083f38c4.jpg")" Alt="@(_companyInfo?.Name ?? "شركتنا")"
                          Width="120" Height="40" ObjectFit="ObjectFit.Contain" />
            </MudDrawerHeader>
            <NavMenu Mode="NavMenuMode.Vertical" />
            <MudDivider />
            <AuthorizeView>
                <Authorized>
                    <MudNavMenu>
                        <MudNavLink OnClick="@(() => NavigateTo("/account/profile"))" Icon="@Icons.Material.Filled.Person">
                            الملف الشخصي
                        </MudNavLink>
                        <MudNavLink OnClick="@(() => NavigateTo("/admin", true))" Icon="@Icons.Material.Filled.Dashboard">
                            لوحة التحكم
                        </MudNavLink>
                        <MudNavLink OnClick="@(() => NavigateTo("/Account/Logout", true))"
                                    Icon="@Icons.Material.Filled.Logout">
                            تسجيل خروج
                        </MudNavLink>
                    </MudNavMenu>
                </Authorized>
                <NotAuthorized>
                    <MudNavMenu>
                        <MudNavLink Href="/Account/Login" Icon="@Icons.Material.Filled.Login">
                            تسجيل الدخول
                        </MudNavLink>
                        <MudNavLink Href="/Account/Register" Icon="@Icons.Material.Filled.PersonAdd">
                            تسجيل جديد
                        </MudNavLink>
                    </MudNavMenu>
                </NotAuthorized>
            </AuthorizeView>
        </MudDrawer>

        <MudMainContent Class="pt-16">
            @Body

            <MudContainer MaxWidth="MaxWidth.Large" Class="mt-8">
                <MudDivider />
                <MudGrid Class="pa-4" dir="rtl">
                    <MudItem xs="12" sm="4" Class="text-right">
                        <MudText Typo="Typo.h6" Class="mb-3 font-weight-bold" Color="Color.Primary">@(_companyInfo?.Name ?? "شركتنا")</MudText>
                        <MudStack Spacing="2" Class="mt-3">
                            @if (!string.IsNullOrEmpty(_companyInfo?.Address))
                            {
                                <div class="d-flex align-center gap-2">
                                    <MudIcon Icon="@Icons.Material.Filled.LocationOn" Size="Size.Small" Color="Color.Secondary" />
                                    <MudText Typo="Typo.body2">@_companyInfo.Address</MudText>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(_companyInfo?.Phone))
                            {
                                <div class="d-flex align-center gap-2">
                                    <MudIcon Icon="@Icons.Material.Filled.Phone" Size="Size.Small" Color="Color.Secondary" />
                                    <MudText Typo="Typo.body2">@_companyInfo.Phone</MudText>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(_companyInfo?.Email))
                            {
                                <div class="d-flex align-center gap-2">
                                    <MudIcon Icon="@Icons.Material.Filled.Email" Size="Size.Small" Color="Color.Secondary" />
                                    <MudText Typo="Typo.body2">@_companyInfo.Email</MudText>
                                </div>
                            }
                        </MudStack>
                    </MudItem>
                    <MudItem xs="12" sm="4" Class="text-right">
                        <MudText Typo="Typo.h6" Class="mb-3 font-weight-bold" Color="Color.Primary">روابط سريعة</MudText>
                        <MudStack Spacing="1" Class="mt-3">
                            <MudLink Href="/" Underline="Underline.Hover" Class="d-block py-1 hover-link">
                                <div class="d-flex align-center gap-2">
                                    <MudIcon Icon="@Icons.Material.Filled.Home" Size="Size.Small" />
                                    <span>الرئيسية</span>
                                </div>
                            </MudLink>
                            <MudLink Href="/about" Underline="Underline.Hover" Class="d-block py-1 hover-link">
                                <div class="d-flex align-center gap-2">
                                    <MudIcon Icon="@Icons.Material.Filled.Info" Size="Size.Small" />
                                    <span>من نحن</span>
                                </div>
                            </MudLink>
                            <MudLink Href="/ourServices" Underline="Underline.Hover" Class="d-block py-1 hover-link">
                                <div class="d-flex align-center gap-2">
                                    <MudIcon Icon="@Icons.Material.Filled.Build" Size="Size.Small" />
                                    <span>خدماتنا</span>
                                </div>
                            </MudLink>
                            <MudLink Href="/news" Underline="Underline.Hover" Class="d-block py-1 hover-link">
                                <div class="d-flex align-center gap-2">
                                    <MudIcon Icon="@Icons.Material.Filled.Article" Size="Size.Small" />
                                    <span>الأخبار</span>
                                </div>
                            </MudLink>
                            <MudLink Href="/branches" Underline="Underline.Hover" Class="d-block py-1 hover-link">
                                <div class="d-flex align-center gap-2">
                                    <MudIcon Icon="@Icons.Material.Filled.Business" Size="Size.Small" />
                                    <span>الفروع</span>
                                </div>
                            </MudLink>
                            <MudLink Href="/contact" Underline="Underline.Hover" Class="d-block py-1 hover-link">
                                <div class="d-flex align-center gap-2">
                                    <MudIcon Icon="@Icons.Material.Filled.ContactMail" Size="Size.Small" />
                                    <span>اتصل بنا</span>
                                </div>
                            </MudLink>
                        </MudStack>
                    </MudItem>
                    <MudItem xs="12" sm="4" Class="text-right">
                        <MudText Typo="Typo.h6" Class="mb-3 font-weight-bold" Color="Color.Primary">تواصل معنا</MudText>
                        <MudText Typo="Typo.body2" Class="mb-3">تابعونا على وسائل التواصل الاجتماعي</MudText>

                        @* تخطيط متجاوب للأيقونات - يعمل على جميع الأجهزة *@
                        <MudHidden Breakpoint="Breakpoint.SmAndDown" Invert="false">
                            @* عرض سطح المكتب والتابلت *@
                            <div class="d-flex flex-wrap gap-3 mt-3 justify-start align-center">
                                @if (!string.IsNullOrEmpty(_companyInfo?.FacebookUrl))
                                {
                                    <MudTooltip Text="فيسبوك">
                                        <MudIconButton Icon="@Icons.Custom.Brands.Facebook"
                                                       Href="@_companyInfo.FacebookUrl"
                                                       Target="_blank"
                                                       Color="Color.Primary"
                                                       Variant="Variant.Filled"
                                                       Size="Size.Medium"
                                                       Class="social-icon-facebook" />
                                    </MudTooltip>
                                }
                                @if (!string.IsNullOrEmpty(_companyInfo?.TwitterUrl))
                                {
                                    <MudTooltip Text="تويتر">
                                        <MudIconButton Icon="@Icons.Custom.Brands.Twitter"
                                                       Href="@_companyInfo.TwitterUrl"
                                                       Target="_blank"
                                                       Color="Color.Info"
                                                       Variant="Variant.Filled"
                                                       Size="Size.Medium"
                                                       Class="social-icon-twitter" />
                                    </MudTooltip>
                                }
                                @if (!string.IsNullOrEmpty(_companyInfo?.InstagramUrl))
                                {
                                    <MudTooltip Text="إنستغرام">
                                        <MudIconButton Icon="@Icons.Custom.Brands.Instagram"
                                                       Href="@_companyInfo.InstagramUrl"
                                                       Target="_blank"
                                                       Color="Color.Secondary"
                                                       Variant="Variant.Filled"
                                                       Size="Size.Medium"
                                                       Class="social-icon-instagram" />
                                    </MudTooltip>
                                }
                                @if (!string.IsNullOrEmpty(_companyInfo?.LinkedInUrl))
                                {
                                    <MudTooltip Text="لينكد إن">
                                        <MudIconButton Icon="@Icons.Custom.Brands.LinkedIn"
                                                       Href="@_companyInfo.LinkedInUrl"
                                                       Target="_blank"
                                                       Color="Color.Primary"
                                                       Variant="Variant.Filled"
                                                       Size="Size.Medium"
                                                       Class="social-icon-linkedin" />
                                    </MudTooltip>
                                }
                            </div>
                        </MudHidden>

                        @* عرض الهواتف المحمولة *@
                        <MudHidden Breakpoint="Breakpoint.SmAndDown" Invert="true">
                            <div class="d-flex flex-wrap gap-4 mt-3 justify-center align-center">
                                @if (!string.IsNullOrEmpty(_companyInfo?.FacebookUrl))
                                {
                                    <MudTooltip Text="فيسبوك">
                                        <MudIconButton Icon="@Icons.Custom.Brands.Facebook"
                                                       Href="@_companyInfo.FacebookUrl"
                                                       Target="_blank"
                                                       Color="Color.Primary"
                                                       Variant="Variant.Filled"
                                                       Size="Size.Large"
                                                       Class="social-icon-facebook" />
                                    </MudTooltip>
                                }
                                @if (!string.IsNullOrEmpty(_companyInfo?.TwitterUrl))
                                {
                                    <MudTooltip Text="تويتر">
                                        <MudIconButton Icon="@Icons.Custom.Brands.Twitter"
                                                       Href="@_companyInfo.TwitterUrl"
                                                       Target="_blank"
                                                       Color="Color.Info"
                                                       Variant="Variant.Filled"
                                                       Size="Size.Large"
                                                       Class="social-icon-twitter" />
                                    </MudTooltip>
                                }
                                @if (!string.IsNullOrEmpty(_companyInfo?.InstagramUrl))
                                {
                                    <MudTooltip Text="إنستغرام">
                                        <MudIconButton Icon="@Icons.Custom.Brands.Instagram"
                                                       Href="@_companyInfo.InstagramUrl"
                                                       Target="_blank"
                                                       Color="Color.Secondary"
                                                       Variant="Variant.Filled"
                                                       Size="Size.Large"
                                                       Class="social-icon-instagram" />
                                    </MudTooltip>
                                }
                                @if (!string.IsNullOrEmpty(_companyInfo?.LinkedInUrl))
                                {
                                    <MudTooltip Text="لينكد إن">
                                        <MudIconButton Icon="@Icons.Custom.Brands.LinkedIn"
                                                       Href="@_companyInfo.LinkedInUrl"
                                                       Target="_blank"
                                                       Color="Color.Primary"
                                                       Variant="Variant.Filled"
                                                       Size="Size.Large"
                                                       Class="social-icon-linkedin" />
                                    </MudTooltip>
                                }
                            </div>
                        </MudHidden>
                    </MudItem>
                </MudGrid>

                <MudDivider Class="my-4" />

                <MudPaper Class="pa-4 text-center" Elevation="0" Style="background-color: var(--mud-palette-background-grey);">
                    <MudText Typo="Typo.body2" Align="Align.Center" Color="Color.Secondary">
                        ©️ @DateTime.Now.Year @(_companyInfo?.Name ?? "شركتنا"). جميع الحقوق محفوظة.
                    </MudText>
                </MudPaper>
            </MudContainer>
        </MudMainContent>
    </MudLayout>
</MudRTLProvider>


<div id="blazor-error-ui">
    حدث خطأ غير متوقع.
    <a href="" class="reload">إعادة تحميل</a>
    <a class="dismiss">🗙</a>
</div>

@code {
    bool _IsRTL = false;
    private bool _drawerOpen = false;
    private BlogMud.Shared.Models.CompanyInfo _companyInfo;
    private MudTheme _theme = new MudTheme()
    {
        PaletteDark = new PaletteDark(),
        PaletteLight = new PaletteLight()
        {
            Primary = "#953735",
            Secondary = "#595959",
            AppbarBackground = "#953735",
            Background = "#FFFFFF",
            DrawerBackground = "#FFF",
            DrawerText = "rgba(0,0,0, 0.7)",
            Surface = "#FFF",
        },
        Typography = new Typography()
        {
            Default = new MudBlazor.DefaultTypography()
            {
                FontFamily = new[] { "Tajawal", "Roboto", "Helvetica", "Arial", "sans-serif" },
                FontSize = "0.9rem",
                FontWeight = "400",
                LineHeight = "1.43",
                LetterSpacing = ".01071em"
            },
        }
    };



    private void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }

    private void NavigateTo(string url, bool forceLoad = false)
    {
        Navigation.NavigateTo(url, forceLoad);
    }

    private HttpClient? _httpClient;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Obtener HttpClient del ServiceProvider
            _httpClient = ServiceProvider.GetService(typeof(HttpClient)) as HttpClient;

            // Intentar obtener los datos de la empresa desde una API en el servidor si HttpClient está disponible
            if (_httpClient != null)
            {
                try
                {
                    _companyInfo = await _httpClient.GetFromJsonAsync<BlogMud.Shared.Models.CompanyInfo>("/api/CompanyInfo");
                }
                catch
                {
                    // En caso de error, usar datos predeterminados
                    SetDefaultCompanyInfo();
                }
            }
            else
            {
                // Si no podemos obtener HttpClient, usar objeto predeterminado
                SetDefaultCompanyInfo();
            }

            // Si no pudimos obtener los datos, usar un objeto predeterminado
            if (_companyInfo == null)
            {
                SetDefaultCompanyInfo();
            }
        }
        catch (Exception)
        {
            // Si hay algún error, usar un objeto predeterminado
            SetDefaultCompanyInfo();
        }

        await base.OnInitializedAsync();
    }

    private void SetDefaultCompanyInfo()
    {
        _companyInfo = new CompanyInfo
        {
            Name = "شركتنا",
            Vision = "أن نكون الشركة الرائدة في مجال عملنا",
            Mission = "توفير خدمات عالية الجودة لعملائنا",
            AboutUs = "نحن شركة رائدة في مجال تكنولوجيا المعلومات.",
            Phone = "+966123456789",
            Email = "<EMAIL>",
            Address = "الرياض، المملكة العربية السعودية",
            LogoUrl = "/imeg/c61a6b2a-796c-4475-aef1-3efa083f38c4.jpg",
            FacebookUrl = "https://www.facebook.com/ahmedfranchy",
            TwitterUrl = "https://twitter.com/example",
            InstagramUrl = "https://instagram.com/example",
            LinkedInUrl = "https://linkedin.com/in/example"
        };
    }
}



