using AutoMapper;
using BlogMud.Data.Repositories;
using BlogMud.Services;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BlogMud.Controllers
{
    /// <summary>
    /// وحدة تحكم واجهة برمجة التطبيقات لإدارة الصور المتحركة
    /// </summary>
    /// <remarks>
    /// توفر هذه الوحدة واجهات برمجة تطبيقات RESTful للتعامل مع الصور المتحركة
    /// بما في ذلك عمليات الإنشاء والقراءة والتحديث والحذف
    /// </remarks>
    [Route("api/[controller]")]
    [ApiController]
    public class AnimatedGifsController : ControllerBase
    {
        #region المتغيرات والمنشئ

        private readonly IUnitOfWork _unitOfWork;
        private readonly IRepository<AnimatedGif> _animatedGifRepository;
        private readonly ILogger<AnimatedGifsController> _logger;
        private readonly IMapper _mapper;
        private readonly IFileService _fileService;

        /// <summary>
        /// منشئ وحدة تحكم الصور المتحركة
        /// </summary>
        /// <param name="unitOfWork">وحدة العمل</param>
        /// <param name="logger">مسجل الأحداث</param>
        /// <param name="mapper">محول البيانات</param>
        /// <param name="fileService">خدمة إدارة الملفات</param>
        public AnimatedGifsController(
            IUnitOfWork unitOfWork,
            ILogger<AnimatedGifsController> logger,
            IMapper mapper,
            IFileService fileService)
        {
            _unitOfWork = unitOfWork;
            _animatedGifRepository = unitOfWork.Repository<AnimatedGif>();
            _logger = logger;
            _mapper = mapper;
            _fileService = fileService;
        }

        #endregion

        #region عمليات القراءة (GET)

        /// <summary>
        /// الحصول على جميع الصور المتحركة
        /// </summary>
        /// <returns>قائمة بجميع الصور المتحركة</returns>
        /// <response code="200">تم استرجاع قائمة الصور المتحركة بنجاح</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<AnimatedGifDto>>> GetAnimatedGifs()
        {
            try
            {
                _logger.LogInformation("Fetching all animated gifs");
                var animatedGifs = await _animatedGifRepository.GetAllAsync(
                    orderBy: q => q.OrderBy(g => g.DisplayOrder)
                );
                var animatedGifDtos = _mapper.Map<IEnumerable<AnimatedGifDto>>(animatedGifs);
                return Ok(animatedGifDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving animated gifs");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على الصور المتحركة النشطة فقط
        /// </summary>
        /// <returns>قائمة بالصور المتحركة النشطة</returns>
        /// <response code="200">تم استرجاع قائمة الصور المتحركة النشطة بنجاح</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        [HttpGet("active")]
        public async Task<ActionResult<IEnumerable<AnimatedGifDto>>> GetActiveAnimatedGifs()
        {
            try
            {
                _logger.LogInformation("Fetching active animated gifs");
                var animatedGifs = await _animatedGifRepository.GetAllAsync(
                    filter: g => g.IsActive,
                    orderBy: q => q.OrderBy(g => g.DisplayOrder)
                );
                var animatedGifDtos = _mapper.Map<IEnumerable<AnimatedGifDto>>(animatedGifs);
                return Ok(animatedGifDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active animated gifs");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على صورة متحركة بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف الصورة المتحركة</param>
        /// <returns>الصورة المتحركة المطلوبة</returns>
        /// <response code="200">تم استرجاع الصورة المتحركة بنجاح</response>
        /// <response code="404">الصورة المتحركة غير موجودة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        [HttpGet("{id}")]
        public async Task<ActionResult<AnimatedGifDto>> GetAnimatedGif(int id)
        {
            try
            {
                _logger.LogInformation("Fetching animated gif with ID: {AnimatedGifId}", id);
                var animatedGif = await _animatedGifRepository.GetByIdAsync(id);

                if (animatedGif == null)
                {
                    _logger.LogWarning("Animated gif not found with ID: {AnimatedGifId}", id);
                    return NotFound();
                }

                var animatedGifDto = _mapper.Map<AnimatedGifDto>(animatedGif);
                return Ok(animatedGifDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving animated gif with ID: {AnimatedGifId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #endregion

        #region عمليات التعديل (PUT/POST/DELETE)

        /// <summary>
        /// تحديث صورة متحركة
        /// </summary>
        /// <param name="id">معرف الصورة المتحركة</param>
        /// <param name="animatedGifDto">بيانات الصورة المتحركة المحدثة</param>
        /// <returns>استجابة بدون محتوى في حالة نجاح التحديث</returns>
        /// <response code="204">تم تحديث الصورة المتحركة بنجاح</response>
        /// <response code="400">معرف الصورة المتحركة غير متطابق</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="404">الصورة المتحركة غير موجودة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> PutAnimatedGif(int id, AnimatedGifDto animatedGifDto)
        {
            if (id != animatedGifDto.Id)
            {
                return BadRequest("Animated gif ID mismatch");
            }

            try
            {
                _logger.LogInformation("Updating animated gif with ID: {AnimatedGifId}", id);

                var existingAnimatedGif = await _animatedGifRepository.GetByIdAsync(id);
                if (existingAnimatedGif == null)
                {
                    _logger.LogWarning("Animated gif not found with ID: {AnimatedGifId}", id);
                    return NotFound();
                }

                _mapper.Map(animatedGifDto, existingAnimatedGif);
                existingAnimatedGif.LastModifiedAt = DateTime.Now;

                _animatedGifRepository.Update(existingAnimatedGif);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Animated gif updated successfully: {AnimatedGifId}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating animated gif: {AnimatedGifId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء صورة متحركة جديدة
        /// </summary>
        /// <param name="animatedGifDto">بيانات الصورة المتحركة الجديدة</param>
        /// <returns>الصورة المتحركة التي تم إنشاؤها مع معرفها الجديد</returns>
        /// <response code="201">تم إنشاء الصورة المتحركة بنجاح</response>
        /// <response code="400">بيانات الصورة المتحركة غير صالحة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="500">حدث خطأ أثناء إنشاء الصورة المتحركة</response>
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<AnimatedGifDto>> PostAnimatedGif(AnimatedGifDto animatedGifDto)
        {
            try
            {
                _logger.LogInformation("Creating new animated gif: {AnimatedGifTitle}", animatedGifDto.Title);

                var animatedGif = _mapper.Map<AnimatedGif>(animatedGifDto);
                animatedGif.CreatedAt = DateTime.Now;

                await _animatedGifRepository.AddAsync(animatedGif);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Animated gif created successfully with ID: {AnimatedGifId}", animatedGif.Id);

                var createdAnimatedGifDto = _mapper.Map<AnimatedGifDto>(animatedGif);

                return CreatedAtAction("GetAnimatedGif", new { id = createdAnimatedGifDto.Id }, createdAnimatedGifDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating animated gif: {AnimatedGifTitle}", animatedGifDto.Title);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف صورة متحركة بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف الصورة المتحركة المراد حذفها</param>
        /// <returns>استجابة بدون محتوى في حالة نجاح الحذف</returns>
        /// <response code="204">تم حذف الصورة المتحركة بنجاح</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="404">الصورة المتحركة غير موجودة</response>
        /// <response code="500">حدث خطأ أثناء حذف الصورة المتحركة</response>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteAnimatedGif(int id)
        {
            try
            {
                _logger.LogInformation("Deleting animated gif with ID: {AnimatedGifId}", id);

                var animatedGif = await _animatedGifRepository.GetByIdAsync(id);
                if (animatedGif == null)
                {
                    _logger.LogWarning("Animated gif not found with ID: {AnimatedGifId}", id);
                    return NotFound();
                }

                // حذف ملف الصورة من المجلد إذا كان موجودًا
                if (!string.IsNullOrEmpty(animatedGif.ImageUrl))
                {
                    try
                    {
                        bool fileDeleted = _fileService.DeleteFileIfExists(animatedGif.ImageUrl, "AnimatedGifsSider");
                        if (fileDeleted)
                        {
                            _logger.LogInformation("Successfully deleted image file: {ImageUrl}", animatedGif.ImageUrl);
                        }
                        else
                        {
                            _logger.LogWarning("Could not delete image file: {ImageUrl}", animatedGif.ImageUrl);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error deleting image file: {ImageUrl}", animatedGif.ImageUrl);
                        // نستمر في حذف السجل حتى لو فشل حذف الملف
                    }
                }

                // حذف السجل من قاعدة البيانات
                _animatedGifRepository.Remove(animatedGif);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Animated gif deleted successfully: {AnimatedGifId}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting animated gif: {AnimatedGifId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #endregion
    }
}
