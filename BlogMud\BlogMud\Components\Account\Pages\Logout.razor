@page "/Account/Logout"
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Options
@using BlogMud.Data
@inject SignInManager<ApplicationUser> SignInManager
@inject NavigationManager Navigation
@inject IConfiguration Configuration

<PageTitle>جاري تسجيل الخروج...</PageTitle>

<div class="d-flex justify-center align-center" style="height: 80vh;">
    <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        await SignInManager.SignOutAsync();
        Navigation.NavigateTo("/", true);
    }
}
