@page "/dev/notification-test"
@using BlogMud.Data
@using BlogMud.Services
@using BlogMud.Shared.Models
@using Microsoft.AspNetCore.Identity
@using System.ComponentModel.DataAnnotations
@inject INotificationService NotificationService
@inject ILogger<NotificationTest> Logger
@inject IConfiguration Configuration

<PageTitle>اختبار إشعارات المنشورات - BlogMud</PageTitle>

<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bell me-2"></i>
                        اختبار إشعارات المنشورات الجديدة
                    </h3>
                </div>
                <div class="card-body">

                    <!-- Base URL Configuration Display -->
                    <div class="alert alert-info" role="alert">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            إعدادات الموقع
                        </h5>
                        <p class="mb-0">
                            <strong>رابط الموقع الأساسي:</strong> @baseUrl<br>
                            <small>سيتم استخدام هذا الرابط في إنشاء روابط "قراءة المزيد" في الإيميلات</small>
                        </p>
                    </div>

                    <!-- Test Article Form -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">إنشاء مقال تجريبي لاختبار الإشعارات</h6>
                                </div>
                                <div class="card-body">
                                    <EditForm Model="testArticle" OnValidSubmit="SendTestNotification">
                                        <DataAnnotationsValidator />

                                        <div class="mb-3">
                                            <label class="form-label">معرف المقال:</label>
                                            <InputNumber @bind-Value="testArticle.Id" class="form-control" />
                                            <ValidationMessage For="() => testArticle.Id" class="text-danger" />
                                            <small class="form-text text-muted">سيتم استخدامه في رابط المقال: @baseUrl/post/@testArticle.Id</small>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">عنوان المقال:</label>
                                            <InputText @bind-Value="testArticle.Title" class="form-control" />
                                            <ValidationMessage For="() => testArticle.Title" class="text-danger" />
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">محتوى المقال:</label>
                                            <InputTextArea @bind-Value="testArticle.Content" class="form-control" rows="4" />
                                            <ValidationMessage For="() => testArticle.Content" class="text-danger" />
                                            <small class="form-text text-muted">سيتم عرض أول 200 حرف في الإيميل</small>
                                        </div>

                                        <button type="submit" class="btn btn-primary" disabled="@isSending">
                                            @if (isSending)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                                <span>جاري الإرسال...</span>
                                            }
                                            else
                                            {
                                                <i class="fas fa-paper-plane me-2"></i>
                                                <span>إرسال إشعارات المنشور الجديد</span>
                                            }
                                        </button>
                                    </EditForm>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">معلومات الاختبار</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>عدد المستخدمين النشطين:</strong> @activeUsersCount</p>
                                    <p><strong>نمط الرابط المتوقع:</strong></p>
                                    <code>@baseUrl/post/{id}</code>
                                    <hr>
                                    <p><strong>مثال على الرابط:</strong></p>
                                    <a href="@($"{baseUrl}/post/{testArticle.Id}")" target="_blank" class="btn btn-sm btn-outline-primary">
                                        @baseUrl/post/@testArticle.Id
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Results -->
                    @if (!string.IsNullOrEmpty(resultMessage))
                    {
                        <div class="alert @(isSuccess ? "alert-success" : "alert-danger") mt-4" role="alert">
                            <h6 class="alert-heading">
                                <i class="fas @(isSuccess ? "fa-check-circle" : "fa-exclamation-circle") me-2"></i>
                                نتيجة الإرسال
                            </h6>
                            <p class="mb-0">@resultMessage</p>
                        </div>
                    }

                    <!-- Quick Links -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6>روابط سريعة:</h6>
                            <div class="btn-group" role="group">
                                <a href="/dev/emails" class="btn btn-outline-primary">
                                    <i class="fas fa-list me-2"></i>
                                    عرض الإيميلات المرسلة
                                </a>
                                <a href="/dev/email-test" class="btn btn-outline-secondary">
                                    <i class="fas fa-envelope me-2"></i>
                                    اختبار الإيميل العام
                                </a>
                                <a href="@($"{baseUrl}/post/{testArticle.Id}")" target="_blank" class="btn btn-outline-info">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    فتح رابط المقال التجريبي
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private TestArticleModel testArticle = new();
    private bool isSending = false;
    private string resultMessage = "";
    private bool isSuccess = false;
    private string baseUrl = "";
    private int activeUsersCount = 0;

    protected override async Task OnInitializedAsync()
    {
        // Get base URL from configuration
        baseUrl = Configuration["BaseAddress"] ?? "http://ahmedakear.runasp.net";
        
        // Get active users count
        try
        {
            activeUsersCount = await NotificationService.GetActiveUsersCountAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get active users count");
            activeUsersCount = 0;
        }
    }

    private async Task SendTestNotification()
    {
        isSending = true;
        resultMessage = "";

        try
        {
            // Create a test article
            var article = new Article
            {
                Id = testArticle.Id,
                Title = testArticle.Title,
                Content = testArticle.Content,
                PublishDate = DateTime.Now,
                IsPublished = true
            };

            // Send notification
            await NotificationService.SendNewPostNotificationAsync(article);

            resultMessage = $"تم إرسال إشعارات المنشور الجديد بنجاح إلى {activeUsersCount} مستخدم. " +
                          $"رابط المقال: {baseUrl}/post/{testArticle.Id}";
            isSuccess = true;

            Logger.LogInformation("Test notification sent successfully for article {ArticleId} to {UserCount} users", 
                testArticle.Id, activeUsersCount);
        }
        catch (Exception ex)
        {
            resultMessage = $"فشل في إرسال الإشعارات: {ex.Message}";
            isSuccess = false;

            Logger.LogError(ex, "Failed to send test notification for article {ArticleId}", testArticle.Id);
        }
        finally
        {
            isSending = false;
        }
    }

    public class TestArticleModel
    {
        [Required(ErrorMessage = "معرف المقال مطلوب")]
        [Range(1, int.MaxValue, ErrorMessage = "معرف المقال يجب أن يكون أكبر من صفر")]
        public int Id { get; set; } = 123;

        [Required(ErrorMessage = "عنوان المقال مطلوب")]
        [StringLength(200, ErrorMessage = "عنوان المقال يجب أن يكون أقل من 200 حرف")]
        public string Title { get; set; } = "مقال تجريبي لاختبار الإشعارات";

        [Required(ErrorMessage = "محتوى المقال مطلوب")]
        [StringLength(1000, ErrorMessage = "محتوى المقال يجب أن يكون أقل من 1000 حرف")]
        public string Content { get; set; } = "هذا محتوى تجريبي لاختبار نظام الإشعارات. سيتم عرض أول 200 حرف من هذا المحتوى في الإيميل المرسل للمستخدمين. الهدف من هذا الاختبار هو التأكد من أن رابط 'قراءة المزيد' يعمل بشكل صحيح ويوجه المستخدمين إلى الصفحة الصحيحة للمقال.";
    }
}
