@page "/admin/dashboard"
@using BlogMud.Shared.DTOs
@using System.Net.Http.Json

@attribute [Authorize(Roles = "Admin")]
@layout BlogMud.Client.Layout.AdminLayout

<PageTitle>لوحة التحكم - إدارة الموقع</PageTitle>
<DashboardCSS />
<div class="dashboard-container">
    <!-- Enhanced Header Section with Breadcrumbs -->
    <MudContainer MaxWidth="MaxWidth.False" Class="dashboard-header" data-aos="fade-down" data-aos-duration="800">
        <!-- Breadcrumb Navigation -->
        <div class="breadcrumb-section" data-aos="fade-right" data-aos-delay="200">
            <MudBreadcrumbs Items="_breadcrumbItems" Class="dashboard-breadcrumbs">
                <ItemTemplate Context="item">
                    <MudLink Href="@item.Href" Class="breadcrumb-link">
                        @if (!string.IsNullOrEmpty(item.Icon))
                        {
                            <MudIcon Icon="@item.Icon" Size="Size.Small" Class="me-1" />
                        }
                        @item.Text
                    </MudLink>
                </ItemTemplate>
                <SeparatorTemplate>
                    <MudIcon Icon="@Icons.Material.Filled.ChevronLeft" Size="Size.Small" Class="breadcrumb-separator" />
                </SeparatorTemplate>
            </MudBreadcrumbs>
        </div>

        <!-- Enhanced Header Content -->
        <MudGrid AlignItems="Center" Justify="Justify.SpaceBetween" Class="header-content">
            <MudItem xs="12" md="8">
                <div class="welcome-section" data-aos="fade-left" data-aos-delay="400">
                    <div class="welcome-icon-container">
                        <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="welcome-icon" />
                    </div>
                    <MudText Typo="Typo.h3" Class="dashboard-title">
                        مرحباً بك في لوحة التحكم
                    </MudText>
                    <MudText Typo="Typo.subtitle1" Class="dashboard-subtitle">
                        إدارة شاملة لموقعك ومحتواه مع أدوات متقدمة للتحكم والمراقبة
                    </MudText>
                    <div class="dashboard-status" data-aos="fade-up" data-aos-delay="600">
                        <MudChip T="bool" Icon="@Icons.Material.Filled.CheckCircle"
                                Color="Color.Success"
                                Size="Size.Small"
                                Class="status-chip">
                            النظام يعمل بشكل طبيعي
                        </MudChip>
                        <MudChip T="bool" Icon="@Icons.Material.Filled.Schedule"
                                Color="Color.Info"
                                Size="Size.Small"
                                Class="status-chip">
                            آخر تحديث: @DateTime.Now.ToString("HH:mm")
                        </MudChip>
                    </div>
                </div>
            </MudItem>
            <MudItem xs="12" md="4" Class="text-end">
                <div class="quick-actions" data-aos="fade-left" data-aos-delay="800">
                    <MudButton Variant="Variant.Filled"
                              Color="Color.Primary"
                              StartIcon="@Icons.Material.Filled.Add"
                              Class="quick-action-btn primary-action"
                              OnClick="@(() => NavigateTo("/admin/posts/postManagement", true))">
                        <span class="button-text">منشور جديد</span>
                    </MudButton>
                    <MudButton Variant="Variant.Outlined"
                              Color="Color.Primary"
                              StartIcon="@Icons.Material.Filled.Analytics"
                              Class="quick-action-btn secondary-action"
                              OnClick="@LoadDashboardData">
                        <span class="button-text">تحديث البيانات</span>
                    </MudButton>
                </div>
            </MudItem>
        </MudGrid>
    </MudContainer>

    <!-- Enhanced Statistics Overview -->
    <MudContainer MaxWidth="MaxWidth.False" Class="statistics-section">
        <div class="section-header" data-aos="fade-up" data-aos-duration="600">
            <MudText Typo="Typo.h5" Class="section-title">
                <MudIcon Icon="@Icons.Material.Filled.Analytics" Class="section-icon" />
                إحصائيات سريعة
            </MudText>
            <MudText Typo="Typo.body2" Class="section-subtitle">
                نظرة عامة على أداء الموقع والمحتوى
            </MudText>
        </div>

        <MudGrid Spacing="3" Class="statistics-grid">
            <MudItem xs="12" sm="6" md="3">
                <MudCard Class="stat-card stat-card-primary enhanced-card dashboard-card" Elevation="0"
                         data-aos="zoom-in" data-aos-delay="100" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">
                    <MudCardContent Class="stat-card-content">
                        <div class="stat-header">
                            <div class="stat-icon-container">
                                <MudIcon Icon="@Icons.Material.Filled.Article"
                                        Size="Size.Large"
                                        Class="stat-icon" />
                            </div>
                            <div class="stat-trend">
                                <MudIcon Icon="@Icons.Material.Filled.TrendingUp"
                                        Size="Size.Small"
                                        Class="trend-icon trend-up" />
                            </div>
                        </div>
                        <div class="stat-details">
                            @if (_isLoading)
                            {
                                <MudSkeleton SkeletonType="SkeletonType.Text" Height="3rem" Width="60%" Class="mb-2" />
                                <MudSkeleton SkeletonType="SkeletonType.Text" Height="1.5rem" Width="80%" />
                            }
                            else
                            {
                                <MudText Typo="Typo.h4" Class="stat-number">
                                    @_statistics.ArticlesCount.ToString("N0")
                                </MudText>
                                <MudText Typo="Typo.body1" Class="stat-label">المنشورات</MudText>
                                <MudText Typo="Typo.caption" Class="stat-description">
                                    إجمالي المنشورات المنشورة
                                </MudText>
                                <div class="stat-progress">
                                    <MudProgressLinear Color="Color.Primary"
                                                      Value="@GetProgressValue(_statistics.ArticlesCount, 100)"
                                                      Size="Size.Small"
                                                      Class="progress-bar" />
                                </div>
                            }
                        </div>
                        <div class="stat-actions">
                            <MudButton Variant="Variant.Text"
                                      Color="Color.Primary"
                                      Size="Size.Small"
                                      StartIcon="@Icons.Material.Filled.Edit"
                                      Class="stat-action-btn"
                                      OnClick="@(() => NavigateTo("/admin/posts/postManagement", true))">
                                إدارة
                            </MudButton>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" sm="6" md="3">
                <MudCard Class="stat-card stat-card-secondary enhanced-card dashboard-card" Elevation="0"
                         data-aos="zoom-in" data-aos-delay="200" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">
                    <MudCardContent Class="stat-card-content">
                        <div class="stat-header">
                            <div class="stat-icon-container">
                                <MudIcon Icon="@Icons.Material.Filled.Category"
                                        Size="Size.Large"
                                        Class="stat-icon" />
                            </div>
                            <div class="stat-trend">
                                <MudIcon Icon="@Icons.Material.Filled.TrendingFlat"
                                        Size="Size.Small"
                                        Class="trend-icon trend-stable" />
                            </div>
                        </div>
                        <div class="stat-details">
                            @if (_isLoading)
                            {
                                <MudSkeleton SkeletonType="SkeletonType.Text" Height="3rem" Width="60%" Class="mb-2" />
                                <MudSkeleton SkeletonType="SkeletonType.Text" Height="1.5rem" Width="80%" />
                            }
                            else
                            {
                                <MudText Typo="Typo.h4" Class="stat-number">
                                    @_statistics.CategoriesCount.ToString("N0")
                                </MudText>
                                <MudText Typo="Typo.body1" Class="stat-label">الأقسام</MudText>
                                <MudText Typo="Typo.caption" Class="stat-description">
                                    أقسام تصنيف المحتوى
                                </MudText>
                                <div class="stat-progress">
                                    <MudProgressLinear Color="Color.Secondary"
                                                      Value="@GetProgressValue(_statistics.CategoriesCount, 20)"
                                                      Size="Size.Small"
                                                      Class="progress-bar" />
                                </div>
                            }
                        </div>
                        <div class="stat-actions">
                            <MudButton Variant="Variant.Text"
                                      Color="Color.Secondary"
                                      Size="Size.Small"
                                      StartIcon="@Icons.Material.Filled.Edit"
                                      Class="stat-action-btn"
                                      Href="/admin/categories">
                                إدارة
                            </MudButton>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" sm="6" md="3">
                <MudCard Class="stat-card stat-card-info enhanced-card dashboard-card" Elevation="0"
                         data-aos="zoom-in" data-aos-delay="300" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">
                    <MudCardContent Class="stat-card-content">
                        <div class="stat-header">
                            <div class="stat-icon-container">
                                <MudIcon Icon="@Icons.Material.Filled.People"
                                        Size="Size.Large"
                                        Class="stat-icon" />
                            </div>
                            <div class="stat-trend">
                                <MudIcon Icon="@Icons.Material.Filled.TrendingUp"
                                        Size="Size.Small"
                                        Class="trend-icon trend-up" />
                            </div>
                        </div>
                        <div class="stat-details">
                            @if (_isLoading)
                            {
                                <MudSkeleton SkeletonType="SkeletonType.Text" Height="3rem" Width="60%" Class="mb-2" />
                                <MudSkeleton SkeletonType="SkeletonType.Text" Height="1.5rem" Width="80%" />
                            }
                            else
                            {
                                <MudText Typo="Typo.h4" Class="stat-number">
                                    @_statistics.UsersCount.ToString("N0")
                                </MudText>
                                <MudText Typo="Typo.body1" Class="stat-label">المستخدمون</MudText>
                                <MudText Typo="Typo.caption" Class="stat-description">
                                    المستخدمون المسجلون
                                </MudText>
                                <div class="stat-progress">
                                    <MudProgressLinear Color="Color.Info"
                                                      Value="@GetProgressValue(_statistics.UsersCount, 50)"
                                                      Size="Size.Small"
                                                      Class="progress-bar" />
                                </div>
                            }
                        </div>
                        <div class="stat-actions">
                            <MudButton Variant="Variant.Text"
                                      Color="Color.Info"
                                      Size="Size.Small"
                                      StartIcon="@Icons.Material.Filled.Visibility"
                                      Class="stat-action-btn"
                                      Href="/admin/users">
                                عرض
                            </MudButton>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" sm="6" md="3">
                <MudCard Class="stat-card stat-card-warning enhanced-card dashboard-card" Elevation="0"
                         data-aos="zoom-in" data-aos-delay="400" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">
                    <MudCardContent Class="stat-card-content">
                        <div class="stat-header">
                            <div class="stat-icon-container">
                                <MudIcon Icon="@Icons.Material.Filled.Message"
                                        Size="Size.Large"
                                        Class="stat-icon" />
                            </div>
                            <div class="stat-trend">
                                @if (_statistics.ContactMessagesCount > 5)
                                {
                                    <MudIcon Icon="@Icons.Material.Filled.TrendingUp"
                                            Size="Size.Small"
                                            Class="trend-icon trend-up" />
                                }
                                else
                                {
                                    <MudIcon Icon="@Icons.Material.Filled.TrendingDown"
                                            Size="Size.Small"
                                            Class="trend-icon trend-down" />
                                }
                            </div>
                        </div>
                        <div class="stat-details">
                            @if (_isLoading)
                            {
                                <MudSkeleton SkeletonType="SkeletonType.Text" Height="3rem" Width="60%" Class="mb-2" />
                                <MudSkeleton SkeletonType="SkeletonType.Text" Height="1.5rem" Width="80%" />
                            }
                            else
                            {
                                <MudText Typo="Typo.h4" Class="stat-number">
                                    @_statistics.ContactMessagesCount.ToString("N0")
                                </MudText>
                                <MudText Typo="Typo.body1" Class="stat-label">الرسائل</MudText>
                                <MudText Typo="Typo.caption" Class="stat-description">
                                    رسائل التواصل الجديدة
                                </MudText>
                                <div class="stat-progress">
                                    <MudProgressLinear Color="Color.Warning"
                                                      Value="@GetProgressValue(_statistics.ContactMessagesCount, 15)"
                                                      Size="Size.Small"
                                                      Class="progress-bar" />
                                </div>
                            }
                        </div>
                        <div class="stat-actions">
                            <MudButton Variant="Variant.Text"
                                      Color="Color.Warning"
                                      Size="Size.Small"
                                      StartIcon="@Icons.Material.Filled.Visibility"
                                      Class="stat-action-btn"
                                      Href="/admin/messages">
                                عرض
                            </MudButton>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    </MudContainer>

    <!-- Management Sections -->
    <MudContainer MaxWidth="MaxWidth.False" Class="management-section">
        <MudText Typo="Typo.h5" Class="section-title mb-4" data-aos="fade-right" data-aos-duration="600">إدارة المحتوى</MudText>
        <MudGrid Spacing="3">
            <MudItem xs="12" sm="6" lg="3">
                <MudCard Class="management-card dashboard-card" Elevation="3"
                         data-aos="flip-left" data-aos-delay="100" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                    <MudCardContent Class="management-card-content">
                        <div class="management-icon-wrapper">
                            <MudIcon Icon="@Icons.Material.Filled.Article"
                                    Size="Size.Large"
                                    Color="Color.Primary" />
                        </div>
                        <MudText Typo="Typo.h6" Class="management-title">إدارة المنشورات</MudText>
                        <MudText Typo="Typo.body2" Class="management-description">
                            إنشاء وتحرير ونشر المقالات والأخبار
                        </MudText>
                        <div class="management-actions">
                            <MudButton Variant="Variant.Filled"
                                      Color="Color.Primary"
                                      FullWidth="true"
                                      Class="management-btn"
                                      OnClick="@(() => NavigateTo("/admin/posts/postManagement", true))">
                                إدارة المنشورات
                            </MudButton>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" sm="6" lg="3">
                <MudCard Class="management-card dashboard-card" Elevation="3"
                         data-aos="flip-left" data-aos-delay="200" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                    <MudCardContent Class="management-card-content">
                        <div class="management-icon-wrapper">
                            <MudIcon Icon="@Icons.Material.Filled.Category"
                                    Size="Size.Large"
                                    Color="Color.Secondary" />
                        </div>
                        <MudText Typo="Typo.h6" Class="management-title">إدارة الأقسام</MudText>
                        <MudText Typo="Typo.body2" Class="management-description">
                            تنظيم وتصنيف المحتوى في أقسام
                        </MudText>
                        <div class="management-actions">
                            <MudButton Variant="Variant.Filled"
                                      Color="Color.Secondary"
                                      FullWidth="true"
                                      Class="management-btn"
                                      Href="/admin/categories">
                                إدارة الأقسام
                            </MudButton>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" sm="6" lg="3">
                <MudCard Class="management-card dashboard-card" Elevation="3"
                         data-aos="flip-left" data-aos-delay="300" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                    <MudCardContent Class="management-card-content">
                        <div class="management-icon-wrapper">
                            <MudIcon Icon="@Icons.Material.Filled.HomeRepairService"
                                    Size="Size.Large"
                                    Color="Color.Info" />
                        </div>
                        <MudText Typo="Typo.h6" Class="management-title">إدارة الخدمات</MudText>
                        <MudText Typo="Typo.body2" Class="management-description">
                            عرض وإدارة خدمات الشركة
                        </MudText>
                        <div class="management-actions">
                            <MudButton Variant="Variant.Filled"
                                      Color="Color.Info"
                                      FullWidth="true"
                                      Class="management-btn"
                                      OnClick="@(() => NavigateTo("/admin/ourServicesManager", true))">
                                إدارة الخدمات
                            </MudButton>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" sm="6" lg="3">
                <MudCard Class="management-card dashboard-card" Elevation="3"
                         data-aos="flip-left" data-aos-delay="400" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                    <MudCardContent Class="management-card-content">
                        <div class="management-icon-wrapper">
                            <MudIcon Icon="@Icons.Material.Filled.Info"
                                    Size="Size.Large"
                                    Color="Color.Success" />
                        </div>
                        <MudText Typo="Typo.h6" Class="management-title">صفحة من نحن</MudText>
                        <MudText Typo="Typo.body2" Class="management-description">
                            تحديث معلومات الشركة والفريق
                        </MudText>
                        <div class="management-actions">
                            <MudButton Variant="Variant.Filled"
                                      Color="Color.Success"
                                      FullWidth="true"
                                      Class="management-btn"
                                      OnClick="@(() => NavigateTo("/admin/aboutus", true))">
                                إدارة الصفحة
                            </MudButton>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    </MudContainer>

    <!-- Recent Activity & System Status -->
    <MudContainer MaxWidth="MaxWidth.False" Class="activity-section">
        <MudGrid Spacing="3">
            <MudItem xs="12" lg="8">
                <MudCard Class="activity-card dashboard-card" Elevation="3"
                         data-aos="slide-right" data-aos-duration="800" data-aos-once="false" data-aos-mirror="true">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6" Class="activity-title">
                                <MudIcon Icon="@Icons.Material.Filled.Timeline" Class="me-2" />
                                النشاط الأخير
                            </MudText>
                        </CardHeaderContent>
                        <CardHeaderActions>
                            <MudIconButton Icon="@Icons.Material.Filled.Refresh"
                                          Color="Color.Default"
                                          Size="Size.Small"
                                          OnClick="@LoadDashboardData" />
                        </CardHeaderActions>
                    </MudCardHeader>
                    <MudCardContent Class="activity-content">
                        @if (_isLoading)
                        {
                            <div class="activity-loading">
                                <MudProgressCircular Indeterminate="true" Size="Size.Medium" />
                                <MudText Typo="Typo.body2" Class="mt-2">جاري تحميل النشاط...</MudText>
                            </div>
                        }
                        else
                        {
                            <MudTimeline TimelineOrientation="TimelineOrientation.Vertical"
                                        TimelinePosition="TimelinePosition.Start"
                                        Class="activity-timeline">
                                <MudTimelineItem Color="Color.Primary" Size="Size.Small">
                                    <ItemContent>
                                        <div class="activity-item" data-aos="fade-left" data-aos-delay="100">
                                            <MudText Typo="Typo.body2" Class="activity-text">
                                                تم إنشاء منشور جديد: "آخر الأخبار والتطورات"
                                            </MudText>
                                            <MudText Typo="Typo.caption" Class="activity-time">
                                                منذ ساعتين
                                            </MudText>
                                        </div>
                                    </ItemContent>
                                </MudTimelineItem>
                                <MudTimelineItem Color="Color.Secondary" Size="Size.Small">
                                    <ItemContent>
                                        <div class="activity-item" data-aos="fade-left" data-aos-delay="200">
                                            <MudText Typo="Typo.body2" Class="activity-text">
                                                تم تحديث قسم "التكنولوجيا"
                                            </MudText>
                                            <MudText Typo="Typo.caption" Class="activity-time">
                                                منذ 4 ساعات
                                            </MudText>
                                        </div>
                                    </ItemContent>
                                </MudTimelineItem>
                                <MudTimelineItem Color="Color.Info" Size="Size.Small">
                                    <ItemContent>
                                        <div class="activity-item" data-aos="fade-left" data-aos-delay="300">
                                            <MudText Typo="Typo.body2" Class="activity-text">
                                                تم استلام رسالة تواصل جديدة
                                            </MudText>
                                            <MudText Typo="Typo.caption" Class="activity-time">
                                                أمس
                                            </MudText>
                                        </div>
                                    </ItemContent>
                                </MudTimelineItem>
                            </MudTimeline>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" lg="4">
                <MudCard Class="system-status-card dashboard-card" Elevation="3"
                         data-aos="slide-left" data-aos-duration="800" data-aos-once="false" data-aos-mirror="true">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6" Class="system-title">
                                <MudIcon Icon="@Icons.Material.Filled.HealthAndSafety" Class="me-2" />
                                حالة النظام
                            </MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent Class="system-content">
                        <div class="system-status-item" data-aos="fade-up" data-aos-delay="100">
                            <div class="status-indicator status-online"></div>
                            <div class="status-details">
                                <MudText Typo="Typo.body2" Class="status-label">الخادم</MudText>
                                <MudText Typo="Typo.caption" Class="status-value">متصل</MudText>
                            </div>
                        </div>
                        <div class="system-status-item" data-aos="fade-up" data-aos-delay="200">
                            <div class="status-indicator status-online"></div>
                            <div class="status-details">
                                <MudText Typo="Typo.body2" Class="status-label">قاعدة البيانات</MudText>
                                <MudText Typo="Typo.caption" Class="status-value">متصلة</MudText>
                            </div>
                        </div>
                        <div class="system-status-item" data-aos="fade-up" data-aos-delay="300">
                            <div class="status-indicator status-warning"></div>
                            <div class="status-details">
                                <MudText Typo="Typo.body2" Class="status-label">مساحة التخزين</MudText>
                                <MudText Typo="Typo.caption" Class="status-value">75% مستخدمة</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    </MudContainer>
</div>

@code {
    #region الخصائص والمتغيرات

    /// <summary>
    /// مدير التنقل المستخدم للانتقال بين الصفحات
    /// </summary>
    [Inject]
    private NavigationManager Navigation { get; set; } = default!;

    /// <summary>
    /// خدمة عرض الرسائل
    /// </summary>
    [Inject]
    private ISnackbar Snackbar { get; set; } = default!;

    /// <summary>
    /// حالة تحميل البيانات
    /// </summary>
    private bool _isLoading = true;

    /// <summary>
    /// إحصائيات لوحة التحكم
    /// </summary>
    private DashboardStatistics _statistics = new();

    /// <summary>
    /// عناصر التنقل المتدرج (Breadcrumbs)
    /// </summary>
    private List<BreadcrumbItem> _breadcrumbItems = new()
    {
        new BreadcrumbItem("الرئيسية", href: "/", icon: Icons.Material.Filled.Home),
        new BreadcrumbItem("لوحة التحكم", href: "/admin/dashboard", icon: Icons.Material.Filled.Dashboard)
    };

    /// <summary>
    /// فئة لتخزين إحصائيات لوحة التحكم
    /// </summary>
    public class DashboardStatistics
    {
        public int ArticlesCount { get; set; } = 0;
        public int CategoriesCount { get; set; } = 0;
        public int UsersCount { get; set; } = 0;
        public int ContactMessagesCount { get; set; } = 0;
        public int ServicesCount { get; set; } = 0;
        public int AnimatedGifsCount { get; set; } = 0;
    }

    #endregion

    #region دورة حياة المكون

    /// <summary>
    /// تهيئة المكون وتحميل البيانات
    /// </summary>
    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    #endregion

    #region تحميل البيانات

    /// <summary>
    /// تحميل بيانات لوحة التحكم
    /// </summary>
    private async Task LoadDashboardData()
    {
        try
        {
            _isLoading = true;
            StateHasChanged();

            // تحميل الإحصائيات بشكل متوازي
            var tasks = new List<Task>
            {
                LoadArticlesCount(),
                LoadCategoriesCount(),
                LoadUsersCount(),
                LoadContactMessagesCount(),
                LoadServicesCount(),
                LoadAnimatedGifsCount()
            };

            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    /// <summary>
    /// تحميل عدد المقالات
    /// </summary>
    private async Task LoadArticlesCount()
    {
        try
        {
            var response = await Http.GetAsync("api/Article");
            if (response.IsSuccessStatusCode)
            {
                var articles = await response.Content.ReadFromJsonAsync<List<ArticleDto>>();
                _statistics.ArticlesCount = articles?.Count ?? 0;
            }
        }
        catch (Exception)
        {
            // في حالة الخطأ، استخدم قيمة افتراضية
            _statistics.ArticlesCount = 0;
        }
    }

    /// <summary>
    /// تحميل عدد الأقسام
    /// </summary>
    private async Task LoadCategoriesCount()
    {
        try
        {
            var response = await Http.GetAsync("api/Categories");
            if (response.IsSuccessStatusCode)
            {
                var categories = await response.Content.ReadFromJsonAsync<List<CategoryDto>>();
                _statistics.CategoriesCount = categories?.Count ?? 0;
            }
        }
        catch (Exception)
        {
            _statistics.CategoriesCount = 0;
        }
    }

    /// <summary>
    /// تحميل عدد المستخدمين (قيمة تقديرية)
    /// </summary>
    private async Task LoadUsersCount()
    {
        try
        {
            // يمكن إضافة API endpoint للمستخدمين لاحقاً
            await Task.Delay(100); // محاكاة استدعاء API
            _statistics.UsersCount = 25; // قيمة تقديرية
        }
        catch (Exception)
        {
            _statistics.UsersCount = 0;
        }
    }

    /// <summary>
    /// تحميل عدد رسائل التواصل (قيمة تقديرية)
    /// </summary>
    private async Task LoadContactMessagesCount()
    {
        try
        {
            // يمكن إضافة API endpoint لرسائل التواصل لاحقاً
            await Task.Delay(100); // محاكاة استدعاء API
            _statistics.ContactMessagesCount = 8; // قيمة تقديرية
        }
        catch (Exception)
        {
            _statistics.ContactMessagesCount = 0;
        }
    }

    /// <summary>
    /// تحميل عدد الخدمات
    /// </summary>
    private async Task LoadServicesCount()
    {
        try
        {
            var response = await Http.GetAsync("api/Services");
            if (response.IsSuccessStatusCode)
            {
                var services = await response.Content.ReadFromJsonAsync<List<ServiceDto>>();
                _statistics.ServicesCount = services?.Count ?? 0;
            }
        }
        catch (Exception)
        {
            _statistics.ServicesCount = 0;
        }
    }

    /// <summary>
    /// تحميل عدد الصور المتحركة
    /// </summary>
    private async Task LoadAnimatedGifsCount()
    {
        try
        {
            var response = await Http.GetAsync("api/AnimatedGifs");
            if (response.IsSuccessStatusCode)
            {
                var gifs = await response.Content.ReadFromJsonAsync<List<AnimatedGifDto>>();
                _statistics.AnimatedGifsCount = gifs?.Count ?? 0;
            }
        }
        catch (Exception)
        {
            _statistics.AnimatedGifsCount = 0;
        }
    }

    #endregion

    #region الأحداث والتفاعلات

    /// <summary>
    /// الانتقال إلى صفحة محددة
    /// </summary>
    /// <param name="url">عنوان URL للصفحة المراد الانتقال إليها</param>
    /// <param name="forceLoad">ما إذا كان يجب إعادة تحميل الصفحة بالكامل</param>
    private void NavigateTo(string url, bool forceLoad = false)
    {
        Navigation.NavigateTo(url, forceLoad);
    }

    /// <summary>
    /// حساب قيمة التقدم للشريط التقدمي
    /// </summary>
    /// <param name="current">القيمة الحالية</param>
    /// <param name="max">القيمة القصوى</param>
    /// <returns>نسبة التقدم</returns>
    private double GetProgressValue(int current, int max)
    {
        if (max == 0) return 0;
        return Math.Min((double)current / max * 100, 100);
    }

    #endregion
}
