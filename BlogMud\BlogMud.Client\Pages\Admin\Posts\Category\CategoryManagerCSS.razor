﻿﻿<style>
    /* CategoryManager Component Scoped Styles */

/* إخفاء الجدول العادي في الشاشات الصغيرة */
.regular-table {
    display: block;
}

.mobile-cards {
    display: none;
}

/* عرض البطاقات في الشاشات الصغيرة */
@@media (max-width: 768px) {
    .regular-table {
        display: none;
    }

    .mobile-cards {
        display: block;
    }
}

/* تحسينات إضافية للجداول */
.regular-table .mud-table {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

/* تنسيق البطاقات للشاشات الصغيرة */
.mobile-cards {
    padding: 0 8px;
}

.mobile-card {
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 1px solid var(--mud-palette-divider);
    background: var(--mud-palette-surface);
}

.mobile-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* تنسيق عمود الترقيم */
.mud-table-cell:first-child {
    font-weight: 600;
    color: var(--mud-palette-primary);
    text-align: center;
    width: 60px;
    min-width: 60px;
}

/* تنسيق رقم القسم */
.category-number {
    font-weight: 600;
    background-color: var(--mud-palette-primary-lighten-4);
    color: var(--mud-palette-primary-darken-2);
}

/* تنسيق عنوان القسم */
.category-title {
    font-weight: 600;
    color: var(--mud-palette-text-primary);
    margin-bottom: 8px;
}

/* تنسيق وصف القسم */
.category-description {
    color: var(--mud-palette-text-secondary);
    line-height: 1.5;
    margin-bottom: 12px;
}

/* تنسيق معلومات القسم */
.category-info {
    direction: rtl;
}

.category-info .info-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    direction: rtl;
}

.category-info .info-icon {
    color: var(--mud-palette-primary);
    flex-shrink: 0;
}

.category-info .info-label {
    color: var(--mud-palette-text-secondary);
    font-weight: 500;
    flex-shrink: 0;
}

.category-info .info-value-chip {
    background-color: var(--mud-palette-primary-lighten-4);
    color: var(--mud-palette-primary-darken-2);
    font-weight: 600;
}

/* تنسيق أزرار الإجراءات */
.category-actions {
    display: flex;
    gap: 4px;
}

/* تحسينات خاصة بإدارة الأقسام */
.category-count-chip {
    background-color: var(--mud-palette-primary-lighten-4);
    color: var(--mud-palette-primary-darken-2);
}

.category-description {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* تحسينات للشاشات الصغيرة جداً */
@@media (max-width: 480px) {
    .mobile-cards {
        padding: 0 4px;
    }
    
    .mobile-card .mud-card-content {
        padding: 1rem !important;
    }
    
    .category-title {
        font-size: 1rem;
    }
    
    .category-description {
        font-size: 0.875rem;
    }
    
    .category-info .info-row {
        flex-wrap: wrap;
    }
    
    .category-info .info-label {
        min-width: 50px;
    }
}

/* دعم الوضع المظلم */
@@media (prefers-color-scheme: dark) {
    .mobile-card {
        background: var(--mud-palette-dark-surface);
        border-color: var(--mud-palette-dark-divider);
    }

    .category-title {
        color: var(--mud-palette-dark-text-primary);
    }

    .category-info .info-label {
        color: var(--mud-palette-dark-text-secondary);
    }
}

/* تحسينات الحركة */
@@media (prefers-reduced-motion: reduce) {
    .mobile-card {
        transition: none;
    }

    .mobile-card:hover {
        transform: none;
    }
}

/* تحسينات للطباعة */
@@media print {
    .mobile-cards {
        display: block !important;
    }

    .regular-table {
        display: none !important;
    }

    .mobile-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
        margin-bottom: 1rem;
    }

    .category-actions {
        display: none;
    }
}

</style>