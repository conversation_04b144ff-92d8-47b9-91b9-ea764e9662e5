﻿
using AutoMapper;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.IO;

namespace BlogMud.Controllers;

/// <summary>
/// وحدة التحكم بالمقالات - تدير عمليات إنشاء وقراءة وتحديث وحذف المقالات
/// </summary>
[Route("api/[controller]")]
[ApiController]
public class ArticleController : ControllerBase
{
    #region المتغيرات والمنشئ

    /// <summary>
    /// وحدة العمل المسؤولة عن إدارة المستودعات وحفظ التغييرات
    /// </summary>
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// مستودع المقالات المسؤول عن عمليات قراءة وكتابة بيانات المقالات
    /// </summary>
    private readonly IRepository<Article> _articleRepository;

    /// <summary>
    /// مسجل الأحداث لتسجيل معلومات التنفيذ والأخطاء
    /// </summary>
    private readonly ILogger<ArticleController> _logger;

    /// <summary>
    /// أداة التحويل بين كائنات النموذج وكائنات نقل البيانات
    /// </summary>
    private readonly IMapper _mapper;

    /// <summary>
    /// منشئ وحدة التحكم بالمقالات
    /// </summary>
    /// <param name="unitOfWork">وحدة العمل لإدارة المستودعات</param>
    /// <param name="logger">مسجل الأحداث لتسجيل المعلومات والأخطاء</param>
    /// <param name="mapper">أداة التحويل بين النماذج وكائنات نقل البيانات</param>
    public ArticleController(IUnitOfWork unitOfWork, ILogger<ArticleController> logger, IMapper mapper)
    {
        _unitOfWork = unitOfWork;
        _articleRepository = _unitOfWork.Repository<Article>();
        _logger = logger;
        _mapper = mapper;
    }

    #endregion

    #region عمليات القراءة (GET)

    /// <summary>
    /// استرجاع جميع المقالات مع الكيانات المرتبطة بها (العميل والتصنيف)
    /// </summary>
    /// <returns>قائمة بجميع المقالات مع بياناتها الكاملة</returns>
    /// <response code="200">تم استرجاع المقالات بنجاح</response>
    /// <response code="500">حدث خطأ أثناء استرجاع المقالات</response>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<ArticleDto>>> GetArticle()
    {
        try
        {
            _logger.LogInformation("Fetching all Articles with related entities");

            // تضمين العميل والتصنيف في الاستعلام
            var articles = await _articleRepository.GetAllAsync(
                includeProperties: "Client,Category"
            );

            // تحويل كائنات النموذج إلى كائنات نقل البيانات
            var articleDtos = _mapper.Map<IEnumerable<ArticleDto>>(articles);
            _logger.LogInformation("Retrieved {Count} Articles", articleDtos.Count());
            return Ok(articleDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving articles");
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    /// <summary>
    /// استرجاع مقالة محددة بواسطة المعرف مع الكيانات المرتبطة بها
    /// </summary>
    /// <param name="id">معرف المقالة المطلوبة</param>
    /// <returns>بيانات المقالة المطلوبة مع العميل والتصنيف المرتبطين بها</returns>
    /// <response code="200">تم استرجاع المقالة بنجاح</response>
    /// <response code="404">المقالة غير موجودة</response>
    /// <response code="500">حدث خطأ أثناء استرجاع المقالة</response>
    [HttpGet("{id}")]
    public async Task<ActionResult<ArticleDto>> GetArticle(int id)
    {
        try
        {
            _logger.LogInformation("Fetching article with ID: {ArticleId}", id);

            // استخدام GetFirstOrDefaultAsync مع تضمين الكيانات المرتبطة بدلاً من GetByIdAsync
            var article = await _articleRepository.GetFirstOrDefaultAsync(
                filter: a => a.Id == id,
                includeProperties: "Client,Category"
            );

            if (article == null)
            {
                _logger.LogWarning("Article not found with ID: {ArticleId}", id);
                return NotFound();
            }

            // تحويل كائن النموذج إلى كائن نقل البيانات
            var articleDto = _mapper.Map<ArticleDto>(article);
            return Ok(articleDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving article with ID: {ArticleId}", id);
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    /// <summary>
    /// استرجاع جميع صور الشرائح المرتبطة بمقالة محددة
    /// </summary>
    /// <param name="id">معرف المقالة المطلوبة</param>
    /// <returns>قائمة بمسارات صور الشرائح المرتبطة بالمقالة</returns>
    /// <response code="200">تم استرجاع صور الشرائح بنجاح</response>
    /// <response code="404">المقالة غير موجودة</response>
    /// <response code="500">حدث خطأ أثناء استرجاع صور الشرائح</response>
    [HttpGet("{id}/slider-images")]
    public async Task<ActionResult<List<string>>> GetSliderImages(int id)
    {
        try
        {
            _logger.LogInformation("Fetching slider images for article with ID: {ArticleId}", id);

            // التحقق من وجود المقالة
            var article = await _articleRepository.GetByIdAsync(id);
            if (article == null)
            {
                _logger.LogWarning("Article not found with ID: {ArticleId}", id);
                return NotFound();
            }

            // قائمة لتخزين مسارات الصور
            var sliderImages = new List<string>();

            // إضافة صورة العرض المتحرك الرئيسية إذا كانت موجودة
            if (!string.IsNullOrEmpty(article.ImageCarousel))
            {
                sliderImages.Add(article.ImageCarousel);
            }

            // إضافة الصورة الرئيسية إذا كانت موجودة
            if (!string.IsNullOrEmpty(article.ImageUrl))
            {
                sliderImages.Add(article.ImageUrl);
            }

            // البحث عن صور إضافية في مجلد Sider
            string uploadPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Sider");
            if (Directory.Exists(uploadPath))
            {
                // الحصول على جميع الملفات في مجلد Sider
                string[] files = Directory.GetFiles(uploadPath);

                // نمط بادئة المقالة
                string articlePrefix = $"article_{id}_";

                foreach (string file in files)
                {
                    string fileName = Path.GetFileName(file);

                    // التحقق مما إذا كان الملف ينتمي إلى هذه المقالة
                    if (fileName.StartsWith(articlePrefix))
                    {
                        // إضافة مسار الصورة إلى القائمة (مع تحويله إلى مسار URL)
                        string imageUrl = $"/Sider/{fileName}";

                        // تجنب تكرار الصور
                        if (!sliderImages.Contains(imageUrl))
                        {
                            sliderImages.Add(imageUrl);
                        }
                    }
                }
            }

            _logger.LogInformation("Retrieved {Count} slider images for article ID: {ArticleId}", sliderImages.Count, id);
            return Ok(sliderImages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving slider images for article with ID: {ArticleId}", id);
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    #endregion

    /// <summary>
    /// حذف جميع صور العرض المتحرك المرتبطة بمقالة
    /// </summary>
    /// <param name="id">معرف المقالة المراد حذف صور العرض المتحرك المرتبطة بها</param>
    /// <returns>استجابة بدون محتوى في حالة نجاح الحذف</returns>
    /// <response code="204">تم حذف صور العرض المتحرك بنجاح</response>
    /// <response code="401">المستخدم غير مصرح له</response>
    /// <response code="404">المقالة غير موجودة</response>
    /// <response code="500">حدث خطأ أثناء حذف صور العرض المتحرك</response>
    [HttpDelete("{id}/carousel-images")]
    [Authorize(Roles = "Admin")]
    public IActionResult DeleteCarouselImages(int id)
    {
        try
        {
            _logger.LogInformation("Deleting carousel images for article with ID: {ArticleId}", id);
            DeleteAllArticleCarouselImages(id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting carousel images for article with ID: {ArticleId}", id);
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    #region عمليات التعديل (PUT/POST/DELETE)

    /// <summary>
    /// تحديث مقالة موجودة بواسطة المعرف
    /// </summary>
    /// <param name="id">معرف المقالة المراد تحديثها</param>
    /// <param name="articleDto">بيانات المقالة المحدثة</param>
    /// <returns>استجابة بدون محتوى في حالة نجاح التحديث</returns>
    /// <response code="204">تم تحديث المقالة بنجاح</response>
    /// <response code="400">بيانات المقالة غير صالحة</response>
    /// <response code="401">المستخدم غير مصرح له</response>
    /// <response code="404">المقالة غير موجودة</response>
    /// <response code="500">حدث خطأ أثناء تحديث المقالة</response>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> PutArticle(int id, ArticleDto articleDto)
    {
        if (id != articleDto.Id)
        {
            return BadRequest("Article ID mismatch");
        }

        try
        {
            _logger.LogInformation("Updating article with ID: {ArticleId}", id);

            // التحقق من وجود المقالة
            var existingArticle = await _articleRepository.GetByIdAsync(id);
            if (existingArticle == null)
            {
                _logger.LogWarning("Article not found with ID: {ArticleId}", id);
                return NotFound();
            }

            // التأكد من وجود معرف تصنيف صالح
            if (articleDto.CategoryId <= 0)
            {
                return BadRequest("A valid CategoryId is required");
            }

            // التحقق من وجود التصنيف
            var categoryRepository = _unitOfWork.Repository<Category>();
            var category = await categoryRepository.GetByIdAsync(articleDto.CategoryId);

            if (category == null)
            {
                return BadRequest($"Category with ID {articleDto.CategoryId} not found");
            }

            // التأكد من وجود معرف عميل صالح
            if (articleDto.ClientId <= 0)
            {
                return BadRequest("A valid ClientId is required");
            }

            // التحقق من وجود العميل
            var clientRepository = _unitOfWork.Repository<Shared.Models.Client>();
            var client = await clientRepository.GetByIdAsync(articleDto.ClientId);

            if (client == null)
            {
                return BadRequest($"Client with ID {articleDto.ClientId} not found");
            }

            // حذف الملفات القديمة إذا تم تغييرها
            await DeleteChangedMediaFiles(existingArticle, articleDto);

            // تعيين قيم افتراضية للحقول المطلوبة إذا كانت مفقودة
            if (string.IsNullOrEmpty(articleDto.ImageUrl))
            {
                articleDto.ImageUrl = existingArticle.ImageUrl ?? "/images/default-article.jpg";
            }

            if (string.IsNullOrEmpty(articleDto.ImageCarousel))
            {
                articleDto.ImageCarousel = existingArticle.ImageCarousel ?? "/images/default-carousel.jpg";
            }

            // مسح خصائص الملاحة لمنع Entity Framework من محاولة إنشاء كيانات جديدة
            articleDto.Client = null;
            articleDto.Category = null;

            // تحديث المقالة الموجودة بالقيم من كائن نقل البيانات
            _mapper.Map(articleDto, existingArticle);
            existingArticle.LastModifiedAt = DateTime.Now;

            // التأكد من عدم إنشاء كيانات مرتبطة جديدة
            existingArticle.Category = null;
            existingArticle.Client = null;

            _articleRepository.Update(existingArticle);
            await _unitOfWork.SaveAsync();

            _logger.LogInformation("Article updated successfully with ID: {ArticleId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating article with ID: {ArticleId}", id);
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    /// <summary>
    /// إنشاء مقالة جديدة
    /// </summary>
    /// <param name="articleDto">بيانات المقالة الجديدة</param>
    /// <returns>المقالة التي تم إنشاؤها مع معرفها الجديد</returns>
    /// <response code="201">تم إنشاء المقالة بنجاح</response>
    /// <response code="400">بيانات المقالة غير صالحة</response>
    /// <response code="401">المستخدم غير مصرح له</response>
    /// <response code="500">حدث خطأ أثناء إنشاء المقالة</response>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ArticleDto>> PostArticle(ArticleDto articleDto)
    {
        try
        {
            _logger.LogInformation("Creating new article: {ArticleTitle}", articleDto.Title);

            // التأكد من وجود معرف تصنيف صالح
            if (articleDto.CategoryId <= 0)
            {
                return BadRequest("A valid CategoryId is required");
            }

            // التحقق من وجود التصنيف
            var categoryRepository = _unitOfWork.Repository<Category>();
            var category = await categoryRepository.GetByIdAsync(articleDto.CategoryId);

            if (category == null)
            {
                return BadRequest($"Category with ID {articleDto.CategoryId} not found");
            }

            // التأكد من وجود معرف عميل صالح
            if (articleDto.ClientId <= 0)
            {
                return BadRequest("A valid ClientId is required");
            }

            // التحقق من وجود العميل
            var clientRepository = _unitOfWork.Repository<Shared.Models.Client>();
            var client = await clientRepository.GetByIdAsync(articleDto.ClientId);

            if (client == null)
            {
                return BadRequest($"Client with ID {articleDto.ClientId} not found");
            }

            // تعيين قيم افتراضية للحقول المطلوبة إذا كانت مفقودة
            if (string.IsNullOrEmpty(articleDto.ImageUrl))
            {
                articleDto.ImageUrl = "/images/default-article.jpg";
            }

            if (string.IsNullOrEmpty(articleDto.ImageCarousel))
            {
                articleDto.ImageCarousel = "/images/default-carousel.jpg";
            }

            // مسح خصائص الملاحة لمنع Entity Framework من محاولة إنشاء كيانات جديدة
            articleDto.Client = null;
            articleDto.Category = null;

            // تحويل كائن نقل البيانات إلى كائن نموذج باستخدام AutoMapper
            var article = _mapper.Map<Article>(articleDto);
            article.CreatedAt = DateTime.Now;

            // التأكد من عدم إنشاء كيانات مرتبطة جديدة
            article.Category = null;
            article.Client = null;

            await _articleRepository.AddAsync(article);
            await _unitOfWork.SaveAsync();

            _logger.LogInformation("Article created successfully with ID: {ArticleId}", article.Id);

            // تحويل المقالة المنشأة مرة أخرى إلى كائن نقل بيانات باستخدام AutoMapper
            var createdArticleDto = _mapper.Map<ArticleDto>(article);

            return CreatedAtAction("GetArticle", new { id = createdArticleDto.Id }, createdArticleDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating article: {ArticleTitle}", articleDto.Title);
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    /// <summary>
    /// حذف مقالة بواسطة المعرف
    /// </summary>
    /// <param name="id">معرف المقالة المراد حذفها</param>
    /// <returns>استجابة بدون محتوى في حالة نجاح الحذف</returns>
    /// <response code="204">تم حذف المقالة بنجاح</response>
    /// <response code="401">المستخدم غير مصرح له</response>
    /// <response code="404">المقالة غير موجودة</response>
    /// <response code="500">حدث خطأ أثناء حذف المقالة</response>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> DeleteArticle(int id)
    {
        try
        {
            _logger.LogInformation("Deleting article with ID: {ArticleId}", id);

            // التحقق من وجود المقالة
            var article = await _articleRepository.GetByIdAsync(id);
            if (article == null)
            {
                _logger.LogWarning("Article not found with ID: {ArticleId}", id);
                return NotFound();
            }

            // حذف الملفات المرتبطة بالمقالة
            await DeleteAssociatedFiles(article);

            // حذف المقالة من قاعدة البيانات
            _articleRepository.Remove(article);
            await _unitOfWork.SaveAsync();

            _logger.LogInformation("Article deleted successfully with ID: {ArticleId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting article with ID: {ArticleId}", id);
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    #endregion

    #region دوال مساعدة لإدارة الملفات

    /// <summary>
    /// دالة مساعدة لحذف الملفات المرتبطة بمقالة
    /// </summary>
    /// <param name="article">كائن المقالة المراد حذف ملفاتها المرتبطة</param>
    /// <returns>مهمة غير متزامنة</returns>
    private async Task DeleteAssociatedFiles(Article article)
    {
        try
        {
            _logger.LogInformation("Starting deletion of associated files for article ID: {ArticleId}", article.Id);

            // حذف الصورة الرئيسية إذا كانت موجودة
            if (!string.IsNullOrEmpty(article.ImageUrl))
            {
                _logger.LogInformation("Deleting main image: {ImageUrl}", article.ImageUrl);
                DeleteFileIfExists(article.ImageUrl, "imeg");
            }

            // حذف صورة العرض المتحرك إذا كانت موجودة
            if (!string.IsNullOrEmpty(article.ImageCarousel))
            {
                _logger.LogInformation("Deleting carousel image: {ImageCarousel}", article.ImageCarousel);
                // حذف صورة العرض المتحرك الرئيسية
                DeleteFileIfExists(article.ImageCarousel, "Sider");

                // حذف جميع صور العرض المتحرك المرتبطة بنفس بادئة المقالة
                DeleteAllArticleCarouselImages(article.Id);
            }

            // حذف الفيديو إذا كان موجودًا
            if (!string.IsNullOrEmpty(article.VideoUrl))
            {
                _logger.LogInformation("Deleting video: {VideoUrl}", article.VideoUrl);
                DeleteFileIfExists(article.VideoUrl, "Video");
            }

            // التحقق مما إذا كان المحتوى يحتوي على علامات وسائط وحذف تلك الملفات
            if (!string.IsNullOrEmpty(article.Content))
            {
                _logger.LogInformation("Scanning article content for media files to delete");
                // حذف مقاطع الفيديو من المحتوى
                DeleteVideosFromContent(article.Content);

                // حذف الصور من المحتوى
                DeleteImagesFromContent(article.Content);
            }

            _logger.LogInformation("Completed deletion of associated files for article ID: {ArticleId}", article.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting associated files for article with ID: {ArticleId}", article.Id);
            // لا نريد إلقاء استثناء هنا، فقط تسجيل الخطأ والاستمرار في حذف المقالة
        }
    }

    /// <summary>
    /// دالة مساعدة لحذف جميع صور العرض المتحرك المرتبطة بمقالة
    /// </summary>
    /// <param name="articleId">معرف المقالة المراد حذف صور العرض المتحرك المرتبطة بها</param>
    private void DeleteAllArticleCarouselImages(int articleId)
    {
        try
        {
            // تحديد مسار مجلد صور العرض المتحرك
            string uploadPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Sider");
            if (!Directory.Exists(uploadPath))
                return;

            // الحصول على جميع الملفات في مجلد Sider
            string[] files = Directory.GetFiles(uploadPath);

            // أنماط بادئة المقالة
            string articlePrefix = $"article_{articleId}_";
            string sessionPrefix = $"session_"; // للتحميلات المؤقتة

            foreach (string file in files)
            {
                string fileName = Path.GetFileName(file);

                // التحقق مما إذا كان الملف ينتمي إلى هذه المقالة أو قد يكون تحميلًا مؤقتًا لهذه المقالة
                if (fileName.StartsWith(articlePrefix) ||
                    (fileName.StartsWith(sessionPrefix) && fileName.Contains($"_{articleId}_")))
                {
                    try
                    {
                        System.IO.File.Delete(file);
                        _logger.LogInformation("Deleted carousel image: {FilePath}", file);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error deleting carousel image: {FilePath}", file);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting carousel images for article with ID: {ArticleId}", articleId);
        }
    }

    /// <summary>
    /// دالة مساعدة لحذف ملف إذا كان موجودًا
    /// </summary>
    /// <param name="filePath">مسار الملف المراد حذفه (يمكن أن يكون URL كاملًا أو مسارًا نسبيًا)</param>
    /// <param name="folderName">اسم المجلد الذي يحتوي على الملف</param>
    private void DeleteFileIfExists(string filePath, string folderName)
    {
        try
        {
            _logger.LogInformation("Attempting to delete file from path: {FilePath} in folder: {FolderName}", filePath, folderName);

            // التعامل مع تنسيقات URL المختلفة
            // 1. URL كامل مع اسم النطاق: https://domain.com/folderName/filename.jpg
            // 2. URL نسبي مع شرطة مائلة في البداية: /folderName/filename.jpg
            // 3. URL نسبي بدون شرطة مائلة في البداية: folderName/filename.jpg
            // 4. اسم الملف فقط: filename.jpg

            string fileName;

            if (filePath.Contains($"/{folderName}/"))
            {
                // استخراج اسم الملف من مسارات مثل "/folderName/filename.jpg" أو "https://domain.com/folderName/filename.jpg"
                int folderIndex = filePath.LastIndexOf($"/{folderName}/") + folderName.Length + 2;
                if (folderIndex < filePath.Length)
                {
                    fileName = filePath.Substring(folderIndex);
                }
                else
                {
                    _logger.LogWarning("Invalid file path format: {FilePath}", filePath);
                    return;
                }
            }
            else
            {
                // استخدام اسم الملف كما هو
                fileName = Path.GetFileName(filePath);
            }

            if (string.IsNullOrEmpty(fileName))
            {
                _logger.LogWarning("Could not extract filename from path: {FilePath}", filePath);
                return;
            }

            // بناء المسار الفعلي الكامل
            string fullPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", folderName, fileName);
            _logger.LogInformation("Resolved file path for deletion: {FullPath}", fullPath);

            if (System.IO.File.Exists(fullPath))
            {
                System.IO.File.Delete(fullPath);
                _logger.LogInformation("Successfully deleted file: {FilePath}", fullPath);
            }
            else
            {
                _logger.LogWarning("File not found for deletion: {FilePath}", fullPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file: {FilePath} in folder: {FolderName}", filePath, folderName);
        }
    }

    /// <summary>
    /// دالة مساعدة لحذف مقاطع الفيديو من محتوى المقالة
    /// </summary>
    /// <param name="content">محتوى المقالة الذي قد يحتوي على علامات فيديو</param>
    private void DeleteVideosFromContent(string content)
    {
        try
        {
            int deletedCount = 0;

            // البحث عن جميع علامات مصدر الفيديو في المحتوى
            var sourceRegex = new System.Text.RegularExpressions.Regex(@"<source\s+src=""([^""]+)""[^>]*>");
            var matches = sourceRegex.Matches(content);

            foreach (System.Text.RegularExpressions.Match match in matches)
            {
                if (match.Success && match.Groups.Count > 1)
                {
                    string videoPath = match.Groups[1].Value;
                    if (!string.IsNullOrEmpty(videoPath) && videoPath.Contains("/Video/"))
                    {
                        _logger.LogInformation("Found video source tag with path: {VideoPath}", videoPath);
                        DeleteFileIfExists(videoPath, "Video");
                        deletedCount++;
                    }
                }
            }

            // البحث أيضًا عن علامات الفيديو مع سمة src مباشرة
            var videoRegex = new System.Text.RegularExpressions.Regex(@"<video[^>]*\ssrc=""([^""]+)""[^>]*>");
            matches = videoRegex.Matches(content);

            foreach (System.Text.RegularExpressions.Match match in matches)
            {
                if (match.Success && match.Groups.Count > 1)
                {
                    string videoPath = match.Groups[1].Value;
                    if (!string.IsNullOrEmpty(videoPath) && videoPath.Contains("/Video/"))
                    {
                        _logger.LogInformation("Found video tag with src attribute: {VideoPath}", videoPath);
                        DeleteFileIfExists(videoPath, "Video");
                        deletedCount++;
                    }
                }
            }

            _logger.LogInformation("Deleted {Count} videos from article content", deletedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting and deleting videos from content");
        }
    }

    /// <summary>
    /// دالة مساعدة لحذف الصور من محتوى المقالة
    /// </summary>
    /// <param name="content">محتوى المقالة الذي قد يحتوي على علامات صور</param>
    private void DeleteImagesFromContent(string content)
    {
        try
        {
            int deletedCount = 0;

            // البحث عن جميع علامات الصور في المحتوى
            var imgRegex = new System.Text.RegularExpressions.Regex(@"<img[^>]*\ssrc=""([^""]+)""[^>]*>");
            var matches = imgRegex.Matches(content);

            foreach (System.Text.RegularExpressions.Match match in matches)
            {
                if (match.Success && match.Groups.Count > 1)
                {
                    string imagePath = match.Groups[1].Value;
                    if (!string.IsNullOrEmpty(imagePath))
                    {
                        // تحديد المجلد الذي توجد فيه الصورة
                        if (imagePath.Contains("/imeg/"))
                        {
                            _logger.LogInformation("Found image tag with path in imeg folder: {ImagePath}", imagePath);
                            DeleteFileIfExists(imagePath, "imeg");
                            deletedCount++;
                        }
                        else if (imagePath.Contains("/Sider/"))
                        {
                            _logger.LogInformation("Found image tag with path in Sider folder: {ImagePath}", imagePath);
                            DeleteFileIfExists(imagePath, "Sider");
                            deletedCount++;
                        }
                    }
                }
            }

            // البحث أيضًا عن صور الخلفية في سمات النمط
            var styleRegex = new System.Text.RegularExpressions.Regex(@"style=""[^""]*background-image:\s*url\(['""]?([^'""]+)['""]?\)[^""]*""");
            matches = styleRegex.Matches(content);

            foreach (System.Text.RegularExpressions.Match match in matches)
            {
                if (match.Success && match.Groups.Count > 1)
                {
                    string imagePath = match.Groups[1].Value;
                    if (!string.IsNullOrEmpty(imagePath))
                    {
                        // تحديد المجلد الذي توجد فيه الصورة
                        if (imagePath.Contains("/imeg/"))
                        {
                            _logger.LogInformation("Found background image in style with path: {ImagePath}", imagePath);
                            DeleteFileIfExists(imagePath, "imeg");
                            deletedCount++;
                        }
                        else if (imagePath.Contains("/Sider/"))
                        {
                            _logger.LogInformation("Found background image in style with path: {ImagePath}", imagePath);
                            DeleteFileIfExists(imagePath, "Sider");
                            deletedCount++;
                        }
                    }
                }
            }

            _logger.LogInformation("Deleted {Count} images from article content", deletedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting and deleting images from content");
        }
    }

    /// <summary>
    /// دالة مساعدة لحذف ملفات الوسائط القديمة إذا تم تغييرها
    /// </summary>
    /// <param name="existingArticle">المقالة الموجودة حاليًا</param>
    /// <param name="updatedArticleDto">بيانات المقالة المحدثة</param>
    /// <returns>مهمة غير متزامنة</returns>
    private async Task DeleteChangedMediaFiles(Article existingArticle, ArticleDto updatedArticleDto)
    {
        try
        {
            _logger.LogInformation("Checking for media files to delete for article ID: {ArticleId}", existingArticle.Id);

            // حذف الصورة الرئيسية القديمة إذا تم تغييرها
            if (!string.IsNullOrEmpty(existingArticle.ImageUrl) &&
                !string.IsNullOrEmpty(updatedArticleDto.ImageUrl) &&
                existingArticle.ImageUrl != updatedArticleDto.ImageUrl &&
                !existingArticle.ImageUrl.Contains("default-article.jpg"))
            {
                _logger.LogInformation("Deleting old main image: {ImageUrl}", existingArticle.ImageUrl);
                DeleteFileIfExists(existingArticle.ImageUrl, "imeg");
            }

            // حذف صورة العرض المتحرك القديمة إذا تم تغييرها
            if (!string.IsNullOrEmpty(existingArticle.ImageCarousel) &&
                !string.IsNullOrEmpty(updatedArticleDto.ImageCarousel) &&
                existingArticle.ImageCarousel != updatedArticleDto.ImageCarousel &&
                !existingArticle.ImageCarousel.Contains("default-carousel.jpg"))
            {
                _logger.LogInformation("Deleting old carousel image: {ImageCarousel}", existingArticle.ImageCarousel);
                DeleteFileIfExists(existingArticle.ImageCarousel, "Sider");
            }

            // حذف الفيديو القديم إذا تم تغييره
            if (!string.IsNullOrEmpty(existingArticle.VideoUrl) &&
                !string.IsNullOrEmpty(updatedArticleDto.VideoUrl) &&
                existingArticle.VideoUrl != updatedArticleDto.VideoUrl)
            {
                _logger.LogInformation("Deleting old video: {VideoUrl}", existingArticle.VideoUrl);
                DeleteFileIfExists(existingArticle.VideoUrl, "Video");
            }

            // إذا تم تغيير المحتوى، نتحقق من الوسائط المضمنة التي تم إزالتها
            if (!string.IsNullOrEmpty(existingArticle.Content) &&
                !string.IsNullOrEmpty(updatedArticleDto.Content) &&
                existingArticle.Content != updatedArticleDto.Content)
            {
                _logger.LogInformation("Content changed, checking for removed media files");

                // استخراج جميع مسارات الوسائط من المحتوى القديم والجديد
                var oldMediaPaths = ExtractMediaPaths(existingArticle.Content);
                var newMediaPaths = ExtractMediaPaths(updatedArticleDto.Content);

                // حذف الوسائط التي كانت موجودة في المحتوى القديم ولكن ليست في المحتوى الجديد
                foreach (var mediaPath in oldMediaPaths)
                {
                    if (!newMediaPaths.Contains(mediaPath))
                    {
                        _logger.LogInformation("Deleting removed media file: {MediaPath}", mediaPath);

                        if (mediaPath.Contains("/imeg/"))
                        {
                            DeleteFileIfExists(mediaPath, "imeg");
                        }
                        else if (mediaPath.Contains("/Sider/"))
                        {
                            DeleteFileIfExists(mediaPath, "Sider");
                        }
                        else if (mediaPath.Contains("/Video/"))
                        {
                            DeleteFileIfExists(mediaPath, "Video");
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting old media files for article with ID: {ArticleId}", existingArticle.Id);
            // لا نريد إلقاء استثناء هنا، فقط تسجيل الخطأ والاستمرار في تحديث المقالة
        }
    }

    /// <summary>
    /// دالة مساعدة لاستخراج مسارات الوسائط من محتوى المقالة
    /// </summary>
    /// <param name="content">محتوى المقالة</param>
    /// <returns>قائمة بمسارات الوسائط المستخرجة</returns>
    private List<string> ExtractMediaPaths(string content)
    {
        var mediaPaths = new List<string>();

        if (string.IsNullOrEmpty(content))
            return mediaPaths;

        try
        {
            // استخراج مسارات الصور من علامات img
            var imgRegex = new System.Text.RegularExpressions.Regex(@"<img[^>]*\ssrc=""([^""]+)""[^>]*>");
            var imgMatches = imgRegex.Matches(content);

            foreach (System.Text.RegularExpressions.Match match in imgMatches)
            {
                if (match.Success && match.Groups.Count > 1)
                {
                    string imagePath = match.Groups[1].Value;
                    if (!string.IsNullOrEmpty(imagePath) &&
                        (imagePath.Contains("/imeg/") || imagePath.Contains("/Sider/")))
                    {
                        mediaPaths.Add(imagePath);
                    }
                }
            }

            // استخراج مسارات الصور من خلفيات CSS
            var styleRegex = new System.Text.RegularExpressions.Regex(@"style=""[^""]*background-image:\s*url\(['""]?([^'""]+)['""]?\)[^""]*""");
            var styleMatches = styleRegex.Matches(content);

            foreach (System.Text.RegularExpressions.Match match in styleMatches)
            {
                if (match.Success && match.Groups.Count > 1)
                {
                    string imagePath = match.Groups[1].Value;
                    if (!string.IsNullOrEmpty(imagePath) &&
                        (imagePath.Contains("/imeg/") || imagePath.Contains("/Sider/")))
                    {
                        mediaPaths.Add(imagePath);
                    }
                }
            }

            // استخراج مسارات الفيديو من علامات source
            var sourceRegex = new System.Text.RegularExpressions.Regex(@"<source\s+src=""([^""]+)""[^>]*>");
            var sourceMatches = sourceRegex.Matches(content);

            foreach (System.Text.RegularExpressions.Match match in sourceMatches)
            {
                if (match.Success && match.Groups.Count > 1)
                {
                    string videoPath = match.Groups[1].Value;
                    if (!string.IsNullOrEmpty(videoPath) && videoPath.Contains("/Video/"))
                    {
                        mediaPaths.Add(videoPath);
                    }
                }
            }

            // استخراج مسارات الفيديو من علامات video
            var videoRegex = new System.Text.RegularExpressions.Regex(@"<video[^>]*\ssrc=""([^""]+)""[^>]*>");
            var videoMatches = videoRegex.Matches(content);

            foreach (System.Text.RegularExpressions.Match match in videoMatches)
            {
                if (match.Success && match.Groups.Count > 1)
                {
                    string videoPath = match.Groups[1].Value;
                    if (!string.IsNullOrEmpty(videoPath) && videoPath.Contains("/Video/"))
                    {
                        mediaPaths.Add(videoPath);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting media paths from content");
        }

        return mediaPaths;
    }

    #endregion
}
