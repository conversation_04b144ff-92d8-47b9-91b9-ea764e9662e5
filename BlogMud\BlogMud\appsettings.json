{
  "ConnectionStrings": {
    // on windos "DefaultConnection": "Server=db18176.public.databaseasp.net; Database=db18176; User Id=db18176; Password=************; Encrypt=False; MultipleActiveResultSets=True;",
    "LocalConnection": "Server=localhost;Database=MudBlog;User Id=sa;Password=*********;Encrypt=False;MultipleActiveResultSets=True;TrustServerCertificate=True;",
    "DefaultConnection": "Server=localhost;Database=MudBlog;User Id=sa;Password=*********;Encrypt=False;MultipleActiveResultSets=True;TrustServerCertificate=True;",
    "Server": "Server=db18176.public.databaseasp.net; Database=db18176; User Id=db18176; Password=************; Encrypt=False; MultipleActiveResultSets=True;"
  },

  "BaseAddress": "http://ahmedakear.runasp.net",
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderName": "BlogMud System",
    "Username": "<EMAIL>",
    "Password": "shvn snjn yzon ngvf",
    "EnableSsl": true
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}

