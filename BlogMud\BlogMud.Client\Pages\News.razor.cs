using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using System.Net.Http.Json;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;

namespace BlogMud.Client.Pages
{
    public partial class News : ComponentBase
    {
        private CompanyInfo _companyInfo;
        private IEnumerable<Article> _allArticles = new List<Article>();
        private IEnumerable<Article> _articles = new List<Article>();
        private List<string> _categories = new List<string>();
        private List<NewsSiderMoveDto> _newsSlideshows = new();
        private bool _loading = true;
        private bool _slideshowsLoading = true;

        private string _searchText = "";
        private string _selectedCategory = "";
        private int _currentPage = 1;
        private int _pageSize = 9;
        private int _totalItemsCount = 0;

        private string _subscribeEmail = "";

        protected override async Task OnInitializedAsync()
        {
            try
            {
                // Load company info, articles, and news slideshows concurrently
                var companyInfoTask = LoadCompanyInfo();
                var articlesTask = LoadArticles();
                var slideshowsTask = LoadNewsSlideshows();

                await Task.WhenAll(companyInfoTask, articlesTask, slideshowsTask);
            }
            catch (Exception ex)
            {
                // Handle error if needed
                Console.WriteLine($"Error loading page data: {ex.Message}");
            }
            finally
            {
                _loading = false;
                _slideshowsLoading = false;
                StateHasChanged();
            }

            await base.OnInitializedAsync();
        }

        private async Task LoadCompanyInfo()
        {
            try
            {
                _companyInfo = await Http.GetFromJsonAsync<CompanyInfo>("/api/CompanyInfo/1");
            }
            catch
            {
                // Si no hay un endpoint para CompanyInfo, usar datos por defecto
                _companyInfo = new CompanyInfo
                {
                    Name = "شركتنا",
                    Vision = "أن نكون الشركة الرائدة في مجال عملنا",
                    Mission = "توفير خدمات عالية الجودة لعملائنا",
                    AboutUs = "نحن شركة رائدة في مجال تكنولوجيا المعلومات.",
                    Phone = "+966123456789",
                    Email = "<EMAIL>",
                    Address = "الرياض، المملكة العربية السعودية"
                };
            }
        }

        private async Task LoadNewsSlideshows()
        {
            try
            {
                _newsSlideshows = await Http.GetFromJsonAsync<List<NewsSiderMoveDto>>("api/NewsSiderMove/active") ?? new List<NewsSiderMoveDto>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading news slideshows: {ex.Message}");
                _newsSlideshows = new List<NewsSiderMoveDto>();
            }
        }

        private async Task LoadArticles()
        {
            try
            {
                // Intentar obtener artículos usando una API
                _allArticles = await Http.GetFromJsonAsync<List<Article>>("/api/Article");

                // Filtrar artículos publicados
                _allArticles = _allArticles.Where(a => a.IsPublished).ToList();
            }
            catch (Exception)
            {
                // Si no hay un endpoint para Articles, usar datos de muestra
                _allArticles = new List<Article>
                {
                    new Article
                    {
                        Id = 1,
                        Title = "مقال تجريبي 1",
                        Content = "محتوى المقال التجريبي الأول لموقع الشركة، يحتوي على معلومات مهمة عن آخر الأخبار والتطورات.",
                        ImageUrl = "/images/article1.jpg",
                        PublishDate = DateTime.Now.AddDays(-1),
                        // Category = "أخبار الشركة",
                        IsPublished = true
                    },
                    new Article
                    {
                        Id = 2,
                        Title = "مقال تجريبي 2",
                        Content = "محتوى المقال التجريبي الثاني لموقع الشركة، يحتوي على معلومات مهمة عن آخر المشاريع.",
                        ImageUrl = "/images/article2.jpg",
                        PublishDate = DateTime.Now.AddDays(-2),
                        // Category = "مشاريع",
                        IsPublished = true
                    },
                    new Article
                    {
                        Id = 3,
                        Title = "مقال تجريبي 3",
                        Content = "محتوى المقال التجريبي الثالث لموقع الشركة، يحتوي على معلومات مهمة عن الخدمات الجديدة.",
                        ImageUrl = "/images/article3.jpg",
                        PublishDate = DateTime.Now.AddDays(-3),
                        // Category = "خدمات",
                        IsPublished = true
                    },
                    new Article
                    {
                        Id = 4,
                        Title = "مقال تجريبي 4",
                        Content = "محتوى المقال التجريبي الرابع لموقع الشركة، يحتوي على معلومات مهمة عن المنتجات الجديدة.",
                        ImageUrl = "/images/article4.jpg",
                        PublishDate = DateTime.Now.AddDays(-4),
                        // Category = "منتجات",
                        IsPublished = true
                    }
                };
            }

            // Extract unique categories
            // _categories = _allArticles
            //     .Select(a => a.Category)
            //     .Where(c => !string.IsNullOrEmpty(c))
            //     .Distinct()
            //     .OrderBy(c => c)
            //     .ToList();

            // Apply filters
            FilterArticles();
        }

        private void FilterArticles()
        {
            var filteredArticles = _allArticles;

            // Apply category filter
            // if (!string.IsNullOrEmpty(_selectedCategory))
            // {
            //     filteredArticles = filteredArticles.Where(a => a.Category == _selectedCategory);
            // }

            // Apply search filter
            if (!string.IsNullOrEmpty(_searchText))
            {
                var searchLower = _searchText.ToLower();
                filteredArticles = filteredArticles.Where(a =>
                    a.Title.ToLower().Contains(searchLower) ||
                    a.Content.ToLower().Contains(searchLower));
            }

            // Get total count
            _totalItemsCount = filteredArticles.Count();

            // Apply pagination
            int skip = (_currentPage - 1) * _pageSize;
            _articles = filteredArticles.Skip(skip).Take(_pageSize).ToList();
        }

        private void HandleSearch()
        {
            _currentPage = 1;
            FilterArticles();
        }

        private void HandleKeyDown(KeyboardEventArgs args)
        {
            if (args.Key == "Enter")
            {
                HandleSearch();
            }
        }

        private void NavigateToArticle(int id)
        {
            _Navigation.NavigateTo($"/news/{id}");
        }

        private string GetArticleSummary(string content)
        {
            if (string.IsNullOrEmpty(content))
                return string.Empty;

            // Create a summary of the article (first 150 characters)
            var summary = content.Length > 150 ? content.Substring(0, 150) + "..." : content;
            return summary;
        }

        private async Task HandleSubscribe()
        {
            if (string.IsNullOrEmpty(_subscribeEmail) || !_subscribeEmail.Contains("@"))
            {
                _Snackbar.Add("يرجى إدخال بريد إلكتروني صحيح", MudBlazor.Severity.Error);
                return;
            }

            try
            {
                var response = await Http.PostAsJsonAsync("/newsletter/subscribe", new { Email = _subscribeEmail });

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<dynamic>();
                    _subscribeEmail = "";
                    _Snackbar.Add("تم الاشتراك بنجاح! يرجى التحقق من بريدك الإلكتروني لتأكيد الاشتراك.", MudBlazor.Severity.Success);
                }
                else
                {
                    var error = await response.Content.ReadFromJsonAsync<dynamic>();
                    _Snackbar.Add("البريد الإلكتروني مشترك بالفعل أو حدث خطأ.", MudBlazor.Severity.Warning);
                }
            }
            catch (Exception ex)
            {
                _Snackbar.Add("حدث خطأ أثناء الاشتراك. يرجى المحاولة مرة أخرى.", MudBlazor.Severity.Error);
            }
        }
    }
}
