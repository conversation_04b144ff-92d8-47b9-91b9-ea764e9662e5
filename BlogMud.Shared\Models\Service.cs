using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    /// <summary>
    /// نموذج الخدمات التي تقدمها الشركة
    /// </summary>
    public class Service
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// عنوان الخدمة
        /// </summary>
        [Required(ErrorMessage = "عنوان الخدمة مطلوب")]
        [StringLength(100, ErrorMessage = "يجب أن يكون عنوان الخدمة بين {2} و {1} حرفًا", MinimumLength = 2)]
        public string Title { get; set; }

        /// <summary>
        /// الوصف الأساسي للخدمة
        /// </summary>
        [Required(ErrorMessage = "الوصف الأساسي مطلوب")]
        [StringLength(500, ErrorMessage = "يجب أن يكون الوصف الأساسي بين {2} و {1} حرفًا", MinimumLength = 10)]
        public string PrimaryDescription { get; set; }

        /// <summary>
        /// الوصف التفصيلي للخدمة
        /// </summary>
        [StringLength(1000, ErrorMessage = "يجب أن يكون الوصف التفصيلي أقل من {1} حرفًا")]
        public string? DetailedDescription { get; set; }

        /// <summary>
        /// الصورة الرئيسية للخدمة
        /// </summary>
        [Required(ErrorMessage = "الصورة الرئيسية مطلوبة")]
        [StringLength(255, ErrorMessage = "مسار الصورة الرئيسية طويل جداً")]
        public string MainImageUrl { get; set; }

        /// <summary>
        /// شرائح الصور للخدمة (مفصولة بفواصل)
        /// </summary>
        [StringLength(1000, ErrorMessage = "مسارات شرائح الصور طويلة جداً")]
        public string? ImageCarousel { get; set; }

        /// <summary>
        /// رابط الفيديو الاختياري للخدمة
        /// </summary>
        [StringLength(255, ErrorMessage = "رابط الفيديو طويل جداً")]
        public string? VideoUrl { get; set; }

        /// <summary>
        /// مميزات الخدمة (مفصولة بفواصل منقوطة)
        /// </summary>
        [StringLength(2000, ErrorMessage = "قائمة المميزات طويلة جداً")]
        public string? Features { get; set; }

        /// <summary>
        /// ترتيب عرض الخدمة
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// حالة الخدمة (نشطة أو غير نشطة)
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ إنشاء الخدمة
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل للخدمة
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }
    }
}
