using BlogMud.Shared.DTOs;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using MudBlazor;
using System.Net.Http.Headers;

namespace BlogMud.Client.Pages.Admin.Posts.AboutPage.SiderAboutUs
{
    /// <summary>
    /// نموذج تعديل الشرائح المتحركة لصفحة "من نحن"
    /// </summary>
    public partial class SiderAboutUsEditForm : ComponentBase
    {
        #region الحقول والخصائص

        /// <summary>
        /// مرجع إلى نموذج MudForm المستخدم للتحقق من صحة البيانات
        /// </summary>
        private MudForm _form;

        /// <summary>
        /// مؤشر على حالة رفع الصور
        /// </summary>
        private bool _imageUploading = false;

        /// <summary>
        /// مؤشر على صحة النموذج
        /// </summary>
        private bool _success = true;

        /// <summary>
        /// فهرس التبويب النشط (0 = المعلومات الأساسية، 1 = الوسائط، 2 = الإعدادات)
        /// </summary>
        private int _activeTabIndex = 0;

        /// <summary>
        /// مثيل مربع الحوار MudDialog الذي يتم تمريره من خلال CascadingParameter
        /// </summary>
        [CascadingParameter] public required IMudDialogInstance MudDialog { get; set; }

        /// <summary>
        /// بيانات شريحة "من نحن" التي يتم تعديلها أو إنشاؤها
        /// </summary>
        [Parameter]
        public SiderAboutUsDto SiderAboutUs { get; set; } = new();

        /// <summary>
        /// مؤشر على ما إذا كان الوضع هو إنشاء شريحة جديدة أم تعديل موجودة
        /// </summary>
        [Parameter]
        public bool IsCreateMode { get; set; } = false;

        /// <summary>
        /// مؤشر على ما إذا كان الوضع هو عرض فقط
        /// </summary>
        [Parameter]
        public bool IsViewMode { get; set; } = false;

        /// <summary>
        /// خدمة عرض الرسائل
        /// </summary>
        [Inject] public ISnackbar Snackbar { get; set; } = default!;

        #endregion

        #region دوال مساعدة

        /// <summary>
        /// الحصول على عنوان مربع الحوار
        /// </summary>
        /// <returns>عنوان مربع الحوار</returns>
        private string GetDialogTitle()
        {
            if (IsViewMode)
                return "عرض تفاصيل الشريحة";
            else if (IsCreateMode)
                return "إضافة شريحة جديدة";
            else
                return "تعديل الشريحة";
        }

        /// <summary>
        /// الحصول على اسم الملف من الرابط
        /// </summary>
        /// <param name="url">رابط الملف</param>
        /// <returns>اسم الملف</returns>
        private string GetFileName(string url)
        {
            if (string.IsNullOrEmpty(url))
                return "";

            try
            {
                var uri = new Uri(url);
                return Path.GetFileName(uri.LocalPath);
            }
            catch
            {
                return "ملف غير معروف";
            }
        }

        #endregion

        #region دوال التحقق من صحة البيانات

        /// <summary>
        /// التحقق من صحة النموذج
        /// </summary>
        /// <param name="model">النموذج المراد التحقق منه</param>
        /// <returns>قائمة برسائل الخطأ</returns>
        private IEnumerable<string> ValidateModel(object model)
        {
            var errors = new List<string>();

            if (model is SiderAboutUsDto siderAboutUs)
            {
                if (string.IsNullOrWhiteSpace(siderAboutUs.Title))
                {
                    errors.Add("عنوان الشريحة مطلوب");
                }

                if (siderAboutUs.Title?.Length > 100)
                {
                    errors.Add("عنوان الشريحة يجب أن يكون أقل من 100 حرف");
                }

                if (siderAboutUs.Description?.Length > 500)
                {
                    errors.Add("وصف الشريحة يجب أن يكون أقل من 500 حرف");
                }

                if (string.IsNullOrWhiteSpace(siderAboutUs.ImageUrls))
                {
                    errors.Add("يجب رفع صورة واحدة على الأقل");
                }

                if (siderAboutUs.Duration < 1 || siderAboutUs.Duration > 60)
                {
                    errors.Add("مدة العرض يجب أن تكون بين 1 و 60 ثانية");
                }

                if (siderAboutUs.LinkUrl?.Length > 255)
                {
                    errors.Add("رابط الانتقال يجب أن يكون أقل من 255 حرف");
                }

                if (siderAboutUs.LinkText?.Length > 100)
                {
                    errors.Add("نص الرابط يجب أن يكون أقل من 100 حرف");
                }

                if (siderAboutUs.Notes?.Length > 500)
                {
                    errors.Add("الملاحظات يجب أن تكون أقل من 500 حرف");
                }
            }

            return errors;
        }

        /// <summary>
        /// التحقق من صحة النموذج
        /// </summary>
        /// <returns>true إذا كان النموذج صحيحاً</returns>
        private bool IsFormValid()
        {
            if (IsViewMode) return false;

            var errors = ValidateModel(SiderAboutUs);
            return !errors.Any();
        }

        #endregion

        #region دوال إدارة النموذج

        /// <summary>
        /// حفظ بيانات الشريحة
        /// </summary>
        private async Task Submit()
        {
            if (_form != null)
                await _form.Validate();
            if (!_success) return;

            try
            {
                // التحقق من الحقول المطلوبة
                if (string.IsNullOrEmpty(SiderAboutUs.Title))
                {
                    Snackbar.Add("يجب إدخال عنوان الشريحة", Severity.Error);
                    _activeTabIndex = 0; // الانتقال إلى تبويب المعلومات الأساسية
                    return;
                }

                if (string.IsNullOrEmpty(SiderAboutUs.ImageUrls))
                {
                    Snackbar.Add("يجب إضافة صور للشريحة", Severity.Error);
                    _activeTabIndex = 1; // الانتقال إلى تبويب الوسائط
                    return;
                }

                // التحقق من طول العنوان
                if (SiderAboutUs.Title.Length > 100)
                {
                    Snackbar.Add("يجب أن لا يتجاوز عنوان الشريحة 100 حرف", Severity.Error);
                    _activeTabIndex = 0;
                    return;
                }

                // التحقق من صحة الرابط إذا تم إدخاله
                if (!string.IsNullOrEmpty(SiderAboutUs.LinkUrl))
                {
                    if (!Uri.TryCreate(SiderAboutUs.LinkUrl, UriKind.Absolute, out _))
                    {
                        Snackbar.Add("الرابط المدخل غير صحيح", Severity.Error);
                        _activeTabIndex = 2; // الانتقال إلى تبويب الإعدادات
                        return;
                    }
                }

                MudDialog.Close(DialogResult.Ok(SiderAboutUs));
            }
            catch (Exception ex)
            {
                Snackbar.Add($"حدث خطأ أثناء حفظ الشريحة: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// إلغاء النموذج
        /// </summary>
        private void Cancel()
        {
            MudDialog.Cancel();
        }

        #endregion

        #region دوال إدارة الصور

        /// <summary>
        /// رفع صور متعددة
        /// </summary>
        /// <param name="files">قائمة الملفات المراد رفعها</param>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task UploadMultipleImages(IReadOnlyList<IBrowserFile> files)
        {
            if (files == null || files.Count == 0 || IsViewMode)
                return;

            try
            {
                _imageUploading = true;
                StateHasChanged();

                var uploadedUrls = new List<string>();
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
                const long maxFileSize = 5 * 1024 * 1024; // 5MB

                // التحقق من عدد الصور الحالية + الجديدة
                var currentImagesCount = SiderAboutUs.Images.Count();
                if (currentImagesCount + files.Count > 10)
                {
                    Snackbar.Add($"لا يمكن رفع أكثر من 10 صور. لديك حالياً {currentImagesCount} صورة", Severity.Warning);
                    return;
                }

                foreach (var file in files)
                {
                    // Validate file size
                    if (file.Size > maxFileSize)
                    {
                        Snackbar.Add($"الملف {file.Name} كبير جداً. الحد الأقصى 5 ميجابايت", Severity.Warning);
                        continue;
                    }

                    // Validate file extension
                    var extension = Path.GetExtension(file.Name).ToLowerInvariant();
                    if (!allowedExtensions.Contains(extension))
                    {
                        Snackbar.Add($"نوع الملف {file.Name} غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WebP", Severity.Warning);
                        continue;
                    }

                    // Create form data
                    using var content = new MultipartFormDataContent();
                    using var fileStream = file.OpenReadStream(maxFileSize);
                    using var streamContent = new StreamContent(fileStream);
                    content.Add(streamContent, "file", file.Name);

                    // Upload file to the specific directory for SiderAboutUsImg
                    var response = await Http.PostAsync("api/upload/sider-aboutus-slideshow", content);

                    if (response.IsSuccessStatusCode)
                    {
                        var result = await response.Content.ReadAsStringAsync();
                        uploadedUrls.Add(result.Trim('"')); // Remove quotes from JSON string
                    }
                    else
                    {
                        var error = await response.Content.ReadAsStringAsync();
                        Snackbar.Add($"خطأ في رفع الصورة {file.Name}: {error}", Severity.Warning);
                    }
                }

                if (uploadedUrls.Any())
                {
                    // Append to existing images
                    var existingImages = SiderAboutUs.Images.ToList();
                    existingImages.AddRange(uploadedUrls);
                    SiderAboutUs.ImageUrls = string.Join(";", existingImages);

                    Snackbar.Add($"تم رفع {uploadedUrls.Count} صورة بنجاح", Severity.Success);
                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"خطأ في رفع الصور: {ex.Message}", Severity.Error);
            }
            finally
            {
                _imageUploading = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// حذف صورة من قائمة الصور المرفوعة
        /// </summary>
        /// <param name="index">فهرس الصورة المراد حذفها</param>
        private void RemoveImage(int index)
        {
            try
            {
                var images = SiderAboutUs.Images.ToList();
                if (index >= 0 && index < images.Count)
                {
                    var removedImageUrl = images[index];
                    images.RemoveAt(index);
                    SiderAboutUs.ImageUrls = string.Join(";", images);
                    StateHasChanged();
                    Snackbar.Add("تم حذف الصورة بنجاح", Severity.Success);

                    // يمكن إضافة منطق لحذف الملف من الخادم هنا إذا لزم الأمر
                    // await DeleteImageFromServer(removedImageUrl);
                }
                else
                {
                    Snackbar.Add("فهرس الصورة غير صحيح", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"خطأ في حذف الصورة: {ex.Message}", Severity.Error);
            }
        }

        #endregion
    }
}
