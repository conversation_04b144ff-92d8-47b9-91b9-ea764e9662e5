using AutoMapper;
using BlogMud.Data.Repositories;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;
using BlogMud.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlogMud.Controllers
{
    /// <summary>
    /// وحدة تحكم واجهة برمجة التطبيقات لإدارة الشرائح المتحركة لصفحة "من نحن"
    /// </summary>
    /// <remarks>
    /// توفر هذه الوحدة واجهات برمجة تطبيقات RESTful للتعامل مع الشرائح المتحركة
    /// تتطلب جميع عمليات الإنشاء والتعديل والحذف صلاحيات المسؤول (Admin)
    /// </remarks>
    [Route("api/[controller]")]
    [ApiController]
    public class SiderAboutUsController : ControllerBase
    {
        #region المتغيرات والمنشئ

        private readonly IUnitOfWork _unitOfWork;
        private readonly ISiderAboutUsRepository _siderAboutUsRepository;
        private readonly ILogger<SiderAboutUsController> _logger;
        private readonly IMapper _mapper;
        private readonly IFileService _fileService;

        /// <summary>
        /// منشئ وحدة تحكم الشرائح المتحركة لصفحة "من نحن"
        /// </summary>
        /// <param name="unitOfWork">وحدة العمل</param>
        /// <param name="siderAboutUsRepository">مستودع الشرائح المتحركة</param>
        /// <param name="logger">مسجل الأحداث</param>
        /// <param name="mapper">محول البيانات</param>
        /// <param name="fileService">خدمة إدارة الملفات</param>
        public SiderAboutUsController(
            IUnitOfWork unitOfWork,
            ISiderAboutUsRepository siderAboutUsRepository,
            ILogger<SiderAboutUsController> logger,
            IMapper mapper,
            IFileService fileService)
        {
            _unitOfWork = unitOfWork;
            _siderAboutUsRepository = siderAboutUsRepository;
            _logger = logger;
            _mapper = mapper;
            _fileService = fileService;
        }

        #endregion

        #region عمليات CRUD

        /// <summary>
        /// استرجاع جميع الشرائح المتحركة
        /// </summary>
        /// <returns>قائمة بجميع الشرائح</returns>
        /// <response code="200">تم استرجاع الشرائح بنجاح</response>
        /// <response code="500">حدث خطأ أثناء استرجاع الشرائح</response>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<SiderAboutUsDto>>> GetAllSiderAboutUs()
        {
            try
            {
                _logger.LogInformation("Fetching all SiderAboutUs slideshows");

                var slideshows = await _siderAboutUsRepository.GetAllOrderedAsync();
                var slideshowDtos = _mapper.Map<IEnumerable<SiderAboutUsDto>>(slideshows);

                _logger.LogInformation("Successfully fetched {Count} SiderAboutUs slideshows", slideshowDtos.Count());
                return Ok(slideshowDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching SiderAboutUs slideshows");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// استرجاع الشرائح النشطة فقط
        /// </summary>
        /// <returns>قائمة بالشرائح النشطة</returns>
        /// <response code="200">تم استرجاع الشرائح النشطة بنجاح</response>
        /// <response code="500">حدث خطأ أثناء استرجاع الشرائح</response>
        [HttpGet("active")]
        public async Task<ActionResult<IEnumerable<SiderAboutUsDto>>> GetActiveSiderAboutUs()
        {
            try
            {
                _logger.LogInformation("Fetching active SiderAboutUs slideshows");

                var slideshows = await _siderAboutUsRepository.GetActiveSlideshowsAsync();
                var slideshowDtos = _mapper.Map<IEnumerable<SiderAboutUsDto>>(slideshows);

                _logger.LogInformation("Successfully fetched {Count} active SiderAboutUs slideshows", slideshowDtos.Count());
                return Ok(slideshowDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching active SiderAboutUs slideshows");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// استرجاع شريحة محددة بالمعرف
        /// </summary>
        /// <param name="id">معرف الشريحة</param>
        /// <returns>بيانات الشريحة</returns>
        /// <response code="200">تم استرجاع الشريحة بنجاح</response>
        /// <response code="404">الشريحة غير موجودة</response>
        /// <response code="500">حدث خطأ أثناء استرجاع الشريحة</response>
        [HttpGet("{id}")]
        public async Task<ActionResult<SiderAboutUsDto>> GetSiderAboutUs(int id)
        {
            try
            {
                _logger.LogInformation("Fetching SiderAboutUs slideshow with ID: {SlideshowId}", id);

                var slideshow = await _siderAboutUsRepository.GetByIdAsync(id);
                if (slideshow == null)
                {
                    _logger.LogWarning("SiderAboutUs slideshow not found with ID: {SlideshowId}", id);
                    return NotFound();
                }

                var slideshowDto = _mapper.Map<SiderAboutUsDto>(slideshow);

                _logger.LogInformation("Successfully fetched SiderAboutUs slideshow with ID: {SlideshowId}", id);
                return Ok(slideshowDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching SiderAboutUs slideshow with ID: {SlideshowId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء شريحة متحركة جديدة
        /// </summary>
        /// <param name="slideshowDto">بيانات الشريحة الجديدة</param>
        /// <returns>الشريحة التي تم إنشاؤها</returns>
        /// <response code="201">تم إنشاء الشريحة بنجاح</response>
        /// <response code="400">بيانات غير صحيحة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="500">حدث خطأ أثناء إنشاء الشريحة</response>
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<SiderAboutUsDto>> CreateSiderAboutUs([FromBody] SiderAboutUsDto slideshowDto)
        {
            try
            {
                _logger.LogInformation("Creating new SiderAboutUs slideshow");

                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Invalid model state for SiderAboutUs creation");
                    return BadRequest(ModelState);
                }

                // تحويل كائن نقل البيانات إلى نموذج البيانات
                var slideshow = _mapper.Map<SiderAboutUs>(slideshowDto);
                slideshow.CreatedAt = DateTime.Now;

                // إضافة الشريحة إلى قاعدة البيانات
                await _siderAboutUsRepository.AddAsync(slideshow);
                await _unitOfWork.SaveAsync();

                // تحويل النتيجة إلى كائن نقل البيانات
                var createdSlideshowDto = _mapper.Map<SiderAboutUsDto>(slideshow);

                _logger.LogInformation("SiderAboutUs slideshow created successfully with ID: {SlideshowId}", slideshow.Id);
                return CreatedAtAction(nameof(GetSiderAboutUs), new { id = slideshow.Id }, createdSlideshowDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating SiderAboutUs slideshow");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث شريحة متحركة موجودة
        /// </summary>
        /// <param name="id">معرف الشريحة</param>
        /// <param name="slideshowDto">البيانات المحدثة للشريحة</param>
        /// <returns>الشريحة المحدثة</returns>
        /// <response code="200">تم تحديث الشريحة بنجاح</response>
        /// <response code="400">بيانات غير صحيحة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="404">الشريحة غير موجودة</response>
        /// <response code="500">حدث خطأ أثناء تحديث الشريحة</response>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<SiderAboutUsDto>> UpdateSiderAboutUs(int id, [FromBody] SiderAboutUsDto slideshowDto)
        {
            try
            {
                _logger.LogInformation("Updating SiderAboutUs slideshow with ID: {SlideshowId}", id);

                if (id != slideshowDto.Id)
                {
                    _logger.LogWarning("ID mismatch in SiderAboutUs update request. URL ID: {UrlId}, Body ID: {BodyId}", id, slideshowDto.Id);
                    return BadRequest("ID mismatch");
                }

                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Invalid model state for SiderAboutUs update");
                    return BadRequest(ModelState);
                }

                var existingSlideshow = await _siderAboutUsRepository.GetByIdAsync(id);
                if (existingSlideshow == null)
                {
                    _logger.LogWarning("SiderAboutUs slideshow not found with ID: {SlideshowId}", id);
                    return NotFound();
                }

                // حفظ روابط الصور القديمة للمقارنة
                var oldImageUrls = existingSlideshow.ImageUrls;

                // تحديث البيانات
                _mapper.Map(slideshowDto, existingSlideshow);
                existingSlideshow.LastModifiedAt = DateTime.Now;

                // حذف الصور القديمة إذا تم تغيير روابط الصور
                if (!string.IsNullOrEmpty(oldImageUrls) && oldImageUrls != existingSlideshow.ImageUrls)
                {
                    try
                    {
                        int deletedCount = _fileService.DeleteSiderAboutUsImages(oldImageUrls);
                        _logger.LogInformation("Deleted {DeletedCount} old image files for SiderAboutUs ID: {SlideshowId}",
                            deletedCount, id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error deleting old image files for SiderAboutUs ID: {SlideshowId}", id);
                        // نستمر في التحديث حتى لو فشل حذف الملفات القديمة
                    }
                }

                _siderAboutUsRepository.Update(existingSlideshow);
                await _unitOfWork.SaveAsync();

                var updatedSlideshowDto = _mapper.Map<SiderAboutUsDto>(existingSlideshow);

                _logger.LogInformation("SiderAboutUs slideshow updated successfully with ID: {SlideshowId}", id);
                return Ok(updatedSlideshowDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating SiderAboutUs slideshow with ID: {SlideshowId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف شريحة متحركة
        /// </summary>
        /// <param name="id">معرف الشريحة المراد حذفها</param>
        /// <returns>نتيجة عملية الحذف</returns>
        /// <response code="204">تم حذف الشريحة بنجاح</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="404">الشريحة غير موجودة</response>
        /// <response code="500">حدث خطأ أثناء حذف الشريحة</response>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteSiderAboutUs(int id)
        {
            try
            {
                _logger.LogInformation("Deleting SiderAboutUs slideshow with ID: {SlideshowId}", id);

                var slideshow = await _siderAboutUsRepository.GetByIdAsync(id);
                if (slideshow == null)
                {
                    _logger.LogWarning("SiderAboutUs slideshow not found with ID: {SlideshowId}", id);
                    return NotFound();
                }

                // حذف ملفات الصور من المجلد إذا كانت موجودة
                if (!string.IsNullOrEmpty(slideshow.ImageUrls))
                {
                    try
                    {
                        int deletedCount = _fileService.DeleteSiderAboutUsImages(slideshow.ImageUrls);
                        _logger.LogInformation("Deleted {DeletedCount} image files for SiderAboutUs ID: {SlideshowId}",
                            deletedCount, id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error deleting image files for SiderAboutUs ID: {SlideshowId}", id);
                        // نستمر في حذف السجل حتى لو فشل حذف الملفات
                    }
                }

                _siderAboutUsRepository.Remove(slideshow);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("SiderAboutUs slideshow deleted successfully with ID: {SlideshowId}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting SiderAboutUs slideshow with ID: {SlideshowId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #endregion
    }
}
