using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;

namespace BlogMud.Data.Repositories
{
    /// <summary>
    /// واجهة مستودع بيانات الشرائح المتحركة لصفحة "من نحن"
    /// </summary>
    public interface ISiderAboutUsRepository : IRepository<SiderAboutUs>
    {
        /// <summary>
        /// الحصول على الشرائح النشطة مرتبة حسب ترتيب العرض
        /// </summary>
        /// <returns>قائمة بالشرائح النشطة</returns>
        Task<IEnumerable<SiderAboutUs>> GetActiveSlideshowsAsync();

        /// <summary>
        /// الحصول على أول شريحة نشطة
        /// </summary>
        /// <returns>أول شريحة نشطة أو null إذا لم توجد</returns>
        Task<SiderAboutUs?> GetFirstActiveSlideshowAsync();

        /// <summary>
        /// الحصول على جميع الشرائح مرتبة حسب ترتيب العرض
        /// </summary>
        /// <returns>قائمة بجميع الشرائح</returns>
        Task<IEnumerable<SiderAboutUs>> GetAllOrderedAsync();
    }
}
