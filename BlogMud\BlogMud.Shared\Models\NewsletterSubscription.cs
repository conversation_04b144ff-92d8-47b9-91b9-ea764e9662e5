using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    public class NewsletterSubscription
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        [StringLength(100)]
        public string? FullName { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime SubscribedAt { get; set; } = DateTime.Now;

        public DateTime? UnsubscribedAt { get; set; }

        public string? UnsubscribeToken { get; set; }

        // Track if this is a registered user or just email subscriber
        public string? UserId { get; set; }

        public bool EmailConfirmed { get; set; } = false;

        public string? ConfirmationToken { get; set; }

        public DateTime? ConfirmedAt { get; set; }
    }
}
