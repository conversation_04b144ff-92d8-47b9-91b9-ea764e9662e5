using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    /// <summary>
    /// نموذج الصور المتحركة التي تظهر في الصفحة الرئيسية
    /// </summary>
    public class AnimatedGif
    {
        [Key]
        public int Id { get; set; }

        [StringLength(100)]
        public string? Title { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        [StringLength(255)]
        public string ImageUrl { get; set; }

        /// <summary>
        /// ترتيب ظهور الصورة المتحركة
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// حالة الصورة المتحركة (نشطة أو غير نشطة)
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ إنشاء الصورة المتحركة
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل للصورة المتحركة
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }
    }
}
