using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BlogMud.Shared.Models
{
    /// <summary>
    /// نموذج عنصر الطاقة الإنتاجية المرتبط بصفحة "من نحن"
    /// </summary>
    public class ProductionCapacityItem
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// معرف صفحة "من نحن" المرتبطة (مفتاح خارجي)
        /// </summary>
        [Required]
        [ForeignKey("AboutUs")]
        public int AboutUsId { get; set; }

        /// <summary>
        /// عنوان عنصر الطاقة الإنتاجية (مطلوب)
        /// </summary>
        [Required(ErrorMessage = "عنوان الطاقة الإنتاجية مطلوب")]
        [StringLength(200, ErrorMessage = "يجب أن يكون العنوان أقل من {1} حرفًا")]
        public string Title { get; set; }

        /// <summary>
        /// وصف عنصر الطاقة الإنتاجية (مطلوب)
        /// </summary>
        [Required(ErrorMessage = "وصف الطاقة الإنتاجية مطلوب")]
        [StringLength(1000, ErrorMessage = "يجب أن يكون الوصف أقل من {1} حرفًا")]
        public string Description { get; set; }

        /// <summary>
        /// رابط صورة عنصر الطاقة الإنتاجية (اختياري)
        /// </summary>
        [StringLength(255, ErrorMessage = "مسار الصورة طويل جداً")]
        public string? ImageUrl { get; set; }

        /// <summary>
        /// ترتيب عرض العنصر
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// حالة العنصر (نشط أو غير نشط)
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ إنشاء العنصر
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل للعنصر
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }

        /// <summary>
        /// العلاقة مع صفحة "من نحن"
        /// </summary>
        public virtual AboutUs AboutUs { get; set; }
    }
}
