using Microsoft.EntityFrameworkCore.Storage;

namespace BlogMud.Shared.Repositories
{
    /// <summary>
    /// Interface for the Unit of Work pattern
    /// Manages transactions across multiple repositories
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        /// <summary>
        /// Gets a generic repository for the specified entity type
        /// </summary>
        /// <typeparam name="T">The entity type</typeparam>
        /// <returns>A generic repository for the entity type</returns>
        IRepository<T> Repository<T>() where T : class;

        /// <summary>
        /// Gets a domain-specific repository for the specified entity type
        /// </summary>
        /// <typeparam name="T">The entity type</typeparam>
        /// <returns>A domain-specific repository for the entity type</returns>
        IGRepository<T> GRepository<T>() where T : class;

        // Domain-specific repositories can be added here
        // IArticleRepository Articles { get; }
        // IBranchRepository Branches { get; }

        /// <summary>
        /// Saves all changes made through the repositories
        /// </summary>
        void Save();

        /// <summary>
        /// Saves all changes made through the repositories asynchronously
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        Task SaveAsync();

        /// <summary>
        /// Begins a new transaction
        /// </summary>
        /// <returns>A transaction object</returns>
        IDbContextTransaction BeginTransaction();

        /// <summary>
        /// Begins a new transaction asynchronously
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        Task<IDbContextTransaction> BeginTransactionAsync();
    }
}
