using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    public class Category
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "يجب إدخال اسم القسم")]
        [StringLength(100, ErrorMessage = "يجب أن لا يتجاوز اسم القسم 100 حرف")]
        public string Name { get; set; }

        [StringLength(500, ErrorMessage = "يجب أن لا يتجاوز الوصف 500 حرف")]
        public string Description { get; set; }

        public int ArticleCount { get; set; }
    }
}
