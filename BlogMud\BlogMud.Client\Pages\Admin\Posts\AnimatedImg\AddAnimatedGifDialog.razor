@using BlogMud.Shared.DTOs
@using Microsoft.AspNetCore.Components.Forms
@inject HttpClient Http
@inject ISnackbar Snackbar

<AddAnimatedGifDialogCSS />

<MudDialog Class="animated-gif-dialog" MaxWidth="MaxWidth.Large">
    <TitleContent>
        <div class="dialog-title-container">
            <MudIcon Icon="@(IsCreateMode ? Icons.Material.Filled.Add : Icons.Material.Filled.Edit)"
                     Class="dialog-title-icon" />
            <MudText Typo="Typo.h5" Class="dialog-title-text">
                @(IsCreateMode ? "إضافة صورة متحركة جديدة" : "تعديل الصورة المتحركة")
            </MudText>
        </div>
    </TitleContent>
    <DialogContent>
        <div class="dialog-content-container">
            <MudForm @ref="_form" Model="NewAnimatedGif" @bind-IsValid="@_success" Class="gif-form">

                <!-- Tabbed Interface -->
                <MudTabs Elevation="1" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6" @bind-ActivePanelIndex="_activeTabIndex">

                    <!-- Tab 1: Basic Information -->
                    <MudTabPanel Text="المعلومات الأساسية" Icon="@Icons.Material.Filled.Info">
                        <MudGrid Spacing="3" Class="form-grid">
                            <!-- العنوان -->
                            <MudItem xs="12">
                                <MudTextField @bind-Value="NewAnimatedGif.Title"
                                             Label="عنوان الصورة المتحركة"
                                             Required="true"
                                             RequiredError="العنوان مطلوب"
                                             MaxLength="100"
                                             Counter="100"
                                             HelperText="الحد الأقصى 100 حرف"
                                             Variant="Variant.Outlined"
                                             Class="form-field"
                                             Immediate="true" />
                            </MudItem>

                            <!-- الوصف -->
                            <MudItem xs="12">
                                <MudTextField @bind-Value="NewAnimatedGif.Description"
                                             Label="وصف الصورة المتحركة"
                                             Lines="3"
                                             MaxLength="500"
                                             Counter="500"
                                             HelperText="الحد الأقصى 500 حرف"
                                             Variant="Variant.Outlined"
                                             Class="form-field" />
                            </MudItem>

                            <!-- ترتيب العرض -->
                            <MudItem xs="12" sm="6">
                                <MudNumericField @bind-Value="NewAnimatedGif.DisplayOrder"
                                                Label="ترتيب العرض"
                                                Min="0"
                                                Max="999"
                                                HideSpinButtons="false"
                                                Variant="Variant.Outlined"
                                                Class="form-field"
                                                HelperText="ترتيب ظهور الصورة المتحركة" />
                            </MudItem>

                            <!-- حالة النشاط -->
                            <MudItem xs="12" sm="6">
                                <MudPaper Class="form-section status-section" Elevation="0">
                                    <div class="status-header">
                                        <MudIcon Icon="@Icons.Material.Filled.Visibility" Class="status-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="status-title">حالة النشر</MudText>
                                    </div>
                                    <div class="status-content">
                                        <MudSwitch @bind-Value="NewAnimatedGif.IsActive"
                                                  Label="نشط"
                                                  Color="Color.Primary"
                                                  Class="status-switch" />
                                        <MudText Typo="Typo.caption" Color="Color.Secondary" Class="status-description">
                                            عند التفعيل، ستظهر هذه الصورة في الموقع
                                        </MudText>
                                    </div>
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                    <!-- Tab 2: Media Upload -->
                    <MudTabPanel Text="رفع الصورة" Icon="@Icons.Material.Filled.PhotoLibrary">
                        <MudGrid Spacing="3" Class="media-grid">

                            <!-- Image Upload Section -->
                            <MudItem xs="12">
                                <MudPaper Class="upload-section image-upload" Elevation="0">
                                    <div class="upload-header">
                                        <MudIcon Icon="@Icons.Material.Filled.Gif" Class="upload-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="upload-title">صورة متحركة (GIF)</MudText>
                                    </div>

                                    <div class="upload-content">
                                        <MudTextField @bind-Value="NewAnimatedGif.ImageUrl" 
                                                      ReadOnly="true" 
                                                      Label="رابط الصورة" 
                                                      Required="true" 
                                                      RequiredError="يجب رفع صورة"
                                                      Variant="Variant.Outlined"
                                                      Class="mb-3 form-field" />

                                        <MudFileUpload T="IBrowserFile" 
                                                       Accept=".png, .jpg, .jpeg, .gif" 
                                                       FilesChanged="UploadFile" 
                                                       MaximumFileCount="1"
                                                       Class="file-upload">
                                            <ActivatorContent>
                                                <MudButton Variant="Variant.Outlined"
                                                           Color="Color.Primary"
                                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                                           Disabled="@_isUploading"
                                                           FullWidth="true"
                                                           Class="upload-button">
                                                    @if (_isUploading)
                                                    {
                                                        <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="me-2" />
                                                        <MudText>جاري الرفع...</MudText>
                                                    }
                                                    else
                                                    {
                                                        <MudText>اختر صورة للرفع (PNG, JPG, GIF)</MudText>
                                                    }
                                                </MudButton>
                                            </ActivatorContent>
                                        </MudFileUpload>

                                        @if (_isUploading)
                                        {
                                            <div class="upload-progress">
                                                <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="mt-2" />
                                                <MudText Typo="Typo.caption" Class="mt-1 upload-status">جاري رفع الصورة...</MudText>
                                            </div>
                                        }

                                        <!-- معاينة الصورة -->
                                        @if (!string.IsNullOrEmpty(NewAnimatedGif.ImageUrl))
                                        {
                                            <div class="uploaded-image-section">
                                                <MudText Typo="Typo.subtitle2" Class="mb-2 image-title">
                                                    معاينة الصورة المرفوعة
                                                </MudText>
                                                <MudCard Class="image-card" Elevation="2">
                                                    <MudCardMedia Image="@NewAnimatedGif.ImageUrl" Height="200" Class="image-preview" />
                                                    <MudCardActions Class="image-actions">
                                                        <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                                      Color="Color.Error"
                                                                      Size="Size.Small"
                                                                      OnClick="RemoveImage"
                                                                      Title="حذف الصورة"
                                                                      Class="delete-image-btn" />
                                                    </MudCardActions>
                                                </MudCard>
                                            </div>
                                        }
                                    </div>
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                    <!-- Tab 3: Preview -->
                    <MudTabPanel Text="المعاينة" Icon="@Icons.Material.Filled.Preview">
                        <MudGrid Spacing="3" Class="preview-grid">
                            <MudItem xs="12">
                                @if (!string.IsNullOrEmpty(NewAnimatedGif.ImageUrl))
                                {
                                    <MudPaper Class="preview-section" Elevation="0">
                                        <div class="preview-header">
                                            <MudIcon Icon="@Icons.Material.Filled.Preview" Class="preview-icon" />
                                            <MudText Typo="Typo.subtitle1" Class="preview-title">معاينة الصورة المتحركة</MudText>
                                        </div>
                                        
                                        <div class="preview-content">
                                            <div class="preview-card">
                                                <div class="preview-image-wrapper">
                                                    <img src="@NewAnimatedGif.ImageUrl" alt="@NewAnimatedGif.Title" class="preview-image-full" />
                                                </div>
                                                <div class="preview-info">
                                                    <MudText Typo="Typo.h6" Class="preview-title-text">@(NewAnimatedGif.Title ?? "عنوان الصورة")</MudText>
                                                    @if (!string.IsNullOrEmpty(NewAnimatedGif.Description))
                                                    {
                                                        <MudText Typo="Typo.body2" Class="preview-description">@NewAnimatedGif.Description</MudText>
                                                    }
                                                    <div class="preview-details">
                                                        <MudChip T="string" Color="Color.Primary" Size="Size.Small" Class="me-2">
                                                            ترتيب: @NewAnimatedGif.DisplayOrder
                                                        </MudChip>
                                                        <MudChip T="string" Color="@(NewAnimatedGif.IsActive ? Color.Success : Color.Default)" Size="Size.Small">
                                                            @(NewAnimatedGif.IsActive ? "نشط" : "غير نشط")
                                                        </MudChip>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </MudPaper>
                                }
                                else
                                {
                                    <MudPaper Class="no-preview-section" Elevation="0">
                                        <div class="no-preview-content">
                                            <MudIcon Icon="@Icons.Material.Filled.ImageNotSupported" Class="no-preview-icon" />
                                            <MudText Typo="Typo.h6" Class="no-preview-title">لا توجد صورة للمعاينة</MudText>
                                            <MudText Typo="Typo.body2" Class="no-preview-subtitle">
                                                قم برفع صورة في تبويب "رفع الصورة" لرؤية المعاينة
                                            </MudText>
                                        </div>
                                    </MudPaper>
                                }
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                </MudTabs>

            </MudForm>
        </div>
    </DialogContent>
    
    <DialogActions>
        <div class="dialog-actions">
            <div class="action-buttons">
                <MudButton Variant="Variant.Text"
                           Color="Color.Default"
                           OnClick="Cancel"
                           StartIcon="@Icons.Material.Filled.Cancel"
                           Class="cancel-button">
                    إلغاء
                </MudButton>
                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           OnClick="Save"
                           Disabled="@(!_success || _isUploading || _isSaving)"
                           StartIcon="@(IsCreateMode ? Icons.Material.Filled.Add : Icons.Material.Filled.Save)"
                           Class="submit-button">
                    @if (_isSaving)
                    {
                        <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="me-2" />
                        <MudText>جاري الحفظ...</MudText>
                    }
                    else
                    {
                        <MudText>@(IsCreateMode ? "إنشاء الصورة المتحركة" : "حفظ التعديلات")</MudText>
                    }
                </MudButton>
            </div>
        </div>
    </DialogActions>
</MudDialog>

@code {
    #region الخصائص والمتغيرات

    /// <summary>
    /// مرجع إلى نموذج MudForm المستخدم للتحقق من صحة البيانات
    /// </summary>
    private MudForm _form;

    /// <summary>
    /// مؤشر على حالة رفع الصور
    /// </summary>
    private bool _isUploading = false;

    /// <summary>
    /// مؤشر على صحة النموذج
    /// </summary>
    private bool _success = false;

    /// <summary>
    /// مؤشر على حالة الحفظ
    /// </summary>
    private bool _isSaving = false;

    /// <summary>
    /// فهرس التبويب النشط
    /// </summary>
    private int _activeTabIndex = 0;

    /// <summary>
    /// مثيل مربع الحوار MudDialog الذي يتم تمريره من خلال CascadingParameter
    /// </summary>
    [CascadingParameter] public required IMudDialogInstance MudDialog { get; set; }

    /// <summary>
    /// بيانات الصورة المتحركة للتعديل (اختياري)
    /// </summary>
    [Parameter] public AnimatedGifDto? AnimatedGif { get; set; }

    /// <summary>
    /// بيانات الصورة المتحركة الجديدة أو المحدثة
    /// </summary>
    private AnimatedGifDto NewAnimatedGif = new() { IsActive = true, DisplayOrder = 0 };

    /// <summary>
    /// تحديد ما إذا كان الحوار في وضع الإنشاء أم التعديل
    /// </summary>
    private bool IsCreateMode => AnimatedGif == null || AnimatedGif.Id == 0;

    #endregion

    #region دورة حياة المكون

    protected override void OnInitialized()
    {
        if (AnimatedGif != null)
        {
            NewAnimatedGif = new AnimatedGifDto
            {
                Id = AnimatedGif.Id,
                Title = AnimatedGif.Title ?? "",
                Description = AnimatedGif.Description ?? "",
                ImageUrl = AnimatedGif.ImageUrl ?? "",
                DisplayOrder = AnimatedGif.DisplayOrder,
                IsActive = AnimatedGif.IsActive,
                CreatedAt = AnimatedGif.CreatedAt,
                LastModifiedAt = AnimatedGif.LastModifiedAt
            };
        }
    }

    #endregion

    #region دوال إدارة الصور

    /// <summary>
    /// حذف الصورة المرفوعة
    /// </summary>
    private void RemoveImage()
    {
        NewAnimatedGif.ImageUrl = string.Empty;
        StateHasChanged();
    }

    #endregion

    private async Task UploadFile(IBrowserFile? file)
    {
        if (file == null)
            return;

        if (_isUploading)
        {
            Snackbar.Add("يتم رفع ملف آخر حالياً، يرجى الانتظار", Severity.Warning);
            return;
        }

        try
        {
            _isUploading = true;
            StateHasChanged();

            // Validate file size (max 5MB)
            const long maxFileSize = 5 * 1024 * 1024;
            if (file.Size > maxFileSize)
            {
                Snackbar.Add("حجم الملف يتجاوز الحد الأقصى (5 ميجابايت)", Severity.Error);
                return;
            }

            // Validate file type
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
            var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();

            if (!allowedExtensions.Contains(fileExtension))
            {
                Snackbar.Add("يُسمح فقط بملفات الصور (jpg, jpeg, png, gif)", Severity.Error);
                return;
            }

            // Generate unique filename
            var fileName = $"{Guid.NewGuid()}{fileExtension}";

            // Create form data
            using var content = new MultipartFormDataContent();
            using var fileStream = file.OpenReadStream(maxAllowedSize: maxFileSize);
            using var streamContent = new StreamContent(fileStream);

            streamContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(file.ContentType);
            content.Add(streamContent, "file", fileName);

            // Upload file
            using var httpClient = new HttpClient { Timeout = TimeSpan.FromMinutes(5) };
            httpClient.BaseAddress = Http.BaseAddress;

            if (Http.DefaultRequestHeaders.Authorization != null)
            {
                httpClient.DefaultRequestHeaders.Authorization = Http.DefaultRequestHeaders.Authorization;
            }

            var response = await httpClient.PostAsync("api/upload/animatedgif", content);

            if (response.IsSuccessStatusCode)
            {
                var uploadedFilePath = await response.Content.ReadAsStringAsync();
                uploadedFilePath = uploadedFilePath.Trim('"');
                NewAnimatedGif.ImageUrl = uploadedFilePath;
                Snackbar.Add("تم رفع الملف بنجاح", Severity.Success);
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                Snackbar.Add($"خطأ في رفع الملف: {error}", Severity.Error);
            }
        }
        catch (TaskCanceledException)
        {
            Snackbar.Add("انتهت مهلة رفع الملف، يرجى المحاولة مرة أخرى", Severity.Error);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في رفع الملف: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isUploading = false;
            StateHasChanged();
        }
    }

    #region دوال الحوار

    /// <summary>
    /// حفظ بيانات الصورة المتحركة
    /// </summary>
    private async Task Save()
    {
        // التحقق من صحة النموذج
        await _form.Validate();
        
        if (!_success)
        {
            Snackbar.Add("يرجى تصحيح الأخطاء في النموذج", Severity.Error);
            return;
        }

        if (string.IsNullOrEmpty(NewAnimatedGif.Title))
        {
            Snackbar.Add("العنوان مطلوب", Severity.Error);
            _activeTabIndex = 0; // الانتقال إلى تبويب المعلومات الأساسية
            return;
        }

        if (string.IsNullOrEmpty(NewAnimatedGif.ImageUrl))
        {
            Snackbar.Add("يجب رفع صورة", Severity.Error);
            _activeTabIndex = 1; // الانتقال إلى تبويب رفع الصورة
            return;
        }

        try
        {
            _isSaving = true;
            StateHasChanged();

            HttpResponseMessage response;
            
            if (NewAnimatedGif.Id > 0)
            {
                // Update existing
                NewAnimatedGif.LastModifiedAt = DateTime.Now;
                response = await Http.PutAsJsonAsync($"api/AnimatedGifs/{NewAnimatedGif.Id}", NewAnimatedGif);
            }
            else
            {
                // Create new
                response = await Http.PostAsJsonAsync("api/AnimatedGifs", NewAnimatedGif);
            }

            if (response.IsSuccessStatusCode)
            {
                AnimatedGifDto? result = null;
                
                // في حالة الإنشاء، نقرأ البيانات المرجعة
                if (IsCreateMode)
                {
                    try
                    {
                        result = await response.Content.ReadFromJsonAsync<AnimatedGifDto>();
                    }
                    catch (Exception)
                    {
                        // في حالة فشل قراءة JSON، نستخدم البيانات الحالية
                        result = NewAnimatedGif;
                    }
                }
                // في حالة التحديث، نستخدم البيانات الحالية لأن API يرجع NoContent
                else
                {
                    result = NewAnimatedGif;
                }
                
                Snackbar.Add(IsCreateMode ? "تم إضافة الصورة المتحركة بنجاح" : "تم تحديث الصورة المتحركة بنجاح", Severity.Success);
                MudDialog.Close(DialogResult.Ok(result));
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                var errorMessage = string.IsNullOrEmpty(error) ? "حدث خطأ غير معروف" : error;
                Snackbar.Add($"خطأ في حفظ الصورة المتحركة: {errorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حفظ الصورة المتحركة: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isSaving = false;
            StateHasChanged();
        }
    }

    /// <summary>
    /// إلغاء الحوار
    /// </summary>
    private void Cancel()
    {
        MudDialog.Cancel();
    }

    #endregion
}