@page "/admin/posts/postManagement"

@attribute [Authorize(Roles = "Admin")]
@layout BlogMud.Client.Layout.AdminLayout


<PageTitle>إدارة المقالات</PageTitle>

<PostManagementCSS />

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-6">
    <div class="d-flex justify-space-between align-center mb-4" data-aos="fade-down" data-aos-duration="600">
        <MudText Typo="Typo.h4" data-aos="fade-right" data-aos-delay="200">إدارة المقالات</MudText>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                   OnClick="@(() => OpenArticleDialog(null))" data-aos="fade-left" data-aos-delay="400">
            إضافة مقال جديد
        </MudButton>
    </div>

    @if (isLoading)
    {
        <div class="d-flex justify-center my-8" data-aos="fade-up" data-aos-duration="800">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
        </div>
    }
    else if (articles.Count == 0)
    {
        <MudAlert Severity="Severity.Info" Class="my-4" data-aos="zoom-in" data-aos-duration="600">لا توجد مقالات متاحة. أضف مقالاً جديداً للبدء.</MudAlert>
    }
    else
    {
        <!-- الجدول العادي للشاشات الكبيرة -->
        <div class="regular-table" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
            <MudTable Items="@articles" Hover="true" Striped="true" Bordered="true" Class="mb-8">
                <HeaderContent>
                    <MudTh>ت</MudTh>
                    <MudTh>عنوان</MudTh>
                    <MudTh>المقدمة</MudTh>
                    <MudTh>القسم</MudTh>
                    <MudTh>العميل</MudTh>
                    <MudTh>تاريخ النشر</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="ت">@(articles.IndexOf(context) + 1)</MudTd>
                    <MudTd DataLabel="عنوان">@context.Title</MudTd>
                    <MudTd DataLabel="المقدمة">@context.Introduction</MudTd>
                    <MudTd DataLabel="القسم">@context.CategoryName</MudTd>
                    <MudTd DataLabel="العميل">@context.ClientName</MudTd>
                    <MudTd DataLabel="تاريخ النشر">@context.PublishDate.ToString("yyyy-MM-dd")</MudTd>
                    <MudTd DataLabel="الإجراءات">
                        <MudIconButton Icon="@Icons.Material.Filled.Edit" Color="Color.Primary"
                                       OnClick="@(() => OpenArticleDialog(context))" />
                        <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error"
                                       OnClick="@(() => ConfirmDelete(context))" />
                    </MudTd>
                </RowTemplate>
                <PagerContent>
                    <MudTablePager PageSizeOptions="new int[] { 5, 10, 15 }" />
                </PagerContent>
            </MudTable>
        </div>

        <!-- تخطيط البطاقات المصغرة للشاشات الصغيرة (التابلت والهاتف) -->
        <div class="mobile-cards">
            @foreach (var article in articles)
            {
                <MudCard Class="mobile-card" Elevation="1"
                         data-aos="fade-up"
                         data-aos-duration="600"
                         data-aos-delay="@((articles.IndexOf(article) * 100).ToString())">
                    <MudCardContent Class="pa-2">
                        <!-- رقم المقال والإجراءات -->
                        <div class="d-flex justify-space-between align-center mb-2">
                            <MudChip T="string" Color="Color.Primary" Size="Size.Small" Class="article-number">
                                @(articles.IndexOf(article) + 1)
                            </MudChip>
                            <div class="article-actions">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                             Color="Color.Primary"
                                             Size="Size.Small"
                                             OnClick="@(() => OpenArticleDialog(article))" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                             Color="Color.Error"
                                             Size="Size.Small"
                                             OnClick="@(() => ConfirmDelete(article))" />
                            </div>
                        </div>

                        <!-- عنوان المقال -->
                        <div class="article-title">
                            @(article.Title ?? "غير محدد")
                        </div>

                        <!-- معلومات المقال -->
                        <div class="article-info">
                            <div class="info-row">
                                <MudIcon Icon="@Icons.Material.Filled.Category" Size="Size.Small" Class="info-icon" />
                                <span class="info-label">القسم:</span>
                                <span class="info-value">@(article.CategoryName ?? "غير محدد")</span>
                            </div>
                            <div class="info-row">
                                <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" Class="info-icon" />
                                <span class="info-label">العميل:</span>
                                <span class="info-value">@(article.ClientName ?? "غير محدد")</span>
                            </div>
                            <div class="info-row">
                                <MudIcon Icon="@Icons.Material.Filled.DateRange" Size="Size.Small" Class="info-icon" />
                                <span class="info-label">تاريخ النشر:</span>
                                <span class="info-value">@article.PublishDate.ToString("yyyy-MM-dd")</span>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            }
        </div>
    }
</MudContainer>

@code{
    #region الخصائص والمتغيرات

    /// <summary>
    /// قائمة المقالات المعروضة في الصفحة
    /// </summary>
    private List<ArticleDto> articles = new();

    /// <summary>
    /// مؤشر على حالة تحميل البيانات
    /// </summary>
    private bool isLoading = true;
    #endregion

    #region دوال دورة الحياة

    /// <summary>
    /// تهيئة مكونات الصفحة عند بدء التشغيل
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بتحميل قائمة المقالات من الخادم عند تحميل الصفحة
    /// </remarks>
    protected override async Task OnInitializedAsync()
    {
        await LoadArticles();
    }
    #endregion

    #region طلبات البيانات

    /// <summary>
    /// تحميل قائمة المقالات من الخادم
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بتعيين مؤشر التحميل إلى true قبل البدء في التحميل
    /// وإعادة تعيينه إلى false بعد الانتهاء من التحميل بغض النظر عن النتيجة
    /// </remarks>
    private async Task LoadArticles()
    {
        try
        {
            isLoading = true;
            articles = await Http.GetFromJsonAsync<List<ArticleDto>>("api/Article") ?? new List<ArticleDto>();
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء تحميل المقالات: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }


    #endregion

    #region الأحداث والتفاعلات



    /// <summary>
    /// فتح مربع حوار إضافة أو تعديل مقال
    /// </summary>
    /// <param name="article">المقال المراد تعديله، أو null لإضافة مقال جديد</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// إذا تم تمرير مقال موجود، يتم فتح مربع الحوار في وضع التعديل
    /// وإلا يتم فتحه في وضع الإضافة
    /// يتم نسخ جميع خصائص المقال إلى كائن جديد لتجنب التعديل المباشر على الكائن الأصلي
    /// بعد إغلاق مربع الحوار، إذا لم يتم إلغاء العملية، يتم إعادة تحميل قائمة المقالات
    /// </remarks>
    private async Task OpenArticleDialog(ArticleDto article)
    {
        var parameters = new DialogParameters();

        if (article != null)
        {
            parameters.Add("articleDto", new ArticleDto
            {
                Id = article.Id,
                Title = article.Title,
                Introduction = article.Introduction,
                Content = article.Content,
                ImageUrl = article.ImageUrl,
                ImageCarousel = article.ImageCarousel,
                VideoUrl = article.VideoUrl,
                ClientId = article.ClientId,
                ClientName = article.ClientName,
                CategoryId = article.CategoryId,
                CategoryName = article.CategoryName,
                PublishDate = article.PublishDate,
                IsPublished = article.IsPublished,
                CreatedAt = article.CreatedAt,
                LastModifiedAt = article.LastModifiedAt
            });
        }

        var dialog = await DialogService.ShowAsync<PostManagementDialog>(
            article == null ? "إضافة مقال جديد" : "تعديل المقال",
            parameters
        );

        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadArticles();
        }
    }

    /// <summary>
    /// عرض مربع حوار تأكيد حذف مقال
    /// </summary>
    /// <param name="article">المقال المراد حذفه</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يعرض مربع حوار تأكيد قبل حذف المقال
    /// إذا تم تأكيد الحذف، يتم استدعاء دالة DeleteArticle لحذف المقال
    /// </remarks>
    private async Task ConfirmDelete(ArticleDto article)
    {
        var parameters = new DialogParameters
        {
            { "ContentText", $"هل أنت متأكد من حذف المقال '{article.Title}'؟" },
            { "ButtonText", "حذف" },
            { "Color", Color.Error }
        };

        var dialog = await DialogService.ShowAsync<BlogMud.Client.Components.ConfirmationDialog>("تأكيد الحذف", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await DeleteArticle(article);
        }
    }

    /// <summary>
    /// حذف مقال من قاعدة البيانات
    /// </summary>
    /// <param name="article">المقال المراد حذفه</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بإرسال طلب حذف إلى الخادم
    /// إذا نجحت العملية، يتم إزالة المقال من القائمة المحلية وعرض رسالة نجاح
    /// وإلا يتم عرض رسالة الخطأ المستلمة من الخادم
    /// </remarks>
    private async Task DeleteArticle(ArticleDto article)
    {
        try
        {
            var response = await Http.DeleteAsync($"api/Article/{article.Id}");

            if (response.IsSuccessStatusCode)
            {
                articles.Remove(article);
                _Snackbar.Add("تم حذف المقال بنجاح", Severity.Success);
            }
            else
            {
                var errorMessage = await response.Content.ReadAsStringAsync();
                _Snackbar.Add(errorMessage, Severity.Error);
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء حذف المقال: {ex.Message}", Severity.Error);
        }
    }
    #endregion
}