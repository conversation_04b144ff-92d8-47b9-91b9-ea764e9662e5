@page "/about"
@using BlogMud.Shared.DTOs

<PageTitle>من نحن | @(_companyInfo?.Name ?? "شركتنا")</PageTitle>

<!-- SiderAboutUs Slideshow Section -->
@if (_slideshowsLoading)
{
    <div class="d-flex justify-center align-center" style="height: 250px; background-color: #f5f5f5;">
        <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
        <MudText Typo="Typo.h6" Class="mr-4">جاري تحميل الصور...</MudText>
    </div>
}
else if (_slideshows?.Any() == true)
{
    <div class="hero-slider position-relative overflow-hidden" data-aos="fade-in" data-aos-duration="800" data-aos-once="false" data-aos-mirror="true">
        <MudCarousel Class="mud-width-full" Style="height:250px;" ShowArrows="true" ShowDelimiters="true" AutoCycle="true" TData="object">
                @{
                    var allImages = new List<(string ImageUrl, string Title, string Description, string? LinkUrl, string? LinkText)>();
                    foreach (var slideshow in _slideshows.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder))
                    {
                        foreach (var imageUrl in slideshow.Images)
                        {
                            allImages.Add((imageUrl, slideshow.Title, slideshow.Description, slideshow.LinkUrl, slideshow.LinkText));
                        }
                    }
                }

            @foreach (var (imageUrl, title, description, linkUrl, linkText) in allImages)
            {
                <MudCarouselItem Transition="Transition.Slide">
                    <MudImage Src="@imageUrl" Alt="@title" ObjectFit="ObjectFit.Fill" Width="100" Height="100" Class="absolute-fill" />
                    <div class="d-flex flex-column justify-center align-center h-100" style="background: rgba(0,0,0,0.4); position: absolute; top: 0; left: 0; right: 0; bottom: 0;">
                        <MudContainer Class="text-center">
                            <MudText Typo="Typo.h3" Color="Color.Surface" Class="mb-4" data-aos="fade-up" data-aos-delay="200">@title</MudText>
                            @if (!string.IsNullOrEmpty(description))
                            {
                                <MudText Typo="Typo.subtitle1" Color="Color.Surface" Class="mb-4" data-aos="fade-up" data-aos-delay="400">@description</MudText>
                            }
                            @if (!string.IsNullOrEmpty(linkUrl) && !string.IsNullOrEmpty(linkText))
                            {
                                <MudButton Variant="Variant.Filled" Color="Color.Primary" Size="Size.Large" Href="@linkUrl" data-aos="fade-up" data-aos-delay="600">@linkText</MudButton>
                            }
                        </MudContainer>
                    </div>
                </MudCarouselItem>
            }
        </MudCarousel>
    </div>
}

<MudContainer MaxWidth="MaxWidth.Large" Class="py-16">
    <!-- Company Logo Section (if available from admin) -->
    @if (!_aboutUsLoading && _aboutUsData != null && !string.IsNullOrEmpty(_aboutUsData.CompanyLogoUrl))
    {
        <MudCard Elevation="0" Class="mb-12 company-logo-section">
            <MudCardContent Class="text-center">
                <div class="d-flex justify-center align-center mb-4 px-4" data-aos="zoom-in" data-aos-duration="800" data-aos-once="false" data-aos-mirror="true">
                    <div class="company-logo-container">
                        <MudImage Src="@_aboutUsData.CompanyLogoUrl" Alt="شعار الشركة"
                                 ObjectFit="ObjectFit.Contain" Class="company-logo-circular" />
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    }

    <!-- Overview Section -->
    <MudCard Elevation="0" Class="mb-12 overview-section">
        <MudCardContent>
            <MudText Typo="Typo.h4" Class="mb-6 section-title" data-aos="fade-up" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">نبذة عن الشركة</MudText>
            @if (_aboutUsLoading)
            {
                <div class="d-flex justify-center align-center" style="height: 100px;">
                    <MudProgressCircular Color="Color.Primary" Size="Size.Medium" Indeterminate="true" />
                    <MudText Typo="Typo.body2" Class="mr-4">جاري تحميل البيانات...</MudText>
                </div>
            }
            else
            {
                <MudText Typo="Typo.body1" Class="mb-8 company-description" data-aos="fade-up" data-aos-delay="200" data-aos-duration="700">
                    @(_aboutUsData?.CompanyDescription ?? _companyInfo?.AboutUs ?? "نحن شركة رائدة في مجالنا منذ سنوات عديدة. نقدم خدمات عالية الجودة لعملائنا مع التركيز على رضا العميل والجودة.")
                </MudText>
            }

            <!-- Vision & Mission -->
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudPaper Elevation="0" Class="pa-6 h-100 vision-mission-card" Style="background-color: #ffffff; border-right: 4px solid var(--mud-palette-primary);"
                              data-aos="slide-right" data-aos-delay="300" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                        <MudText Typo="Typo.h5" Class="mb-4 primary-text" data-aos="fade-up" data-aos-delay="400">رؤيتنا</MudText>
                        <MudText Typo="Typo.body1" data-aos="fade-up" data-aos-delay="500">@(_companyInfo?.Vision ?? "أن نكون الشركة الرائدة والمفضلة في مجالنا محلياً وإقليمياً.")</MudText>
                    </MudPaper>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudPaper Elevation="0" Class="pa-6 h-100 vision-mission-card" Style="background-color: #ffffff; border-right: 4px solid var(--mud-palette-primary);"
                              data-aos="slide-left" data-aos-delay="400" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                        <MudText Typo="Typo.h5" Class="mb-4 primary-text" data-aos="fade-up" data-aos-delay="500">رسالتنا</MudText>
                        <MudText Typo="Typo.body1" data-aos="fade-up" data-aos-delay="600">@(_companyInfo?.Mission ?? "تقديم خدمات متميزة ومبتكرة تلبي احتياجات عملائنا وتتجاوز توقعاتهم.")</MudText>
                    </MudPaper>
                </MudItem>
            </MudGrid>

            <!-- Company Capabilities Section (from admin) -->
            @if (!_aboutUsLoading && _aboutUsData != null && !string.IsNullOrEmpty(_aboutUsData.CompanyCapabilities))
            {
                <MudDivider Class="my-8" data-aos="fade-up" data-aos-delay="600" />
                <MudText Typo="Typo.h5" Class="mb-4 primary-text capabilities-title" data-aos="fade-up" data-aos-delay="700">قدراتنا ومميزاتنا</MudText>
                <MudText Typo="Typo.body1" Class="mb-4 capabilities-text" data-aos="fade-up" data-aos-delay="800">@_aboutUsData.CompanyCapabilities</MudText>
            }
        </MudCardContent>
    </MudCard>

    <!-- Our Values Section -->
    <MudCard Elevation="0" Class="mb-12 values-section">
        <MudCardContent>
            <MudText Typo="Typo.h4" Class="mb-6 section-title" data-aos="fade-up" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">قيمنا</MudText>
            <MudGrid>
                <MudItem xs="12" sm="6" md="3">
                    <div class="d-flex flex-column align-center text-center pa-4 value-card"
                         data-aos="flip-up" data-aos-delay="100" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                        <MudIcon Icon="@Icons.Material.Filled.VerifiedUser" Color="Color.Primary" Size="Size.Large" Class="value-icon" data-aos="zoom-in" data-aos-delay="200" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2 value-title" data-aos="fade-up" data-aos-delay="300">الجودة</MudText>
                        <MudText Typo="Typo.body2" Class="value-description" data-aos="fade-up" data-aos-delay="400">نلتزم بتقديم أعلى معايير الجودة في جميع خدماتنا ومنتجاتنا.</MudText>
                    </div>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <div class="d-flex flex-column align-center text-center pa-4 value-card"
                         data-aos="flip-up" data-aos-delay="200" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                        <MudIcon Icon="@Icons.Material.Filled.People" Color="Color.Primary" Size="Size.Large" Class="value-icon" data-aos="zoom-in" data-aos-delay="300" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2 value-title" data-aos="fade-up" data-aos-delay="400">التعاون</MudText>
                        <MudText Typo="Typo.body2" Class="value-description" data-aos="fade-up" data-aos-delay="500">نعمل معاً كفريق واحد لتحقيق أهدافنا المشتركة وخدمة عملائنا.</MudText>
                    </div>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <div class="d-flex flex-column align-center text-center pa-4 value-card"
                         data-aos="flip-up" data-aos-delay="300" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                        <MudIcon Icon="@Icons.Material.Filled.Lightbulb" Color="Color.Primary" Size="Size.Large" Class="value-icon" data-aos="zoom-in" data-aos-delay="400" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2 value-title" data-aos="fade-up" data-aos-delay="500">الابتكار</MudText>
                        <MudText Typo="Typo.body2" Class="value-description" data-aos="fade-up" data-aos-delay="600">نسعى دائماً لتقديم حلول مبتكرة وخدمات متطورة تلبي احتياجات السوق.</MudText>
                    </div>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <div class="d-flex flex-column align-center text-center pa-4 value-card"
                         data-aos="flip-up" data-aos-delay="400" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                        <MudIcon Icon="@Icons.Material.Filled.Security" Color="Color.Primary" Size="Size.Large" Class="value-icon" data-aos="zoom-in" data-aos-delay="500" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2 value-title" data-aos="fade-up" data-aos-delay="600">المسؤولية</MudText>
                        <MudText Typo="Typo.body2" Class="value-description" data-aos="fade-up" data-aos-delay="700">نتحمل المسؤولية تجاه عملائنا والمجتمع والبيئة في جميع أعمالنا.</MudText>
                    </div>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>

    <!-- Production Capacity Section (from admin) -->
    @if (!_aboutUsLoading && _aboutUsData?.ProductionCapacityItems?.Any() == true)
    {
        <MudCard Elevation="0" Class="mb-12 production-section">
            <MudCardContent>
                <MudText Typo="Typo.h4" Class="mb-6 section-title" data-aos="fade-up" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">طاقتنا الإنتاجية</MudText>
                <MudGrid>
                    @{
                        var productionItems = _aboutUsData.ProductionCapacityItems.Where(i => i.IsActive).OrderBy(i => i.DisplayOrder).ToList();
                    }
                    @for (int i = 0; i < productionItems.Count; i++)
                    {
                        var item = productionItems[i];
                        var delay = (i * 150) + 200;

                        <MudItem xs="12" sm="6" md="4">
                            <MudCard Elevation="0" Class="h-100 production-card"
                                     data-aos="zoom-in" data-aos-delay="@delay" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                                @if (!string.IsNullOrEmpty(item.ImageUrl))
                                {
                                    <MudCardMedia Image="@item.ImageUrl" Height="200" Class="production-image" />
                                }
                                <MudCardContent>
                                    <MudText Typo="Typo.h6" Class="mb-3 production-title" data-aos="fade-up" data-aos-delay="@(delay + 100)">@item.Title</MudText>
                                    <MudText Typo="Typo.body2" Class="production-description" data-aos="fade-up" data-aos-delay="@(delay + 200)">@item.Description</MudText>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                    }
                </MudGrid>
            </MudCardContent>
        </MudCard>
    }

    <!-- Our Team Section -->
@*     <MudCard Elevation="0">
        <MudCardContent>
            <MudText Typo="Typo.h4" Class="mb-6">فريق العمل</MudText>
            <MudGrid>
                <MudItem xs="12" sm="6" md="4">
                    <MudCard Elevation="2" Class="h-100">
                        <MudCardMedia Image="/images/team1.jpg" Height="300" />
                        <MudCardContent Class="text-center">
                            <MudText Typo="Typo.h5">محمد أحمد</MudText>
                            <MudText Typo="Typo.subtitle1" Color="Color.Primary">المدير التنفيذي</MudText>
                            <MudText Typo="Typo.body2" Class="mt-3">خبرة أكثر من 15 عاماً في مجال [مجال الشركة]، حاصل على شهادة [الشهادة] من جامعة [الجامعة].</MudText>
                        </MudCardContent>
                    </MudCard>
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudCard Elevation="2" Class="h-100">
                        <MudCardMedia Image="/images/team2.jpg" Height="300" />
                        <MudCardContent Class="text-center">
                            <MudText Typo="Typo.h5">سارة محمد</MudText>
                            <MudText Typo="Typo.subtitle1" Color="Color.Primary">مدير العمليات</MudText>
                            <MudText Typo="Typo.body2" Class="mt-3">خبرة أكثر من 10 سنوات في إدارة المشاريع والعمليات، حاصلة على شهادة [الشهادة] من جامعة [الجامعة].</MudText>
                        </MudCardContent>
                    </MudCard>
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudCard Elevation="2" Class="h-100">
                        <MudCardMedia Image="/images/team3.jpg" Height="300" />
                        <MudCardContent Class="text-center">
                            <MudText Typo="Typo.h5">خالد عبدالله</MudText>
                            <MudText Typo="Typo.subtitle1" Color="Color.Primary">مدير التسويق</MudText>
                            <MudText Typo="Typo.body2" Class="mt-3">خبرة أكثر من 8 سنوات في مجال التسويق والمبيعات، حاصل على شهادة [الشهادة] من جامعة [الجامعة].</MudText>
                        </MudCardContent>
                    </MudCard>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard> *@
</MudContainer>

<!-- Call to Action Section -->
<div class="py-12 cta-section" style="background-color: var(--mud-palette-primary); color: white;" data-aos="fade-up" data-aos-duration="800" data-aos-once="false" data-aos-mirror="true">
    <MudContainer MaxWidth="MaxWidth.Large">
        <MudGrid Justify="Justify.Center" Class="text-center">
            <MudItem xs="12" md="8">
                <MudText Typo="Typo.h4" Class="mb-4 cta-title" data-aos="fade-up" data-aos-delay="200">هل تريد معرفة المزيد عنا؟</MudText>
                <MudText Typo="Typo.body1" Class="mb-6 cta-description" data-aos="fade-up" data-aos-delay="400">نحن سعداء بالتواصل معكم والإجابة على جميع استفساراتكم. يمكنكم الاتصال بنا مباشرة أو زيارة أحد فروعنا.</MudText>
                <div class="d-flex justify-center gap-4 flex-wrap cta-buttons" data-aos="fade-up" data-aos-delay="600">
                    <MudButton Variant="Variant.Filled" Color="Color.Secondary" Href="/contact" Class="cta-button" data-aos="zoom-in" data-aos-delay="700">اتصل بنا</MudButton>
                    <MudButton Variant="Variant.Outlined" Color="Color.Surface" Href="/branches" Class="cta-button-outline" data-aos="zoom-in" data-aos-delay="800">فروعنا</MudButton>
                </div>
            </MudItem>
        </MudGrid>
    </MudContainer>
</div>

<style>
    /* Clean and Modern Design for About Page */

    /* General Page Styling */
    .about-page {
        background-color: #ffffff;
        min-height: 100vh;
    }

    /* Hero Slider */
    .hero-slider {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .absolute-fill {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    /* Company Logo Section */
    .company-logo-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 3rem;
    }

    .company-logo-container {
        width: 300px;
        height: 300px;
        border-radius: 50%;
        overflow: hidden;
        border: 4px solid var(--mud-palette-primary);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
    }

    .company-logo-container:hover {
        transform: scale(1.05);
        box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
    }

    .company-logo-circular {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: contain;
        object-position: center;
    }

    /* Overview Section */
    .overview-section {
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .section-title {
        color: var(--mud-palette-text-primary);
        font-weight: 700;
        text-align: center;
    }

    .company-description {
        color: var(--mud-palette-text-secondary);
        line-height: 1.8;
        text-align: center;
        font-size: 1.1rem;
    }

    /* Vision & Mission Cards */
    .vision-mission-card {
        border-radius: 16px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .vision-mission-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .primary-text {
        color: var(--mud-palette-primary);
        font-weight: 600;
    }

    /* Values Section */
    .values-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 16px;
        padding: 2rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    }

    .value-card {
        background: #ffffff;
        border-radius: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
        margin: 0.5rem;
    }

    .value-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
    }

    .value-icon {
        transition: all 0.3s ease;
    }

    .value-card:hover .value-icon {
        transform: scale(1.1) rotate(5deg);
    }

    .value-title {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
    }

    .value-description {
        color: var(--mud-palette-text-secondary);
        line-height: 1.6;
    }

    /* Production Capacity Section */
    .production-section {
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .production-card {
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        overflow: hidden;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .production-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    }

    .production-image {
        transition: transform 0.3s ease;
    }

    .production-card:hover .production-image {
        transform: scale(1.05);
    }

    .production-title {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
    }

    .production-description {
        color: var(--mud-palette-text-secondary);
        line-height: 1.6;
    }

    /* Call to Action Section */
    .cta-section {
        background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-primary-darken) 100%);
        position: relative;
        overflow: hidden;
    }

    .cta-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, transparent 50%);
    }

    .cta-title {
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .cta-description {
        opacity: 0.95;
        line-height: 1.6;
    }

    .cta-button,
    .cta-button-outline {
        border-radius: 12px;
        padding: 16px 32px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .cta-button:hover,
    .cta-button-outline:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    }

    /* Responsive design for different screen sizes */
    @@media (max-width: 768px) {
        .company-logo-container {
            width: 240px;
            height: 240px;
            padding: 8px;
        }

        .company-logo-section,
        .values-section {
            padding: 1.5rem;
        }

        .value-card {
            margin: 0.25rem;
        }
    }

    /* Medium mobile devices (tablets in portrait) */
    @@media (max-width: 600px) {
        .company-logo-container {
            width: 250px;
            height: 250px;
            padding: 10px;
            margin: 0 auto;
        }
    }

    @@media (max-width: 480px) {
        .company-logo-container {
            width: 230px;
            height: 230px;
            padding: 8px;
            border-width: 3px;
        }

        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }

        .cta-button,
        .cta-button-outline {
            width: 100%;
            max-width: 280px;
        }
    }

    /* Extra small devices (very small phones) */
    @@media (max-width: 360px) {
        .company-logo-container {
            width: 200px;
            height: 200px;
            padding: 6px;
            border-width: 2px;
        }
    }

    /* RTL Support */
    [dir="rtl"] .vision-mission-card {
        border-right: none;
        border-left: 4px solid var(--mud-palette-primary);
    }

    /* AOS Animation Support */
    [data-aos] {
        pointer-events: none;
    }

    [data-aos].aos-animate {
        pointer-events: auto;
    }

    /* Accessibility */
    @@media (prefers-reduced-motion: reduce) {
        * {
            transition: none !important;
            animation: none !important;
        }
    }

    /* Focus States */
    .cta-button:focus,
    .cta-button-outline:focus {
        outline: 2px solid var(--mud-palette-primary);
        outline-offset: 2px;
    }
</style>