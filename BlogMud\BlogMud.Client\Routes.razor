﻿<Router AppAssembly="typeof(Program).Assembly">
    <Found Context="routeData">
        <AuthorizeRouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)">
            <NotAuthorized>
                <RedirectToLogin />
            </NotAuthorized>
        </AuthorizeRouteView>
        <FocusOnNavigate RouteData="routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>Not found</PageTitle>
        <LayoutView Layout="@typeof(Layout.MainLayout)">
            <MudContainer MaxWidth="MaxWidth.Large" Class="py-16">
                <MudAlert Severity="Severity.Error" Class="my-8">
                    عذراً، الصفحة المطلوبة غير موجودة.
                </MudAlert>
                <MudButton Variant="Variant.Outlined" Color="Color.Primary" Href="/">
                    العودة إلى الصفحة الرئيسية
                </MudButton>
            </MudContainer>
        </LayoutView>
    </NotFound>
</Router>
