using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.IO;

namespace BlogMud.Services
{
    /// <summary>
    /// خدمة لإدارة الملفات (رفع، حذف، إلخ)
    /// </summary>
    public class FileService : IFileService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<FileService> _logger;

        public FileService(IWebHostEnvironment environment, ILogger<FileService> logger)
        {
            _environment = environment;
            _logger = logger;
        }

        /// <summary>
        /// حذف ملف إذا كان موجودًا
        /// </summary>
        /// <param name="filePath">مسار الملف المراد حذفه (يمكن أن يكون URL كاملًا أو مسارًا نسبيًا)</param>
        /// <param name="folderName">اسم المجلد الذي يحتوي على الملف</param>
        /// <returns>نجاح أو فشل عملية الحذف</returns>
        public bool DeleteFileIfExists(string filePath, string folderName)
        {
            try
            {
                _logger.LogInformation("Attempting to delete file from path: {FilePath} in folder: {FolderName}", filePath, folderName);

                // التعامل مع تنسيقات URL المختلفة
                // 1. URL كامل مع اسم النطاق: https://domain.com/folderName/filename.jpg
                // 2. URL نسبي مع شرطة مائلة في البداية: /folderName/filename.jpg
                // 3. URL نسبي بدون شرطة مائلة في البداية: folderName/filename.jpg
                // 4. اسم الملف فقط: filename.jpg

                string fileName;

                if (filePath.Contains($"/{folderName}/"))
                {
                    // استخراج اسم الملف من مسارات مثل "/folderName/filename.jpg" أو "https://domain.com/folderName/filename.jpg"
                    int folderIndex = filePath.LastIndexOf($"/{folderName}/") + folderName.Length + 2;
                    if (folderIndex < filePath.Length)
                    {
                        fileName = filePath.Substring(folderIndex);
                    }
                    else
                    {
                        _logger.LogWarning("Invalid file path format: {FilePath}", filePath);
                        return false;
                    }
                }
                else
                {
                    // استخدام اسم الملف كما هو
                    fileName = Path.GetFileName(filePath);
                }

                if (string.IsNullOrEmpty(fileName))
                {
                    _logger.LogWarning("Could not extract filename from path: {FilePath}", filePath);
                    return false;
                }

                // بناء المسار الفعلي الكامل
                string fullPath = Path.Combine(_environment.WebRootPath, folderName, fileName);
                _logger.LogInformation("Resolved file path for deletion: {FullPath}", fullPath);

                if (System.IO.File.Exists(fullPath))
                {
                    System.IO.File.Delete(fullPath);
                    _logger.LogInformation("Successfully deleted file: {FilePath}", fullPath);
                    return true;
                }
                else
                {
                    _logger.LogWarning("File not found for deletion: {FilePath}", fullPath);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file: {FilePath} in folder: {FolderName}", filePath, folderName);
                return false;
            }
        }

        /// <summary>
        /// حذف جميع الصور المرتبطة بشريحة متحركة معينة
        /// </summary>
        /// <param name="imageUrls">روابط الصور مفصولة بفاصلة منقوطة</param>
        /// <returns>عدد الملفات التي تم حذفها بنجاح</returns>
        public int DeleteSidertMoveServiceImages(string imageUrls)
        {
            int deletedCount = 0;

            try
            {
                if (string.IsNullOrEmpty(imageUrls))
                {
                    _logger.LogInformation("No image URLs provided for deletion");
                    return deletedCount;
                }

                _logger.LogInformation("Deleting SidertMoveService images: {ImageUrls}", imageUrls);

                var imageUrlArray = imageUrls.Split(';', StringSplitOptions.RemoveEmptyEntries);

                foreach (var imageUrl in imageUrlArray)
                {
                    var trimmedUrl = imageUrl.Trim();
                    if (!string.IsNullOrEmpty(trimmedUrl))
                    {
                        bool fileDeleted = DeleteFileIfExists(trimmedUrl, "OurServImg/SiderMoveOurServicesImg");
                        if (fileDeleted)
                        {
                            deletedCount++;
                            _logger.LogInformation("Successfully deleted SidertMoveService image: {ImageUrl}", trimmedUrl);
                        }
                        else
                        {
                            _logger.LogWarning("Could not delete SidertMoveService image: {ImageUrl}", trimmedUrl);
                        }
                    }
                }

                _logger.LogInformation("Deleted {DeletedCount} out of {TotalCount} SidertMoveService images",
                    deletedCount, imageUrlArray.Length);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting SidertMoveService images: {ImageUrls}", imageUrls);
            }

            return deletedCount;
        }

        /// <summary>
        /// حذف الصور القديمة عند تحديث شريحة متحركة
        /// </summary>
        /// <param name="oldImageUrls">روابط الصور القديمة</param>
        /// <param name="newImageUrls">روابط الصور الجديدة</param>
        /// <returns>عدد الملفات التي تم حذفها بنجاح</returns>
        public int DeleteOldSidertMoveServiceImages(string oldImageUrls, string newImageUrls)
        {
            int deletedCount = 0;

            try
            {
                if (string.IsNullOrEmpty(oldImageUrls))
                {
                    _logger.LogInformation("No old image URLs to delete");
                    return deletedCount;
                }

                _logger.LogInformation("Checking for old SidertMoveService images to delete. Old: {OldImageUrls}, New: {NewImageUrls}",
                    oldImageUrls, newImageUrls);

                var oldImages = oldImageUrls.Split(';', StringSplitOptions.RemoveEmptyEntries);
                var newImages = newImageUrls?.Split(';', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>();

                foreach (var oldImage in oldImages)
                {
                    var trimmedOldImage = oldImage.Trim();
                    if (!string.IsNullOrEmpty(trimmedOldImage) && !newImages.Contains(trimmedOldImage))
                    {
                        bool fileDeleted = DeleteFileIfExists(trimmedOldImage, "OurServImg/SiderMoveOurServicesImg");
                        if (fileDeleted)
                        {
                            deletedCount++;
                            _logger.LogInformation("Successfully deleted old SidertMoveService image: {ImageUrl}", trimmedOldImage);
                        }
                        else
                        {
                            _logger.LogWarning("Could not delete old SidertMoveService image: {ImageUrl}", trimmedOldImage);
                        }
                    }
                }

                _logger.LogInformation("Deleted {DeletedCount} old SidertMoveService images", deletedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting old SidertMoveService images. Old: {OldImageUrls}, New: {NewImageUrls}",
                    oldImageUrls, newImageUrls);
            }

            return deletedCount;
        }

        /// <summary>
        /// حذف جميع الصور المرتبطة بشريحة متحركة للأخبار معينة
        /// </summary>
        /// <param name="imageUrls">روابط الصور مفصولة بفاصلة منقوطة</param>
        /// <returns>عدد الملفات التي تم حذفها بنجاح</returns>
        public int DeleteNewsSiderMoveImages(string imageUrls)
        {
            int deletedCount = 0;

            try
            {
                if (string.IsNullOrEmpty(imageUrls))
                {
                    _logger.LogInformation("No image URLs provided for deletion");
                    return deletedCount;
                }

                _logger.LogInformation("Deleting NewsSiderMove images: {ImageUrls}", imageUrls);

                var imageUrlArray = imageUrls.Split(';', StringSplitOptions.RemoveEmptyEntries);

                foreach (var imageUrl in imageUrlArray)
                {
                    var trimmedUrl = imageUrl.Trim();
                    if (!string.IsNullOrEmpty(trimmedUrl))
                    {
                        bool fileDeleted = DeleteFileIfExists(trimmedUrl, "NewsSiderMoveImg");
                        if (fileDeleted)
                        {
                            deletedCount++;
                            _logger.LogInformation("Successfully deleted NewsSiderMove image: {ImageUrl}", trimmedUrl);
                        }
                        else
                        {
                            _logger.LogWarning("Could not delete NewsSiderMove image: {ImageUrl}", trimmedUrl);
                        }
                    }
                }

                _logger.LogInformation("Deleted {DeletedCount} out of {TotalCount} NewsSiderMove images",
                    deletedCount, imageUrlArray.Length);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting NewsSiderMove images: {ImageUrls}", imageUrls);
            }

            return deletedCount;
        }

        /// <summary>
        /// حذف جميع الملفات المرتبطة بمحتوى صفحة "من نحن"
        /// </summary>
        /// <param name="companyLogoUrl">رابط شعار الشركة</param>
        /// <param name="productionCapacityImageUrl">رابط صورة الطاقة الإنتاجية</param>
        /// <returns>عدد الملفات التي تم حذفها بنجاح</returns>
        public int DeleteAboutUsFiles(string companyLogoUrl, string productionCapacityImageUrl)
        {
            int deletedCount = 0;

            try
            {
                _logger.LogInformation("Deleting AboutUs files. Logo: {CompanyLogoUrl}, Production Image: {ProductionCapacityImageUrl}",
                    companyLogoUrl, productionCapacityImageUrl);

                // حذف شعار الشركة
                if (!string.IsNullOrEmpty(companyLogoUrl))
                {
                    bool logoDeleted = DeleteFileIfExists(companyLogoUrl, "AboutUsImg");
                    if (logoDeleted)
                    {
                        deletedCount++;
                        _logger.LogInformation("Successfully deleted company logo: {CompanyLogoUrl}", companyLogoUrl);
                    }
                    else
                    {
                        _logger.LogWarning("Could not delete company logo: {CompanyLogoUrl}", companyLogoUrl);
                    }
                }

                // حذف صورة الطاقة الإنتاجية
                if (!string.IsNullOrEmpty(productionCapacityImageUrl))
                {
                    bool productionImageDeleted = DeleteFileIfExists(productionCapacityImageUrl, "AboutUsImg");
                    if (productionImageDeleted)
                    {
                        deletedCount++;
                        _logger.LogInformation("Successfully deleted production capacity image: {ProductionCapacityImageUrl}", productionCapacityImageUrl);
                    }
                    else
                    {
                        _logger.LogWarning("Could not delete production capacity image: {ProductionCapacityImageUrl}", productionCapacityImageUrl);
                    }
                }

                _logger.LogInformation("Deleted {DeletedCount} AboutUs files", deletedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting AboutUs files. Logo: {CompanyLogoUrl}, Production Image: {ProductionCapacityImageUrl}",
                    companyLogoUrl, productionCapacityImageUrl);
            }

            return deletedCount;
        }

        /// <summary>
        /// حذف جميع الصور المرتبطة بشريحة متحركة لصفحة "من نحن" معينة
        /// </summary>
        /// <param name="imageUrls">روابط الصور مفصولة بفاصلة منقوطة</param>
        /// <returns>عدد الملفات التي تم حذفها بنجاح</returns>
        public int DeleteSiderAboutUsImages(string imageUrls)
        {
            int deletedCount = 0;

            try
            {
                if (string.IsNullOrEmpty(imageUrls))
                {
                    _logger.LogInformation("No image URLs provided for deletion");
                    return deletedCount;
                }

                _logger.LogInformation("Deleting SiderAboutUs images: {ImageUrls}", imageUrls);

                var imageUrlArray = imageUrls.Split(';', StringSplitOptions.RemoveEmptyEntries);

                foreach (var imageUrl in imageUrlArray)
                {
                    var trimmedUrl = imageUrl.Trim();
                    if (!string.IsNullOrEmpty(trimmedUrl))
                    {
                        bool fileDeleted = DeleteFileIfExists(trimmedUrl, "AboutUsImg/SiderAboutUsImg");
                        if (fileDeleted)
                        {
                            deletedCount++;
                            _logger.LogInformation("Successfully deleted SiderAboutUs image: {ImageUrl}", trimmedUrl);
                        }
                        else
                        {
                            _logger.LogWarning("Could not delete SiderAboutUs image: {ImageUrl}", trimmedUrl);
                        }
                    }
                }

                _logger.LogInformation("Deleted {DeletedCount} out of {TotalCount} SiderAboutUs images",
                    deletedCount, imageUrlArray.Length);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting SiderAboutUs images: {ImageUrls}", imageUrls);
            }

            return deletedCount;
        }
    }

    /// <summary>
    /// واجهة خدمة إدارة الملفات
    /// </summary>
    public interface IFileService
    {
        /// <summary>
        /// حذف ملف إذا كان موجودًا
        /// </summary>
        /// <param name="filePath">مسار الملف المراد حذفه</param>
        /// <param name="folderName">اسم المجلد الذي يحتوي على الملف</param>
        /// <returns>نجاح أو فشل عملية الحذف</returns>
        bool DeleteFileIfExists(string filePath, string folderName);

        /// <summary>
        /// حذف جميع الصور المرتبطة بشريحة متحركة معينة
        /// </summary>
        /// <param name="imageUrls">روابط الصور مفصولة بفاصلة منقوطة</param>
        /// <returns>عدد الملفات التي تم حذفها بنجاح</returns>
        int DeleteSidertMoveServiceImages(string imageUrls);

        /// <summary>
        /// حذف الصور القديمة عند تحديث شريحة متحركة
        /// </summary>
        /// <param name="oldImageUrls">روابط الصور القديمة</param>
        /// <param name="newImageUrls">روابط الصور الجديدة</param>
        /// <returns>عدد الملفات التي تم حذفها بنجاح</returns>
        int DeleteOldSidertMoveServiceImages(string oldImageUrls, string newImageUrls);

        /// <summary>
        /// حذف جميع الصور المرتبطة بشريحة متحركة للأخبار معينة
        /// </summary>
        /// <param name="imageUrls">روابط الصور مفصولة بفاصلة منقوطة</param>
        /// <returns>عدد الملفات التي تم حذفها بنجاح</returns>
        int DeleteNewsSiderMoveImages(string imageUrls);

        /// <summary>
        /// حذف جميع الملفات المرتبطة بمحتوى صفحة "من نحن"
        /// </summary>
        /// <param name="companyLogoUrl">رابط شعار الشركة</param>
        /// <param name="productionCapacityImageUrl">رابط صورة الطاقة الإنتاجية</param>
        /// <returns>عدد الملفات التي تم حذفها بنجاح</returns>
        int DeleteAboutUsFiles(string companyLogoUrl, string productionCapacityImageUrl);

        /// <summary>
        /// حذف جميع الصور المرتبطة بشريحة متحركة لصفحة "من نحن" معينة
        /// </summary>
        /// <param name="imageUrls">روابط الصور مفصولة بفاصلة منقوطة</param>
        /// <returns>عدد الملفات التي تم حذفها بنجاح</returns>
        int DeleteSiderAboutUsImages(string imageUrls);
    }
}
