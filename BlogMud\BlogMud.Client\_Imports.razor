@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using static Microsoft.AspNetCore.Components.Web.RenderMode
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.Extensions.DependencyInjection
@using Microsoft.JSInterop
@using MudBlazor
@using MudBlazor.Services

@using BlogMud.Client.Pages
@using BlogMud.Client.Pages.Admin
@using BlogMud.Client.Pages.Admin.Posts
@using BlogMud.Client.Layout
@using BlogMud.Client.Components
@using BlogMud.Shared.Models
@using BlogMud.Shared.Repositories
@using BlogMud.Shared.Enums
@using MudBlazor.Interfaces

@using BlogMud.Shared.DTOs
@using System.Threading.Tasks
@using System.Collections.Generic
@using System.ComponentModel.DataAnnotations

@using FluentValidation.Results

@inject HttpClient Http
@inject ISnackbar _Snackbar
@inject NavigationManager _Navigation
@inject IDialogService DialogService
@inject IJSRuntime _JSRuntime
@inject IServiceProvider ServiceProvider
