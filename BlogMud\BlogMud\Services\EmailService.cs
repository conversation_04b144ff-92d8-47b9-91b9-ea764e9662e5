using BlogMud.Data;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.Extensions.Options;
using System.Net;
using System.Net.Mail;
using System.Text;

namespace BlogMud.Services
{
    public class EmailService : IEmailSender<ApplicationUser>
    {
        private readonly EmailSettings _emailSettings;
        private readonly ILogger<EmailService> _logger;

        public EmailService(IOptions<EmailSettings> emailSettings, ILogger<EmailService> logger)
        {
            _emailSettings = emailSettings.Value;
            _logger = logger;
        }

        public async Task SendConfirmationLinkAsync(ApplicationUser user, string email, string confirmationLink)
        {
            var subject = "تأكيد البريد الإلكتروني - BlogMud";
            var htmlBody = GetEmailConfirmationTemplate(user.FullName, confirmationLink);

            await SendEmailAsync(email, subject, htmlBody);
        }

        public async Task SendPasswordResetLinkAsync(ApplicationUser user, string email, string resetLink)
        {
            var subject = "إعادة تعيين كلمة المرور - BlogMud";
            var htmlBody = GetPasswordResetTemplate(user.FullName, resetLink);

            await SendEmailAsync(email, subject, htmlBody);
        }

        public async Task SendPasswordResetCodeAsync(ApplicationUser user, string email, string resetCode)
        {
            var subject = "رمز إعادة تعيين كلمة المرور - BlogMud";
            var htmlBody = GetPasswordResetCodeTemplate(user.FullName, resetCode);

            await SendEmailAsync(email, subject, htmlBody);
        }

        public async Task SendEmailAsync(string email, string subject, string htmlMessage)
        {
            try
            {
                // Check if SMTP credentials are configured
                if (string.IsNullOrEmpty(_emailSettings.Username) || string.IsNullOrEmpty(_emailSettings.Password) ||
                    _emailSettings.Password == "your-app-password-here")
                {
                    _logger.LogWarning("SMTP credentials not configured properly. Email will be logged instead of sent.");
                    _logger.LogInformation("=== EMAIL SIMULATION ===");
                    _logger.LogInformation("To: {Email}", email);
                    _logger.LogInformation("Subject: {Subject}", subject);
                    _logger.LogInformation("Body: {Body}", htmlMessage);
                    _logger.LogInformation("=== END EMAIL SIMULATION ===");

                    // In development, we'll simulate successful email sending
                    _logger.LogInformation("Email simulated successfully for {Email}", email);
                    return;
                }

                using var client = new SmtpClient(_emailSettings.SmtpServer, _emailSettings.SmtpPort);
                client.EnableSsl = _emailSettings.EnableSsl;
                client.UseDefaultCredentials = false;
                client.Credentials = new NetworkCredential(_emailSettings.Username, _emailSettings.Password);

                var mailMessage = new MailMessage
                {
                    From = new MailAddress(_emailSettings.SenderEmail, _emailSettings.SenderName),
                    Subject = subject,
                    Body = htmlMessage,
                    IsBodyHtml = true
                };

                mailMessage.To.Add(email);

                await client.SendMailAsync(mailMessage);
                _logger.LogInformation("Email sent successfully to {Email}", email);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email to {Email}", email);
                throw;
            }
        }

        /// <summary>
        /// Send contact form notification to admin
        /// </summary>
        /// <param name="contactMessage">Contact message details</param>
        /// <returns>Task</returns>
        public async Task SendContactNotificationAsync(BlogMud.Shared.Models.ContactMessage contactMessage)
        {
            try
            {
                var subject = $"رسالة جديدة من موقع BlogMud - {contactMessage.Subject}";
                var htmlBody = GetContactNotificationTemplate(contactMessage);

                // Send to the configured sender email (admin email)
                await SendEmailAsync(_emailSettings.SenderEmail, subject, htmlBody);

                _logger.LogInformation("Contact notification sent for message from {Name} ({Email})",
                    contactMessage.Name, contactMessage.Email);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send contact notification for message from {Name} ({Email})",
                    contactMessage.Name, contactMessage.Email);
                throw;
            }
        }

        private string GetContactNotificationTemplate(BlogMud.Shared.Models.ContactMessage contactMessage)
        {
            return $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>رسالة جديدة من موقع BlogMud</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            direction: rtl;
        }}
        .container {{
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            padding: 20px 0;
            border-bottom: 2px solid #28a745;
            margin-bottom: 20px;
        }}
        .logo {{
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }}
        .title {{
            color: #333;
            font-size: 18px;
            margin: 0;
        }}
        .content {{
            padding: 20px 0;
        }}
        .info-section {{
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-right: 4px solid #28a745;
        }}
        .info-row {{
            margin: 10px 0;
            padding: 5px 0;
        }}
        .label {{
            font-weight: bold;
            color: #495057;
            display: inline-block;
            min-width: 120px;
        }}
        .value {{
            color: #333;
        }}
        .message-section {{
            background-color: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }}
        .message-title {{
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }}
        .message-content {{
            color: #333;
            line-height: 1.6;
        }}
        .footer {{
            text-align: center;
            padding: 20px 0;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
            margin-top: 30px;
        }}
        .admin-note {{
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: center;
        }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>BlogMud</div>
            <h3 class='title'>رسالة جديدة من موقع الويب</h3>
        </div>

        <div class='content'>
            <h2 style='color: #333; margin-bottom: 20px;'>تفاصيل الرسالة</h2>

            <div class='info-section'>
                <div class='info-row'>
                    <span class='label'>الاسم:</span>
                    <span class='value'>{contactMessage.Name}</span>
                </div>
                <div class='info-row'>
                    <span class='label'>البريد الإلكتروني:</span>
                    <span class='value'>{contactMessage.Email}</span>
                </div>
                {(!string.IsNullOrEmpty(contactMessage.Phone) ? $@"
                <div class='info-row'>
                    <span class='label'>رقم الهاتف:</span>
                    <span class='value'>{contactMessage.Phone}</span>
                </div>" : "")}
                <div class='info-row'>
                    <span class='label'>الموضوع:</span>
                    <span class='value'>{contactMessage.Subject}</span>
                </div>
                <div class='info-row'>
                    <span class='label'>تاريخ الإرسال:</span>
                    <span class='value'>{contactMessage.SubmissionDate:dd/MM/yyyy HH:mm}</span>
                </div>
            </div>

            <div class='message-section'>
                <div class='message-title'>نص الرسالة:</div>
                <div class='message-content'>{contactMessage.Message.Replace("\n", "<br>")}</div>
            </div>

            <div class='admin-note'>
                <strong>يرجى الرد على هذه الرسالة في أقرب وقت ممكن.</strong>
            </div>
        </div>

        <div class='footer'>
            <p>© 2024 BlogMud. جميع الحقوق محفوظة.</p>
            <p>هذه رسالة تلقائية من نظام إدارة الموقع</p>
        </div>
    </div>
</body>
</html>";
        }

        private string GetEmailConfirmationTemplate(string fullName, string confirmationLink)
        {
            return $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تأكيد البريد الإلكتروني</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; direction: rtl; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; padding: 20px 0; border-bottom: 2px solid #007bff; }}
        .logo {{ font-size: 24px; font-weight: bold; color: #007bff; }}
        .content {{ padding: 30px 0; }}
        .button {{ display: inline-block; padding: 12px 30px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
        .footer {{ text-align: center; padding: 20px 0; border-top: 1px solid #eee; color: #666; font-size: 14px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>BlogMud</div>
        </div>
        <div class='content'>
            <h2>مرحباً {fullName}!</h2>
            <p>شكراً لك على التسجيل في موقع BlogMud. لإكمال عملية التسجيل، يرجى تأكيد بريدك الإلكتروني بالنقر على الرابط أدناه:</p>
            <div style='text-align: center;'>
                <a href='{confirmationLink}' class='button'>تأكيد البريد الإلكتروني</a>
            </div>
            <p>إذا لم تقم بإنشاء هذا الحساب، يرجى تجاهل هذا البريد الإلكتروني.</p>
            <p>مع أطيب التحيات،<br>فريق BlogMud</p>
        </div>
        <div class='footer'>
            <p>© 2024 BlogMud. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html>";
        }

        private string GetPasswordResetTemplate(string fullName, string resetLink)
        {
            return $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعادة تعيين كلمة المرور</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; direction: rtl; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; padding: 20px 0; border-bottom: 2px solid #dc3545; }}
        .logo {{ font-size: 24px; font-weight: bold; color: #dc3545; }}
        .content {{ padding: 30px 0; }}
        .button {{ display: inline-block; padding: 12px 30px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
        .footer {{ text-align: center; padding: 20px 0; border-top: 1px solid #eee; color: #666; font-size: 14px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>BlogMud</div>
        </div>
        <div class='content'>
            <h2>مرحباً {fullName}!</h2>
            <p>تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك. انقر على الرابط أدناه لإعادة تعيين كلمة المرور:</p>
            <div style='text-align: center;'>
                <a href='{resetLink}' class='button'>إعادة تعيين كلمة المرور</a>
            </div>
            <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.</p>
            <p>مع أطيب التحيات،<br>فريق BlogMud</p>
        </div>
        <div class='footer'>
            <p>© 2024 BlogMud. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html>";
        }

        private string GetPasswordResetCodeTemplate(string fullName, string resetCode)
        {
            return $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>رمز إعادة تعيين كلمة المرور</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; direction: rtl; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; padding: 20px 0; border-bottom: 2px solid #dc3545; }}
        .logo {{ font-size: 24px; font-weight: bold; color: #dc3545; }}
        .content {{ padding: 30px 0; }}
        .code {{ font-size: 24px; font-weight: bold; color: #dc3545; text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 5px; margin: 20px 0; }}
        .footer {{ text-align: center; padding: 20px 0; border-top: 1px solid #eee; color: #666; font-size: 14px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>BlogMud</div>
        </div>
        <div class='content'>
            <h2>مرحباً {fullName}!</h2>
            <p>استخدم الرمز التالي لإعادة تعيين كلمة المرور الخاصة بك:</p>
            <div class='code'>{resetCode}</div>
            <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.</p>
            <p>مع أطيب التحيات،<br>فريق BlogMud</p>
        </div>
        <div class='footer'>
            <p>© 2024 BlogMud. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html>";
        }
    }
}
