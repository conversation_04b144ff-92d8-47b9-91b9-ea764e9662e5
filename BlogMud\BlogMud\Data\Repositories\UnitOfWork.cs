using BlogMud.Shared.Repositories;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BlogMud.Data.Repositories
{
    /// <summary>
    /// Implementation of the Unit of Work pattern
    /// Manages transactions across multiple repositories
    /// </summary>
    public class UnitOfWork : IUnitOfWork
    {
        private readonly ApplicationDbContext _db;
        private readonly Dictionary<Type, object> _repositories;
        private readonly Dictionary<Type, object> _gRepositories;

        /// <summary>
        /// Initializes a new instance of the UnitOfWork class
        /// </summary>
        /// <param name="db">The database context</param>
        public UnitOfWork(ApplicationDbContext db)
        {
            _db = db;
            _repositories = new Dictionary<Type, object>();
            _gRepositories = new Dictionary<Type, object>();
        }

        /// <summary>
        /// Gets a generic repository for the specified entity type
        /// </summary>
        /// <typeparam name="T">The entity type</typeparam>
        /// <returns>A generic repository for the entity type</returns>
        public IRepository<T> Repository<T>() where T : class
        {
            var type = typeof(T);
            if (!_repositories.ContainsKey(type))
            {
                var repositoryType = typeof(Repository<>);
                var repositoryInstance = Activator.CreateInstance(
                    repositoryType.MakeGenericType(type), _db);
                _repositories.Add(type, repositoryInstance);
            }
            return (IRepository<T>)_repositories[type];
        }

        /// <summary>
        /// Gets a domain-specific repository for the specified entity type
        /// </summary>
        /// <typeparam name="T">The entity type</typeparam>
        /// <returns>A domain-specific repository for the entity type</returns>
        public IGRepository<T> GRepository<T>() where T : class
        {
            var type = typeof(T);
            if (!_gRepositories.ContainsKey(type))
            {
                var repositoryType = typeof(GRepository<>);
                var repositoryInstance = Activator.CreateInstance(
                    repositoryType.MakeGenericType(type), _db);
                _gRepositories.Add(type, repositoryInstance);
            }
            return (IGRepository<T>)_gRepositories[type];
        }

        /// <summary>
        /// Saves all changes made through the repositories
        /// </summary>
        public void Save()
        {
            _db.SaveChanges();
        }

        /// <summary>
        /// Saves all changes made through the repositories asynchronously
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task SaveAsync()
        {
            await _db.SaveChangesAsync();
        }

        /// <summary>
        /// Begins a new transaction
        /// </summary>
        /// <returns>A transaction object</returns>
        public IDbContextTransaction BeginTransaction()
        {
            return _db.Database.BeginTransaction();
        }

        /// <summary>
        /// Begins a new transaction asynchronously
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task<IDbContextTransaction> BeginTransactionAsync()
        {
            return await _db.Database.BeginTransactionAsync();
        }

        private bool _disposed = false;

        /// <summary>
        /// Disposes the resources used by the UnitOfWork
        /// </summary>
        /// <param name="disposing">Whether to dispose managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _db.Dispose();
                }
            }
            _disposed = true;
        }

        /// <summary>
        /// Disposes the resources used by the UnitOfWork
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
