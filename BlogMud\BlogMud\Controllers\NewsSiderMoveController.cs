using AutoMapper;
using BlogMud.Services;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlogMud.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class NewsSiderMoveController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IRepository<NewsSiderMove> _newsSiderMoveRepository;
        private readonly ILogger<NewsSiderMoveController> _logger;
        private readonly IMapper _mapper;
        private readonly IFileService _fileService;

        public NewsSiderMoveController(
            IUnitOfWork unitOfWork,
            IRepository<NewsSiderMove> newsSiderMoveRepository,
            ILogger<NewsSiderMoveController> logger,
            IMapper mapper,
            IFileService fileService)
        {
            _unitOfWork = unitOfWork;
            _newsSiderMoveRepository = newsSiderMoveRepository;
            _logger = logger;
            _mapper = mapper;
            _fileService = fileService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<NewsSiderMoveDto>>> GetAllNewsSiderMoves()
        {
            try
            {
                _logger.LogInformation("Fetching all news sider moves");
                var newsSiderMoves = await _newsSiderMoveRepository.GetAllAsync(
                    orderBy: q => q.OrderBy(s => s.DisplayOrder).ThenByDescending(s => s.CreatedAt)
                );
                var newsSiderMoveDtos = _mapper.Map<IEnumerable<NewsSiderMoveDto>>(newsSiderMoves);
                return Ok(newsSiderMoveDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching all news sider moves");
                return StatusCode(500, "حدث خطأ أثناء استرجاع الشرائح المتحركة للأخبار");
            }
        }

        [HttpGet("active")]
        public async Task<ActionResult<IEnumerable<NewsSiderMoveDto>>> GetActiveNewsSiderMoves()
        {
            try
            {
                _logger.LogInformation("Fetching active news sider moves");
                var newsSiderMoves = await _newsSiderMoveRepository.GetAllAsync(
                    filter: s => s.IsActive,
                    orderBy: q => q.OrderBy(s => s.DisplayOrder)
                );
                var newsSiderMoveDtos = _mapper.Map<IEnumerable<NewsSiderMoveDto>>(newsSiderMoves);
                return Ok(newsSiderMoveDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching active news sider moves");
                return StatusCode(500, "حدث خطأ أثناء استرجاع الشرائح المتحركة للأخبار النشطة");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<NewsSiderMoveDto>> GetNewsSiderMove(int id)
        {
            try
            {
                _logger.LogInformation("Fetching news sider move with ID: {NewsSiderMoveId}", id);
                var newsSiderMove = await _newsSiderMoveRepository.GetByIdAsync(id);

                if (newsSiderMove == null)
                {
                    _logger.LogWarning("News sider move not found with ID: {NewsSiderMoveId}", id);
                    return NotFound();
                }

                var newsSiderMoveDto = _mapper.Map<NewsSiderMoveDto>(newsSiderMove);
                return Ok(newsSiderMoveDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching news sider move with ID: {NewsSiderMoveId}", id);
                return StatusCode(500, "حدث خطأ أثناء استرجاع الشريحة المتحركة للأخبار");
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<NewsSiderMoveDto>> CreateNewsSiderMove(NewsSiderMoveDto newsSiderMoveDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Invalid model state for creating news sider move");
                    return BadRequest(ModelState);
                }

                _logger.LogInformation("Creating new news sider move: {Title}", newsSiderMoveDto.Title);

                var newsSiderMove = _mapper.Map<NewsSiderMove>(newsSiderMoveDto);
                newsSiderMove.CreatedAt = DateTime.Now;
                newsSiderMove.LastModifiedAt = null;

                await _newsSiderMoveRepository.AddAsync(newsSiderMove);
                await _unitOfWork.SaveAsync();

                var createdNewsSiderMoveDto = _mapper.Map<NewsSiderMoveDto>(newsSiderMove);

                _logger.LogInformation("Successfully created news sider move with ID: {NewsSiderMoveId}", newsSiderMove.Id);
                return CreatedAtAction(nameof(GetNewsSiderMove), new { id = newsSiderMove.Id }, createdNewsSiderMoveDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating news sider move");
                return StatusCode(500, "حدث خطأ أثناء إنشاء الشريحة المتحركة للأخبار");
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<NewsSiderMoveDto>> UpdateNewsSiderMove(int id, NewsSiderMoveDto newsSiderMoveDto)
        {
            try
            {
                if (id != newsSiderMoveDto.Id)
                {
                    _logger.LogWarning("ID mismatch in update request. URL ID: {UrlId}, DTO ID: {DtoId}", id, newsSiderMoveDto.Id);
                    return BadRequest("معرف الشريحة المتحركة للأخبار غير متطابق");
                }

                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Invalid model state for updating news sider move with ID: {NewsSiderMoveId}", id);
                    return BadRequest(ModelState);
                }

                var existingNewsSiderMove = await _newsSiderMoveRepository.GetByIdAsync(id);
                if (existingNewsSiderMove == null)
                {
                    _logger.LogWarning("News sider move not found with ID: {NewsSiderMoveId}", id);
                    return NotFound();
                }

                _logger.LogInformation("Updating news sider move with ID: {NewsSiderMoveId}", id);

                var oldImageUrls = existingNewsSiderMove.ImageUrls;

                _mapper.Map(newsSiderMoveDto, existingNewsSiderMove);
                existingNewsSiderMove.LastModifiedAt = DateTime.Now;

                _newsSiderMoveRepository.Update(existingNewsSiderMove);
                await _unitOfWork.SaveAsync();

                if (!string.IsNullOrEmpty(oldImageUrls) && oldImageUrls != newsSiderMoveDto.ImageUrls)
                {
                    try
                    {
                        int deletedCount = _fileService.DeleteNewsSiderMoveImages(oldImageUrls);
                        _logger.LogInformation("Deleted {DeletedCount} old image files for NewsSiderMove ID: {NewsSiderMoveId}",
                            deletedCount, id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error deleting old image files for NewsSiderMove ID: {NewsSiderMoveId}", id);
                    }
                }

                var updatedNewsSiderMoveDto = _mapper.Map<NewsSiderMoveDto>(existingNewsSiderMove);

                _logger.LogInformation("Successfully updated news sider move with ID: {NewsSiderMoveId}", id);
                return Ok(updatedNewsSiderMoveDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating news sider move with ID: {NewsSiderMoveId}", id);
                return StatusCode(500, "حدث خطأ أثناء تحديث الشريحة المتحركة للأخبار");
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> DeleteNewsSiderMove(int id)
        {
            try
            {
                _logger.LogInformation("Deleting news sider move with ID: {NewsSiderMoveId}", id);

                var newsSiderMove = await _newsSiderMoveRepository.GetByIdAsync(id);
                if (newsSiderMove == null)
                {
                    _logger.LogWarning("News sider move not found with ID: {NewsSiderMoveId}", id);
                    return NotFound();
                }

                if (!string.IsNullOrEmpty(newsSiderMove.ImageUrls))
                {
                    try
                    {
                        int deletedCount = _fileService.DeleteNewsSiderMoveImages(newsSiderMove.ImageUrls);
                        _logger.LogInformation("Deleted {DeletedCount} image files for NewsSiderMove ID: {NewsSiderMoveId}",
                            deletedCount, id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error deleting image files for NewsSiderMove ID: {NewsSiderMoveId}", id);
                    }
                }

                _newsSiderMoveRepository.Remove(newsSiderMove);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Successfully deleted news sider move with ID: {NewsSiderMoveId}", id);
                return Ok(new { message = "تم حذف الشريحة المتحركة للأخبار بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting news sider move with ID: {NewsSiderMoveId}", id);
                return StatusCode(500, "حدث خطأ أثناء حذف الشريحة المتحركة للأخبار");
            }
        }
    }
}
