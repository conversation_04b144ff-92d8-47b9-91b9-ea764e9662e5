@using BlogMud.Shared.DTOs
@using System.Net.Http.Json
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject MudBlazor.IDialogService DialogService

<MudForm @ref="_form" Model="@AnimatedGif">
    <!-- Debug information - hidden by default, can be shown for troubleshooting -->
    <MudAlert Severity="Severity.Info" Class="mb-3" Style="display:none">
        <strong>Debug Info:</strong><br />
        ID: @AnimatedGif?.Id<br />
        Title: @AnimatedGif?.Title<br />
        Description: @AnimatedGif?.Description<br />
        ImageUrl: @AnimatedGif?.ImageUrl<br />
        DisplayOrder: @AnimatedGif?.DisplayOrder<br />
        IsActive: @AnimatedGif?.IsActive
    </MudAlert>

    @if (AnimatedGif != null)
    {
        <MudGrid>
            <MudItem xs="12">
                <MudTextField @bind-Value="AnimatedGif.Title"
                             Label="العنوان" />
            </MudItem>
            <MudItem xs="12">
                <MudTextField @bind-Value="AnimatedGif.Description"
                             Label="الوصف" Lines="3" />
            </MudItem>
            <MudItem xs="12">
                <MudTextField @bind-Value="AnimatedGif.ImageUrl"
                             Label="رابط الصورة" Required="true" />
            </MudItem>
            <MudItem xs="12">
                <MudNumericField @bind-Value="AnimatedGif.DisplayOrder"
                                Label="ترتيب العرض" Min="0" />
            </MudItem>
            <MudItem xs="12">
                <MudSwitch T="bool" @bind-Checked="AnimatedGif.IsActive"
                          Label="نشط" Color="Color.Primary" />
            </MudItem>
        </MudGrid>
    }
    else
    {
        <div class="d-flex align-center">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
            <MudText Class="ml-3">جاري تحميل البيانات...</MudText>
        </div>
    }

    <MudDivider Class="my-3" />

    <MudText Typo="Typo.subtitle1" Class="mt-4 mb-2">تحميل صورة جديدة</MudText>
    @if (AnimatedGif != null)
    {
        <MudFileUpload T="IBrowserFile" Accept=".png, .jpg, .jpeg, .gif" FilesChanged="@UploadSingleFile" MaximumFileCount="1">
            <ActivatorContent>
                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           StartIcon="@Icons.Material.Filled.CloudUpload"
                           Disabled="@_isUploading">
                    @if (_isUploading)
                    {
                        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                        <MudText Class="ms-2">جاري الرفع...</MudText>
                    }
                    else
                    {
                        <MudText>تحميل صورة</MudText>
                    }
                </MudButton>
            </ActivatorContent>
        </MudFileUpload>
    }

    @if (AnimatedGif != null && !string.IsNullOrEmpty(AnimatedGif.ImageUrl))
    {
        <MudPaper Class="mt-3 pa-2" Elevation="0">
            <MudText Typo="Typo.subtitle2">معاينة الصورة الحالية:</MudText>
            <img src="@AnimatedGif.ImageUrl" style="max-height: 150px; max-width: 100%; object-fit: contain;" alt="@AnimatedGif.Title" />
        </MudPaper>
    }
</MudForm>

<div class="d-flex justify-end mt-4">
    <MudButton Variant="Variant.Outlined" Color="Color.Default" Class="mr-2" OnClick="Cancel">إلغاء</MudButton>
    <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="Submit">حفظ</MudButton>
</div>

@code {
    private MudForm _form;
    private bool _isUploading = false;

    [CascadingParameter] public required IMudDialogInstance MudDialog { get; set; }

    [Parameter]
    public AnimatedGifDto AnimatedGif { get; set; }

    private void Submit()
    {
        MudDialog.Close(DialogResult.Ok(AnimatedGif));
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    /// <summary>
    /// Optimized single file upload method for edit dialog
    /// </summary>
    /// <param name="file">Single browser file to upload</param>
    private async Task UploadSingleFile(IBrowserFile? file)
    {
        if (file == null)
            return;

        // Prevent multiple simultaneous uploads
        if (_isUploading)
        {
            Snackbar.Add("يتم رفع ملف آخر حالياً، يرجى الانتظار", Severity.Warning);
            return;
        }

        try
        {
            _isUploading = true;
            StateHasChanged(); // Update UI to show loading state

            // Validate file size (max 5MB)
            const long maxFileSize = 5 * 1024 * 1024; // 5MB
            if (file.Size > maxFileSize)
            {
                Snackbar.Add("حجم الملف يتجاوز الحد الأقصى (5 ميجابايت)", Severity.Error);
                return;
            }

            // Validate file type
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
            var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();

            if (!allowedExtensions.Contains(fileExtension))
            {
                Snackbar.Add("يُسمح فقط بملفات الصور (jpg, jpeg, png, gif)", Severity.Error);
                return;
            }

            // Store the old image URL for potential deletion
            string oldImageUrl = AnimatedGif.ImageUrl;

            // Generate a unique filename to prevent conflicts
            var fileName = $"{Guid.NewGuid()}{fileExtension}";

            // Create the form data for the file upload
            using var content = new MultipartFormDataContent();

            // Add the old image URL if it exists for server-side cleanup
            if (!string.IsNullOrEmpty(oldImageUrl))
            {
                content.Add(new StringContent(oldImageUrl), "oldImageUrl");
            }

            // Open file stream with size limit to prevent memory issues
            using var fileStream = file.OpenReadStream(maxAllowedSize: maxFileSize);
            using var streamContent = new StreamContent(fileStream);

            // Set content type for better server handling
            streamContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(file.ContentType);

            // Add the file to the form data
            content.Add(streamContent, "file", fileName);

            // Send the file to the server with timeout
            using var httpClient = new HttpClient { Timeout = TimeSpan.FromMinutes(5) };
            httpClient.BaseAddress = Http.BaseAddress;

            // Copy authorization headers if they exist
            if (Http.DefaultRequestHeaders.Authorization != null)
            {
                httpClient.DefaultRequestHeaders.Authorization = Http.DefaultRequestHeaders.Authorization;
            }

            var response = await httpClient.PostAsync("api/upload/animatedgif", content);

            if (response.IsSuccessStatusCode)
            {
                // Get the file path from the response
                var uploadedFilePath = await response.Content.ReadAsStringAsync();

                // Clean up the response (remove quotes if present)
                uploadedFilePath = uploadedFilePath.Trim('"');

                // Update the image URL in the form
                AnimatedGif.ImageUrl = uploadedFilePath;

                Snackbar.Add("تم رفع الملف بنجاح", Severity.Success);
                StateHasChanged();
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                Snackbar.Add($"خطأ في رفع الملف: {error}", Severity.Error);
            }
        }
        catch (TaskCanceledException)
        {
            Snackbar.Add("انتهت مهلة رفع الملف، يرجى المحاولة مرة أخرى", Severity.Error);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في رفع الملف: {ex.Message}", Severity.Error);
            Console.WriteLine($"File upload error: {ex}");
        }
        finally
        {
            _isUploading = false;
            StateHasChanged(); // Update UI to hide loading state
        }
    }
}
