using Microsoft.JSInterop;
using System.Threading.Tasks;

namespace BlogMud.Client.Services
{
    public class RtlService
    {
        private readonly IJSRuntime _jsRuntime;
        private const string StorageKey = "blogmud_rtl_enabled";
        private bool _isRtlEnabled = true; // Default to true (RTL enabled)

        public bool IsRtlEnabled => _isRtlEnabled;

        public RtlService(IJSRuntime jsRuntime)
        {
            _jsRuntime = jsRuntime;
        }

        public async Task InitializeAsync()
        {
            // Try to load the RTL setting from local storage
            var storedValue = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", StorageKey);
            
            if (!string.IsNullOrEmpty(storedValue) && bool.TryParse(storedValue, out bool result))
            {
                _isRtlEnabled = result;
            }
        }

        public async Task ToggleRtlAsync()
        {
            _isRtlEnabled = !_isRtlEnabled;
            await SaveRtlSettingAsync();
        }

        public async Task SetRtlAsync(bool isEnabled)
        {
            _isRtlEnabled = isEnabled;
            await SaveRtlSettingAsync();
        }

        private async Task SaveRtlSettingAsync()
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", StorageKey, _isRtlEnabled.ToString().ToLower());
        }
    }
}
