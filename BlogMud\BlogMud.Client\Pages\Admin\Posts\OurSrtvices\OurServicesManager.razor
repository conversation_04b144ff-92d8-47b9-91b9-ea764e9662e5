﻿@page "/admin/ourServicesManager"


@attribute [Authorize(Roles = "Admin")]
@layout BlogMud.Client.Layout.AdminLayout

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">إدارة خدماتنا</MudText>

    <!-- شريط الأدوات العلوي المحسن -->
    <MudPaper Class="pa-3 pa-md-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center" Spacing="2">
            <!-- معلومات الخدمات -->
            <MudItem xs="12" sm="12" md="7" lg="8" xl="9">
                <div class="d-flex flex-column">
                    <MudText Typo="Typo.h5" Class="mb-1 d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.MiscellaneousServices" Class="me-2" Color="Color.Primary" />
                        إدارة الخدمات
                    </MudText>
                    
                    <!-- إحصائيات مفصلة للشاشات الكبيرة -->
                    <div class="d-none d-lg-flex align-center gap-4 mt-2">
                        <MudChip T="string" Size="Size.Small" Color="Color.Info" Icon="@Icons.Material.Filled.List">
                            المجموع: @_services.Count
                        </MudChip>
                        <MudChip T="string" Size="Size.Small" Color="Color.Success" Icon="@Icons.Material.Filled.CheckCircle">
                            النشطة: @_services.Count(s => s.IsActive)
                        </MudChip>
                        <MudChip T="string" Size="Size.Small" Color="Color.Warning" Icon="@Icons.Material.Filled.Cancel">
                            غير النشطة: @_services.Count(s => !s.IsActive)
                        </MudChip>
                    </div>
                    
                    <!-- إحصائيات مبسطة للشاشات المتوسطة -->
                    <div class="d-none d-md-flex d-lg-none align-center gap-2 mt-2">
                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                            المجموع: @_services.Count | النشطة: @_services.Count(s => s.IsActive) | غير النشطة: @_services.Count(s => !s.IsActive)
                        </MudText>
                    </div>
                    
                    <!-- إحصائيات مختصرة للشاشات الصغيرة -->
                    <div class="d-flex d-md-none align-center gap-2 mt-1">
                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                            @_services.Count خدمة (@_services.Count(s => s.IsActive) نشطة)
                        </MudText>
                    </div>
                </div>
            </MudItem>

            <!-- الأزرار -->
            <MudItem xs="12" sm="12" md="5" lg="4" xl="3">
                <div class="d-flex justify-end justify-sm-center justify-md-end">
                    <!-- للشاشات الكبيرة: أزرار بالنص -->
                    <div class="d-none d-lg-flex gap-2 flex-wrap">
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Warning"
                                   OnClick="CleanupUnusedFiles"
                                   StartIcon="@Icons.Material.Filled.CleaningServices"
                                   Size="Size.Small"
                                   Class="text-nowrap">
                            تنظيف الملفات
                        </MudButton>
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   OnClick="OpenAddServiceDialog"
                                   StartIcon="@Icons.Material.Filled.Add"
                                   Size="Size.Small"
                                   Class="text-nowrap">
                            إضافة خدمة
                        </MudButton>
                    </div>

                    <!-- للشاشات المتوسطة: أزرار مختصرة -->
                    <div class="d-none d-md-flex d-lg-none gap-2">
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Warning"
                                   OnClick="CleanupUnusedFiles"
                                   StartIcon="@Icons.Material.Filled.CleaningServices"
                                   Size="Size.Small">
                            تنظيف
                        </MudButton>
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   OnClick="OpenAddServiceDialog"
                                   StartIcon="@Icons.Material.Filled.Add"
                                   Size="Size.Small">
                            إضافة
                        </MudButton>
                    </div>

                    <!-- للشاشات الصغيرة: أزرار أيقونات فقط -->
                    <div class="d-flex d-md-none gap-2">
                        <MudTooltip Text="تنظيف الملفات غير المستخدمة">
                            <MudIconButton Icon="@Icons.Material.Filled.CleaningServices"
                                           Color="Color.Warning"
                                           OnClick="CleanupUnusedFiles"
                                           Size="Size.Large"
                                           Variant="Variant.Outlined"
                                           Class="rounded-lg" />
                        </MudTooltip>
                        <MudTooltip Text="إضافة خدمة جديدة">
                            <MudIconButton Icon="@Icons.Material.Filled.Add"
                                           Color="Color.Primary"
                                           OnClick="OpenAddServiceDialog"
                                           Size="Size.Large"
                                           Variant="Variant.Filled"
                                           Class="rounded-lg" />
                        </MudTooltip>
                    </div>
                </div>
            </MudItem>
        </MudGrid>
        
        <!-- شريط إضافي للإحصائيات السريعة على الشاشات الصغيرة -->
        <div class="d-flex d-md-none justify-space-between align-center mt-3 pt-3" style="border-top: 1px solid var(--mud-palette-divider);">
            <MudText Typo="Typo.caption" Color="Color.Primary">
                <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Size="Size.Small" Class="me-1" />
                آخر تحديث: @DateTime.Now.ToString("HH:mm")
            </MudText>
            <div class="d-flex gap-1">
                <MudChip T="string" Size="Size.Small" Color="Color.Success" Variant="Variant.Text">
                    @_services.Count(s => s.IsActive)
                </MudChip>
                <MudChip T="string" Size="Size.Small" Color="Color.Default" Variant="Variant.Text">
                    من @_services.Count
                </MudChip>
            </div>
        </div>
    </MudPaper>

    <MudGrid>
        <!-- معاينة الخدمات النشطة -->
        <MudItem xs="12">
            <MudPaper Class="pa-4 mb-4">
                <MudText Typo="Typo.h6" Class="mb-3">معاينة الخدمات النشطة</MudText>

                @if (_services.Any(s => s.IsActive))
                {
                    <MudCarousel Class="mud-width-full" 
                                Style="height: 350px; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);"
                                ShowArrows="true" 
                                ShowBullets="true" 
                                EnableSwipeGesture="true" 
                                AutoCycle="true"
                                AutoCycleTime="TimeSpan.FromSeconds(5)"
                                TData="object">
                        @foreach (var service in _services.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder))
                        {
                            <MudCarouselItem Transition="Transition.Slide" Color="Color.Default">
                                <div style="padding: 8px; height: 100%;">
                                    <MudCard Class="h-100 d-flex flex-column" Elevation="3" Style="border-radius: 12px; background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);">
                                        <!-- صورة الخدمة -->
                                        <div style="height: 200px; overflow: hidden; position: relative; border-radius: 8px 8px 0 0;">
                                            @if (!string.IsNullOrEmpty(service.MainImageUrl))
                                            {
                                                <MudImage Src="@service.MainImageUrl"
                                                         Alt="@service.Title"
                                                         Style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.3s ease;" />
                                            }
                                            else
                                            {
                                                <div class="d-flex justify-center align-center h-100" 
                                                     style="background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
                                                    <MudIcon Icon="@Icons.Material.Filled.MiscellaneousServices" 
                                                             Size="Size.Large" 
                                                             Color="Color.Primary" />
                                                </div>
                                            }
                                            
                                            <!-- شارة الحالة -->
                                            <div class="position-absolute" style="top: 12px; right: 12px;">
                                                <MudChip T="string" 
                                                        Size="Size.Small" 
                                                        Color="Color.Success" 
                                                        Icon="@Icons.Material.Filled.CheckCircle"
                                                        Variant="Variant.Filled"
                                                        Style="backdrop-filter: blur(10px); box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
                                                    نشط
                                                </MudChip>
                                            </div>
                                            
                                            <!-- رقم الترتيب -->
                                            <div class="position-absolute" style="top: 12px; left: 12px;">
                                                <MudChip T="string" 
                                                        Size="Size.Small" 
                                                        Color="Color.Info" 
                                                        Variant="Variant.Filled"
                                                        Style="backdrop-filter: blur(10px); box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
                                                    #@service.DisplayOrder
                                                </MudChip>
                                            </div>
                                        </div>
                                        
                                        <!-- محتوى البطاقة -->
                                        <MudCardContent Class="flex-grow-1 d-flex flex-column pa-4">
                                            <MudText Typo="Typo.h5" Class="mb-2 text-center" Color="Color.Primary" Style="font-weight: 600; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);">
                                                @service.Title
                                            </MudText>
                                            
                                            <MudDivider Class="mb-3" Style="background: linear-gradient(90deg, transparent 0%, var(--mud-palette-divider) 50%, transparent 100%);" />
                                            
                                            <div class="flex-grow-1 d-flex align-center">
                                                <MudText Typo="Typo.body1" 
                                                        Class="text-center" 
                                                        Color="Color.Secondary"
                                                        Style="line-height: 1.6;">
                                                    @(service.PrimaryDescription?.Length > 120 ?
                                                      service.PrimaryDescription.Substring(0, 120) + "..." :
                                                      service.PrimaryDescription ?? "لا يوجد وصف متاح")
                                                </MudText>
                                            </div>
                                            
                                            <!-- معلومات إضافية -->
                                            <div class="d-flex justify-space-between align-center mt-3 pt-3" 
                                                 style="border-top: 1px solid var(--mud-palette-divider);">
                                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                    <MudIcon Icon="@Icons.Material.Filled.DateRange" Size="Size.Small" Class="me-1" />
                                                    @service.CreatedAt.ToString("yyyy-MM-dd")
                                                </MudText>
                                                
                                                @if (!string.IsNullOrEmpty(service.Features))
                                                {
                                                    <MudTooltip Text="@($"المميزات: {service.Features.Replace(";", " • ")}")">
                                                        <MudChip T="string" 
                                                                Size="Size.Small" 
                                                                Color="Color.Warning" 
                                                                Icon="@Icons.Material.Filled.Star"
                                                                Variant="Variant.Text">
                                                            @service.Features.Split(';', StringSplitOptions.RemoveEmptyEntries).Length مميزة
                                                        </MudChip>
                                                    </MudTooltip>
                                                }
                                            </div>
                                        </MudCardContent>
                                    </MudCard>
                                </div>
                            </MudCarouselItem>
                        }
                    </MudCarousel>
                }
                else
                {
                    <MudAlert Severity="Severity.Info" Class="text-center">
                        <div class="d-flex flex-column align-center pa-4">
                            <MudIcon Icon="@Icons.Material.Filled.Info" Size="Size.Large" Class="mb-2" />
                            <MudText Typo="Typo.h6">لا توجد خدمات نشطة للعرض</MudText>
                            <MudText Typo="Typo.body2" Color="Color.Secondary">
                                قم بإضافة خدمات جديدة أو تفعيل الخدمات الموجودة لعرضها هنا
                            </MudText>
                        </div>
                    </MudAlert>
                }
            </MudPaper>
        </MudItem>
    </MudGrid>

    <!-- جدول إدارة الخدمات -->
    <MudPaper Class="pa-3 pa-md-4" Elevation="1">
        <div class="d-flex flex-column flex-md-row justify-space-between align-center mb-3 gap-2">
            <MudText Typo="Typo.h6" Class="d-flex align-center">
                <MudIcon Icon="@Icons.Material.Filled.TableView" Class="me-2" Color="Color.Primary" />
                جميع الخدمات
            </MudText>
            
            <!-- عداد سريع للشاشات الكبيرة -->
            <div class="d-none d-md-flex align-center gap-2">
                <MudText Typo="Typo.body2" Color="Color.Secondary">
                    عرض @_services.Count من الخدمات
                </MudText>
            </div>
        </div>

        <!-- شريط البحث المحسن -->
        <MudTextField @bind-Value="_searchString"
                     Placeholder="البحث في الخدمات..."
                     Adornment="Adornment.Start"
                     AdornmentIcon="@Icons.Material.Filled.Search"
                     IconSize="Size.Medium"
                     Class="mb-3"
                     Variant="Variant.Outlined"
                     Margin="Margin.Dense" />

        <!-- عرض الجدول للشاشات الكبيرة -->
        <div class="d-none d-lg-block">
            <MudTable Items="@_services"
                     Loading="@_loading"
                     Filter="FilterFunc"
                     Dense="true"
                     Hover="true"
                     Bordered="true"
                     Striped="true"
                     FixedHeader="true"
                     Height="600px">
                <HeaderContent>
                    <MudTh><MudText Typo="Typo.subtitle2">المعرف</MudText></MudTh>
                    <MudTh><MudText Typo="Typo.subtitle2">العنوان</MudText></MudTh>
                    <MudTh><MudText Typo="Typo.subtitle2">الوصف</MudText></MudTh>
                    <MudTh><MudText Typo="Typo.subtitle2">الصورة</MudText></MudTh>
                    <MudTh><MudText Typo="Typo.subtitle2">الترتيب</MudText></MudTh>
                    <MudTh><MudText Typo="Typo.subtitle2">الحالة</MudText></MudTh>
                    <MudTh><MudText Typo="Typo.subtitle2">التاريخ</MudText></MudTh>
                    <MudTh><MudText Typo="Typo.subtitle2">الإجراءات</MudText></MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                            #@context.Id
                        </MudText>
                    </MudTd>
                    <MudTd>
                        <MudText Typo="Typo.body2" Style="max-width: 200px; font-weight: 500;">
                            @context.Title
                        </MudText>
                    </MudTd>
                    <MudTd>
                        <MudText Typo="Typo.body2" Style="max-width: 250px;" Color="Color.Secondary">
                            @(context.PrimaryDescription?.Length > 50 ?
                              context.PrimaryDescription.Substring(0, 50) + "..." :
                              context.PrimaryDescription)
                        </MudText>
                    </MudTd>
                    <MudTd>
                        @if (!string.IsNullOrEmpty(context.MainImageUrl))
                        {
                            <MudImage Src="@context.MainImageUrl"
                                     Alt="@context.Title"
                                     Width="60"
                                     Height="40"
                                     ObjectFit="ObjectFit.Cover"
                                     Class="rounded-lg shadow-sm" />
                        }
                        else
                        {
                            <MudIcon Icon="@Icons.Material.Filled.Image" Color="Color.Default" Size="Size.Large" />
                        }
                    </MudTd>
                    <MudTd>
                        <MudChip T="string" Size="Size.Small" Color="Color.Info" Variant="Variant.Filled">
                            @context.DisplayOrder
                        </MudChip>
                    </MudTd>
                    <MudTd>
                        <MudChip T="string" 
                                Size="Size.Small" 
                                Color="@(context.IsActive ? Color.Success : Color.Error)"
                                Icon="@(context.IsActive ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)"
                                Variant="Variant.Text">
                            @(context.IsActive ? "نشط" : "غير نشط")
                        </MudChip>
                    </MudTd>
                    <MudTd>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                            @context.CreatedAt.ToString("yyyy-MM-dd")
                        </MudText>
                    </MudTd>
                    <MudTd>
                        <div class="d-flex gap-1">
                            <MudTooltip Text="عرض التفاصيل">
                                <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                              Color="Color.Info"
                                              Size="Size.Small"
                                              Variant="Variant.Text"
                                              OnClick="@(() => ViewServiceDetails(context))" />
                            </MudTooltip>
                            <MudTooltip Text="تعديل">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                              Color="Color.Primary"
                                              Size="Size.Small"
                                              Variant="Variant.Text"
                                              OnClick="@(() => OpenEditDialog(context))" />
                            </MudTooltip>
                            <MudTooltip Text="حذف">
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                              Color="Color.Error"
                                              Size="Size.Small"
                                              Variant="Variant.Text"
                                              OnClick="@(() => DeleteService(context.Id))" />
                            </MudTooltip>
                        </div>
                    </MudTd>
                </RowTemplate>
                <NoRecordsContent>
                    <div class="d-flex flex-column align-center pa-4">
                        <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Secondary" />
                        <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mt-2">لا توجد خدمات</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">جرب تعديل معايير البحث</MudText>
                    </div>
                </NoRecordsContent>
                <LoadingContent>
                    <div class="d-flex justify-center pa-4">
                        <MudProgressCircular Indeterminate="true" />
                        <MudText Class="ms-2">جاري التحميل...</MudText>
                    </div>
                </LoadingContent>
                <PagerContent>
                    <MudTablePager />
                </PagerContent>
            </MudTable>
        </div>

        <!-- عرض البطاقات للشاشات المتوسطة والصغيرة -->
        <div class="d-block d-lg-none">
            @if (_loading)
            {
                <div class="d-flex justify-center pa-4">
                    <MudProgressCircular Indeterminate="true" />
                    <MudText Class="ms-2">جاري التحميل...</MudText>
                </div>
            }
            else if (!_services.Where(FilterFunc).Any())
            {
                <div class="d-flex flex-column align-center pa-4">
                    <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Secondary" />
                    <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mt-2">لا توجد خدمات</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">جرب تعديل معايير البحث</MudText>
                </div>
            }
            else
            {
                <!-- عرض مختلف للشاشات المتوسطة والصغيرة -->
                <MudGrid Spacing="2">
                    @foreach (var service in _services.Where(FilterFunc))
                    {
                        <!-- للشاشات المتوسطة: 2 بطاقات في الصف -->
                        <MudItem xs="12" sm="6" Class="d-none d-sm-block d-md-block">
                            <MudCard Class="mb-2" Elevation="1" Style="border-radius: 8px; min-height: 140px;">
                                <MudCardContent Class="pa-2" Style="padding: 12px !important;">
                                    <!-- رأس البطاقة المضغوط -->
                                    <div class="d-flex justify-space-between align-start mb-2">
                                        <div class="flex-grow-1" style="min-width: 0;">
                                            <MudText Typo="Typo.subtitle2" Class="mb-1" Style="font-weight: 600; line-height: 1.3; font-size: 0.95rem;">
                                                @service.Title
                                            </MudText>
                                            <MudText Typo="Typo.caption" Color="Color.Secondary" Style="font-size: 0.7rem;">
                                                #@service.Id | @service.CreatedAt.ToString("MM-dd")
                                            </MudText>
                                        </div>
                                        <div class="d-flex align-center gap-1 flex-shrink-0">
                                            <MudChip T="string" Size="Size.Small" Color="Color.Info" Style="font-size: 0.65rem; height: 20px;">
                                                @service.DisplayOrder
                                            </MudChip>
                                            <MudIcon Icon="@(service.IsActive ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)"
                                                     Color="@(service.IsActive ? Color.Success : Color.Error)"
                                                     Size="Size.Small" />
                                        </div>
                                    </div>

                                    <!-- محتوى البطاقة المضغوط -->
                                    <div class="d-flex gap-2 mb-2">
                                        @if (!string.IsNullOrEmpty(service.MainImageUrl))
                                        {
                                            <MudImage Src="@service.MainImageUrl"
                                                     Alt="@service.Title"
                                                     Width="50"
                                                     Height="40"
                                                     ObjectFit="ObjectFit.Cover"
                                                     Class="rounded flex-shrink-0" />
                                        }
                                        <div class="flex-grow-1" style="min-width: 0;">
                                            <MudText Typo="Typo.body2" Color="Color.Secondary" Style="font-size: 0.8rem; line-height: 1.4;">
                                                @(service.PrimaryDescription?.Length > 70 ?
                                                  service.PrimaryDescription.Substring(0, 70) + "..." :
                                                  service.PrimaryDescription)
                                            </MudText>
                                        </div>
                                    </div>

                                    <!-- أزرار الإجراءات المضغوطة -->
                                    <div class="d-flex justify-end gap-1">
                                        <MudTooltip Text="عرض">
                                            <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                                          Color="Color.Info"
                                                          Size="Size.Small"
                                                          Variant="Variant.Text"
                                                          Style="width: 28px; height: 28px;"
                                                          OnClick="@(() => ViewServiceDetails(service))" />
                                        </MudTooltip>
                                        <MudTooltip Text="تعديل">
                                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                                          Color="Color.Primary"
                                                          Size="Size.Small"
                                                          Variant="Variant.Text"
                                                          Style="width: 28px; height: 28px;"
                                                          OnClick="@(() => OpenEditDialog(service))" />
                                        </MudTooltip>
                                        <MudTooltip Text="حذف">
                                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                          Color="Color.Error"
                                                          Size="Size.Small"
                                                          Variant="Variant.Text"
                                                          Style="width: 28px; height: 28px;"
                                                          OnClick="@(() => DeleteService(service.Id))" />
                                        </MudTooltip>
                                    </div>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- للشاشات الصغيرة (الهاتف): بطاقة واحدة في الصف مع تصميم مضغوط جداً -->
                        <MudItem xs="12" Class="d-block d-sm-none">
                            <MudCard Class="mb-2" Elevation="1" Style="border-radius: 6px; min-height: 100px;">
                                <MudCardContent Class="pa-2" Style="padding: 8px !important;">
                                    <!-- تصميم أفقي مضغوط للهاتف -->
                                    <div class="d-flex gap-2 align-center">
                                        <!-- الصورة الصغيرة -->
                                        @if (!string.IsNullOrEmpty(service.MainImageUrl))
                                        {
                                            <MudImage Src="@service.MainImageUrl"
                                                     Alt="@service.Title"
                                                     Width="40"
                                                     Height="40"
                                                     ObjectFit="ObjectFit.Cover"
                                                     Class="rounded flex-shrink-0" />
                                        }
                                        else
                                        {
                                            <div class="d-flex align-center justify-center flex-shrink-0" 
                                                 style="width: 40px; height: 40px; background: var(--mud-palette-grey-lighten4); border-radius: 4px;">
                                                <MudIcon Icon="@Icons.Material.Filled.MiscellaneousServices" 
                                                         Size="Size.Small" 
                                                         Color="Color.Secondary" />
                                            </div>
                                        }

                                        <!-- المحتوى الرئيسي -->
                                        <div class="flex-grow-1" style="min-width: 0;">
                                            <div class="d-flex justify-space-between align-start mb-1">
                                                <MudText Typo="Typo.subtitle2" Style="font-weight: 600; line-height: 1.2; font-size: 0.9rem;">
                                                    @service.Title
                                                </MudText>
                                                <div class="d-flex align-center gap-1 flex-shrink-0 ms-2">
                                                    <MudChip T="string" Size="Size.Small" Color="Color.Info" Style="font-size: 0.6rem; height: 18px; padding: 0 6px;">
                                                        @service.DisplayOrder
                                                    </MudChip>
                                                    <MudIcon Icon="@(service.IsActive ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)"
                                                             Color="@(service.IsActive ? Color.Success : Color.Error)"
                                                             Size="Size.Small" />
                                                </div>
                                            </div>
                                            
                                            <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mb-1" Style="font-size: 0.65rem;">
                                                #@service.Id | @service.CreatedAt.ToString("MM-dd")
                                            </MudText>
                                            
                                            <MudText Typo="Typo.body2" Color="Color.Secondary" Style="font-size: 0.75rem; line-height: 1.3;">
                                                @(service.PrimaryDescription?.Length > 50 ?
                                                  service.PrimaryDescription.Substring(0, 50) + "..." :
                                                  service.PrimaryDescription)
                                            </MudText>
                                        </div>

                                        <!-- أزرار الإجراءات العمودية -->
                                        <div class="d-flex flex-column gap-1 flex-shrink-0">
                                            <MudTooltip Text="عرض">
                                                <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                                              Color="Color.Info"
                                                              Size="Size.Small"
                                                              Variant="Variant.Text"
                                                              Style="width: 24px; height: 24px;"
                                                              OnClick="@(() => ViewServiceDetails(service))" />
                                            </MudTooltip>
                                            <MudTooltip Text="تعديل">
                                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                                              Color="Color.Primary"
                                                              Size="Size.Small"
                                                              Variant="Variant.Text"
                                                              Style="width: 24px; height: 24px;"
                                                              OnClick="@(() => OpenEditDialog(service))" />
                                            </MudTooltip>
                                            <MudTooltip Text="حذف">
                                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                              Color="Color.Error"
                                                              Size="Size.Small"
                                                              Variant="Variant.Text"
                                                              Style="width: 24px; height: 24px;"
                                                              OnClick="@(() => DeleteService(service.Id))" />
                                            </MudTooltip>
                                        </div>
                                    </div>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                    }
                </MudGrid>
            }
        </div>

    </MudPaper>
</MudContainer>



