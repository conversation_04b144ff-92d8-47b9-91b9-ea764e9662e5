using BlogMud.Data;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using System.Text.Json;

namespace BlogMud.Services
{
    /// <summary>
    /// Development email service that simulates email sending and provides
    /// a way to retrieve sent emails for testing purposes
    /// </summary>
    public class DevelopmentEmailService : IEmailSender<ApplicationUser>
    {
        private readonly ILogger<DevelopmentEmailService> _logger;
        private readonly IWebHostEnvironment _environment;
        private static readonly List<EmailMessage> _sentEmails = new();

        public DevelopmentEmailService(ILogger<DevelopmentEmailService> logger, IWebHostEnvironment environment)
        {
            _logger = logger;
            _environment = environment;
        }

        public async Task SendConfirmationLinkAsync(ApplicationUser user, string email, string confirmationLink)
        {
            var subject = "تأكيد البريد الإلكتروني - BlogMud";
            var htmlBody = GetEmailConfirmationTemplate(user.FullName, confirmationLink);

            await SendEmailAsync(email, subject, htmlBody);
        }

        public async Task SendPasswordResetLinkAsync(ApplicationUser user, string email, string resetLink)
        {
            var subject = "إعادة تعيين كلمة المرور - BlogMud";
            var htmlBody = GetPasswordResetTemplate(user.FullName, resetLink);

            await SendEmailAsync(email, subject, htmlBody);
        }

        public async Task SendPasswordResetCodeAsync(ApplicationUser user, string email, string resetCode)
        {
            var subject = "رمز إعادة تعيين كلمة المرور - BlogMud";
            var htmlBody = GetPasswordResetCodeTemplate(user.FullName, resetCode);

            await SendEmailAsync(email, subject, htmlBody);
        }

        public async Task SendEmailAsync(string email, string subject, string htmlMessage)
        {
            var emailMessage = new EmailMessage
            {
                To = email,
                Subject = subject,
                Body = htmlMessage,
                SentAt = DateTime.UtcNow
            };

            _sentEmails.Add(emailMessage);

            _logger.LogInformation("=== DEVELOPMENT EMAIL SENT ===");
            _logger.LogInformation("To: {Email}", email);
            _logger.LogInformation("Subject: {Subject}", subject);
            _logger.LogInformation("Sent At: {SentAt}", emailMessage.SentAt);

            // Extract confirmation link if present
            if (htmlMessage.Contains("href='") && subject.Contains("تأكيد"))
            {
                var startIndex = htmlMessage.IndexOf("href='") + 6;
                var endIndex = htmlMessage.IndexOf("'", startIndex);
                if (endIndex > startIndex)
                {
                    var confirmationLink = htmlMessage.Substring(startIndex, endIndex - startIndex);
                    _logger.LogWarning("CONFIRMATION LINK: {ConfirmationLink}", confirmationLink);
                    _logger.LogWarning("Copy this link to your browser to confirm the email address");
                }
            }

            _logger.LogInformation("=== END DEVELOPMENT EMAIL ===");

            // Save to file for easy access during development
            if (_environment.IsDevelopment())
            {
                await SaveEmailToFileAsync(emailMessage);
            }

            await Task.CompletedTask;
        }

        private async Task SaveEmailToFileAsync(EmailMessage emailMessage)
        {
            try
            {
                var emailsDir = Path.Combine(_environment.ContentRootPath, "DevelopmentEmails");
                Directory.CreateDirectory(emailsDir);

                var fileName = $"email_{DateTime.Now:yyyyMMdd_HHmmss}_{Guid.NewGuid().ToString("N")[..8]}.json";
                var filePath = Path.Combine(emailsDir, fileName);

                var json = JsonSerializer.Serialize(emailMessage, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                await File.WriteAllTextAsync(filePath, json);
                _logger.LogInformation("Email saved to file: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save email to file");
            }
        }

        public static List<EmailMessage> GetSentEmails() => _sentEmails.ToList();

        public static void ClearSentEmails() => _sentEmails.Clear();

        private string GetEmailConfirmationTemplate(string fullName, string confirmationLink)
        {
            return $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تأكيد البريد الإلكتروني</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; direction: rtl; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; padding: 20px 0; border-bottom: 2px solid #007bff; }}
        .logo {{ font-size: 24px; font-weight: bold; color: #007bff; }}
        .content {{ padding: 30px 0; }}
        .button {{ display: inline-block; padding: 12px 30px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
        .footer {{ text-align: center; padding: 20px 0; border-top: 1px solid #eee; color: #666; font-size: 14px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>BlogMud</div>
        </div>
        <div class='content'>
            <h2>مرحباً {fullName}!</h2>
            <p>شكراً لك على التسجيل في موقع BlogMud. لإكمال عملية التسجيل، يرجى تأكيد بريدك الإلكتروني بالنقر على الرابط أدناه:</p>
            <div style='text-align: center;'>
                <a href='{confirmationLink}' class='button'>تأكيد البريد الإلكتروني</a>
            </div>
            <p>إذا لم تقم بإنشاء هذا الحساب، يرجى تجاهل هذا البريد الإلكتروني.</p>
            <p>مع أطيب التحيات،<br>فريق BlogMud</p>
        </div>
        <div class='footer'>
            <p>© 2024 BlogMud. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html>";
        }

        private string GetPasswordResetTemplate(string fullName, string resetLink)
        {
            return $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعادة تعيين كلمة المرور</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; direction: rtl; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; padding: 20px 0; border-bottom: 2px solid #dc3545; }}
        .logo {{ font-size: 24px; font-weight: bold; color: #dc3545; }}
        .content {{ padding: 30px 0; }}
        .button {{ display: inline-block; padding: 12px 30px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
        .footer {{ text-align: center; padding: 20px 0; border-top: 1px solid #eee; color: #666; font-size: 14px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>BlogMud</div>
        </div>
        <div class='content'>
            <h2>مرحباً {fullName}!</h2>
            <p>تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك. انقر على الرابط أدناه لإعادة تعيين كلمة المرور:</p>
            <div style='text-align: center;'>
                <a href='{resetLink}' class='button'>إعادة تعيين كلمة المرور</a>
            </div>
            <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.</p>
            <p>مع أطيب التحيات،<br>فريق BlogMud</p>
        </div>
        <div class='footer'>
            <p>© 2024 BlogMud. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html>";
        }

        private string GetPasswordResetCodeTemplate(string fullName, string resetCode)
        {
            return $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>رمز إعادة تعيين كلمة المرور</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; direction: rtl; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; padding: 20px 0; border-bottom: 2px solid #dc3545; }}
        .logo {{ font-size: 24px; font-weight: bold; color: #dc3545; }}
        .content {{ padding: 30px 0; }}
        .code {{ font-size: 24px; font-weight: bold; color: #dc3545; text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 5px; margin: 20px 0; }}
        .footer {{ text-align: center; padding: 20px 0; border-top: 1px solid #eee; color: #666; font-size: 14px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>BlogMud</div>
        </div>
        <div class='content'>
            <h2>مرحباً {fullName}!</h2>
            <p>استخدم الرمز التالي لإعادة تعيين كلمة المرور الخاصة بك:</p>
            <div class='code'>{resetCode}</div>
            <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.</p>
            <p>مع أطيب التحيات،<br>فريق BlogMud</p>
        </div>
        <div class='footer'>
            <p>© 2024 BlogMud. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html>";
        }
    }

    public class EmailMessage
    {
        public string To { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public DateTime SentAt { get; set; }
    }
}
