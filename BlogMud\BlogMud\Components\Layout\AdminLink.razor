@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Identity
@using BlogMud.Data
@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager NavigationManager
@inject SignInManager<ApplicationUser> SignInManager

<AuthorizeView Roles="Admin">
    <Authorized>
        <MudMenu Icon="@Icons.Material.Filled.AdminPanelSettings" Color="Color.Inherit"
            AnchorOrigin="Origin.BottomCenter" TransformOrigin="Origin.TopCenter">
            <MudText Typo="Typo.subtitle1" Class="px-4 py-2">مرحباً، @context.User.Identity?.Name</MudText>
            <MudDivider />
            <MudMenuItem OnClick="@(() => NavigateTo("/admin/dashboard"))">لوحة التحكم</MudMenuItem>
            <MudMenuItem OnClick="@(() => NavigateTo("/admin/posts"))">المنشورات</MudMenuItem>
            <MudMenuItem OnClick="@(() => NavigateTo("/admin/categories"))">الأقسام</MudMenuItem>
            <MudMenuItem OnClick="@(() => NavigateTo("/admin/images"))">معرض الصور</MudMenuItem>
            <MudMenuItem OnClick="@(() => NavigateTo("/admin/videos"))">الفيديوهات</MudMenuItem>
            <MudDivider />
            <MudMenuItem OnClick="@(() => LogoutAsync())">تسجيل الخروج</MudMenuItem>
        </MudMenu>
    </Authorized>
</AuthorizeView>

@code {
    private async Task LogoutAsync()
    {
        await SignInManager.SignOutAsync();
        NavigationManager.NavigateTo("/", true);
    }

    private void NavigateTo(string url)
    {
        NavigationManager.NavigateTo(url, forceLoad: true);
    }
}
