<style>
    /* AnimatedGifs Component - Beautiful & Elegant Styles (Based on NewsSiderMove) */

    /* Container Styling */
    .animated-gifs-container {
        margin-top: 2rem;
        padding: 0 1rem;
    }

    /* Page Header Section */
    .page-header-section {
        margin-bottom: 2rem;
    }

    .page-header-card {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        border-radius: 20px;
        padding: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(149, 55, 53, 0.3);
    }

    .page-header-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .header-content {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 1;
    }

    .header-icon-wrapper {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .header-icon {
        font-size: 2rem;
        color: white;
    }

    .header-title {
        color: white;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .header-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 400;
        line-height: 1.6;
    }

    .header-stats {
        display: flex;
        gap: 2rem;
        position: relative;
        z-index: 1;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        padding: 1rem 1.5rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
    }

    .stat-icon {
        font-size: 1.5rem;
        color: rgba(255, 255, 255, 0.9);
    }

    .stat-icon.active {
        color: #4caf50;
    }

    .stat-number {
        color: white;
        font-weight: 700;
        margin: 0;
    }

    .stat-label {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
    }

    /* Preview Section */
    .preview-section {
        margin-bottom: 2rem;
    }

    .preview-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .preview-card:hover {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(149, 55, 53, 0.1);
    }

    .preview-title-wrapper {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .preview-icon {
        color: #953735;
        font-size: 1.5rem;
    }

    .preview-title {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
    }

    .carousel-controls {
        display: flex;
        gap: 0.5rem;
    }

    .carousel-container {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        background: #f8f9fa;
        position: relative;
    }

    .carousel-container .mud-carousel {
        height: 400px;
        width: 100%;
    }

    .enhanced-carousel {
        height: 400px;
        border-radius: 16px;
        width: 100%;
    }

    .enhanced-carousel .mud-carousel-item {
        height: 400px;
        width: 100%;
    }

    .carousel-item {
        position: relative;
        height: 400px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .slide-content {
        position: relative;
        height: 100%;
        width: 100%;
        display: block;
    }

    .slide-image-wrapper {
        position: relative;
        height: 100%;
        width: 100%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .slide-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        transition: transform 0.3s ease;
    }

    .slide-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 40%;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        pointer-events: none;
        z-index: 1;
    }

    .slide-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 1.5rem 2rem;
        color: white;
        text-align: center;
        z-index: 2;
    }

    .slide-title {
        color: white;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    .slide-description {
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .slide-link-btn {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        font-weight: 600;
    }

    .slide-link-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }

    .no-active-slides {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .no-slides-icon {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }

    .no-slides-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .no-slides-subtitle {
        color: #6c757d;
    }

    /* Controls Section */
    .controls-section {
        margin-bottom: 2rem;
    }

    .controls-card {
        background: white;
        border-radius: 20px;
        padding: 1.5rem 2rem;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .controls-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 2rem;
    }

    .controls-left {
        flex: 0 0 auto;
    }

    .controls-right {
        flex: 1;
        display: flex;
        justify-content: flex-end;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
    }

    .primary-action-btn {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: none;
        box-shadow: 0 4px 16px rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .primary-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 24px rgba(149, 55, 53, 0.4);
    }

    .secondary-action-btn {
        border: 2px solid #953735;
        color: #953735;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: none;
        transition: all 0.3s ease;
    }

    .secondary-action-btn:hover {
        background: rgba(149, 55, 53, 0.1);
        transform: translateY(-2px);
    }

    .search-wrapper {
        position: relative;
        max-width: 400px;
        width: 100%;
    }

    .enhanced-search-field {
        border-radius: 12px;
    }

    .enhanced-search-field .mud-input-control {
        border-radius: 12px;
        border: 2px solid rgba(149, 55, 53, 0.2);
        transition: all 0.3s ease;
    }

    .enhanced-search-field .mud-input-control:hover {
        border-color: rgba(149, 55, 53, 0.4);
    }

    .enhanced-search-field .mud-input-control.mud-input-control-focused {
        border-color: #953735;
        box-shadow: 0 0 0 3px rgba(149, 55, 53, 0.1);
    }

    .search-clear-btn {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
    }

    .button-text-short {
        display: none;
    }

    .button-text-full {
        display: inline;
    }

    /* Table Section */
    .table-section {
        margin-bottom: 2rem;
    }

    .table-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(149, 55, 53, 0.1);
    }

    .table-title-wrapper {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .table-icon {
        color: #953735;
        font-size: 1.5rem;
    }

    .table-title {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
    }

    /* Loading Section */
    .loading-section {
        margin: 3rem 0;
    }

    .loading-card {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        text-align: center;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .loading-text {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
    }

    .loading-subtitle {
        color: #6c757d;
        margin: 0;
    }

    /* Empty State Section */
    .empty-state-section {
        margin: 3rem 0;
    }

    .empty-state-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 20px;
        padding: 4rem 2rem;
        text-align: center;
        border: 2px dashed rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .empty-state-card:hover {
        border-color: rgba(149, 55, 53, 0.5);
        transform: translateY(-2px);
    }

    .empty-state-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .empty-state-icon-wrapper {
        background: rgba(149, 55, 53, 0.1);
        border-radius: 50%;
        padding: 2rem;
        margin-bottom: 1rem;
    }

    .empty-state-icon {
        font-size: 4rem;
        color: #953735;
    }

    .empty-state-title {
        color: #2c3e50;
        font-weight: 700;
        margin: 0;
    }

    .empty-state-subtitle {
        color: #6c757d;
        max-width: 400px;
        line-height: 1.6;
        margin: 0;
    }

    .empty-state-action-btn {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        border-radius: 12px;
        padding: 1rem 2rem;
        font-weight: 600;
        text-transform: none;
        box-shadow: 0 4px 16px rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .empty-state-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 24px rgba(149, 55, 53, 0.4);
    }

    /* Table Enhancements */
    .regular-table {
        display: block;
    }

    .regular-table .mud-table {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .regular-table .mud-table-head {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
    }

    .regular-table .mud-table-head .mud-table-cell {
        color: white;
        font-weight: 600;
        border-bottom: none;
    }

    .regular-table .mud-table-row:hover {
        background: rgba(149, 55, 53, 0.05);
    }

    .mud-table-cell:first-child {
        font-weight: 600;
        color: #953735;
        text-align: center;
        width: 60px;
        min-width: 60px;
    }

    .mobile-cards {
        display: none;
    }

    /* Enhanced Mobile Cards - Redesigned */
    .mobile-cards {
        display: none;
        gap: 1.5rem;
    }

    .mobile-card {
        background: #ffffff !important;
        border-radius: 20px;
        transition: all 0.3s ease;
        border: 1px solid rgba(149, 55, 53, 0.15);
        box-shadow: 0 4px 20px rgba(149, 55, 53, 0.1);
        overflow: hidden;
        position: relative;
        margin-bottom: 1.5rem;
    }

    .mobile-card .mud-card {
        background: #ffffff !important;
    }

    .mobile-card .mud-card-content {
        background: #ffffff !important;
    }

    .mobile-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        z-index: 1;
    }

    .mobile-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 32px rgba(149, 55, 53, 0.2);
        border-color: rgba(149, 55, 53, 0.3);
    }

    .compact-content {
        padding: 1.5rem !important;
        background: #ffffff !important;
        position: relative;
        z-index: 2;
    }

    /* Card Header */
    .gif-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(149, 55, 53, 0.15);
        background: #ffffff;
    }

    .gif-number {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        color: white;
        font-weight: 700;
        min-width: 45px;
        height: 45px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(149, 55, 53, 0.3);
        font-size: 1.1rem;
    }

    .gif-actions {
        display: flex;
        gap: 0.5rem;
    }

    .action-btn {
        border-radius: 10px;
        transition: all 0.3s ease;
        width: 40px;
        height: 40px;
    }

    .action-btn:hover {
        transform: scale(1.1);
    }

    /* Title Section */
    .gif-title {
        color: #2c3e50;
        font-weight: 700;
        line-height: 1.4;
        word-break: break-word;
        margin: 0 0 1rem 0;
        flex: 1;
    }

    /* Info Items */
    .gif-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        direction: rtl;
        margin-bottom: 0.75rem;
    }

    .info-icon {
        color: #953735;
        font-size: 1rem;
    }

    .info-label {
        color: #6c757d;
        font-weight: 500;
        margin: 0;
        font-size: 0.85rem;
    }

    .status-chip {
        flex-shrink: 0;
        font-weight: 600;
    }

    /* Images Preview Section */
    .card-images-section {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1rem;
        border: 1px solid rgba(149, 55, 53, 0.1);
    }

    .images-label {
        color: #6c757d;
        font-weight: 600;
        margin-bottom: 0.75rem;
        display: block;
    }

    .images-preview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: 0.75rem;
        max-height: 200px;
        overflow-y: auto;
    }

    .preview-image-wrapper {
        position: relative;
        aspect-ratio: 1;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid rgba(149, 55, 53, 0.2);
    }

    .preview-image-wrapper:hover {
        transform: scale(1.05);
        border-color: #953735;
        box-shadow: 0 4px 12px rgba(149, 55, 53, 0.3);
    }

    .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        transition: all 0.3s ease;
    }

    .preview-image-wrapper:hover .preview-image {
        transform: scale(1.1);
    }

    /* Image Modal Styles */
    .image-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
        animation: fadeIn 0.3s ease-in-out;
        overflow: auto;
        padding: 20px;
        box-sizing: border-box;
    }

    .image-modal-close {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        z-index: 10000;
    }

    .image-modal-close:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    .image-modal-close .mud-icon-root {
        color: white;
        font-size: 1.5rem;
    }

    .image-modal-content-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        max-width: 90%;
        max-height: 90%;
    }

    .image-modal-content {
        max-width: 100%;
        max-height: calc(100% - 60px);
        object-fit: contain;
        border-radius: 12px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        animation: zoomIn 0.3s ease-in-out;
    }

    .image-modal-title {
        color: white;
        background: rgba(0, 0, 0, 0.7);
        padding: 10px 20px;
        border-radius: 20px;
        margin-top: 15px;
        font-size: 1.1rem;
        font-weight: 500;
        text-align: center;
        backdrop-filter: blur(10px);
        max-width: 100%;
        word-wrap: break-word;
    }

    @@keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @@keyframes zoomIn {
        from {
            opacity: 0;
            transform: scale(0.8);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* Responsive Design - Mobile First */

    /* عرض البطاقات في الشاشات الصغيرة */
    @@media (max-width: 768px) {
        .regular-table {
            display: none;
        }

        .mobile-cards {
            display: block;
        }

        .page-header-card {
            padding: 1.5rem;
            border-radius: 16px;
        }

        .header-content {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .header-stats {
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .stat-item {
            flex: 1;
            min-width: 140px;
        }

        .preview-card,
        .controls-card {
            padding: 1.5rem;
            border-radius: 16px;
        }

        .enhanced-carousel {
            height: 300px;
        }

        .enhanced-carousel .mud-carousel {
            height: 300px;
        }

        .carousel-item {
            height: 300px;
        }

        .slide-image-wrapper {
            height: 300px;
        }

        .slide-image {
            max-height: 300px;
        }

        .controls-container {
            flex-direction: column;
            gap: 1.5rem;
        }

        .controls-left,
        .controls-right {
            width: 100%;
        }

        .action-buttons {
            justify-content: center;
        }

        .search-wrapper {
            max-width: none;
        }
    }

    /* تحسينات للشاشات الصغيرة جداً */
    @@media (max-width: 480px) {
        .animated-gifs-container {
            padding: 0 0.5rem;
        }

        .page-header-card {
            padding: 1rem;
            margin: 0 0.5rem 1.5rem;
        }

        .header-title {
            font-size: 1.5rem;
        }

        .stat-item {
            padding: 0.75rem 1rem;
            min-width: 120px;
        }

        .button-text-short {
            display: inline;
        }

        .button-text-full {
            display: none;
        }

        .action-buttons {
            flex-direction: column;
            width: 100%;
        }

        .primary-action-btn,
        .secondary-action-btn {
            width: 100%;
            justify-content: center;
        }

        .compact-content {
            padding: 1rem !important;
        }

        .gif-header {
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
        }

        .gif-number {
            min-width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .action-btn {
            width: 36px;
            height: 36px;
        }

        .gif-title {
            font-size: 1.1rem;
        }
    }

    /* Animations and Transitions */
    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .page-header-section {
        animation: fadeInUp 0.6s ease-out;
    }

    .preview-section {
        animation: fadeInUp 0.6s ease-out 0.1s both;
    }

    .controls-section {
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }

    .mobile-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .mobile-card:nth-child(2n) {
        animation-delay: 0.1s;
    }

    .mobile-card:nth-child(2n+1) {
        animation-delay: 0.2s;
    }
</style>
