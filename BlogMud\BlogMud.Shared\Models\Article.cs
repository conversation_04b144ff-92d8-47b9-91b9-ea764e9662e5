﻿using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    public class Article
    {

        public int Id { get; set; }
        /// <summary>
        /// عنوان
        /// </summary>
        [Required(ErrorMessage = "عنوان مطلوب")]
        [StringLength(100, ErrorMessage = "يجب أن يكون عنوان بين {2} و {1} حرفًا", MinimumLength = 2)]
        public string? Title { get; set; }

        /// <summary>
        /// مقدمة
        /// </summary>
        [Required(ErrorMessage = "المقدمة مطلوبة")]
        [StringLength(200, ErrorMessage = "يجب أن يكون المقدمة بين {2} و {1} حرفًا", MinimumLength = 2)]
        public string? Introduction { get; set; }

        /// <summary>
        /// محتوى
        /// </summary>
        [Required(ErrorMessage = "محتوى مطلوب")]
        public string? Content { get; set; }

        [Required(ErrorMessage = "الصورة مطلوبة")]
        public string? ImageUrl { get; set; }

        [Required(ErrorMessage = "شرائح الصور مطلوبة")]
        public string? ImageCarousel { get; set; }

        /// <summary>
        /// رابط الفيديو
        /// </summary>
        public string? VideoUrl { get; set; }
        /// <summary>
        ///ربط بين جدول عميل والمقال
        /// </summary>
        public int ClientId { get; set; }
        public Client? Client { get; set; }

        /// <summary>
        ///ربط بين جدول فئات والمقال
        /// </summary>
        public int CategoryId { get; set; }
        public Category? Category { get; set; }

        public DateTime PublishDate { get; set; } = DateTime.Now;

        public bool IsPublished { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? LastModifiedAt { get; set; }
    }
}
