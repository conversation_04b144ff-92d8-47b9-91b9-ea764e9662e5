/* Enhanced Dashboard Container with Advanced Background */
.dashboard-container {
    padding: 0;
    margin: 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
    min-height: calc(100vh - 64px);
    position: relative;
    overflow-x: hidden;
    animation: backgroundShift 20s ease-in-out infinite;
}

/* Floating Background Elements */
.dashboard-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(149, 55, 53, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(149, 55, 53, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(149, 55, 53, 0.01) 0%, transparent 50%);
    animation: particleFloat 25s ease-in-out infinite;
    z-index: 0;
}

.dashboard-container > * {
    position: relative;
    z-index: 1;
}

/* Enhanced Header Section with Advanced Styling */
.dashboard-header {
    background: linear-gradient(135deg,
        var(--mud-palette-primary) 0%,
        var(--mud-palette-primary-darken) 50%,
        #7a2d2b 100%);
    color: white;
    padding: 2rem 1.5rem;
    margin-bottom: 2rem;
    border-radius: 0 0 32px 32px;
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.15),
        0 4px 16px rgba(149, 55, 53, 0.2),
        inset 0 -1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(255,255,255,0.05) 0%, transparent 60%);
    animation: headerShimmer 8s ease-in-out infinite;
}

/* Breadcrumb Section */
.breadcrumb-section {
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
}

.dashboard-breadcrumbs {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.breadcrumb-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.breadcrumb-link:hover {
    color: white;
    transform: translateX(2px);
}

.breadcrumb-separator {
    color: rgba(255, 255, 255, 0.6);
    margin: 0 0.5rem;
}

/* Header Content */
.header-content {
    position: relative;
    z-index: 2;
}

/* Enhanced Welcome Section */
.welcome-section {
    text-align: start;
    animation: welcomeFadeIn 1s ease-out;
}

.welcome-icon-container {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: iconPulse 3s ease-in-out infinite;
}

.welcome-icon {
    font-size: 2rem !important;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.dashboard-title {
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    letter-spacing: -0.5px;
    animation: titleSlideIn 0.8s ease-out 0.2s both;
}

.dashboard-subtitle {
    opacity: 0.95;
    font-weight: 400;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    line-height: 1.5;
    animation: subtitleSlideIn 0.8s ease-out 0.4s both;
}

/* Dashboard Status Chips */
.dashboard-status {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
    flex-wrap: wrap;
    animation: statusFadeIn 0.8s ease-out 0.6s both;
}

.status-chip {
    background: rgba(255, 255, 255, 0.15) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white !important;
    font-weight: 500;
    transition: all 0.3s ease;
}

.status-chip:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    transform: translateY(-1px);
}

/* Enhanced Quick Actions */
.quick-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    flex-wrap: wrap;
    animation: actionsFadeIn 0.8s ease-out 0.8s both;
}

.quick-action-btn {
    backdrop-filter: blur(15px);
    color: white;
    font-weight: 600;
    padding: 12px 24px;
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 48px;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    letter-spacing: 0.3px;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.2) 50%,
        transparent 100%);
    transition: left 0.6s ease;
}

.quick-action-btn:hover::before {
    left: 100%;
}

.primary-action {
    background: rgba(255, 255, 255, 0.2);
}

.primary-action:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.secondary-action {
    background: rgba(255, 255, 255, 0.1);
}

.secondary-action:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.button-text {
    position: relative;
    z-index: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Enhanced Statistics Section */
.statistics-section {
    padding: 0 1.5rem;
    margin-bottom: 3rem;
    animation: sectionSlideUp 0.8s ease-out 0.3s both;
}

/* Section Header */
.section-header {
    margin-bottom: 2rem;
    text-align: center;
    animation: headerFadeIn 0.6s ease-out 0.5s both;
}

.section-title {
    font-weight: 700;
    color: var(--mud-palette-text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-icon {
    color: var(--mud-palette-primary);
    filter: drop-shadow(0 1px 2px rgba(149, 55, 53, 0.2));
}

.section-subtitle {
    color: var(--mud-palette-text-secondary);
    opacity: 0.8;
    font-weight: 400;
}

/* Statistics Grid */
.statistics-grid {
    animation: gridFadeIn 0.8s ease-out 0.7s both;
}

/* Enhanced Statistics Cards */
.stat-card {
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    min-height: 200px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04);
}

.enhanced-card {
    animation: cardSlideUp 0.6s ease-out;
}

.enhanced-card:nth-child(1) { animation-delay: 0.1s; }
.enhanced-card:nth-child(2) { animation-delay: 0.2s; }
.enhanced-card:nth-child(3) { animation-delay: 0.3s; }
.enhanced-card:nth-child(4) { animation-delay: 0.4s; }

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        var(--card-color) 0%,
        var(--card-color-light) 50%,
        var(--card-color) 100%);
    animation: gradientShift 3s ease-in-out infinite;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%,
        rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
}

/* Enhanced Card Color Variables */
.stat-card-primary {
    --card-color: var(--mud-palette-primary);
    --card-color-light: var(--mud-palette-primary-lighten);
    --card-color-rgb: 149, 55, 53;
}

.stat-card-secondary {
    --card-color: var(--mud-palette-secondary);
    --card-color-light: var(--mud-palette-secondary-lighten);
    --card-color-rgb: 89, 89, 89;
}

.stat-card-info {
    --card-color: var(--mud-palette-info);
    --card-color-light: var(--mud-palette-info-lighten);
    --card-color-rgb: 33, 150, 243;
}

.stat-card-warning {
    --card-color: var(--mud-palette-warning);
    --card-color-light: var(--mud-palette-warning-lighten);
    --card-color-rgb: 255, 152, 0;
}

.stat-card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow:
        0 16px 50px rgba(0, 0, 0, 0.15),
        0 6px 20px rgba(var(--card-color-rgb), 0.2);
    border-color: rgba(var(--card-color-rgb), 0.3);
}

.stat-card-content {
    padding: 2rem 1.5rem;
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

/* Enhanced Stat Header */
.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

/* Enhanced Stat Trend Indicators */
.stat-trend {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.trend-icon {
    font-size: 1.2rem !important;
    transition: all 0.3s ease;
}

.trend-up {
    color: #4caf50;
    animation: trendPulse 2s ease-in-out infinite;
}

.trend-down {
    color: #f44336;
    animation: trendPulse 2s ease-in-out infinite reverse;
}

.trend-stable {
    color: #ff9800;
}

/* Enhanced Stat Icon Container */
.stat-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    border-radius: 18px;
    background: linear-gradient(135deg,
        var(--card-color) 0%,
        var(--card-color-light) 100%);
    color: white;
    margin-bottom: 0.5rem;
    box-shadow:
        0 4px 16px rgba(var(--card-color-rgb), 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-icon-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
    animation: iconShimmer 4s ease-in-out infinite;
}

.stat-icon {
    font-size: 2.2rem !important;
    position: relative;
    z-index: 1;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon-container {
    transform: scale(1.1) rotate(5deg);
    box-shadow:
        0 6px 20px rgba(var(--card-color-rgb), 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.stat-card:hover .stat-icon {
    transform: scale(1.1);
}

/* Enhanced Stat Details */
.stat-details {
    flex: 1;
    animation: detailsFadeIn 0.6s ease-out 0.3s both;
}

.stat-number {
    font-weight: 700;
    color: var(--card-color);
    margin-bottom: 0.5rem;
    font-size: 2.8rem;
    line-height: 1;
    text-shadow: 0 2px 4px rgba(var(--card-color-rgb), 0.2);
    transition: all 0.3s ease;
    letter-spacing: -1px;
}

.stat-card:hover .stat-number {
    transform: scale(1.05);
    text-shadow: 0 3px 6px rgba(var(--card-color-rgb), 0.3);
}

.stat-label {
    font-weight: 600;
    color: var(--mud-palette-text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.stat-description {
    color: var(--mud-palette-text-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: 1rem;
}

/* Enhanced Progress Bar */
.stat-progress {
    margin: 1rem 0;
    animation: progressSlideIn 0.8s ease-out 0.5s both;
}

.progress-bar {
    border-radius: 4px;
    background: rgba(var(--card-color-rgb), 0.1);
    overflow: hidden;
}

.progress-bar .mud-progress-linear-bar {
    background: linear-gradient(90deg,
        var(--card-color) 0%,
        var(--card-color-light) 100%);
    animation: progressGlow 2s ease-in-out infinite;
}

/* Enhanced Stat Actions */
.stat-actions {
    animation: actionsFadeIn 0.6s ease-out 0.7s both;
}

.stat-action-btn {
    font-weight: 600;
    border-radius: 12px;
    padding: 10px 20px;
    min-height: 40px;
    transition: all 0.3s ease;
    background: rgba(var(--card-color-rgb), 0.1);
    border: 1px solid rgba(var(--card-color-rgb), 0.2);
    position: relative;
    overflow: hidden;
}

.stat-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(var(--card-color-rgb), 0.1) 50%,
        transparent 100%);
    transition: left 0.5s ease;
}

.stat-action-btn:hover::before {
    left: 100%;
}

.stat-action-btn:hover {
    background: rgba(var(--card-color-rgb), 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--card-color-rgb), 0.2);
}

/* Management Section */
.management-section {
    padding: 0 1.5rem;
    margin-bottom: 3rem;
}

.section-title {
    font-weight: 700;
    color: var(--mud-palette-text-primary);
    margin-bottom: 1.5rem;
    padding-left: 1rem;
    border-left: 4px solid var(--mud-palette-primary);
}

.management-card {
    border-radius: 16px;
    border: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 220px;
}

.management-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.management-card-content {
    padding: 2rem 1.5rem;
    display: flex;
    flex-direction: column;
    height: 100%;
    text-align: center;
    gap: 1rem;
}

.management-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border-radius: 20px;
    background: linear-gradient(135deg, rgba(var(--mud-palette-primary-rgb), 0.1) 0%, rgba(var(--mud-palette-primary-rgb), 0.05) 100%);
    margin: 0 auto 1rem;
}

.management-title {
    font-weight: 700;
    color: var(--mud-palette-text-primary);
    margin-bottom: 0.5rem;
}

.management-description {
    color: var(--mud-palette-text-secondary);
    flex: 1;
    margin-bottom: 1rem;
}

.management-actions {
    margin-top: auto;
}

.management-btn {
    border-radius: 12px;
    font-weight: 600;
    padding: 12px 24px;
    min-height: 44px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.management-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Activity Section */
.activity-section {
    padding: 0 1.5rem 2rem;
}

.activity-card,
.system-status-card {
    border-radius: 16px;
    border: none;
    min-height: 400px;
}

.activity-title,
.system-title {
    font-weight: 700;
    color: var(--mud-palette-text-primary);
    display: flex;
    align-items: center;
}

.activity-content,
.system-content {
    padding: 1rem 0;
}

.activity-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: var(--mud-palette-text-secondary);
}

.activity-timeline {
    padding: 1rem 0;
}

.activity-item {
    padding: 0.5rem 0;
}

.activity-text {
    color: var(--mud-palette-text-primary);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: var(--mud-palette-text-secondary);
}

/* System Status */
.system-status-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.system-status-item:last-child {
    border-bottom: none;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-online {
    background: var(--mud-palette-success);
    box-shadow: 0 0 0 3px rgba(var(--mud-palette-success-rgb), 0.2);
}

.status-warning {
    background: var(--mud-palette-warning);
    box-shadow: 0 0 0 3px rgba(var(--mud-palette-warning-rgb), 0.2);
}

.status-details {
    flex: 1;
}

.status-label {
    font-weight: 600;
    color: var(--mud-palette-text-primary);
    margin-bottom: 0.25rem;
}

.status-value {
    color: var(--mud-palette-text-secondary);
}

/* Responsive Design */
@media (max-width: 767px) {
    .dashboard-header {
        padding: 1.5rem 1rem;
        margin-bottom: 1.5rem;
        border-radius: 0 0 16px 16px;
    }

    .welcome-section {
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .quick-actions {
        justify-content: center;
    }

    .quick-action-btn {
        width: 100%;
        max-width: 280px;
    }

    .statistics-section,
    .management-section,
    .activity-section {
        padding: 0 1rem;
    }

    .stat-card,
    .management-card {
        min-height: 160px;
    }

    .stat-card-content,
    .management-card-content {
        padding: 1.25rem;
    }

    .stat-icon-container {
        width: 50px;
        height: 50px;
    }

    .stat-number {
        font-size: 2rem;
    }

    .management-icon-wrapper {
        width: 60px;
        height: 60px;
    }

    .activity-card,
    .system-status-card {
        min-height: 300px;
    }
}

@media (min-width: 768px) and (max-width: 1023px) {
    .dashboard-header {
        padding: 1.75rem 1.25rem;
    }

    .statistics-section,
    .management-section,
    .activity-section {
        padding: 0 1.25rem;
    }

    .stat-card,
    .management-card {
        min-height: 170px;
    }
}

@media (min-width: 1024px) {
    .dashboard-header {
        padding: 2.5rem 2rem;
    }

    .statistics-section,
    .management-section,
    .activity-section {
        padding: 0 2rem;
    }
}

/* RTL Support */
[dir="rtl"] .section-title {
    border-left: none;
    border-right: 4px solid var(--mud-palette-primary);
    padding-left: 0;
    padding-right: 1rem;
}

[dir="rtl"] .quick-actions {
    justify-content: flex-start;
}

[dir="rtl"] .activity-title,
[dir="rtl"] .system-title {
    flex-direction: row-reverse;
}

/* ===== ADVANCED ANIMATION KEYFRAMES ===== */

/* Background Animations */
@keyframes backgroundShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    33% {
        transform: translateY(-15px) rotate(120deg);
        opacity: 1;
    }
    66% {
        transform: translateY(10px) rotate(240deg);
        opacity: 0.8;
    }
}

/* Header Animations */
@keyframes headerShimmer {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        opacity: 0.8;
    }
    50% {
        transform: rotate(180deg) scale(1.1);
        opacity: 1;
    }
}

@keyframes welcomeFadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes titleSlideIn {
    0% {
        opacity: 0;
        transform: translateX(-30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes subtitleSlideIn {
    0% {
        opacity: 0;
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes statusFadeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes actionsFadeIn {
    0% {
        opacity: 0;
        transform: translateY(15px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Icon Animations */
@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
}

@keyframes iconShimmer {
    0%, 100% {
        transform: rotate(0deg);
        opacity: 0.8;
    }
    50% {
        transform: rotate(180deg);
        opacity: 1;
    }
}

/* Section Animations */
@keyframes sectionSlideUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes headerFadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes gridFadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Card Animations */
@keyframes cardSlideUp {
    0% {
        opacity: 0;
        transform: translateY(40px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes detailsFadeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes progressSlideIn {
    0% {
        opacity: 0;
        transform: scaleX(0);
        transform-origin: left;
    }
    100% {
        opacity: 1;
        transform: scaleX(1);
    }
}

@keyframes progressGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(var(--card-color-rgb), 0.3);
    }
    50% {
        box-shadow: 0 0 15px rgba(var(--card-color-rgb), 0.5);
    }
}

/* Trend Animations */
@keyframes trendPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Accessibility: Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Smooth Transitions */
* {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
}

/* Focus States */
.quick-action-btn:focus,
.stat-action-btn:focus,
.management-btn:focus {
    outline: 2px solid var(--mud-palette-primary);
    outline-offset: 2px;
}
