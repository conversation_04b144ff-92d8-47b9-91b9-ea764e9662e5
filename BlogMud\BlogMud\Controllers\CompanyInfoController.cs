using AutoMapper;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BlogMud.Controllers
{
    /// <summary>
    /// وحدة تحكم واجهة برمجة التطبيقات لإدارة معلومات الشركة
    /// </summary>
    /// <remarks>
    /// توفر هذه الوحدة واجهات برمجة تطبيقات RESTful للتعامل مع معلومات الشركة
    /// بما في ذلك عمليات الإنشاء والقراءة والتحديث
    /// </remarks>
    [Route("api/[controller]")]
    [ApiController]
    public class CompanyInfoController : ControllerBase
    {
        #region المتغيرات والمنشئ

        private readonly IUnitOfWork _unitOfWork;
        private readonly IRepository<CompanyInfo> _companyInfoRepository;
        private readonly ILogger<CompanyInfoController> _logger;
        private readonly IMapper _mapper;

        /// <summary>
        /// منشئ وحدة تحكم معلومات الشركة
        /// </summary>
        /// <param name="unitOfWork">واجهة وحدة العمل للتعامل مع قاعدة البيانات</param>
        /// <param name="logger">خدمة تسجيل الأحداث</param>
        /// <param name="mapper">خدمة التحويل بين الكيانات ونماذج نقل البيانات</param>
        /// <remarks>
        /// يقوم بتهيئة المستودعات والخدمات اللازمة لإدارة معلومات الشركة
        /// </remarks>
        public CompanyInfoController(IUnitOfWork unitOfWork, ILogger<CompanyInfoController> logger, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _companyInfoRepository = _unitOfWork.Repository<CompanyInfo>();
            _logger = logger;
            _mapper = mapper;
        }

        #endregion

        #region عمليات القراءة (GET)

        /// <summary>
        /// الحصول على جميع معلومات الشركة
        /// </summary>
        /// <returns>قائمة بجميع سجلات معلومات الشركة</returns>
        /// <response code="200">تم استرجاع قائمة معلومات الشركة بنجاح</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// هذه الطريقة تقوم باسترجاع جميع سجلات معلومات الشركة من قاعدة البيانات
        /// وتحويلها إلى نماذج نقل البيانات (DTOs) قبل إرجاعها للمستخدم
        /// </remarks>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<CompanyInfoDto>>> GetCompanyInfos()
        {
            try
            {
                _logger.LogInformation("Fetching all company info records");
                var companyInfos = await _companyInfoRepository.GetAllAsync();
                var companyInfoDtos = _mapper.Map<IEnumerable<CompanyInfoDto>>(companyInfos);
                _logger.LogInformation("Retrieved {Count} company info records", companyInfoDtos.Count());
                return Ok(companyInfoDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving company info records");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على معلومات شركة محددة بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف معلومات الشركة المطلوبة</param>
        /// <returns>معلومات الشركة المطلوبة</returns>
        /// <response code="200">تم استرجاع معلومات الشركة بنجاح</response>
        /// <response code="404">لم يتم العثور على معلومات الشركة بالمعرف المحدد</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تقوم هذه الطريقة بالبحث عن سجل معلومات الشركة بناءً على المعرف المقدم
        /// وإرجاع نموذج نقل البيانات (DTO) المقابل إذا تم العثور عليه
        /// </remarks>
        [HttpGet("{id}")]
        public async Task<ActionResult<CompanyInfoDto>> GetCompanyInfo(int id)
        {
            try
            {
                _logger.LogInformation("Fetching company info with ID: {CompanyInfoId}", id);
                var companyInfo = await _companyInfoRepository.GetByIdAsync(id);

                if (companyInfo == null)
                {
                    _logger.LogWarning("Company info not found with ID: {CompanyInfoId}", id);
                    return NotFound();
                }

                var companyInfoDto = _mapper.Map<CompanyInfoDto>(companyInfo);
                return Ok(companyInfoDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving company info with ID: {CompanyInfoId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #endregion

        #region عمليات التعديل (PUT/POST)

        /// <summary>
        /// تحديث معلومات شركة موجودة
        /// </summary>
        /// <param name="id">معرف معلومات الشركة المراد تحديثها</param>
        /// <param name="companyInfoDto">نموذج نقل البيانات المحتوي على المعلومات المحدثة</param>
        /// <returns>استجابة HTTP بدون محتوى في حالة نجاح التحديث</returns>
        /// <response code="204">تم تحديث معلومات الشركة بنجاح</response>
        /// <response code="400">معرف الشركة في المسار لا يتطابق مع المعرف في النموذج</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="404">لم يتم العثور على معلومات الشركة بالمعرف المحدد</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// وتقوم بالتحقق من وجود السجل قبل تحديثه
        /// ثم تستخدم AutoMapper لتحديث الكيان الموجود بالبيانات الجديدة
        /// </remarks>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> PutCompanyInfo(int id, CompanyInfoDto companyInfoDto)
        {
            if (id != companyInfoDto.Id)
            {
                return BadRequest("Company info ID mismatch");
            }

            try
            {
                _logger.LogInformation("Updating company info with ID: {CompanyInfoId}", id);

                // First check if the company info exists
                var existingCompanyInfo = await _companyInfoRepository.GetByIdAsync(id);
                if (existingCompanyInfo == null)
                {
                    _logger.LogWarning("Company info not found with ID: {CompanyInfoId}", id);
                    return NotFound();
                }

                // Use AutoMapper to map from DTO to existing entity
                _mapper.Map(companyInfoDto, existingCompanyInfo);

                // Update in the repository
                _companyInfoRepository.Update(existingCompanyInfo);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Company info updated successfully: {CompanyInfoId}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating company info: {CompanyInfoId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء سجل جديد لمعلومات الشركة
        /// </summary>
        /// <param name="companyInfoDto">نموذج نقل البيانات المحتوي على معلومات الشركة الجديدة</param>
        /// <returns>معلومات الشركة التي تم إنشاؤها مع رابط للوصول إليها</returns>
        /// <response code="201">تم إنشاء معلومات الشركة بنجاح</response>
        /// <response code="400">البيانات المقدمة غير صالحة</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// وتقوم بتحويل نموذج نقل البيانات إلى كيان قاعدة البيانات
        /// ثم حفظه في قاعدة البيانات وإرجاع النموذج المنشأ مع رابط للوصول إليه
        /// </remarks>
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<CompanyInfoDto>> PostCompanyInfo(CompanyInfoDto companyInfoDto)
        {
            try
            {
                _logger.LogInformation("Creating new company info");

                // Convert DTO to domain model using AutoMapper
                var companyInfo = _mapper.Map<CompanyInfo>(companyInfoDto);

                await _companyInfoRepository.AddAsync(companyInfo);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Company info created successfully with ID: {CompanyInfoId}", companyInfo.Id);

                // Convert the created company info back to DTO using AutoMapper
                var createdCompanyInfoDto = _mapper.Map<CompanyInfoDto>(companyInfo);

                return CreatedAtAction("GetCompanyInfo", new { id = createdCompanyInfoDto.Id }, createdCompanyInfoDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating company info");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #endregion
    }
}
