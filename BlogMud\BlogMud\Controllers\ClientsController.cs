using AutoMapper;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

// Alias to avoid namespace conflict
using ClientModel = BlogMud.Shared.Models.Client;

namespace BlogMud.Controllers
{
    /// <summary>
    /// وحدة التحكم بالعملاء - تدير عمليات إنشاء وقراءة وتحديث وحذف العملاء
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ClientsController : ControllerBase
    {
        #region المتغيرات والمنشئ

        /// <summary>
        /// وحدة العمل المسؤولة عن إدارة المستودعات وحفظ التغييرات
        /// </summary>
        private readonly IUnitOfWork _unitOfWork;

        /// <summary>
        /// مستودع العملاء المسؤول عن عمليات قراءة وكتابة بيانات العملاء
        /// </summary>
        private readonly IRepository<ClientModel> _clientRepository;

        /// <summary>
        /// مسجل الأحداث لتسجيل معلومات التنفيذ والأخطاء
        /// </summary>
        private readonly ILogger<ClientsController> _logger;

        /// <summary>
        /// أداة التحويل بين كائنات النموذج وكائنات نقل البيانات
        /// </summary>
        private readonly IMapper _mapper;

        /// <summary>
        /// منشئ وحدة التحكم بالعملاء
        /// </summary>
        /// <param name="unitOfWork">وحدة العمل لإدارة المستودعات</param>
        /// <param name="logger">مسجل الأحداث لتسجيل المعلومات والأخطاء</param>
        /// <param name="mapper">أداة التحويل بين النماذج وكائنات نقل البيانات</param>
        public ClientsController(IUnitOfWork unitOfWork, ILogger<ClientsController> logger, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _clientRepository = _unitOfWork.Repository<ClientModel>();
            _logger = logger;
            _mapper = mapper;
        }

        #endregion

        #region عمليات القراءة (GET)

        /// <summary>
        /// استرجاع جميع العملاء
        /// </summary>
        /// <returns>قائمة بجميع العملاء</returns>
        /// <response code="200">تم استرجاع العملاء بنجاح</response>
        /// <response code="500">حدث خطأ أثناء استرجاع العملاء</response>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ClientDto>>> GetClients()
        {
            try
            {
                _logger.LogInformation("Fetching all clients");
                // استرجاع جميع العملاء من قاعدة البيانات
                var clients = await _clientRepository.GetAllAsync();
                // تحويل كائنات النموذج إلى كائنات نقل البيانات
                var clientDtos = _mapper.Map<IEnumerable<ClientDto>>(clients);
                _logger.LogInformation("Retrieved {Count} clients", clientDtos.Count());
                return Ok(clientDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving clients");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// استرجاع عميل محدد بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف العميل المطلوب</param>
        /// <returns>بيانات العميل المطلوب</returns>
        /// <response code="200">تم استرجاع العميل بنجاح</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="404">العميل غير موجود</response>
        /// <response code="500">حدث خطأ أثناء استرجاع العميل</response>
        /// <remarks>
        /// هذه الطريقة متاحة فقط للمستخدمين بدور المسؤول (Admin)
        /// </remarks>
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ClientDto>> GetClient(int id)
        {
            try
            {
                _logger.LogInformation("Fetching client with ID: {ClientId}", id);
                // استرجاع العميل من قاعدة البيانات باستخدام المعرف
                var client = await _clientRepository.GetByIdAsync(id);

                // التحقق من وجود العميل
                if (client == null)
                {
                    _logger.LogWarning("Client not found with ID: {ClientId}", id);
                    return NotFound();
                }

                // تحويل كائن النموذج إلى كائن نقل البيانات
                var clientDto = _mapper.Map<ClientDto>(client);
                return Ok(clientDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving client with ID: {ClientId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #endregion

        #region عمليات التعديل (PUT/POST/DELETE)

        /// <summary>
        /// تحديث عميل موجود بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف العميل المراد تحديثه</param>
        /// <param name="clientDto">بيانات العميل المحدثة</param>
        /// <returns>استجابة بدون محتوى في حالة نجاح التحديث</returns>
        /// <response code="204">تم تحديث العميل بنجاح</response>
        /// <response code="400">بيانات العميل غير صالحة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="404">العميل غير موجود</response>
        /// <response code="500">حدث خطأ أثناء تحديث العميل</response>
        /// <remarks>
        /// هذه الطريقة متاحة فقط للمستخدمين بدور المسؤول (Admin)
        /// </remarks>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> PutClient(int id, ClientDto clientDto)
        {
            // التحقق من تطابق المعرف في المسار مع المعرف في البيانات
            if (id != clientDto.Id)
            {
                return BadRequest("Client ID mismatch");
            }

            try
            {
                _logger.LogInformation("Updating client with ID: {ClientId}", id);

                // التحقق أولاً من وجود العميل
                var existingClient = await _clientRepository.GetByIdAsync(id);
                if (existingClient == null)
                {
                    _logger.LogWarning("Client not found with ID: {ClientId}", id);
                    return NotFound();
                }

                // استخدام AutoMapper لنسخ البيانات من كائن نقل البيانات إلى الكائن الموجود
                _mapper.Map(clientDto, existingClient);

                // تحديث العميل في المستودع
                _clientRepository.Update(existingClient);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Client updated successfully: {ClientId}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating client: {ClientId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء عميل جديد
        /// </summary>
        /// <param name="clientDto">بيانات العميل الجديد</param>
        /// <returns>العميل الذي تم إنشاؤه مع معرفه الجديد</returns>
        /// <response code="201">تم إنشاء العميل بنجاح</response>
        /// <response code="400">بيانات العميل غير صالحة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="500">حدث خطأ أثناء إنشاء العميل</response>
        /// <remarks>
        /// هذه الطريقة متاحة فقط للمستخدمين بدور المسؤول (Admin)
        /// تقوم هذه الطريقة تلقائيًا بتعيين تاريخ الإنشاء للعميل الجديد
        /// </remarks>
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ClientDto>> PostClient(ClientDto clientDto)
        {
            try
            {
                _logger.LogInformation("Creating new client: {ClientName}", clientDto.Name);

                // تحويل كائن نقل البيانات إلى كائن نموذج باستخدام AutoMapper
                var client = _mapper.Map<ClientModel>(clientDto);
                // تعيين تاريخ الإنشاء تلقائيًا
                client.CreatedAt = DateTime.Now;

                // إضافة العميل الجديد إلى قاعدة البيانات
                await _clientRepository.AddAsync(client);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Client created successfully with ID: {ClientId}", client.Id);

                // تحويل العميل المنشأ مرة أخرى إلى كائن نقل بيانات باستخدام AutoMapper
                var createdClientDto = _mapper.Map<ClientDto>(client);

                // إرجاع استجابة بحالة 201 Created مع رابط للحصول على العميل الجديد
                return CreatedAtAction("GetClient", new { id = createdClientDto.Id }, createdClientDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating client: {ClientName}", clientDto.Name);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف عميل بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف العميل المراد حذفه</param>
        /// <returns>استجابة بدون محتوى في حالة نجاح الحذف</returns>
        /// <response code="204">تم حذف العميل بنجاح</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="404">العميل غير موجود</response>
        /// <response code="500">حدث خطأ أثناء حذف العميل</response>
        /// <remarks>
        /// هذه الطريقة متاحة فقط للمستخدمين بدور المسؤول (Admin)
        /// </remarks>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteClient(int id)
        {
            try
            {
                _logger.LogInformation("Deleting client with ID: {ClientId}", id);

                // التحقق من وجود العميل
                var client = await _clientRepository.GetByIdAsync(id);
                if (client == null)
                {
                    _logger.LogWarning("Client not found with ID: {ClientId}", id);
                    return NotFound();
                }

                // حذف العميل من قاعدة البيانات
                _clientRepository.Remove(client);
                await _unitOfWork.SaveAsync();
                _logger.LogInformation("Client deleted successfully: {ClientId}", id);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting client with ID: {ClientId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #endregion

        #region دوال مساعدة

        /// <summary>
        /// التحقق من وجود عميل بمعرف محدد
        /// </summary>
        /// <param name="id">معرف العميل المراد التحقق منه</param>
        /// <returns>قيمة منطقية تشير إلى وجود العميل (true) أو عدم وجوده (false)</returns>
        /// <exception cref="Exception">يتم إلقاء استثناء في حالة حدوث خطأ أثناء التحقق</exception>
        /// <remarks>
        /// تستخدم هذه الدالة للتحقق من وجود عميل قبل إجراء عمليات عليه
        /// </remarks>
        private async Task<bool> ClientExists(int id)
        {
            try
            {
                _logger.LogInformation("Checking if client exists with ID: {ClientId}", id);
                // البحث عن العميل باستخدام المعرف
                var client = await _clientRepository.GetFirstOrDefaultAsync(c => c.Id == id);
                var exists = client != null;
                _logger.LogInformation("Client with ID: {ClientId} exists: {Exists}", id, exists);
                return exists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if client exists with ID: {ClientId}", id);
                // إعادة إلقاء الاستثناء ليتم معالجته في المستوى الأعلى
                throw;
            }
        }

        #endregion
    }
}
