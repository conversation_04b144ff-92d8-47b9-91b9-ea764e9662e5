using BlogMud.Client.Pages.Admin.Posts.OurSrtvices;
using BlogMud.Shared.DTOs;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using MudBlazor;
using System.Net.Http.Json;

namespace BlogMud.Client.Pages.Admin.Posts.OurSrtvices;

public partial class OurServicesManager : ComponentBase
{
    [Inject]
    private HttpClient Https { get; set; }

    [Inject]
    private ISnackbar Snackbars { get; set; }

    [Inject]
    private IDialogService DialogServices { get; set; }

    private List<ServiceDto> _services = new();
    private bool _loading = true;
    private string _searchString = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadServices();
    }

    private async Task LoadServices()
    {
        try
        {
            _loading = true;
            _services = await Https.GetFromJsonAsync<List<ServiceDto>>("api/Services") ?? new List<ServiceDto>();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbars.Add($"خطأ في تحميل الخدمات: {ex.Message}", Severity.Error);
        }
        finally
        {
            _loading = false;
            StateHasChanged();
        }
    }

    private async Task OpenAddServiceDialog()
    {
        var options = new DialogOptions()
        {
            MaxWidth = MaxWidth.ExtraLarge,
            FullWidth = true,
            CloseButton = true,
            BackdropClick = false,
            CloseOnEscapeKey = false
        };

        var dialog = await DialogServices.ShowAsync<ServiceAddDialog>("إضافة خدمة جديدة", options);
        var result = await dialog.Result;

        if (!result.Canceled && result.Data is ServiceDto newService)
        {
            await CreateService(newService);
        }
    }

    private async Task CreateService(ServiceDto newService)
    {
        try
        {
            var response = await Https.PostAsJsonAsync("api/Services", newService);

            if (response.IsSuccessStatusCode)
            {
                var createdService = await response.Content.ReadFromJsonAsync<ServiceDto>();
                if (createdService != null)
                {
                    _services.Add(createdService);
                    StateHasChanged(); // Trigger UI refresh
                }

                Snackbars.Add("تم إنشاء الخدمة بنجاح", Severity.Success);
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                Snackbars.Add($"خطأ في إنشاء الخدمة: {error}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbars.Add($"خطأ في إنشاء الخدمة: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteService(int id)
    {
        var parameters = new DialogParameters
        {
            { "ContentText", "هل أنت متأكد من حذف هذه الخدمة؟ لا يمكن التراجع عن هذا الإجراء." },
            { "ButtonText", "حذف" },
            { "Color", Color.Error }
        };

        var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.ExtraSmall };
        var dialog = await DialogServices.ShowAsync<BlogMud.Client.Components.ConfirmationDialog>("تأكيد الحذف", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            try
            {
                var response = await Https.DeleteAsync($"api/Services/{id}");

                if (response.IsSuccessStatusCode)
                {
                    _services.RemoveAll(s => s.Id == id);
                    StateHasChanged(); // Trigger UI refresh
                    Snackbars.Add("تم حذف الخدمة بنجاح", Severity.Success);
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    Snackbars.Add($"خطأ في حذف الخدمة: {error}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbars.Add($"خطأ في حذف الخدمة: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task OpenEditDialog(ServiceDto service)
    {
        var parameters = new DialogParameters<ServiceEditForm>
        {
            { x => x.Service, service }
        };

        var options = new DialogOptions()
        {
            MaxWidth = MaxWidth.Large,
            FullWidth = true,
            CloseButton = true,
            BackdropClick = false,
            CloseOnEscapeKey = false
        };

        try
        {
            // Fetch the data before opening the dialog
            var freshData = await Https.GetFromJsonAsync<ServiceDto>($"api/Services/{service.Id}");

            if (freshData == null)
            {
                Snackbars.Add("خطأ: لم يتم العثور على بيانات الخدمة", Severity.Error);
                return;
            }

            // Create a completely new instance to avoid reference issues
            var editingService = new ServiceDto
            {
                Id = freshData.Id,
                Title = freshData.Title ?? "",
                PrimaryDescription = freshData.PrimaryDescription ?? "",
                DetailedDescription = freshData.DetailedDescription ?? "",
                MainImageUrl = freshData.MainImageUrl ?? "",
                ImageCarousel = freshData.ImageCarousel ?? "",
                VideoUrl = freshData.VideoUrl ?? "",
                Features = freshData.Features ?? "",
                DisplayOrder = freshData.DisplayOrder,
                IsActive = freshData.IsActive,
                CreatedAt = freshData.CreatedAt,
                LastModifiedAt = freshData.LastModifiedAt
            };

            // Update the parameters with the fresh data
            parameters = new DialogParameters<ServiceEditForm>
            {
                { x => x.Service, editingService }
            };

            // Open the dialog using the dialog service
            var dialog = await DialogServices.ShowAsync<ServiceEditForm>(
                "تعديل الخدمة",
                parameters,
                options);

            // Wait for the dialog result
            var result = await dialog.Result;

            // If the dialog was not cancelled, update the service
            if (!result.Canceled && result.Data is ServiceDto updatedService)
            {
                await HandleSaveService(updatedService);
            }
        }
        catch (Exception ex)
        {
            Snackbars.Add($"خطأ في تحميل بيانات الخدمة: {ex.Message}", Severity.Error);
            Console.WriteLine($"Error loading service data: {ex}");
        }
    }

    private async Task HandleSaveService(ServiceDto updatedService)
    {
        try
        {
            // Ensure we're not sending null values
            updatedService.PrimaryDescription ??= "";
            updatedService.DetailedDescription ??= "";
            updatedService.MainImageUrl ??= "";
            updatedService.ImageCarousel ??= "";
            updatedService.VideoUrl ??= "";
            updatedService.Features ??= "";

            // Add the current timestamp for LastModifiedAt
            updatedService.LastModifiedAt = DateTime.Now;

            // Send the update request
            var response = await Https.PutAsJsonAsync($"api/Services/{updatedService.Id}", updatedService);

            if (response.IsSuccessStatusCode)
            {
                // Update the local list
                var index = _services.FindIndex(s => s.Id == updatedService.Id);
                if (index >= 0)
                {
                    _services[index] = updatedService;
                    StateHasChanged(); // Trigger UI refresh
                }

                // Show success message
                Snackbars.Add("تم تحديث الخدمة بنجاح", Severity.Success);
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                Snackbars.Add($"خطأ في تحديث الخدمة: {error}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbars.Add($"خطأ في تحديث الخدمة: {ex.Message}", Severity.Error);
            Console.WriteLine($"Error updating service: {ex}");
        }
    }

    private bool FilterFunc(ServiceDto service)
    {
        if (string.IsNullOrWhiteSpace(_searchString))
            return true;

        if (service.Title.Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        if (service.PrimaryDescription?.Contains(_searchString, StringComparison.OrdinalIgnoreCase) == true)
            return true;

        if (service.Features?.Contains(_searchString, StringComparison.OrdinalIgnoreCase) == true)
            return true;

        return false;
    }

    private async Task ViewServiceDetails(ServiceDto service)
    {
        var parameters = new DialogParameters<ServiceDetailsDialog>
        {
            { x => x.Service, service }
        };

        var options = new DialogOptions()
        {
            MaxWidth = MaxWidth.Large,
            FullWidth = true,
            CloseButton = true
        };

        await DialogServices.ShowAsync<ServiceDetailsDialog>("تفاصيل الخدمة", parameters, options);
    }

    private async Task CleanupUnusedFiles()
    {
        try
        {
            var parameters = new DialogParameters
            {
                { "ContentText", "هل أنت متأكد من تنظيف الملفات غير المستخدمة؟ سيتم حذف جميع الملفات التي لا ترتبط بأي خدمة." },
                { "ButtonText", "تنظيف" },
                { "Color", Color.Warning }
            };

            var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.Small };
            var dialog = await DialogServices.ShowAsync<BlogMud.Client.Components.ConfirmationDialog>("تأكيد التنظيف", parameters, options);
            var result = await dialog.Result;

            if (!result.Canceled)
            {
                var response = await Https.PostAsync("api/upload/cleanup/services", null);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var cleanupResult = System.Text.Json.JsonSerializer.Deserialize<CleanupResult>(responseContent, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    Snackbars.Add($"تم تنظيف الملفات بنجاح. تم حذف {cleanupResult?.DeletedCount ?? 0} ملف غير مستخدم", Severity.Success);
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    Snackbars.Add($"خطأ في تنظيف الملفات: {error}", Severity.Error);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbars.Add($"خطأ في تنظيف الملفات: {ex.Message}", Severity.Error);
        }
    }

    private class CleanupResult
    {
        public int DeletedCount { get; set; }
        public string Message { get; set; } = "";
    }
}