﻿@page "/admin/animatedgifs"
@attribute [Authorize(Roles = "Admin")]
@layout BlogMud.Client.Layout.AdminLayout

<PageTitle>إدارة الصور المتحركة</PageTitle>
<AnimatedGifsCSS />

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="animated-gifs-container">
    <!-- عنوان الصفحة المحسن -->
    <div class="page-header-section">
        <MudPaper Class="page-header-card" Elevation="0">
            <div class="header-content">
                <div class="header-icon-wrapper">
                    <MudIcon Icon="@Icons.Material.Filled.Gif" Class="header-icon" />
                </div>
                <div class="header-text">
                    <MudText Typo="Typo.h4" Class="header-title">إدارة الصور المتحركة</MudText>
                    <MudText Typo="Typo.body1" Class="header-subtitle">
                        إدارة مجموعة الصور المتحركة (GIF) التي تظهر في الموقع بتصميم أنيق ومتجاوب
                    </MudText>
                </div>
            </div>
            <div class="header-stats">
                <div class="stat-item">
                    <MudIcon Icon="@Icons.Material.Filled.Image" Class="stat-icon" />
                    <div class="stat-content">
                        <MudText Typo="Typo.h6" Class="stat-number">@_animatedGifs.Count</MudText>
                        <MudText Typo="Typo.caption" Class="stat-label">إجمالي الصور</MudText>
                    </div>
                </div>
                <div class="stat-item">
                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="stat-icon active" />
                    <div class="stat-content">
                        <MudText Typo="Typo.h6" Class="stat-number">@_animatedGifs.Count(g => g.IsActive)</MudText>
                        <MudText Typo="Typo.caption" Class="stat-label">الصور النشطة</MudText>
                    </div>
                </div>
            </div>
        </MudPaper>
    </div>

    <!-- معاينة الصور المتحركة المحسنة -->
    @if (_animatedGifs.Any())
    {
        <div class="preview-section">
            <MudPaper Class="preview-card" Elevation="0">
                <div class="preview-header">
                    <div class="preview-title-wrapper">
                        <MudIcon Icon="@Icons.Material.Filled.Preview" Class="preview-icon" />
                        <MudText Typo="Typo.h6" Class="preview-title">معاينة الصور المتحركة</MudText>
                    </div>
                    <div class="carousel-controls">
                        <MudIconButton Icon="@(autocycle ? Icons.Material.Filled.Pause : Icons.Material.Filled.PlayArrow)"
                                       Color="Color.Primary"
                                       Size="Size.Small"
                                       OnClick="() => autocycle = !autocycle"
                                       Title="@(autocycle ? "إيقاف التشغيل التلقائي" : "تشغيل تلقائي")" />
                        <MudIconButton Icon="@(arrows ? Icons.Material.Filled.VisibilityOff : Icons.Material.Filled.Visibility)"
                                       Color="Color.Secondary"
                                       Size="Size.Small"
                                       OnClick="() => arrows = !arrows"
                                       Title="@(arrows ? "إخفاء الأسهم" : "إظهار الأسهم")" />
                    </div>
                </div>

                @if (_animatedGifs.Any(g => g.IsActive))
                {
                    <div class="carousel-container">
                        <MudCarousel Class="enhanced-carousel"
                                     ShowArrows="@arrows"
                                     ShowBullets="@bullets"
                                     EnableSwipeGesture="@enableSwipeGesture"
                                     AutoCycle="@autocycle"
                                     TData="object"
                                     AutoCycleTime="TimeSpan.FromSeconds(4)"
                                     BulletsColor="Color.Primary">
                            @foreach (var gif in _animatedGifs.Where(g => g.IsActive).OrderBy(g => g.DisplayOrder))
                            {
                                <MudCarouselItem Transition="transition">
                                    <div class="carousel-item">
                                        <div class="slide-content">
                                            <div class="slide-image-wrapper">
                                                <img src="@gif.ImageUrl"
                                                     alt="@gif.Title"
                                                     class="slide-image"
                                                     @onclick="() => ShowImageModal(gif.ImageUrl, gif.Title)"
                                                     @onclick:stopPropagation="true"
                                                     title="انقر لعرض الصورة بحجم كامل" />
                                                <div class="slide-overlay"></div>
                                            </div>
                                            <div class="slide-info">
                                                <MudText Typo="Typo.h6" Class="slide-title">@gif.Title</MudText>
                                                @if (!string.IsNullOrEmpty(gif.Description))
                                                {
                                                    <MudText Typo="Typo.body2" Class="slide-description">@gif.Description</MudText>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </MudCarouselItem>
                            }
                        </MudCarousel>
                    </div>
                }
                else
                {
                    <div class="no-active-slides">
                        <MudIcon Icon="@Icons.Material.Filled.Image" Class="no-slides-icon" />
                        <MudText Typo="Typo.h6" Class="no-slides-title">لا توجد صور نشطة</MudText>
                        <MudText Typo="Typo.body2" Class="no-slides-subtitle">قم بتفعيل بعض الصور لرؤية المعاينة</MudText>
                    </div>
                }
            </MudPaper>
        </div>
    }



    <!-- أدوات التحكم المحسنة -->
    <div class="controls-section">
        <MudPaper Class="controls-card" Elevation="0">
            <div class="controls-container">
                <div class="controls-left">
                    <div class="action-buttons">
                        <MudButton Color="Color.Primary"
                                   StartIcon="@Icons.Material.Filled.Add"
                                   OnClick="OpenAddDialog"
                                   Variant="Variant.Filled"
                                   Size="Size.Large"
                                   Class="primary-action-btn">
                            <span class="button-text-full">إضافة صورة متحركة جديدة</span>
                            <span class="button-text-short">إضافة</span>
                        </MudButton>

                        <MudButton Color="Color.Secondary"
                                   StartIcon="@Icons.Material.Filled.Refresh"
                                   OnClick="LoadAnimatedGifs"
                                   Variant="Variant.Outlined"
                                   Size="Size.Large"
                                   Class="secondary-action-btn"
                                   Loading="_loading">
                            تحديث
                        </MudButton>
                    </div>
                </div>

                <div class="controls-right">
                    <!-- شريط البحث المحسن -->
                    <div class="search-wrapper">
                        <MudTextField @bind-Value="_searchString"
                                      Placeholder="البحث في الصور المتحركة..."
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Search"
                                      IconSize="Size.Medium"
                                      Variant="Variant.Outlined"
                                      Class="enhanced-search-field"
                                      Immediate="true"
                                      DebounceInterval="300">
                        </MudTextField>
                        @if (!string.IsNullOrEmpty(_searchString))
                        {
                            <MudIconButton Icon="@Icons.Material.Filled.Clear"
                                           Color="Color.Default"
                                           Size="Size.Small"
                                           OnClick="() => _searchString = string.Empty"
                                           Class="search-clear-btn"
                                           Title="مسح البحث" />
                        }
                    </div>
                </div>
            </div>
        </MudPaper>
    </div>

    @if (_loading)
    {
        <div class="loading-section">
            <MudPaper Class="loading-card" Elevation="0">
                <div class="loading-content">
                    <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                    <MudText Typo="Typo.h6" Class="loading-text">جاري تحميل الصور المتحركة...</MudText>
                    <MudText Typo="Typo.body2" Class="loading-subtitle">يرجى الانتظار</MudText>
                </div>
            </MudPaper>
        </div>
    }
    else if (_animatedGifs.Count == 0)
    {
        <div class="empty-state-section">
            <MudPaper Class="empty-state-card" Elevation="0">
                <div class="empty-state-content">
                    <div class="empty-state-icon-wrapper">
                        <MudIcon Icon="@Icons.Material.Filled.Gif" Class="empty-state-icon" />
                    </div>
                    <MudText Typo="Typo.h5" Class="empty-state-title">لا توجد صور متحركة</MudText>
                    <MudText Typo="Typo.body1" Class="empty-state-subtitle">
                        ابدأ بإضافة صور متحركة جديدة لعرضها في الموقع
                    </MudText>
                    <MudButton Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Add"
                               OnClick="OpenAddDialog"
                               Variant="Variant.Filled"
                               Size="Size.Large"
                               Class="empty-state-action-btn">
                        إضافة أول صورة متحركة
                    </MudButton>
                </div>
            </MudPaper>
        </div>
    }
    else
    {
        <!-- الجدول العادي للشاشات الكبيرة -->
        <div class="regular-table">
            <MudPaper Class="pa-4">
                <MudTable Items="@_animatedGifs" Loading="@_loading" Filter="FilterFunc" Dense="true" Hover="true" Bordered="true">
                    <HeaderContent>
                        <MudTh>ت</MudTh>
                        <MudTh>العنوان</MudTh>
                        <MudTh>الوصف</MudTh>
                        <MudTh>الترتيب</MudTh>
                        <MudTh>نشط</MudTh>
                        <MudTh>تاريخ الإنشاء</MudTh>
                        <MudTh>الإجراءات</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="ت">@(_animatedGifs.IndexOf(context) + 1)</MudTd>
                        <MudTd>@(context.Title?.Length > 30 ? context.Title.Substring(0, 30) + "..." : context.Title)</MudTd>
                        <MudTd>@(context.Description?.Length > 30 ? context.Description.Substring(0, 30) + "..." : context.Description)</MudTd>
                        <MudTd>@context.DisplayOrder</MudTd>
                        <MudTd>
                            <MudChip T="string" Color="@(context.IsActive ? Color.Success : Color.Default)" Size="Size.Small">
                                @(context.IsActive ? "نشط" : "غير نشط")
                            </MudChip>
                        </MudTd>
                        <MudTd>@context.CreatedAt.ToString("yyyy/MM/dd")</MudTd>
                        <MudTd>
                            <div class="d-flex gap-1">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                               Color="Color.Primary"
                                               Size="Size.Small"
                                               OnClick="@(() => OpenEditDialog(context))"
                                               Title="تعديل" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                               Color="Color.Error"
                                               Size="Size.Small"
                                               OnClick="@(() => DeleteAnimatedGif(context.Id))"
                                               Title="حذف" />
                            </div>
                        </MudTd>
                    </RowTemplate>
                    <PagerContent>
                        <MudTablePager PageSizeOptions="new int[] { 10, 25, 50, 100 }" />
                    </PagerContent>
                </MudTable>
            </MudPaper>
        </div>

        <!-- تخطيط البطاقات المحسن للشاشات الصغيرة -->
        <div class="mobile-cards">
            @foreach (var gif in _animatedGifs.Where(FilterFunc))
            {
                <MudCard Class="mobile-card" Elevation="1">
                    <MudCardContent Class="compact-content">
                        <!-- رقم الصورة والإجراءات -->
                        <div class="gif-header">
                            <MudChip T="string" Color="Color.Primary" Size="Size.Small" Class="gif-number">
                                @(_animatedGifs.IndexOf(gif) + 1)
                            </MudChip>
                            <div class="gif-actions">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                               Color="Color.Primary"
                                               Size="Size.Small"
                                               OnClick="@(() => OpenEditDialog(gif))"
                                               Title="تعديل" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                               Color="Color.Error"
                                               Size="Size.Small"
                                               OnClick="@(() => DeleteAnimatedGif(gif.Id))"
                                               Title="حذف" />
                            </div>
                        </div>

                        <!-- عنوان الصورة -->
                        <div class="gif-title">
                            @(gif.Title ?? "غير محدد")
                        </div>

                        <!-- معلومات الصورة -->
                        @if (!string.IsNullOrEmpty(gif.Description))
                        {
                            <div class="gif-info">
                                <MudIcon Icon="@Icons.Material.Filled.Description" Size="Size.Small" Class="info-icon" />
                                <span class="info-label">الوصف:</span>
                                <span>@(gif.Description.Length > 30 ? gif.Description.Substring(0, 30) + "..." : gif.Description)</span>
                            </div>
                        }
                        <div class="gif-info">
                            <MudIcon Icon="@Icons.Material.Filled.Sort" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">الترتيب:</span>
                            <span>@gif.DisplayOrder</span>
                        </div>
                        <div class="gif-info">
                            <MudIcon Icon="@Icons.Material.Filled.DateRange" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">تاريخ الإنشاء:</span>
                            <span>@gif.CreatedAt.ToString("yyyy/MM/dd")</span>
                        </div>

                        <!-- حالة الصورة -->
                        <div class="gif-info">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">الحالة:</span>
                            @if (gif.IsActive)
                            {
                                <MudChip T="string" Color="Color.Success" Size="Size.Small" Class="status-chip">نشط</MudChip>
                            }
                            else
                            {
                                <MudChip T="string" Color="Color.Error" Size="Size.Small" Class="status-chip">غير نشط</MudChip>
                            }
                        </div>

                    </MudCardContent>
                </MudCard>
            }
        </div>
    }
</MudContainer>

<!-- مودال عرض الصورة بحجم كامل -->
@if (_showImageModal)
{
    <div class="image-modal-overlay" @onclick="HideImageModal" @onkeydown="@(async (e) => { if (e.Key == "Escape") HideImageModal(); })" tabindex="0">
        <button class="image-modal-close" @onclick="HideImageModal" @onclick:stopPropagation="true" title="إغلاق">
            <MudIcon Icon="@Icons.Material.Filled.Close" />
        </button>
        <div class="image-modal-content-wrapper" @onclick:stopPropagation="true">
            <img src="@_modalImageUrl"
                 alt="@_modalImageTitle"
                 class="image-modal-content" />
            <div class="image-modal-title">@_modalImageTitle</div>
        </div>
    </div>
}
