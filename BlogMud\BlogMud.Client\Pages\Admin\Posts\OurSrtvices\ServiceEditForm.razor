@using BlogMud.Shared.DTOs
@using System.Net.Http.Json
@using System.ComponentModel.DataAnnotations
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject MudBlazor.IDialogService DialogService

<MudDialog MaxWidth="MaxWidth.Large" FullWidth="true">
    <TitleContent>
        <div class="d-flex align-center">
            <MudIcon Icon="@Icons.Material.Filled.Edit" Class="me-2" Color="Color.Primary" />
            <MudText Typo="Typo.h6">تحرير الخدمة: @(Service?.Title ?? "جاري التحميل...")</MudText>
        </div>
    </TitleContent>
    
    <DialogContent>
        @if (Service != null)
        {
            <MudContainer MaxWidth="MaxWidth.False" Class="pa-0">
                <MudForm @ref="_form" Model="@Service">
                    
                    <!-- التبويبات الرئيسية -->
                    <MudTabs Elevation="1" 
                             Rounded="true" 
                             PanelClass="pa-4" 
                             @bind-ActivePanelIndex="_activeTabIndex"
                             Class="mb-4">
                        
                        <!-- تبويب تفاصيل الخدمة -->
                        <MudTabPanel Text="تفاصيل الخدمة" 
                                    Icon="@Icons.Material.Filled.Info"
                                    BadgeData="@GetServiceDetailsBadge()"
                                    BadgeColor="@GetServiceDetailsBadgeColor()">
                            
                            <MudGrid Spacing="3">
                                <!-- المعلومات الأساسية -->
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6" Class="d-flex align-center mb-3">
                                        <MudIcon Icon="@Icons.Material.Filled.Article" Class="me-2" Color="Color.Primary" />
                                        المعلومات الأساسية
                                    </MudText>
                                    <MudDivider Class="mb-4" />
                                </MudItem>

                                <!-- العنوان وترتيب العرض -->
                                <MudItem xs="12" sm="8">
                                    <MudTextField @bind-Value="Service.Title"
                                                  For="@(() => Service.Title)"
                                                  Label="عنوان الخدمة"
                                                  Variant="Variant.Outlined"
                                                  Margin="Margin.Dense"
                                                  Required="true"
                                                  RequiredError="عنوان الخدمة مطلوب"
                                                  MaxLength="100"
                                                  Counter="100"
                                                  HelperText="أدخل عنواً واضحاً ومميزاً للخدمة"
                                                  Adornment="Adornment.Start"
                                                  AdornmentIcon="@Icons.Material.Filled.Title" />
                                </MudItem>

                                <MudItem xs="12" sm="4">
                                    <MudNumericField @bind-Value="Service.DisplayOrder"
                                                     For="@(() => Service.DisplayOrder)"
                                                     Label="ترتيب العرض"
                                                     Variant="Variant.Outlined"
                                                     Margin="Margin.Dense"
                                                     Min="0"
                                                     Max="999"
                                                     HelperText="ترتيب ظهور الخدمة"
                                                     Adornment="Adornment.Start"
                                                     AdornmentIcon="@Icons.Material.Filled.Sort" />
                                </MudItem>

                                <!-- الوصف الأساسي -->
                                <MudItem xs="12">
                                    <MudTextField @bind-Value="Service.PrimaryDescription"
                                                  For="@(() => Service.PrimaryDescription)"
                                                  Label="الوصف الأساسي"
                                                  Variant="Variant.Outlined"
                                                  Lines="3"
                                                  MaxLines="5"
                                                  Required="true"
                                                  RequiredError="الوصف الأساسي مطلوب"
                                                  MaxLength="500"
                                                  Counter="500"
                                                  HelperText="وصف مختصر وجذاب للخدمة (يظهر في البطاقات)"
                                                  Adornment="Adornment.Start"
                                                  AdornmentIcon="@Icons.Material.Filled.Description" />
                                </MudItem>

                                <!-- الوصف التفصيلي -->
                                <MudItem xs="12">
                                    <MudTextField @bind-Value="Service.DetailedDescription"
                                                  For="@(() => Service.DetailedDescription)"
                                                  Label="الوصف التفصيلي"
                                                  Variant="Variant.Outlined"
                                                  Lines="5"
                                                  MaxLines="10"
                                                  MaxLength="2000"
                                                  Counter="2000"
                                                  HelperText="وصف شامل ومفصل للخدمة (يظهر في صفحة التفاصيل)"
                                                  Adornment="Adornment.Start"
                                                  AdornmentIcon="@Icons.Material.Filled.Notes" />
                                </MudItem>

                                <!-- المميزات -->
                                <MudItem xs="12">
                                    <MudTextField @bind-Value="Service.Features"
                                                  For="@(() => Service.Features)"
                                                  Label="مميزات الخدمة"
                                                  Variant="Variant.Outlined"
                                                  Lines="3"
                                                  MaxLines="5"
                                                  MaxLength="1000"
                                                  Counter="1000"
                                                  HelperText="أدخل مميزات الخدمة مفصولة بفواصل منقوطة (;)"
                                                  Placeholder="ميزة أولى; ميزة ثانية; ميزة ثالثة"
                                                  Adornment="Adornment.Start"
                                                  AdornmentIcon="@Icons.Material.Filled.Star" />
                                </MudItem>

                                <!-- حالة الخدمة -->
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6" Class="d-flex align-center mb-3">
                                        <MudIcon Icon="@Icons.Material.Filled.Settings" Class="me-2" Color="Color.Primary" />
                                        إعدادات الخدمة
                                    </MudText>
                                    <MudDivider Class="mb-4" />
                                </MudItem>

                                <MudItem xs="12" sm="6">
                                    <MudPaper Class="pa-4" Elevation="1">
                                        <div class="d-flex align-center justify-space-between">
                                            <div>
                                                <MudText Typo="Typo.subtitle1" Class="mb-1">حالة الخدمة</MudText>
                                                <MudText Typo="Typo.body2" Color="Color.Secondary">
                                                    @(Service.IsActive ? "الخدمة نشطة ومرئية للزوار" : "الخدمة غير نشطة ومخفية")
                                                </MudText>
                                            </div>
                                            <MudSwitch @bind-Value="Service.IsActive"
                                                       For="@(() => Service.IsActive)"
                                                       Color="Color.Success"
                                                       Size="Size.Large"
                                                       ThumbIcon="@(Service.IsActive ? Icons.Material.Filled.Check : Icons.Material.Filled.Close)"
                                                       ThumbIconColor="@(Service.IsActive ? Color.Success : Color.Error)" />
                                        </div>
                                    </MudPaper>
                                </MudItem>

                                <!-- معاينة سريعة -->
                                <MudItem xs="12" sm="6">
                                    <MudPaper Class="pa-4" Elevation="1">
                                        <MudText Typo="Typo.subtitle1" Class="mb-2">معاينة سريعة</MudText>
                                        <div class="d-flex flex-column gap-2">
                                            <div class="d-flex align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.Title" Size="Size.Small" Class="me-2" />
                                                <MudText Typo="Typo.body2">
                                                    @(!string.IsNullOrEmpty(Service.Title) ? Service.Title : "لا يوجد عنوان")
                                                </MudText>
                                            </div>
                                            <div class="d-flex align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.Sort" Size="Size.Small" Class="me-2" />
                                                <MudText Typo="Typo.body2">ترتيب: @Service.DisplayOrder</MudText>
                                            </div>
                                            <div class="d-flex align-center">
                                                <MudIcon Icon="@(Service.IsActive ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff)" 
                                                         Size="Size.Small" 
                                                         Class="me-2" 
                                                         Color="@(Service.IsActive ? Color.Success : Color.Error)" />
                                                <MudText Typo="Typo.body2">@(Service.IsActive ? "نشطة" : "غير نشطة")</MudText>
                                            </div>
                                        </div>
                                    </MudPaper>
                                </MudItem>
                            </MudGrid>
                        </MudTabPanel>

                        <!-- تبويب الوسائط -->
                        <MudTabPanel Text="الوسائط" 
                                    Icon="@Icons.Material.Filled.PermMedia"
                                    BadgeData="@GetMediaBadge()"
                                    BadgeColor="@GetMediaBadgeColor()">
                            
                            <MudGrid Spacing="3">
                                <!-- عنوان القسم -->
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6" Class="d-flex align-center mb-3">
                                        <MudIcon Icon="@Icons.Material.Filled.CloudUpload" Class="me-2" Color="Color.Primary" />
                                        إدارة الوسائط
                                    </MudText>
                                    <MudDivider Class="mb-4" />
                                </MudItem>

                                <!-- الصورة الرئيسية -->
                                <MudItem xs="12" md="6" lg="4">
                                    <MudPaper Class="pa-4" Elevation="2" Style="min-height: 350px;">
                                        <div class="d-flex flex-column h-100">
                                            <MudText Typo="Typo.h6" Class="mb-2 d-flex align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.Image" Class="me-2" Color="Color.Primary" />
                                                الصورة الرئيسية
                                            </MudText>
                                            <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3">
                                                الصورة التي تظهر كغلاف للخدمة
                                            </MudText>

                                            <div class="flex-grow-1 d-flex flex-column justify-center">
                                                @if (!string.IsNullOrEmpty(Service.MainImageUrl))
                                                {
                                                    <div class="text-center mb-3">
                                                        <MudImage Src="@Service.MainImageUrl" 
                                                                 Alt="الصورة الرئيسية"
                                                                 Class="rounded-lg shadow-sm"
                                                                 Style="max-height: 150px; max-width: 100%; object-fit: cover;" />
                                                    </div>
                                                    <div class="d-flex gap-2 mb-3">
                                                        <MudButton Variant="Variant.Outlined" 
                                                                  Color="Color.Info" 
                                                                  Size="Size.Small"
                                                                  StartIcon="@Icons.Material.Filled.Visibility"
                                                                  Href="@Service.MainImageUrl"
                                                                  Target="_blank"
                                                                  FullWidth="true">
                                                            معاينة
                                                        </MudButton>
                                                        <MudButton Variant="Variant.Outlined" 
                                                                  Color="Color.Error" 
                                                                  Size="Size.Small"
                                                                  StartIcon="@Icons.Material.Filled.Delete"
                                                                  OnClick="@(() => RemoveMainImage())"
                                                                  FullWidth="true">
                                                            حذف
                                                        </MudButton>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <div class="text-center mb-3">
                                                        <MudIcon Icon="@Icons.Material.Filled.CloudUpload" 
                                                                Size="Size.Large" 
                                                                Color="Color.Secondary" 
                                                                Class="mb-2" />
                                                        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3">
                                                            لا توجد صورة رئيسية
                                                        </MudText>
                                                    </div>
                                                }
                                            </div>

                                            <MudFileUpload T="IBrowserFile" 
                                                          Accept=".png,.jpg,.jpeg,.webp" 
                                                          FilesChanged="@UploadMainImage"
                                                          MaximumFileCount="1">
                                                <ActivatorContent>
                                                    <MudButton Variant="Variant.Filled"
                                                              Color="Color.Primary"
                                                              StartIcon="@Icons.Material.Filled.Upload"
                                                              FullWidth="true"
                                                              Size="Size.Medium">
                                                        @(!string.IsNullOrEmpty(Service.MainImageUrl) ? "تغيير الصورة" : "رفع صورة")
                                                    </MudButton>
                                                </ActivatorContent>
                                            </MudFileUpload>
                                            
                                            <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mt-2 text-center">
                                                الحد الأقصى: 5 ميجابايت | PNG, JPG, JPEG, WebP
                                            </MudText>
                                        </div>
                                    </MudPaper>
                                </MudItem>

                                <!-- صور الدوار -->
                                <MudItem xs="12" md="6" lg="4">
                                    <MudPaper Class="pa-4" Elevation="2" Style="min-height: 350px;">
                                        <div class="d-flex flex-column h-100">
                                            <MudText Typo="Typo.h6" Class="mb-2 d-flex align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.PhotoLibrary" Class="me-2" Color="Color.Secondary" />
                                                صور الدوار
                                            </MudText>
                                            <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3">
                                                مجموعة صور تظهر في معرض الخدمة
                                            </MudText>

                                            <div class="flex-grow-1 d-flex flex-column justify-center">
                                                @if (!string.IsNullOrEmpty(Service.ImageCarousel))
                                                {
                                                    var carouselImages = Service.ImageCarousel.Split(';', StringSplitOptions.RemoveEmptyEntries);
                                                    <div class="text-center mb-3">
                                                        <MudChip T="string" 
                                                                Size="Size.Medium" 
                                                                Color="Color.Info"
                                                                Icon="@Icons.Material.Filled.Collections">
                                                            @carouselImages.Length صورة
                                                        </MudChip>
                                                    </div>
                                                    <div class="d-flex gap-2 mb-3">
                                                        <MudButton Variant="Variant.Outlined" 
                                                                  Color="Color.Info" 
                                                                  Size="Size.Small"
                                                                  StartIcon="@Icons.Material.Filled.Visibility"
                                                                  OnClick="@(() => PreviewCarouselImages())"
                                                                  FullWidth="true">
                                                            معاينة
                                                        </MudButton>
                                                        <MudButton Variant="Variant.Outlined" 
                                                                  Color="Color.Error" 
                                                                  Size="Size.Small"
                                                                  StartIcon="@Icons.Material.Filled.Delete"
                                                                  OnClick="@(() => ClearCarouselImages())"
                                                                  FullWidth="true">
                                                            حذف الكل
                                                        </MudButton>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <div class="text-center mb-3">
                                                        <MudIcon Icon="@Icons.Material.Filled.PhotoLibrary" 
                                                                Size="Size.Large" 
                                                                Color="Color.Secondary" 
                                                                Class="mb-2" />
                                                        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3">
                                                            لا توجد صور في الدوار
                                                        </MudText>
                                                    </div>
                                                }
                                            </div>

                                            <div class="d-flex gap-2 mb-2">
                                                <MudFileUpload T="IReadOnlyList<IBrowserFile>" 
                                                              Accept=".png,.jpg,.jpeg,.webp" 
                                                              FilesChanged="@AddCarouselImages"
                                                              MaximumFileCount="10">
                                                    <ActivatorContent>
                                                        <MudButton Variant="Variant.Outlined"
                                                                  Color="Color.Secondary"
                                                                  StartIcon="@Icons.Material.Filled.Add"
                                                                  FullWidth="true"
                                                                  Size="Size.Small">
                                                            إضافة صور
                                                        </MudButton>
                                                    </ActivatorContent>
                                                </MudFileUpload>
                                                
                                                <MudFileUpload T="IReadOnlyList<IBrowserFile>" 
                                                              Accept=".png,.jpg,.jpeg,.webp" 
                                                              FilesChanged="@ReplaceCarouselImages"
                                                              MaximumFileCount="10">
                                                    <ActivatorContent>
                                                        <MudButton Variant="Variant.Filled"
                                                                  Color="Color.Secondary"
                                                                  StartIcon="@Icons.Material.Filled.SwapHoriz"
                                                                  FullWidth="true"
                                                                  Size="Size.Small">
                                                            استبدال الكل
                                                        </MudButton>
                                                    </ActivatorContent>
                                                </MudFileUpload>
                                            </div>
                                            
                                            <MudText Typo="Typo.caption" Color="Color.Secondary" Class="text-center">
                                                الحد الأقصى: 5 ميجابايت لكل صورة | حتى 10 صور
                                            </MudText>
                                        </div>
                                    </MudPaper>
                                </MudItem>

                                <!-- الفيديو -->
                                <MudItem xs="12" md="12" lg="4">
                                    <MudPaper Class="pa-4" Elevation="2" Style="min-height: 350px;">
                                        <div class="d-flex flex-column h-100">
                                            <MudText Typo="Typo.h6" Class="mb-2 d-flex align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.VideoFile" Class="me-2" Color="Color.Tertiary" />
                                                فيديو الخدمة
                                            </MudText>
                                            <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3">
                                                فيديو توضيحي أو تعريفي للخدمة
                                            </MudText>

                                            <div class="flex-grow-1 d-flex flex-column justify-center">
                                                @if (!string.IsNullOrEmpty(Service.VideoUrl))
                                                {
                                                    <div class="text-center mb-3">
                                                        <MudIcon Icon="@Icons.Material.Filled.PlayCircle" 
                                                                Size="Size.Large" 
                                                                Color="Color.Success" 
                                                                Class="mb-2" />
                                                        <MudText Typo="Typo.body2" Color="Color.Success">
                                                            يوجد فيديو مرفوع
                                                        </MudText>
                                                    </div>
                                                    <div class="d-flex gap-2 mb-3">
                                                        <MudButton Variant="Variant.Outlined" 
                                                                  Color="Color.Info" 
                                                                  Size="Size.Small"
                                                                  StartIcon="@Icons.Material.Filled.PlayArrow"
                                                                  Href="@Service.VideoUrl"
                                                                  Target="_blank"
                                                                  FullWidth="true">
                                                            تشغيل
                                                        </MudButton>
                                                        <MudButton Variant="Variant.Outlined" 
                                                                  Color="Color.Error" 
                                                                  Size="Size.Small"
                                                                  StartIcon="@Icons.Material.Filled.Delete"
                                                                  OnClick="@(() => RemoveVideo())"
                                                                  FullWidth="true">
                                                            حذف
                                                        </MudButton>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <div class="text-center mb-3">
                                                        <MudIcon Icon="@Icons.Material.Filled.VideoFile" 
                                                                Size="Size.Large" 
                                                                Color="Color.Secondary" 
                                                                Class="mb-2" />
                                                        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3">
                                                            لا يوجد فيديو مرفوع
                                                        </MudText>
                                                    </div>
                                                }
                                            </div>

                                            <MudFileUpload T="IBrowserFile" 
                                                          Accept=".mp4,.avi,.mov,.wmv,.webm" 
                                                          FilesChanged="@UploadVideo"
                                                          MaximumFileCount="1">
                                                <ActivatorContent>
                                                    <MudButton Variant="Variant.Filled"
                                                              Color="Color.Tertiary"
                                                              StartIcon="@Icons.Material.Filled.Upload"
                                                              FullWidth="true"
                                                              Size="Size.Medium">
                                                        @(!string.IsNullOrEmpty(Service.VideoUrl) ? "تغيير الفيديو" : "رفع فيديو")
                                                    </MudButton>
                                                </ActivatorContent>
                                            </MudFileUpload>
                                            
                                            <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mt-2 text-center">
                                                الحد الأقصى: 50 ميجابايت | MP4, AVI, MOV, WMV, WebM
                                            </MudText>
                                        </div>
                                    </MudPaper>
                                </MudItem>

                                <!-- معاينة صور الدوار -->
                                @if (!string.IsNullOrEmpty(Service.ImageCarousel))
                                {
                                    <MudItem xs="12">
                                        <MudExpansionPanels Elevation="1" Class="mt-4">
                                            <MudExpansionPanel Text="معاينة وإدارة صور الدوار" 
                                                              Icon="@Icons.Material.Filled.PhotoLibrary"
                                                              MaxHeight="400">
                                                <MudGrid Spacing="2" Class="mt-2">
                                                    @foreach (var imageUrl in Service.ImageCarousel.Split(';', StringSplitOptions.RemoveEmptyEntries))
                                                    {
                                                        <MudItem xs="6" sm="4" md="3" lg="2">
                                                            <MudPaper Class="pa-2" Elevation="2">
                                                                <MudImage Src="@imageUrl.Trim()" 
                                                                         Alt="صورة دوار"
                                                                         Class="rounded"
                                                                         Style="width: 100%; height: 100px; object-fit: cover;" />
                                                                <div class="d-flex gap-1 mt-2">
                                                                    <MudButton Variant="Variant.Text"
                                                                              Color="Color.Info"
                                                                              Size="Size.Small"
                                                                              StartIcon="@Icons.Material.Filled.Visibility"
                                                                              Href="@imageUrl.Trim()"
                                                                              Target="_blank"
                                                                              FullWidth="true">
                                                                        عرض
                                                                    </MudButton>
                                                                    <MudButton Variant="Variant.Text"
                                                                              Color="Color.Error"
                                                                              Size="Size.Small"
                                                                              StartIcon="@Icons.Material.Filled.Delete"
                                                                              OnClick="@(() => RemoveCarouselImage(imageUrl.Trim()))"
                                                                              FullWidth="true">
                                                                        حذف
                                                                    </MudButton>
                                                                </div>
                                                            </MudPaper>
                                                        </MudItem>
                                                    }
                                                </MudGrid>
                                            </MudExpansionPanel>
                                        </MudExpansionPanels>
                                    </MudItem>
                                }

                                <!-- الروابط اليدوية -->
                                <MudItem xs="12">
                                    <MudExpansionPanels Elevation="1" Class="mt-4">
                                        <MudExpansionPanel Text="تحرير الروابط يدوياً (متقدم)" 
                                                          Icon="@Icons.Material.Filled.Link"
                                                          MaxHeight="400">
                                            <MudGrid Spacing="3" Class="mt-2">
                                                <MudItem xs="12">
                                                    <MudAlert Severity="Severity.Info" Class="mb-3">
                                                        يمكنك تحرير روابط الوسائط مباشرة. كن حذراً عند التعديل لتجنب كسر الروابط.
                                                    </MudAlert>
                                                </MudItem>
                                                
                                                <MudItem xs="12" md="6">
                                                    <MudTextField @bind-Value="Service.MainImageUrl"
                                                                  Label="رابط الصورة الرئيسية"
                                                                  Variant="Variant.Outlined"
                                                                  Margin="Margin.Dense"
                                                                  HelperText="رابط مباشر للصورة الرئيسية"
                                                                  Adornment="Adornment.End"
                                                                  AdornmentIcon="@Icons.Material.Filled.Link"
                                                                  AdornmentColor="Color.Primary" />
                                                </MudItem>

                                                <MudItem xs="12" md="6">
                                                    <MudTextField @bind-Value="Service.VideoUrl"
                                                                  Label="رابط الفيديو"
                                                                  Variant="Variant.Outlined"
                                                                  Margin="Margin.Dense"
                                                                  HelperText="رابط مباشر للفيديو"
                                                                  Adornment="Adornment.End"
                                                                  AdornmentIcon="@Icons.Material.Filled.Link"
                                                                  AdornmentColor="Color.Tertiary" />
                                                </MudItem>

                                                <MudItem xs="12">
                                                    <MudTextField @bind-Value="Service.ImageCarousel"
                                                                  Label="روابط صور الدوار"
                                                                  Variant="Variant.Outlined"
                                                                  Lines="3"
                                                                  HelperText="روابط الصور مفصولة بفواصل منقوطة (;)"
                                                                  Placeholder="https://example.com/image1.jpg;https://example.com/image2.jpg"
                                                                  Adornment="Adornment.End"
                                                                  AdornmentIcon="@Icons.Material.Filled.Link"
                                                                  AdornmentColor="Color.Secondary" />
                                                </MudItem>
                                            </MudGrid>
                                        </MudExpansionPanel>
                                    </MudExpansionPanels>
                                </MudItem>
                            </MudGrid>
                        </MudTabPanel>
                    </MudTabs>
                </MudForm>
            </MudContainer>
        }
        else
        {
            <div class="d-flex align-center justify-center" style="min-height: 300px;">
                <div class="text-center">
                    <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
                    <MudText Class="mt-3" Typo="Typo.h6">جاري تحميل بيانات الخدمة...</MudText>
                </div>
            </div>
        }
    </DialogContent>

    <DialogActions>
        <div class="d-flex justify-space-between align-center w-100">
            <!-- معلومات التقدم -->
            <div class="d-none d-sm-flex align-center gap-2">
                @if (Service != null)
                {
                    <MudChip T="string" Size="Size.Small" Color="@GetOverallProgressColor()" Variant="Variant.Text">
                        @GetOverallProgressText()
                    </MudChip>
                }
            </div>
            
            <!-- الأزرار -->
            <div class="d-flex gap-2">
                <MudButton OnClick="Cancel" 
                          Color="Color.Default"
                          Variant="Variant.Outlined"
                          StartIcon="@Icons.Material.Filled.Cancel">
                    إلغاء
                </MudButton>
                <MudButton OnClick="Submit" 
                          Color="Color.Primary" 
                          Variant="Variant.Filled" 
                          StartIcon="@Icons.Material.Filled.Save"
                          Loading="_isSaving"
                          Disabled="_isSaving || Service == null">
                    @(_isSaving ? "جاري الحفظ..." : "حفظ التغييرات")
                </MudButton>
            </div>
        </div>
    </DialogActions>
</MudDialog>

@code {
    private MudForm _form = new();
    private int _activeTabIndex = 0;
    private bool _isSaving = false;

    [CascadingParameter] public required IMudDialogInstance MudDialog { get; set; }

    [Parameter]
    public ServiceDto? Service { get; set; }

    #region Badge Methods
    private string GetServiceDetailsBadge()
    {
        if (Service == null) return "0/4";
        
        var completedFields = 0;
        var totalFields = 4;

        if (!string.IsNullOrWhiteSpace(Service.Title)) completedFields++;
        if (!string.IsNullOrWhiteSpace(Service.PrimaryDescription)) completedFields++;
        if (!string.IsNullOrWhiteSpace(Service.DetailedDescription)) completedFields++;
        if (!string.IsNullOrWhiteSpace(Service.Features)) completedFields++;

        return $"{completedFields}/{totalFields}";
    }

    private Color GetServiceDetailsBadgeColor()
    {
        var badge = GetServiceDetailsBadge();
        return badge switch
        {
            "4/4" => Color.Success,
            "3/4" or "2/4" => Color.Warning,
            _ => Color.Error
        };
    }

    private string GetMediaBadge()
    {
        if (Service == null) return "0";
        
        var mediaCount = 0;
        if (!string.IsNullOrWhiteSpace(Service.MainImageUrl)) mediaCount++;
        if (!string.IsNullOrWhiteSpace(Service.ImageCarousel)) mediaCount++;
        if (!string.IsNullOrWhiteSpace(Service.VideoUrl)) mediaCount++;

        return mediaCount.ToString();
    }

    private Color GetMediaBadgeColor()
    {
        var count = int.Parse(GetMediaBadge());
        return count switch
        {
            >= 2 => Color.Success,
            1 => Color.Warning,
            _ => Color.Error
        };
    }

    private string GetOverallProgressText()
    {
        if (Service == null) return "جاري التحميل";
        
        var hasRequiredFields = !string.IsNullOrWhiteSpace(Service.Title) && 
                               !string.IsNullOrWhiteSpace(Service.PrimaryDescription);
        var hasMedia = !string.IsNullOrWhiteSpace(Service.MainImageUrl);

        if (hasRequiredFields && hasMedia) return "جاهز للحفظ";
        if (hasRequiredFields) return "يحتاج صورة رئيسية";
        return "يحتاج معلومات أساسية";
    }

    private Color GetOverallProgressColor()
    {
        var text = GetOverallProgressText();
        return text switch
        {
            "جاهز للحفظ" => Color.Success,
            "يحتاج صورة رئيسية" => Color.Warning,
            _ => Color.Error
        };
    }
    #endregion

    #region Preview Methods
    private void PreviewCarouselImages()
    {
        // This could open a dialog to preview carousel images
        Snackbar.Add("معاينة الصور - يمكنك استخدام قسم المعاينة أدناه", Severity.Info);
    }

    private void ClearCarouselImages()
    {
        if (Service != null)
        {
            Service.ImageCarousel = string.Empty;
            Snackbar.Add("تم حذف جميع صور الدوار", Severity.Success);
            StateHasChanged();
        }
    }
    #endregion

    #region Dialog Actions
    private async Task Submit()
    {
        if (Service == null) return;
        
        _isSaving = true;
        StateHasChanged();

        try
        {
            // Basic validation
            if (string.IsNullOrWhiteSpace(Service.Title))
            {
                Snackbar.Add("عنوان الخدمة مطلوب", Severity.Error);
                _activeTabIndex = 0;
                return;
            }

            if (string.IsNullOrWhiteSpace(Service.PrimaryDescription))
            {
                Snackbar.Add("الوصف الأساسي مطلوب", Severity.Error);
                _activeTabIndex = 0;
                return;
            }

            MudDialog.Close(DialogResult.Ok(Service));
        }
        finally
        {
            _isSaving = false;
            StateHasChanged();
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
    #endregion

    #region File Upload Methods
    private async Task UploadMainImage(IBrowserFile file)
    {
        if (file == null || Service == null) return;

        try
        {
            // Validate file
            if (!await ValidateImageFile(file)) return;

            // Store old image URL for potential deletion
            string oldImageUrl = Service.MainImageUrl ?? string.Empty;

            // Generate unique filename
            var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.Name).ToLowerInvariant()}";

            // Upload file
            var uploadedUrl = await UploadFile(file, "api/upload/service/image", fileName, 5 * 1024 * 1024, oldImageUrl);
            
            if (!string.IsNullOrEmpty(uploadedUrl))
            {
                Service.MainImageUrl = uploadedUrl;
                Snackbar.Add("تم رفع الصورة الرئيسية بنجاح", Severity.Success);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في رفع الصورة الرئيسية: {ex.Message}", Severity.Error);
        }
    }

    private async Task AddCarouselImages(IReadOnlyList<IBrowserFile> files)
    {
        if (files == null || files.Count == 0 || Service == null) return;

        try
        {
            var uploadedUrls = new List<string>();
            var successCount = 0;

            foreach (var file in files)
            {
                if (!await ValidateImageFile(file)) continue;

                var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.Name).ToLowerInvariant()}";
                var uploadedUrl = await UploadFile(file, "api/upload/service/slider", fileName, 5 * 1024 * 1024);
                
                if (!string.IsNullOrEmpty(uploadedUrl))
                {
                    uploadedUrls.Add(uploadedUrl);
                    successCount++;
                }
            }

            if (uploadedUrls.Any())
            {
                // Add to existing images
                var existingImages = string.IsNullOrEmpty(Service.ImageCarousel)
                    ? new List<string>()
                    : Service.ImageCarousel.Split(';', StringSplitOptions.RemoveEmptyEntries).ToList();

                existingImages.AddRange(uploadedUrls);
                Service.ImageCarousel = string.Join(";", existingImages);

                Snackbar.Add($"تم إضافة {successCount} صورة جديدة", Severity.Success);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في إضافة صور الدوار: {ex.Message}", Severity.Error);
        }
    }

    private async Task ReplaceCarouselImages(IReadOnlyList<IBrowserFile> files)
    {
        if (files == null || files.Count == 0 || Service == null) return;

        try
        {
            var uploadedUrls = new List<string>();
            var successCount = 0;

            foreach (var file in files)
            {
                if (!await ValidateImageFile(file)) continue;

                var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.Name).ToLowerInvariant()}";
                var uploadedUrl = await UploadFile(file, "api/upload/service/slider", fileName, 5 * 1024 * 1024);
                
                if (!string.IsNullOrEmpty(uploadedUrl))
                {
                    uploadedUrls.Add(uploadedUrl);
                    successCount++;
                }
            }

            if (uploadedUrls.Any())
            {
                // Replace all carousel images
                Service.ImageCarousel = string.Join(";", uploadedUrls);

                Snackbar.Add($"تم استبدال صور الدوار بـ {successCount} صورة جديدة", Severity.Success);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في استبدال صور الدوار: {ex.Message}", Severity.Error);
        }
    }

    private async Task UploadVideo(IBrowserFile file)
    {
        if (file == null || Service == null) return;

        try
        {
            // Validate video file
            if (!await ValidateVideoFile(file)) return;

            // Store old video URL for potential deletion
            string oldVideoUrl = Service.VideoUrl ?? string.Empty;

            var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.Name).ToLowerInvariant()}";
            var uploadedUrl = await UploadFile(file, "api/upload/service/video", fileName, 50 * 1024 * 1024, oldVideoUrl);
            
            if (!string.IsNullOrEmpty(uploadedUrl))
            {
                Service.VideoUrl = uploadedUrl;
                Snackbar.Add("تم رفع الفيديو بنجاح", Severity.Success);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في رفع الفيديو: {ex.Message}", Severity.Error);
        }
    }

    private async Task<bool> ValidateImageFile(IBrowserFile file)
    {
        // Check file size (max 5MB)
        if (file.Size > 5 * 1024 * 1024)
        {
            Snackbar.Add($"حجم الملف {file.Name} يتجاوز الحد الأقصى (5 ميجابايت)", Severity.Error);
            return false;
        }

        // Check file type
        var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".webp" };
        var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();

        if (!allowedExtensions.Contains(fileExtension))
        {
            Snackbar.Add($"نوع الملف {file.Name} غير مدعوم. الأنواع المدعومة: JPG, JPEG, PNG, WebP", Severity.Error);
            return false;
        }

        return true;
    }

    private async Task<bool> ValidateVideoFile(IBrowserFile file)
    {
        // Check file size (max 50MB)
        if (file.Size > 50 * 1024 * 1024)
        {
            Snackbar.Add($"حجم الملف {file.Name} يتجاوز الحد الأقصى (50 ميجابايت)", Severity.Error);
            return false;
        }

        // Check file type
        var allowedExtensions = new[] { ".mp4", ".avi", ".mov", ".wmv", ".webm" };
        var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();

        if (!allowedExtensions.Contains(fileExtension))
        {
            Snackbar.Add($"نوع الملف {file.Name} غير مدعوم. الأنواع المدعومة: MP4, AVI, MOV, WMV, WebM", Severity.Error);
            return false;
        }

        return true;
    }

    private async Task<string> UploadFile(IBrowserFile file, string endpoint, string fileName, long maxSize, string? oldFileUrl = null)
    {
        try
        {
            using var content = new MultipartFormDataContent();
            using var fileStream = file.OpenReadStream(maxAllowedSize: maxSize);
            using var streamContent = new StreamContent(fileStream);

            content.Add(streamContent, "file", fileName);

            // Add old file URL for deletion if provided
            if (!string.IsNullOrEmpty(oldFileUrl))
            {
                content.Add(new StringContent(oldFileUrl), "oldFileUrl");
            }

            var response = await Http.PostAsync(endpoint, content);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                Snackbar.Add($"خطأ في رفع الملف: {error}", Severity.Error);
                return string.Empty;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في رفع الملف: {ex.Message}", Severity.Error);
            return string.Empty;
        }
    }
    #endregion

    #region Media Management
    private void RemoveMainImage()
    {
        if (Service != null)
        {
            Service.MainImageUrl = string.Empty;
            Snackbar.Add("تم حذف الصورة الرئيسية", Severity.Success);
            StateHasChanged();
        }
    }

    private void RemoveVideo()
    {
        if (Service != null)
        {
            Service.VideoUrl = string.Empty;
            Snackbar.Add("تم حذف الفيديو", Severity.Success);
            StateHasChanged();
        }
    }

    private void RemoveCarouselImage(string imageUrl)
    {
        if (Service == null || string.IsNullOrEmpty(Service.ImageCarousel))
            return;

        try
        {
            // Get current carousel images
            var currentImages = Service.ImageCarousel.Split(';', StringSplitOptions.RemoveEmptyEntries).ToList();

            // Remove the specified image
            currentImages.RemoveAll(img => img.Trim() == imageUrl);

            // Update the carousel string
            Service.ImageCarousel = string.Join(";", currentImages);

            Snackbar.Add("تم حذف الصورة من الدوار", Severity.Success);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف الصورة: {ex.Message}", Severity.Error);
        }
    }
    #endregion
}
