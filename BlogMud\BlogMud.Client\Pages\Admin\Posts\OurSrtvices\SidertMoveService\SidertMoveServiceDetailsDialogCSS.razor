<style>
    /* SidertMoveServiceDetailsDialog - Component Scoped Styles */

    /* Dialog Container */
    .service-details-dialog {
        direction: rtl;
        font-family: '<PERSON><PERSON><PERSON>', '<PERSON>o', 'Helvetica', 'Arial', sans-serif;
    }

        .service-details-dialog .mud-dialog-content {
            padding: 0;
            max-height: 80vh;
            overflow-y: auto;
        }

    /* Dialog Title */
    .dialog-title-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem 0;
    }

    .dialog-title-icon {
        color: #953735;
        font-size: 1.5rem;
    }

    .dialog-title-text {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
        margin: 0;
    }

    /* Dialog Content */
    .dialog-content-container {
        padding: 1.5rem;
        background: var(--mud-palette-background-grey);
    }

    /* Tab Styling */
    .service-details-dialog .mud-tabs {
        margin-bottom: 1.5rem;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

        .service-details-dialog .mud-tabs .mud-tab {
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            border-radius: 8px 8px 0 0;
        }

            .service-details-dialog .mud-tabs .mud-tab:hover {
                background: var(--mud-palette-action-hover);
            }

            .service-details-dialog .mud-tabs .mud-tab.mud-tab-active {
                background: #953735;
                color: white;
                font-weight: 600;
            }

        .service-details-dialog .mud-tabs .mud-tab-panel {
            background: white;
            border-radius: 0 0 12px 12px;
            min-height: 300px;
        }

    /* Form Section */
    .form-section {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border: 1px solid var(--mud-palette-divider);
        transition: all 0.3s ease;
    }

        .form-section:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

    /* Info Header */
    .info-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .info-icon {
        color: #953735;
        font-size: 1.25rem;
    }

    .info-title {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
        margin: 0;
    }

    .info-value {
        color: var(--mud-palette-text-primary);
        font-weight: 500;
        margin: 0;
        line-height: 1.6;
    }

    .description-text {
        line-height: 1.8;
        white-space: pre-wrap;
    }

    .notes-text {
        line-height: 1.8;
        white-space: pre-wrap;
        font-style: italic;
    }

    .link-value {
        color: #953735;
        text-decoration: none;
        font-weight: 500;
        word-break: break-all;
    }

        .link-value:hover {
            text-decoration: underline;
        }

    .info-chip {
        font-weight: 600;
    }

    /* Status Section */
    .status-section {
        border: 2px solid var(--mud-palette-info);
        background: var(--mud-palette-info-lighten-5);
    }

    .status-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .status-icon {
        color: var(--mud-palette-info);
        font-size: 1.25rem;
    }

    .status-title {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
        margin: 0;
    }

    .status-chip {
        font-weight: 600;
    }

    /* Upload Section for Image Display */
    .upload-section.image-display {
        background: white;
        border: 2px solid var(--mud-palette-divider);
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .upload-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .upload-icon {
        color: #953735;
        font-size: 1.25rem;
    }

    .upload-title {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
        margin: 0;
    }

    .upload-content {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    /* Uploaded Images Section */
    .uploaded-images-section {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid var(--mud-palette-divider);
    }

    .image-card {
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid var(--mud-palette-divider);
    }

        .image-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

    .image-preview {
        border-radius: 8px 8px 0 0;
        object-fit: cover;
    }

    .image-info {
        padding: 0.5rem;
        text-align: center;
        background: var(--mud-palette-background-grey);
    }

    .image-label {
        color: var(--mud-palette-text-secondary);
        font-weight: 500;
    }

    /* No Images Section */
    .no-images-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
    }

    /* Preview Section */
    .preview-section {
        border: 2px solid #953735;
        background: rgba(149, 55, 53, 0.05);
    }

    .carousel-container {
        margin-top: 1rem;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .enhanced-carousel {
        border-radius: 12px;
        overflow: hidden;
    }

    .carousel-slide {
        display: flex;
        flex-direction: column;
        height: 100%;
        background: white;
    }

    .slide-image-wrapper {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
        padding: 1rem;
    }

    .slide-image {
        max-width: 100%;
        max-height: 280px;
        object-fit: contain;
        border-radius: 8px;
    }

    .slide-content {
        padding: 1.5rem;
        text-align: center;
        background: white;
        border-top: 1px solid var(--mud-palette-divider);
    }

    .slide-title {
        color: var(--mud-palette-text-primary);
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .slide-description {
        margin-bottom: 0.75rem;
        line-height: 1.6;
    }

    .slide-duration {
        margin-bottom: 1rem;
        font-style: italic;
    }

    .slide-button {
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

        .slide-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

    /* No Preview Section */
    .no-preview-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 4rem 1rem;
        text-align: center;
        background: #f5f5f5;
        border-radius: 12px;
        margin-top: 1rem;
    }

    /* Dialog Actions */
    .dialog-actions {
        padding: 1rem 1.5rem;
        background: var(--mud-palette-background-grey);
        border-top: 1px solid var(--mud-palette-divider);
    }

    .action-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        align-items: center;
    }

    .submit-button {
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        min-width: 140px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

        .submit-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

    /* Responsive Design */

    /* Mobile Phones (up to 599px) */
    @@media (max-width: 599px) {
        /* Full Screen Dialog for Mobile */
        .service-details-dialog {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            max-width: 100vw !important;
            max-height: 100vh !important;
            margin: 0 !important;
            border-radius: 0 !important;
        }

        .service-details-dialog .mud-dialog-container {
            width: 100vw !important;
            height: 100vh !important;
            max-width: 100vw !important;
            max-height: 100vh !important;
            margin: 0 !important;
            border-radius: 0 !important;
        }

        .service-details-dialog .mud-dialog {
            width: 100vw !important;
            height: 100vh !important;
            max-width: 100vw !important;
            max-height: 100vh !important;
            margin: 0 !important;
            border-radius: 0 !important;
            display: flex !important;
            flex-direction: column !important;
        }

        .service-details-dialog .mud-dialog-content {
            flex: 1 !important;
            max-height: none !important;
            height: auto !important;
            overflow-y: auto !important;
            padding: 0 !important;
        }

        .dialog-content-container {
            padding: 1rem;
            height: 100%;
            overflow-y: auto;
        }

        .form-section {
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .upload-section {
            padding: 1rem;
        }

        .image-preview {
            height: 100px;
        }

        .action-buttons {
            flex-direction: column-reverse;
            gap: 0.75rem;
        }

        .submit-button {
            width: 100%;
            min-width: unset;
        }

        /* Mobile Tab Adjustments */
        .service-details-dialog .mud-tabs .mud-tab {
            font-size: 0.8rem;
            padding: 0.4rem 0.75rem;
            height: 38px;
            min-height: 38px;
        }

        .service-details-dialog .mud-tabs .mud-tab .mud-icon {
            font-size: 0.9rem;
        }

        /* Dialog Actions Fixed at Bottom */
        .service-details-dialog .mud-dialog-actions {
            position: sticky !important;
            bottom: 0 !important;
            background: var(--mud-palette-background-grey) !important;
            border-top: 1px solid var(--mud-palette-divider) !important;
            z-index: 1000 !important;
        }

        /* Enhanced mobile scrolling */
        .service-details-dialog .dialog-content-container {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }
    }

    /* Tablets (600px to 959px) */
    @@media (min-width: 600px) and (max-width: 959px) {
        .dialog-content-container {
            padding: 1.25rem;
        }

        .form-section {
            padding: 1.25rem;
        }

        .image-preview {
            height: 110px;
        }

        /* Tablet Tab Adjustments */
        .service-details-dialog .mud-tabs .mud-tab {
            font-size: 0.875rem;
            padding: 0.45rem 0.875rem;
            height: 40px;
            min-height: 40px;
        }
    }

    /* Desktop (960px and up) */
    @@AboutUsDtomedia (min-width: 960px) {
        .service-details-dialog .mud-dialog-content {
            max-height: 85vh;
        }

        .dialog-content-container {
            padding: 2rem;
        }

        .form-section {
            padding: 2rem;
        }

        .upload-section {
            padding: 1.5rem;
        }
    }

    /* Touch Target Optimization for Mobile */
    .submit-button {
        min-height: 44px;
        min-width: 44px;
    }

    /* High Contrast Mode Support */
    @@media (prefers-contrast: high) {
        .form-section, .upload-section {
            border-width: 3px;
            border-style: solid;
        }

        .image-preview {
            border: 2px solid var(--mud-palette-text-primary);
        }
    }

    /* Reduced Motion Support */
    @@media (prefers-reduced-motion: reduce) {
        .form-section, .image-preview, .submit-button {
            transition: none;
        }

        .submit-button:hover:not(:disabled) {
            transform: none;
        }
    }
</style>
