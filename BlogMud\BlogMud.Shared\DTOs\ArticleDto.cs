using BlogMud.Shared.Models;
using System;
using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.DTOs
{
    /// <summary>
    /// كائن نقل البيانات للمقالات
    /// يستخدم لنقل بيانات المقالات بين واجهة المستخدم والخادم
    /// </summary>
    public class ArticleDto
    {
        public int Id { get; set; }
        /// <summary>
        /// عنوان
        /// </summary>
        public string? Title { get; set; }
        /// <summary>
        /// مقدمة
        /// </summary>
        public string? Introduction { get; set; }
        /// <summary>
        /// محتوى
        /// </summary>
        public string? Content { get; set; }
        public string? ImageUrl { get; set; }
        public string? ImageCarousel { get; set; }
        /// <summary>
        /// رابط الفيديو
        /// </summary>
        public string? VideoUrl { get; set; }

        /// <summary>
        /// معرف العميل
        /// </summary>
        public int ClientId { get; set; }

        /// <summary>
        /// اسم العميل
        /// </summary>
        public string? ClientName { get; set; }

        /// <summary>
        ///ربط بين جدول عميل والمقال
        /// </summary>
        public Client? Client { get; set; }

        /// <summary>
        /// معرف الفئة
        /// </summary>
        public int CategoryId { get; set; }

        /// <summary>
        /// اسم الفئة
        /// </summary>
        public string? CategoryName { get; set; }

        /// <summary>
        ///ربط بين جدول فئات والمقال
        /// </summary>
        public Category? Category { get; set; }

        public DateTime PublishDate { get; set; } = DateTime.Now;
        public bool IsPublished { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? LastModifiedAt { get; set; }
    }
}
