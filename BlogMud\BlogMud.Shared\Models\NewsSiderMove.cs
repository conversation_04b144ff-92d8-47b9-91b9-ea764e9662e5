using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    /// <summary>
    /// نموذج الشرائح المتحركة للأخبار لإدارة عروض الصور المتعددة
    /// نظام منفصل عن إدارة الخدمات والشرائح الأخرى - يدعم فقط الصور المتعددة للعرض
    /// </summary>
    public class NewsSiderMove
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// عنوان الشريحة أو العرض
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// وصف الشريحة أو العرض
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// روابط الصور المتعددة مفصولة بفاصلة منقوطة (;)
        /// مسار الحفظ: wwwroot/NewsSiderMoveImg/
        /// </summary>
        [Required]
        [StringLength(2000)]
        public string ImageUrls { get; set; } = string.Empty;

        /// <summary>
        /// مدة العرض بالثواني لكل صورة في الشريحة
        /// </summary>
        [Range(1, 60)]
        public int Duration { get; set; } = 5;

        /// <summary>
        /// ترتيب العرض (للتحكم في ترتيب الشرائح)
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// حالة النشاط - هل الشريحة نشطة أم لا
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }

        /// <summary>
        /// رابط اختياري للتوجيه عند النقر على الشريحة
        /// </summary>
        [StringLength(500)]
        public string LinkUrl { get; set; } = string.Empty;

        /// <summary>
        /// نص الرابط الاختياري
        /// </summary>
        [StringLength(100)]
        public string LinkText { get; set; } = string.Empty;

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// خاصية مساعدة لتحويل روابط الصور إلى قائمة
        /// </summary>
        public List<string> Images
        {
            get
            {
                if (string.IsNullOrEmpty(ImageUrls))
                    return new List<string>();

                return ImageUrls.Split(';', StringSplitOptions.RemoveEmptyEntries)
                               .Select(url => url.Trim())
                               .Where(url => !string.IsNullOrEmpty(url))
                               .ToList();
            }
        }
    }
}
