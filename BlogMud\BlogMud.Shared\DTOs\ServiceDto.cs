using System;

namespace BlogMud.Shared.DTOs
{
    public class ServiceDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string PrimaryDescription { get; set; }
        public string? DetailedDescription { get; set; }
        public string MainImageUrl { get; set; }
        public string? ImageCarousel { get; set; }
        public string? VideoUrl { get; set; }
        public string? Features { get; set; }
        public int DisplayOrder { get; set; } = 0;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? LastModifiedAt { get; set; }
    }
}
