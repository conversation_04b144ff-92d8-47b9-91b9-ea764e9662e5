using AutoMapper;
using BlogMud.Data.Repositories;
using BlogMud.Services;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlogMud.Controllers
{
    /// <summary>
    /// تحكم في عمليات إدارة محتوى صفحة "من نحن"
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AboutUsController : ControllerBase
    {
        private readonly IAboutUsRepository _aboutUsRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<AboutUsController> _logger;
        private readonly IFileService _fileService;

        public AboutUsController(
            IAboutUsRepository aboutUsRepository,
            IUnitOfWork unitOfWork,
            <PERSON><PERSON>apper mapper,
            ILogger<AboutUsController> logger,
            IFileService fileService)
        {
            _aboutUsRepository = aboutUsRepository;
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _fileService = fileService;
        }

        #region عمليات القراءة

        /// <summary>
        /// استرجاع جميع محتويات صفحة "من نحن"
        /// </summary>
        /// <returns>قائمة بجميع المحتويات</returns>
        /// <response code="200">تم استرجاع المحتويات بنجاح</response>
        /// <response code="500">حدث خطأ أثناء استرجاع المحتويات</response>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<AboutUsDto>>> GetAboutUs()
        {
            try
            {
                _logger.LogInformation("Fetching all AboutUs content");

                // استرجاع جميع المحتويات مرتبة حسب ترتيب العرض مع عناصر الطاقة الإنتاجية
                var aboutUsContent = await _aboutUsRepository.GetAllAsync(
                    orderBy: q => q.OrderBy(a => a.DisplayOrder).ThenBy(a => a.CreatedAt),
                    includeProperties: "ProductionCapacityItems"
                );

                // تحويل كائنات النموذج إلى كائنات نقل البيانات
                var aboutUsDtos = _mapper.Map<IEnumerable<AboutUsDto>>(aboutUsContent);

                _logger.LogInformation("Retrieved {Count} AboutUs content items with total {TotalProductionItems} production capacity items",
                    aboutUsDtos.Count(), aboutUsDtos.Sum(x => x.ProductionCapacityItems.Count));
                return Ok(aboutUsDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching AboutUs content");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// استرجاع محتوى صفحة "من نحن" بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف المحتوى</param>
        /// <returns>محتوى صفحة "من نحن"</returns>
        /// <response code="200">تم استرجاع المحتوى بنجاح</response>
        /// <response code="404">المحتوى غير موجود</response>
        /// <response code="500">حدث خطأ أثناء استرجاع المحتوى</response>
        [HttpGet("{id}")]
        public async Task<ActionResult<AboutUsDto>> GetAboutUs(int id)
        {
            try
            {
                _logger.LogInformation("Fetching AboutUs content with ID: {AboutUsId}", id);

                var aboutUs = await _aboutUsRepository.GetAboutUsWithProductionItemsAsync(id);
                if (aboutUs == null)
                {
                    _logger.LogWarning("AboutUs content not found with ID: {AboutUsId}", id);
                    return NotFound();
                }

                var aboutUsDto = _mapper.Map<AboutUsDto>(aboutUs);
                _logger.LogInformation("Retrieved AboutUs content with ID: {AboutUsId} and {ProductionItemsCount} production capacity items",
                    id, aboutUsDto.ProductionCapacityItems.Count);
                return Ok(aboutUsDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching AboutUs content with ID: {AboutUsId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// استرجاع المحتوى النشط لصفحة "من نحن"
        /// </summary>
        /// <returns>قائمة بالمحتوى النشط</returns>
        /// <response code="200">تم استرجاع المحتوى النشط بنجاح</response>
        /// <response code="500">حدث خطأ أثناء استرجاع المحتوى النشط</response>
        [HttpGet("active")]
        public async Task<ActionResult<IEnumerable<AboutUsDto>>> GetActiveAboutUs()
        {
            try
            {
                _logger.LogInformation("Fetching active AboutUs content");

                var activeAboutUs = await _aboutUsRepository.GetActiveAboutUsAsync();
                var aboutUsDtos = _mapper.Map<IEnumerable<AboutUsDto>>(activeAboutUs);

                _logger.LogInformation("Retrieved {Count} active AboutUs content items", aboutUsDtos.Count());
                return Ok(aboutUsDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching active AboutUs content");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// استرجاع أول محتوى نشط لصفحة "من نحن"
        /// </summary>
        /// <returns>أول محتوى نشط</returns>
        /// <response code="200">تم استرجاع المحتوى بنجاح</response>
        /// <response code="404">لا يوجد محتوى نشط</response>
        /// <response code="500">حدث خطأ أثناء استرجاع المحتوى</response>
        [HttpGet("first-active")]
        public async Task<ActionResult<AboutUsDto>> GetFirstActiveAboutUs()
        {
            try
            {
                _logger.LogInformation("Fetching first active AboutUs content");

                var aboutUs = await _aboutUsRepository.GetFirstActiveAboutUsAsync();
                if (aboutUs == null)
                {
                    _logger.LogWarning("No active AboutUs content found");
                    return NotFound();
                }

                var aboutUsDto = _mapper.Map<AboutUsDto>(aboutUs);
                _logger.LogInformation("Retrieved first active AboutUs content with ID: {AboutUsId}", aboutUs.Id);
                return Ok(aboutUsDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching first active AboutUs content");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #endregion

        #region عمليات الكتابة

        /// <summary>
        /// إنشاء محتوى جديد لصفحة "من نحن"
        /// </summary>
        /// <param name="aboutUsDto">بيانات المحتوى الجديد</param>
        /// <returns>المحتوى الذي تم إنشاؤه</returns>
        /// <response code="201">تم إنشاء المحتوى بنجاح</response>
        /// <response code="400">بيانات غير صحيحة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="500">حدث خطأ أثناء إنشاء المحتوى</response>
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<AboutUsDto>> CreateAboutUs([FromBody] AboutUsDto aboutUsDto)
        {
            try
            {
                _logger.LogInformation("Creating new AboutUs content");

                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Invalid model state for AboutUs creation");
                    return BadRequest(ModelState);
                }

                // تحويل كائن نقل البيانات إلى كائن النموذج
                var aboutUs = _mapper.Map<AboutUs>(aboutUsDto);
                aboutUs.CreatedAt = DateTime.Now;
                aboutUs.LastModifiedAt = null;

                // تعيين ترتيب العرض لعناصر الطاقة الإنتاجية وتعيين AboutUsId
                for (int i = 0; i < aboutUs.ProductionCapacityItems.Count; i++)
                {
                    var item = aboutUs.ProductionCapacityItems.ElementAt(i);
                    item.DisplayOrder = i;
                    item.CreatedAt = DateTime.Now;
                    item.AboutUsId = 0; // سيتم تعيينه تلقائياً بعد حفظ AboutUs
                }

                // إضافة المحتوى إلى قاعدة البيانات
                await _aboutUsRepository.AddAsync(aboutUs);
                await _unitOfWork.SaveAsync();

                // تحويل النتيجة إلى كائن نقل البيانات
                var createdAboutUsDto = _mapper.Map<AboutUsDto>(aboutUs);

                _logger.LogInformation("AboutUs content created successfully with ID: {AboutUsId}", aboutUs.Id);
                return CreatedAtAction(nameof(GetAboutUs), new { id = aboutUs.Id }, createdAboutUsDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating AboutUs content");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث محتوى صفحة "من نحن"
        /// </summary>
        /// <param name="id">معرف المحتوى المراد تحديثه</param>
        /// <param name="aboutUsDto">البيانات المحدثة</param>
        /// <returns>المحتوى المحدث</returns>
        /// <response code="200">تم تحديث المحتوى بنجاح</response>
        /// <response code="400">بيانات غير صحيحة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="404">المحتوى غير موجود</response>
        /// <response code="500">حدث خطأ أثناء تحديث المحتوى</response>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<AboutUsDto>> UpdateAboutUs(int id, [FromBody] AboutUsDto aboutUsDto)
        {
            try
            {
                _logger.LogInformation("Updating AboutUs content with ID: {AboutUsId}", id);

                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Invalid model state for AboutUs update");
                    return BadRequest(ModelState);
                }

                // التحقق من وجود المحتوى مع عناصر الطاقة الإنتاجية
                var existingAboutUs = await _aboutUsRepository.GetAboutUsWithProductionItemsAsync(id);
                if (existingAboutUs == null)
                {
                    _logger.LogWarning("AboutUs content not found with ID: {AboutUsId}", id);
                    return NotFound();
                }

                // حذف الملفات القديمة إذا تم تغييرها
                DeleteOldFilesOnUpdate(existingAboutUs, aboutUsDto);

                // تحديث الخصائص الأساسية
                existingAboutUs.CompanyLogoUrl = aboutUsDto.CompanyLogoUrl;
                existingAboutUs.CompanyDescription = aboutUsDto.CompanyDescription;
                existingAboutUs.CompanyCapabilities = aboutUsDto.CompanyCapabilities;
                existingAboutUs.DisplayOrder = aboutUsDto.DisplayOrder;
                existingAboutUs.IsActive = aboutUsDto.IsActive;
                existingAboutUs.LastModifiedAt = DateTime.Now;

                // تحديث عناصر الطاقة الإنتاجية
                await UpdateProductionCapacityItems(existingAboutUs, aboutUsDto.ProductionCapacityItems);

                // حفظ التغييرات - لا نحتاج لاستدعاء Update لأن الكائن مُتتبع بالفعل من GetAboutUsWithProductionItemsAsync
                await _unitOfWork.SaveAsync();

                // إعادة جلب البيانات المحدثة مع العلاقات للتأكد من الحصول على أحدث البيانات
                var refreshedAboutUs = await _aboutUsRepository.GetAboutUsWithProductionItemsAsync(id);
                var updatedAboutUsDto = _mapper.Map<AboutUsDto>(refreshedAboutUs);

                _logger.LogInformation("AboutUs update completed. Final ProductionCapacityItems count: {Count}",
                    updatedAboutUsDto.ProductionCapacityItems.Count);

                _logger.LogInformation("AboutUs content updated successfully with ID: {AboutUsId}", id);
                return Ok(updatedAboutUsDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating AboutUs content with ID: {AboutUsId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف محتوى صفحة "من نحن" بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف المحتوى المراد حذفه</param>
        /// <returns>استجابة بدون محتوى في حالة نجاح الحذف</returns>
        /// <response code="204">تم حذف المحتوى بنجاح</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="404">المحتوى غير موجود</response>
        /// <response code="500">حدث خطأ أثناء حذف المحتوى</response>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteAboutUs(int id)
        {
            try
            {
                _logger.LogInformation("Deleting AboutUs content with ID: {AboutUsId}", id);

                // التحقق من وجود المحتوى مع تحميل عناصر الطاقة الإنتاجية
                var aboutUs = await _aboutUsRepository.GetAboutUsWithProductionItemsAsync(id);
                if (aboutUs == null)
                {
                    _logger.LogWarning("AboutUs content not found with ID: {AboutUsId}", id);
                    return NotFound();
                }

                // حذف جميع الملفات المرتبطة بالمحتوى قبل حذفه من قاعدة البيانات
                DeleteAboutUsFiles(aboutUs);

                // حذف المحتوى من قاعدة البيانات
                _aboutUsRepository.Remove(aboutUs);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("AboutUs content and associated files deleted successfully with ID: {AboutUsId}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting AboutUs content with ID: {AboutUsId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #endregion

        #region دوال مساعدة لإدارة الملفات

        /// <summary>
        /// حذف جميع الملفات المرتبطة بمحتوى صفحة "من نحن"
        /// </summary>
        /// <param name="aboutUs">المحتوى المراد حذف ملفاته</param>
        private void DeleteAboutUsFiles(AboutUs aboutUs)
        {
            try
            {
                _logger.LogInformation("Deleting files for AboutUs ID: {AboutUsId}", aboutUs.Id);

                // حذف شعار الشركة
                if (!string.IsNullOrEmpty(aboutUs.CompanyLogoUrl))
                {
                    _fileService.DeleteFileIfExists(aboutUs.CompanyLogoUrl, "AboutUsImg");
                    _logger.LogInformation("Deleted company logo: {CompanyLogoUrl}", aboutUs.CompanyLogoUrl);
                }

                // حذف صور عناصر الطاقة الإنتاجية
                if (aboutUs.ProductionCapacityItems?.Any() == true)
                {
                    foreach (var item in aboutUs.ProductionCapacityItems)
                    {
                        if (!string.IsNullOrEmpty(item.ImageUrl))
                        {
                            _fileService.DeleteFileIfExists(item.ImageUrl, "AboutUsImg/ProductionCapacity");
                            _logger.LogInformation("Deleted production capacity item image: {ImageUrl}", item.ImageUrl);
                        }
                    }
                }

                _logger.LogInformation("Successfully deleted all files for AboutUs ID: {AboutUsId}", aboutUs.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting files for AboutUs ID: {AboutUsId}", aboutUs.Id);
            }
        }

        /// <summary>
        /// حذف الملفات القديمة عند تحديث المحتوى
        /// </summary>
        /// <param name="oldAboutUs">المحتوى القديم</param>
        /// <param name="newAboutUs">المحتوى الجديد</param>
        private void DeleteOldFilesOnUpdate(AboutUs oldAboutUs, AboutUsDto newAboutUs)
        {
            try
            {
                _logger.LogInformation("Checking for old files to delete for AboutUs ID: {AboutUsId}", oldAboutUs.Id);

                // حذف شعار الشركة القديم إذا تم تغييره
                if (!string.IsNullOrEmpty(oldAboutUs.CompanyLogoUrl) &&
                    oldAboutUs.CompanyLogoUrl != newAboutUs.CompanyLogoUrl)
                {
                    _fileService.DeleteFileIfExists(oldAboutUs.CompanyLogoUrl, "AboutUsImg");
                    _logger.LogInformation("Deleted old company logo: {OldCompanyLogoUrl}", oldAboutUs.CompanyLogoUrl);
                }

                _logger.LogInformation("Successfully checked and deleted old files for AboutUs ID: {AboutUsId}", oldAboutUs.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting old files for AboutUs ID: {AboutUsId}", oldAboutUs.Id);
            }
        }

        /// <summary>
        /// تحديث عناصر الطاقة الإنتاجية
        /// </summary>
        /// <param name="existingAboutUs">المحتوى الموجود</param>
        /// <param name="newProductionItems">عناصر الطاقة الإنتاجية الجديدة</param>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task UpdateProductionCapacityItems(AboutUs existingAboutUs, List<ProductionCapacityItemDto> newProductionItems)
        {
            try
            {
                _logger.LogInformation("Updating production capacity items for AboutUs ID: {AboutUsId}. Current items count: {CurrentCount}, New items count: {NewCount}",
                    existingAboutUs.Id, existingAboutUs.ProductionCapacityItems.Count, newProductionItems.Count);

                // حذف العناصر القديمة التي لم تعد موجودة
                // نتجاهل العناصر الجديدة التي لها Id = 0
                var itemsToDelete = existingAboutUs.ProductionCapacityItems
                    .Where(existing => !newProductionItems.Any(newItem => newItem.Id > 0 && newItem.Id == existing.Id))
                    .ToList();

                foreach (var itemToDelete in itemsToDelete)
                {
                    _logger.LogInformation("Deleting production capacity item with ID: {ItemId}", itemToDelete.Id);

                    // حذف الصورة المرتبطة بالعنصر
                    if (!string.IsNullOrEmpty(itemToDelete.ImageUrl))
                    {
                        _fileService.DeleteFileIfExists(itemToDelete.ImageUrl, "AboutUsImg/ProductionCapacity");
                        _logger.LogInformation("Deleted image for production capacity item: {ImageUrl}", itemToDelete.ImageUrl);
                    }

                    existingAboutUs.ProductionCapacityItems.Remove(itemToDelete);
                }

                _logger.LogInformation("Deleted {DeletedCount} production capacity items", itemsToDelete.Count);

                // تحديث العناصر الموجودة وإضافة العناصر الجديدة
                for (int i = 0; i < newProductionItems.Count; i++)
                {
                    var newItem = newProductionItems[i];
                    var existingItem = newItem.Id > 0
                        ? existingAboutUs.ProductionCapacityItems.FirstOrDefault(x => x.Id == newItem.Id)
                        : null;

                    if (existingItem != null)
                    {
                        // تحديث العنصر الموجود
                        _logger.LogInformation("Updating existing production capacity item with ID: {ItemId}", existingItem.Id);
                        existingItem.Title = newItem.Title;
                        existingItem.Description = newItem.Description;
                        existingItem.DisplayOrder = i;
                        existingItem.IsActive = newItem.IsActive;
                        existingItem.LastModifiedAt = DateTime.Now;

                        // تحديث الصورة إذا تم تغييرها
                        if (existingItem.ImageUrl != newItem.ImageUrl)
                        {
                            // حذف الصورة القديمة
                            if (!string.IsNullOrEmpty(existingItem.ImageUrl))
                            {
                                _fileService.DeleteFileIfExists(existingItem.ImageUrl, "AboutUsImg/ProductionCapacity");
                            }
                            existingItem.ImageUrl = newItem.ImageUrl;
                        }
                    }
                    else
                    {
                        // إضافة عنصر جديد
                        _logger.LogInformation("Adding new production capacity item: {Title}", newItem.Title);
                        var productionItem = new ProductionCapacityItem
                        {
                            AboutUsId = existingAboutUs.Id,
                            Title = newItem.Title,
                            Description = newItem.Description,
                            ImageUrl = newItem.ImageUrl,
                            DisplayOrder = i,
                            IsActive = newItem.IsActive,
                            CreatedAt = DateTime.Now
                        };

                        existingAboutUs.ProductionCapacityItems.Add(productionItem);
                    }
                }

                _logger.LogInformation("Successfully updated production capacity items for AboutUs ID: {AboutUsId}. Final items count: {FinalCount}",
                    existingAboutUs.Id, existingAboutUs.ProductionCapacityItems.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating production capacity items for AboutUs ID: {AboutUsId}", existingAboutUs.Id);
                throw;
            }
        }

        #endregion
    }
}
