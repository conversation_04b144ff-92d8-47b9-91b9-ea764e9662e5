@using BlogMud.Shared.DTOs

<MudDialog Class="service-details-dialog" MaxWidth="MaxWidth.Large">
    <TitleContent>
        <div class="dialog-title-container">
            <MudIcon Icon="@Icons.Material.Filled.Info"
                     Class="dialog-title-icon" />
            <MudText Typo="Typo.h5" Class="dialog-title-text">
                تفاصيل شريحة الخدمة
            </MudText>
        </div>
    </TitleContent>
    <DialogContent>
        <div class="dialog-content-container">
            @if (SidertMoveService != null)
            {
                <!-- Tabbed Interface -->
                <MudTabs Elevation="1" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">

                    <!-- Tab 1: Basic Information -->
                    <MudTabPanel Text="المعلومات الأساسية" Icon="@Icons.Material.Filled.Info">
                        <MudGrid Spacing="3" Class="form-grid">
                            <!-- العنوان -->
                            <MudItem xs="12">
                                <MudPaper Class="form-section" Elevation="0">
                                    <div class="info-header">
                                        <MudIcon Icon="@Icons.Material.Filled.Title" Class="info-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="info-title">عنوان الشريحة</MudText>
                                    </div>
                                    <MudText Typo="Typo.h6" Class="info-value">@SidertMoveService.Title</MudText>
                                </MudPaper>
                            </MudItem>

                            <!-- ترتيب العرض ومدة العرض -->
                            <MudItem xs="12" sm="6">
                                <MudPaper Class="form-section" Elevation="0">
                                    <div class="info-header">
                                        <MudIcon Icon="@Icons.Material.Filled.Sort" Class="info-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="info-title">ترتيب العرض</MudText>
                                    </div>
                                    <MudChip T="string" Color="Color.Primary" Size="Size.Medium" Class="info-chip">
                                        @SidertMoveService.DisplayOrder
                                    </MudChip>
                                </MudPaper>
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <MudPaper Class="form-section" Elevation="0">
                                    <div class="info-header">
                                        <MudIcon Icon="@Icons.Material.Filled.Timer" Class="info-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="info-title">مدة العرض</MudText>
                                    </div>
                                    <MudText Typo="Typo.body1" Class="info-value">@SidertMoveService.Duration ثانية</MudText>
                                </MudPaper>
                            </MudItem>

                            <!-- عدد الصور والحالة -->
                            <MudItem xs="12" sm="6">
                                <MudPaper Class="form-section" Elevation="0">
                                    <div class="info-header">
                                        <MudIcon Icon="@Icons.Material.Filled.PhotoLibrary" Class="info-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="info-title">عدد الصور</MudText>
                                    </div>
                                    <MudChip T="string" Color="Color.Info" Size="Size.Medium" Class="info-chip">
                                        @SidertMoveService.Images.Count صورة
                                    </MudChip>
                                </MudPaper>
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <MudPaper Class="form-section status-section" Elevation="0">
                                    <div class="status-header">
                                        <MudIcon Icon="@Icons.Material.Filled.Visibility" Class="status-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="status-title">حالة النشر</MudText>
                                    </div>
                                    <MudChip T="string" Color="@(SidertMoveService.IsActive ? Color.Success : Color.Error)"
                                            Icon="@(SidertMoveService.IsActive ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)"
                                            Size="Size.Medium" Class="status-chip">
                                        @(SidertMoveService.IsActive ? "نشط" : "غير نشط")
                                    </MudChip>
                                </MudPaper>
                            </MudItem>

                            <!-- تواريخ -->
                            <MudItem xs="12" sm="6">
                                <MudPaper Class="form-section" Elevation="0">
                                    <div class="info-header">
                                        <MudIcon Icon="@Icons.Material.Filled.DateRange" Class="info-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="info-title">تاريخ الإنشاء</MudText>
                                    </div>
                                    <MudText Typo="Typo.body1" Class="info-value">@SidertMoveService.CreatedAt.ToString("yyyy-MM-dd HH:mm")</MudText>
                                </MudPaper>
                            </MudItem>

                            @if (SidertMoveService.LastModifiedAt.HasValue)
                            {
                                <MudItem xs="12" sm="6">
                                    <MudPaper Class="form-section" Elevation="0">
                                        <div class="info-header">
                                            <MudIcon Icon="@Icons.Material.Filled.Update" Class="info-icon" />
                                            <MudText Typo="Typo.subtitle1" Class="info-title">آخر تعديل</MudText>
                                        </div>
                                        <MudText Typo="Typo.body1" Class="info-value">@SidertMoveService.LastModifiedAt.Value.ToString("yyyy-MM-dd HH:mm")</MudText>
                                    </MudPaper>
                                </MudItem>
                            }
                        </MudGrid>
                    </MudTabPanel>

                    <!-- Tab 2: Description -->
                    @if (!string.IsNullOrEmpty(SidertMoveService.Description))
                    {
                        <MudTabPanel Text="الوصف" Icon="@Icons.Material.Filled.Description">
                            <MudGrid Spacing="3" Class="form-grid">
                                <MudItem xs="12">
                                    <MudPaper Class="form-section" Elevation="0">
                                        <div class="info-header">
                                            <MudIcon Icon="@Icons.Material.Filled.Description" Class="info-icon" />
                                            <MudText Typo="Typo.subtitle1" Class="info-title">وصف الشريحة</MudText>
                                        </div>
                                        <MudText Typo="Typo.body1" Class="info-value description-text">@SidertMoveService.Description</MudText>
                                    </MudPaper>
                                </MudItem>
                            </MudGrid>
                        </MudTabPanel>
                    }

                    <!-- Tab 3: Media -->
                    <MudTabPanel Text="الوسائط" Icon="@Icons.Material.Filled.PhotoLibrary">
                        <MudGrid Spacing="3" Class="media-grid">
                            <MudItem xs="12">
                                <MudPaper Class="upload-section image-display" Elevation="0">
                                    <div class="upload-header">
                                        <MudIcon Icon="@Icons.Material.Filled.PhotoLibrary" Class="upload-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="upload-title">صور الشريحة (@SidertMoveService.Images.Count صورة)</MudText>
                                    </div>

                                    <div class="upload-content">
                                        @if (SidertMoveService.Images.Any())
                                        {
                                            <div class="uploaded-images-section">
                                                <MudGrid Spacing="2">
                                                    @foreach (var (imageUrl, index) in SidertMoveService.Images.Select((url, i) => (url, i)))
                                                    {
                                                        <MudItem xs="6" sm="4" md="3">
                                                            <MudCard Class="image-card" Elevation="2">
                                                                <MudCardMedia Image="@imageUrl" Height="120" Class="image-preview" />
                                                                <MudCardContent Class="image-info">
                                                                    <MudText Typo="Typo.caption" Class="image-label">صورة @(index + 1)</MudText>
                                                                </MudCardContent>
                                                            </MudCard>
                                                        </MudItem>
                                                    }
                                                </MudGrid>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="no-images-section">
                                                <MudIcon Icon="@Icons.Material.Filled.ImageNotSupported" Size="Size.Large" Color="Color.Secondary" />
                                                <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mt-2">لا توجد صور مرفقة</MudText>
                                            </div>
                                        }
                                    </div>
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                    <!-- Tab 4: Settings -->
                    <MudTabPanel Text="الإعدادات" Icon="@Icons.Material.Filled.Settings">
                        <MudGrid Spacing="3" Class="settings-grid">

                            <!-- رابط اختياري -->
                            @if (!string.IsNullOrEmpty(SidertMoveService.LinkUrl))
                            {
                                <MudItem xs="12" md="8">
                                    <MudPaper Class="form-section" Elevation="0">
                                        <div class="info-header">
                                            <MudIcon Icon="@Icons.Material.Filled.Link" Class="info-icon" />
                                            <MudText Typo="Typo.subtitle1" Class="info-title">رابط الانتقال</MudText>
                                        </div>
                                        <MudLink Href="@SidertMoveService.LinkUrl" Target="_blank" Class="link-value">
                                            @SidertMoveService.LinkUrl
                                        </MudLink>
                                    </MudPaper>
                                </MudItem>

                                <!-- نص الرابط -->
                                @if (!string.IsNullOrEmpty(SidertMoveService.LinkText))
                                {
                                    <MudItem xs="12" md="4">
                                        <MudPaper Class="form-section" Elevation="0">
                                            <div class="info-header">
                                                <MudIcon Icon="@Icons.Material.Filled.Label" Class="info-icon" />
                                                <MudText Typo="Typo.subtitle1" Class="info-title">نص الرابط</MudText>
                                            </div>
                                            <MudText Typo="Typo.body1" Class="info-value">@SidertMoveService.LinkText</MudText>
                                        </MudPaper>
                                    </MudItem>
                                }
                            }

                            <!-- ملاحظات -->
                            @if (!string.IsNullOrEmpty(SidertMoveService.Notes))
                            {
                                <MudItem xs="12">
                                    <MudPaper Class="form-section" Elevation="0">
                                        <div class="info-header">
                                            <MudIcon Icon="@Icons.Material.Filled.Notes" Class="info-icon" />
                                            <MudText Typo="Typo.subtitle1" Class="info-title">ملاحظات إضافية</MudText>
                                        </div>
                                        <MudText Typo="Typo.body1" Class="info-value notes-text">@SidertMoveService.Notes</MudText>
                                    </MudPaper>
                                </MudItem>
                            }
                        </MudGrid>
                    </MudTabPanel>

                    <!-- Tab 5: Preview -->
                    <MudTabPanel Text="معاينة الشريحة" Icon="@Icons.Material.Filled.Preview">
                        <MudGrid Spacing="3" Class="preview-grid">
                            <MudItem xs="12">
                                <MudPaper Class="form-section preview-section" Elevation="0">
                                    <div class="info-header">
                                        <MudIcon Icon="@Icons.Material.Filled.Preview" Class="info-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="info-title">معاينة الشريحة المتحركة</MudText>
                                    </div>

                                    @if (SidertMoveService.Images.Any())
                                    {
                                        <div class="carousel-container">
                                            <MudCarousel Class="mud-width-full enhanced-carousel" Style="height:400px;" ShowArrows="true" ShowBullets="true"
                                                         EnableSwipeGesture="true" AutoCycle="true" TData="object">
                                                @foreach (var imageUrl in SidertMoveService.Images)
                                                {
                                                    <MudCarouselItem>
                                                        <div class="carousel-slide">
                                                            <div class="slide-image-wrapper">
                                                                <img src="@imageUrl" class="slide-image" alt="@SidertMoveService.Title" />
                                                            </div>
                                                            <div class="slide-content">
                                                                <MudText Typo="Typo.h5" Class="slide-title">@SidertMoveService.Title</MudText>
                                                                @if (!string.IsNullOrEmpty(SidertMoveService.Description))
                                                                {
                                                                    <MudText Typo="Typo.body1" Color="Color.Secondary" Class="slide-description">
                                                                        @SidertMoveService.Description
                                                                    </MudText>
                                                                }
                                                                <MudText Typo="Typo.caption" Color="Color.Secondary" Class="slide-duration">
                                                                    مدة العرض: @SidertMoveService.Duration ثانية لكل صورة
                                                                </MudText>
                                                                @if (!string.IsNullOrEmpty(SidertMoveService.LinkUrl) && !string.IsNullOrEmpty(SidertMoveService.LinkText))
                                                                {
                                                                    <MudButton Variant="Variant.Filled"
                                                                              Color="Color.Primary"
                                                                              Size="Size.Medium"
                                                                              Class="slide-button"
                                                                              Href="@SidertMoveService.LinkUrl"
                                                                              Target="_blank"
                                                                              StartIcon="@Icons.Material.Filled.OpenInNew">
                                                                        @SidertMoveService.LinkText
                                                                    </MudButton>
                                                                }
                                                            </div>
                                                        </div>
                                                    </MudCarouselItem>
                                                }
                                            </MudCarousel>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="no-preview-section">
                                            <MudIcon Icon="@Icons.Material.Filled.ImageNotSupported" Size="Size.Large" Color="Color.Secondary" />
                                            <MudText Typo="Typo.h5" Class="mt-2">@SidertMoveService.Title</MudText>
                                            <MudText Typo="Typo.body2" Color="Color.Secondary">لا توجد صور للعرض</MudText>
                                        </div>
                                    }
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                </MudTabs>
            }
        </div>
    </DialogContent>
    <DialogActions>
        <div class="dialog-actions">
            <div class="action-buttons">
                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           OnClick="Close"
                           StartIcon="@Icons.Material.Filled.Close"
                           Class="submit-button">
                    إغلاق
                </MudButton>
            </div>
        </div>
    </DialogActions>
</MudDialog>

<SidertMoveServiceDetailsDialogCSS />

@code {
    [CascadingParameter] public required IMudDialogInstance MudDialog { get; set; }

    [Parameter]
    public SidertMoveServicesDto SidertMoveService { get; set; }

    private void Close()
    {
        MudDialog.Close();
    }
}
