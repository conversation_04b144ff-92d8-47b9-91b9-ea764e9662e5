-- <PERSON><PERSON><PERSON> to manually add indexes to the Clients table
-- Run this script against the MudBlog database

-- Add index on CreatedAt for better sorting performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Clients_CreatedAt' AND object_id = OBJECT_ID('Clients'))
BEGIN
    CREATE INDEX IX_Clients_CreatedAt ON Clients(CreatedAt);
    PRINT 'Created index IX_Clients_CreatedAt';
END
ELSE
BEGIN
    PRINT 'Index IX_Clients_CreatedAt already exists';
END

-- Add index on IsActive for better filtering performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Clients_IsActive' AND object_id = OBJECT_ID('Clients'))
BEGIN
    CREATE INDEX IX_Clients_IsActive ON Clients(IsActive);
    PRINT 'Created index IX_Clients_IsActive';
END
ELSE
BEGIN
    PRINT 'Index IX_Clients_IsActive already exists';
END

-- Add index on Name for better search performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Clients_Name' AND object_id = OBJECT_ID('Clients'))
BEGIN
    CREATE INDEX IX_Clients_Name ON Clients(Name);
    PRINT 'Created index IX_Clients_Name';
END
ELSE
BEGIN
    PRINT 'Index IX_Clients_Name already exists';
END

-- Add index on Email for better search performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Clients_Email' AND object_id = OBJECT_ID('Clients'))
BEGIN
    CREATE INDEX IX_Clients_Email ON Clients(Email);
    PRINT 'Created index IX_Clients_Email';
END
ELSE
BEGIN
    PRINT 'Index IX_Clients_Email already exists';
END

-- Update the __EFMigrationsHistory table to record this migration
IF NOT EXISTS (SELECT * FROM __EFMigrationsHistory WHERE MigrationId = '20250520000000_AddClientIndexes')
BEGIN
    INSERT INTO __EFMigrationsHistory (MigrationId, ProductVersion)
    VALUES ('20250520000000_AddClientIndexes', '8.0.16');
    PRINT 'Added migration record to __EFMigrationsHistory';
END
ELSE
BEGIN
    PRINT 'Migration record already exists in __EFMigrationsHistory';
END
