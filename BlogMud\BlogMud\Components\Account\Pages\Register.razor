﻿@page "/Account/Register"

@using System.ComponentModel.DataAnnotations
@using System.Text
@using System.Text.Encodings.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using Microsoft.AspNetCore.Components.Forms
@using BlogMud.Data
@using BlogMud.Services

@inject UserManager<ApplicationUser> UserManager
@inject IUserStore<ApplicationUser> UserStore
@inject SignInManager<ApplicationUser> SignInManager
@inject RoleManager<IdentityRole> RoleManager
@inject IEmailSender<ApplicationUser> EmailSender
@inject INotificationService NotificationService
@inject ILogger<Register> Logger
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager

@rendermode InteractiveServer

<PageTitle>تسجيل حساب جديد</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium" Class="mt-8">
    <MudPaper Elevation="3" Class="pa-8">
        <MudText Typo="Typo.h4" Align="Align.Center" GutterBottom="true" Class="mb-6">
            تسجيل حساب جديد
        </MudText>

        @* <StatusMessage Message="@Message" /> *@

        <EditForm Model="Input" OnValidSubmit="RegisterUser">
            <DataAnnotationsValidator />

            @if (identityErrors is not null)
            {
                <MudAlert Severity="Severity.Error" Class="mb-4">
                    @foreach (var error in identityErrors)
                    {
                        <div>@error.Description</div>
                    }
                </MudAlert>
            }

            <ValidationSummary class="text-danger" role="alert" />

            <MudText Typo="Typo.body1" GutterBottom="true" Class="mb-4">
                أنشئ حساباً جديداً للاستفادة من جميع خدمات الموقع
            </MudText>

            <MudGrid>
                <MudItem xs="12">
                    <MudTextField @bind-Value="Input.FullName"
                                  Label="الاسم الكامل" Placeholder="أدخل اسمك الكامل"
                                  Variant="Variant.Outlined" Margin="Margin.Normal"
                                  Required="true" RequiredError="الاسم الكامل مطلوب"
                                  UserAttributes="@(new() { { "autocomplete", "name" }, { "aria-required", "true" } } )" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="Input.Email"
                                  Label="البريد الإلكتروني" Placeholder="<EMAIL>"
                                  Variant="Variant.Outlined" Margin="Margin.Normal"
                                  Required="true" RequiredError="البريد الإلكتروني مطلوب"
                                  Validation="@(new EmailAddressAttribute() { ErrorMessage = "البريد الإلكتروني غير صحيح" })"
                                  UserAttributes="@(new() { { "autocomplete", "email" }, { "aria-required", "true" } } )" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="Input.Password"
                                  Label="كلمة المرور" InputType="InputType.Password" Placeholder="6 أحرف على الأقل مع رقم وحرف صغير"
                                  Variant="Variant.Outlined" Margin="Margin.Normal"
                                  Required="true" RequiredError="كلمة المرور مطلوبة"
                                  UserAttributes="@(new() { { "autocomplete", "new-password" }, { "aria-required", "true" } } )" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="Input.ConfirmPassword"
                                  Label="تأكيد كلمة المرور" InputType="InputType.Password" Placeholder="أعد إدخال كلمة المرور"
                                  Variant="Variant.Outlined" Margin="Margin.Normal"
                                  Required="true" RequiredError="تأكيد كلمة المرور مطلوب"
                                  UserAttributes="@(new() { { "autocomplete", "new-password" }, { "aria-required", "true" } } )" />
                </MudItem>
                <MudItem xs="12" Class="mt-4">
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" FullWidth="true"
                               Size="Size.Large" Disabled="@_processing"
                               OnClick="@(async () => await HandleSubmit())">
                        @if (_processing)
                        {
                            <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                            <MudText Class="ms-2">جاري التسجيل...</MudText>
                        }
                        else
                        {
                            <MudIcon Icon="Icons.Material.Filled.PersonAdd" Class="mr-2" />
                            <span>تسجيل الحساب</span>
                        }
                    </MudButton>
                </MudItem>
            </MudGrid>
        </EditForm>

        <MudDivider Class="my-6" />

        <MudText Typo="Typo.body2" Align="Align.Center" Class="mb-4">
            لديك حساب بالفعل؟
            <MudLink Href="/Account/Login" Color="Color.Primary">سجل دخولك هنا</MudLink>
        </MudText>

        <ExternalLoginPicker />
    </MudPaper>
</MudContainer>

@code {
    private IEnumerable<IdentityError>? identityErrors;
    private bool _processing = false;

    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    protected override void OnInitialized()
    {
        Input ??= new();
    }

    private string? Message => identityErrors is null ? null : $"Error: {string.Join(", ", identityErrors.Select(error => error.Description))}";

    public async Task TestButtonClick()
    {
        Logger.LogInformation("TEST: Button clicked!");
    }

    public async Task HandleSubmit()
    {
        Logger.LogInformation("HandleSubmit called!");

        // Create a dummy EditContext for validation
        var editContext = new EditContext(Input);
        await RegisterUser(editContext);
    }

    public async Task RegisterUser(EditContext editContext)
    {
        Logger.LogInformation("RegisterUser method called!");

        if (_processing) return;

        _processing = true;
        identityErrors = null;
        StateHasChanged();

        try
        {
            Logger.LogInformation("Starting registration process for email: {Email}", Input.Email);
            Logger.LogInformation("Form data - FullName: {FullName}, Email: {Email}, Password length: {PasswordLength}",
                Input.FullName, Input.Email, Input.Password?.Length ?? 0);

            // Use built-in validation instead of manual validation
            if (!editContext.Validate())
            {
                Logger.LogWarning("Form validation failed");
                identityErrors = new[] { new IdentityError { Description = "يرجى التأكد من صحة جميع البيانات المدخلة" } };
                StateHasChanged();
                return;
            }

            // Additional validation
            if (string.IsNullOrWhiteSpace(Input.FullName))
            {
                identityErrors = new[] { new IdentityError { Description = "الاسم الكامل مطلوب" } };
                StateHasChanged();
                return;
            }

            if (string.IsNullOrWhiteSpace(Input.Email) || !Input.Email.Contains("@"))
            {
                identityErrors = new[] { new IdentityError { Description = "البريد الإلكتروني غير صحيح" } };
                StateHasChanged();
                return;
            }

            if (string.IsNullOrWhiteSpace(Input.Password) || Input.Password.Length < 6)
            {
                identityErrors = new[] { new IdentityError { Description = "كلمة المرور يجب أن تكون 6 أحرف على الأقل وتحتوي على رقم وحرف صغير" } };
                StateHasChanged();
                return;
            }

            if (Input.Password != Input.ConfirmPassword)
            {
                identityErrors = new[] { new IdentityError { Description = "كلمة المرور وتأكيد كلمة المرور غير متطابقتين" } };
                StateHasChanged();
                return;
            }

            var user = CreateUser();

            // Set user properties
            user.FullName = Input.FullName;
            user.CreatedAt = DateTime.Now;
            user.IsActive = true;

            await UserStore.SetUserNameAsync(user, Input.Email, CancellationToken.None);
            var emailStore = GetEmailStore();
            await emailStore.SetEmailAsync(user, Input.Email, CancellationToken.None);

            var result = await UserManager.CreateAsync(user, Input.Password);

            if (!result.Succeeded)
            {
                identityErrors = result.Errors;
                Logger.LogWarning("User creation failed: {Errors}", string.Join(", ", result.Errors.Select(e => e.Description)));
                StateHasChanged();
                return;
            }

            Logger.LogInformation("User created a new account with password.");

            // Assign default role to user
            try
            {
                if (await RoleManager.RoleExistsAsync("User"))
                {
                    await UserManager.AddToRoleAsync(user, "User");
                }
                else
                {
                    Logger.LogWarning("User role does not exist. Creating it now.");
                    await RoleManager.CreateAsync(new IdentityRole("User"));
                    await UserManager.AddToRoleAsync(user, "User");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Failed to assign role to user");
                // Continue with registration even if role assignment fails
            }

            var userId = await UserManager.GetUserIdAsync(user);
            var code = await UserManager.GenerateEmailConfirmationTokenAsync(user);
            code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
            var callbackUrl = NavigationManager.GetUriWithQueryParameters(
                NavigationManager.ToAbsoluteUri("Account/ConfirmEmail").AbsoluteUri,
                new Dictionary<string, object?> { ["userId"] = userId, ["code"] = code, ["returnUrl"] = ReturnUrl });

            try
            {
                await EmailSender.SendConfirmationLinkAsync(user, Input.Email, HtmlEncoder.Default.Encode(callbackUrl));
                Logger.LogInformation("Confirmation email sent to {Email}", Input.Email);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Failed to send confirmation email to {Email}", Input.Email);
                // Continue with registration even if email fails
            }

            if (UserManager.Options.SignIn.RequireConfirmedAccount)
            {
                var confirmationUrl = NavigationManager.GetUriWithQueryParameters(
                    "Account/RegisterConfirmation",
                    new Dictionary<string, object?> { ["email"] = Input.Email, ["returnUrl"] = ReturnUrl });
                NavigationManager.NavigateTo(confirmationUrl);
            }
            else
            {
                // If email confirmation is not required, send welcome notification
                try
                {
                    await NotificationService.SendWelcomeNotificationAsync(userId);
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Failed to send welcome notification");
                }

                await SignInManager.SignInAsync(user, isPersistent: false);
                NavigationManager.NavigateTo(ReturnUrl ?? "/");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Registration failed");
            identityErrors = new[] { new IdentityError { Description = "حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى." } };
        }
        finally
        {
            _processing = false;
            StateHasChanged();
        }
    }

    private ApplicationUser CreateUser()
    {
        try
        {
            return Activator.CreateInstance<ApplicationUser>();
        }
        catch
        {
            throw new InvalidOperationException($"Can't create an instance of '{nameof(ApplicationUser)}'. " +
                $"Ensure that '{nameof(ApplicationUser)}' is not an abstract class and has a parameterless constructor.");
        }
    }

    private IUserEmailStore<ApplicationUser> GetEmailStore()
    {
        if (!UserManager.SupportsUserEmail)
        {
            throw new NotSupportedException("The default UI requires a user store with email support.");
        }
        return (IUserEmailStore<ApplicationUser>)UserStore;
    }

    private sealed class InputModel
    {
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم يجب أن يكون بين {2} و {1} حرف", MinimumLength = 2)]
        [Display(Name = "الاسم الكامل")]
        public string FullName { get; set; } = "";

        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = "";

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [StringLength(100, ErrorMessage = "كلمة المرور يجب أن تكون بين {2} و {1} حرف", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "كلمة المرور")]
        public string Password { get; set; } = "";

        [DataType(DataType.Password)]
        [Display(Name = "تأكيد كلمة المرور")]
        [Compare("Password", ErrorMessage = "كلمة المرور وتأكيد كلمة المرور غير متطابقتين")]
        public string ConfirmPassword { get; set; } = "";
    }
}
