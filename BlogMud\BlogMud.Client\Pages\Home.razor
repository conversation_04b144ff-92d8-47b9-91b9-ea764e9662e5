@page "/"
@using BlogMud.Shared.DTOs

<PageTitle>الرئيسية | @(_companyInfo?.Name ?? "شركتنا")</PageTitle>

<!-- Hero Slider Section -->
<div class="hero-slider position-relative overflow-hidden" data-aos="fade-in" data-aos-duration="1000">
    <MudCarousel Class="mud-width-full" Style="height:250px;" ShowArrows="true" ShowDelimiters="true" AutoCycle="true" TData="object">
        @foreach (var slide in _slides)
        {
            <MudCarouselItem Transition="Transition.Slide">
                <MudImage Src="@slide.ImageUrl" Alt="@slide.Title" ObjectFit="ObjectFit.Fill" Width="100" Height="100" Class="absolute-fill" />
                <div class="d-flex flex-column justify-center align-center h-100" style="background: rgba(0,0,0,0.4); position: absolute; top: 0; left: 0; right: 0; bottom: 0;">
                    <MudContainer Class="text-center">
                        <MudText Typo="Typo.h3" Color="Color.Surface" Class="mb-4" data-aos="fade-down" data-aos-delay="300" data-aos-duration="800">@slide.Title</MudText>
                        <MudText Color="Color.Surface" Class="mb-8" data-aos="fade-up" data-aos-delay="500" data-aos-duration="800">@slide.Description</MudText>
                        <MudButton Variant="Variant.Filled" Color="Color.Primary" Size="Size.Large" Href="@slide.ButtonLink" data-aos="zoom-in" data-aos-delay="700" data-aos-duration="600">@slide.ButtonText</MudButton>
                    </MudContainer>
                </div>
            </MudCarouselItem>
        }
    </MudCarousel>
</div>

<style>
    .absolute-fill {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    /* تحسينات AOS للصفحة الرئيسية */
    [data-aos] {
        pointer-events: none;
    }

    [data-aos].aos-animate {
        pointer-events: auto;
    }

    /* تأثيرات مخصصة للكروت */
    .mud-card[data-aos] {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .mud-card[data-aos]:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    /* تحسين الأداء للحركات */
    .hero-slider,
    .mud-card,
    .mud-image {
        will-change: transform, opacity;
    }

    /* تأثيرات إضافية للنصوص */
    .mud-text[data-aos] {
        transition: all 0.6s ease;
    }

    /* تحسينات للأزرار */
    .mud-button {
        transition: all 0.3s ease;
    }

    .mud-button:hover {
        transform: translateY(-2px);
    }

    /* تحسين الألوان */
    .primary-text {
        color: var(--mud-palette-primary) !important;
    }

    /* تحسين الاستجابة للشاشات الصغيرة */
    @@media (max-width: 768px) {
        [data-aos] {
            animation-duration: 0.6s !important;
        }
    }

    /* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
    @@media (prefers-reduced-motion: reduce) {
        [data-aos] {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }

    /* أنماط اللوجو الدائري */
    .company-logo-container {
        width: 300px;
        height: 300px;
        border-radius: 50%;
        overflow: hidden;
        border: 4px solid var(--mud-palette-primary);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
    }

    .company-logo-container:hover {
        transform: scale(1.05);
        box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
    }

    .company-logo-circular {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: contain;
        object-position: center;
    }

    /* تصميم متجاوب للوجو الدائري */
    @@media (max-width: 768px) {
        .company-logo-container {
            width: 240px;
            height: 240px;
            padding: 8px;
        }
    }

    /* Medium mobile devices (tablets in portrait) */
    @@media (max-width: 600px) {
        .company-logo-container {
            width: 250px;
            height: 250px;
            padding: 10px;
            margin: 0 auto;
        }
    }

    @@media (max-width: 480px) {
        .company-logo-container {
            width: 230px;
            height: 230px;
            padding: 8px;
            border-width: 3px;
        }
    }

    /* Extra small devices (very small phones) */
    @@media (max-width: 360px) {
        .company-logo-container {
            width: 200px;
            height: 200px;
            padding: 6px;
            border-width: 2px;
        }
    }

    /* Line clamp utility for text truncation */
    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>

<!-- About Company Section - Comprehensive -->
<MudContainer MaxWidth="MaxWidth.Large" Class="py-16">
    <!-- Company Logo Section (if available from admin) -->
    @if (!_aboutUsLoading && _aboutUsData != null && !string.IsNullOrEmpty(_aboutUsData.CompanyLogoUrl))
    {
        <MudCard Elevation="0" Class="mb-12" data-aos="fade-in" data-aos-duration="800">
            <MudCardContent Class="text-center">
                <div class="d-flex justify-center align-center mb-4 px-4" data-aos="zoom-in" data-aos-delay="300" data-aos-duration="800">
                    <div class="company-logo-container">
                        <MudImage Src="@_aboutUsData.CompanyLogoUrl" Alt="شعار الشركة"
                                 ObjectFit="ObjectFit.Contain" Class="company-logo-circular" />
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    }

    <!-- Overview Section -->
    <MudCard Elevation="0" Class="mb-12" data-aos="fade-up" data-aos-duration="800">
        <MudCardContent>
            <MudText Typo="Typo.h4" Class="mb-6" data-aos="slide-right" data-aos-duration="800">نبذة عن الشركة</MudText>
            @if (_aboutUsLoading)
            {
                <div class="d-flex justify-center align-center" style="height: 100px;">
                    <MudProgressCircular Color="Color.Primary" Size="Size.Medium" Indeterminate="true" />
                    <MudText Typo="Typo.body2" Class="mr-4">جاري تحميل البيانات...</MudText>
                </div>
            }
            else
            {
                <MudText Typo="Typo.body1" Class="mb-8" data-aos="fade-up" data-aos-delay="200" data-aos-duration="800">
                    @(_aboutUsData?.CompanyDescription ?? _companyInfo?.AboutUs ?? "نحن شركة رائدة في مجالنا منذ سنوات عديدة. نقدم خدمات عالية الجودة لعملائنا مع التركيز على رضا العميل والجودة.")
                </MudText>
            }

            <!-- Vision & Mission -->
            <MudGrid data-aos="fade-up" data-aos-delay="300" data-aos-duration="800">
                <MudItem xs="12" md="6">
                    <MudPaper Elevation="2" Class="pa-6 h-100" Style="background-color: #f5f5f5; border-right: 4px solid var(--mud-palette-primary);">
                        <MudText Typo="Typo.h5" Class="mb-4 primary-text">رؤيتنا</MudText>
                        <MudText Typo="Typo.body1">@(_companyInfo?.Vision ?? "أن نكون الشركة الرائدة والمفضلة في مجالنا محلياً وإقليمياً.")</MudText>
                    </MudPaper>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudPaper Elevation="2" Class="pa-6 h-100" Style="background-color: #f5f5f5; border-right: 4px solid var(--mud-palette-primary);">
                        <MudText Typo="Typo.h5" Class="mb-4 primary-text">رسالتنا</MudText>
                        <MudText Typo="Typo.body1">@(_companyInfo?.Mission ?? "تقديم خدمات متميزة ومبتكرة تلبي احتياجات عملائنا وتتجاوز توقعاتهم.")</MudText>
                    </MudPaper>
                </MudItem>
            </MudGrid>

            <!-- Company Capabilities Section (from admin) -->
            @if (!_aboutUsLoading && _aboutUsData != null && !string.IsNullOrEmpty(_aboutUsData.CompanyCapabilities))
            {
                <MudDivider Class="my-8" />
                <MudText Typo="Typo.h5" Class="mb-4 primary-text" data-aos="fade-right" data-aos-delay="400" data-aos-duration="600">قدراتنا ومميزاتنا</MudText>
                <MudText Typo="Typo.body1" Class="mb-4" data-aos="fade-up" data-aos-delay="500" data-aos-duration="600">@_aboutUsData.CompanyCapabilities</MudText>
            }
        </MudCardContent>
    </MudCard>

    <!-- Our Values Section -->
    <MudCard Elevation="0" Class="mb-12" data-aos="fade-up" data-aos-delay="200" data-aos-duration="800">
        <MudCardContent>
            <MudText Typo="Typo.h4" Class="mb-6" data-aos="slide-right" data-aos-duration="800">قيمنا</MudText>
            <MudGrid>
                <MudItem xs="12" sm="6" md="3">
                    <div class="d-flex flex-column align-center text-center pa-4" data-aos="zoom-in" data-aos-delay="100" data-aos-duration="600">
                        <MudIcon Icon="@Icons.Material.Filled.VerifiedUser" Color="Color.Primary" Size="Size.Large" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2">الجودة</MudText>
                        <MudText Typo="Typo.body2">نلتزم بتقديم أعلى معايير الجودة في جميع خدماتنا ومنتجاتنا.</MudText>
                    </div>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <div class="d-flex flex-column align-center text-center pa-4" data-aos="zoom-in" data-aos-delay="200" data-aos-duration="600">
                        <MudIcon Icon="@Icons.Material.Filled.People" Color="Color.Primary" Size="Size.Large" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2">التعاون</MudText>
                        <MudText Typo="Typo.body2">نعمل معاً كفريق واحد لتحقيق أهدافنا المشتركة وخدمة عملائنا.</MudText>
                    </div>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <div class="d-flex flex-column align-center text-center pa-4" data-aos="zoom-in" data-aos-delay="300" data-aos-duration="600">
                        <MudIcon Icon="@Icons.Material.Filled.Lightbulb" Color="Color.Primary" Size="Size.Large" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2">الابتكار</MudText>
                        <MudText Typo="Typo.body2">نسعى دائماً لتقديم حلول مبتكرة وخدمات متطورة تلبي احتياجات السوق.</MudText>
                    </div>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <div class="d-flex flex-column align-center text-center pa-4" data-aos="zoom-in" data-aos-delay="400" data-aos-duration="600">
                        <MudIcon Icon="@Icons.Material.Filled.Security" Color="Color.Primary" Size="Size.Large" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2">المسؤولية</MudText>
                        <MudText Typo="Typo.body2">نتحمل المسؤولية تجاه عملائنا والمجتمع والبيئة في جميع أعمالنا.</MudText>
                    </div>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>

    <!-- Production Capacity Section (from admin) -->
    @if (!_aboutUsLoading && _aboutUsData?.ProductionCapacityItems?.Any() == true)
    {
        <MudCard Elevation="0" Class="mb-12" data-aos="fade-up" data-aos-delay="300" data-aos-duration="800">
            <MudCardContent>
                <MudText Typo="Typo.h4" Class="mb-6" data-aos="slide-right" data-aos-duration="800">طاقتنا الإنتاجية</MudText>
                <MudGrid>
                    @{
                        var delay = 0;
                    }
                    @foreach (var item in _aboutUsData.ProductionCapacityItems.Where(i => i.IsActive).OrderBy(i => i.DisplayOrder))
                    {
                        var currentDelay = delay;
                        <MudItem xs="12" sm="6" md="4">
                            <MudCard Elevation="2" Class="h-100" data-aos="fade-up" data-aos-delay="@(currentDelay)" data-aos-duration="800">
                                @if (!string.IsNullOrEmpty(item.ImageUrl))
                                {
                                    <MudCardMedia Image="@item.ImageUrl" Height="200" />
                                }
                                <MudCardContent>
                                    <MudText Typo="Typo.h6" Class="mb-3">@item.Title</MudText>
                                    <MudText Typo="Typo.body2">@item.Description</MudText>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                        delay += 200;
                    }
                </MudGrid>
            </MudCardContent>
        </MudCard>
    }

    <!-- Call to Action for About Section -->
    <MudCard Elevation="0" Class="mb-12" data-aos="fade-up" data-aos-delay="400" data-aos-duration="800">
        <MudCardContent Class="text-center" Style="background-color: #f5f5f5;">
            <MudText Typo="Typo.h5" Class="mb-4">هل تريد معرفة المزيد عنا؟</MudText>
            <MudText Typo="Typo.body1" Class="mb-6">نحن سعداء بالتواصل معكم والإجابة على جميع استفساراتكم. يمكنكم الاتصال بنا مباشرة أو زيارة أحد فروعنا.</MudText>
            <div class="d-flex justify-center gap-4 flex-wrap">
                <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/contact" data-aos="zoom-in" data-aos-delay="500" data-aos-duration="600">اتصل بنا</MudButton>
                <MudButton Variant="Variant.Outlined" Color="Color.Primary" Href="/about" data-aos="zoom-in" data-aos-delay="600" data-aos-duration="600">تفاصيل أكثر</MudButton>
            </div>
        </MudCardContent>
    </MudCard>
</MudContainer>
<!-- Latest News Section -->
<MudContainer MaxWidth="MaxWidth.Large" Class="py-16">
    <div class="d-flex justify-space-between align-center mb-8" data-aos="fade-right" data-aos-duration="800">
        <MudText Typo="Typo.h4">آخر الأخبار</MudText>
        <MudButton Variant="Variant.Text" Color="Color.Primary" Href="/news" data-aos="fade-left" data-aos-delay="200" data-aos-duration="600">عرض المزيد</MudButton>
    </div>

    <MudGrid>
        @if (_latestArticles != null && _latestArticles.Any())
        {
            var articleDelay = 0;
            @foreach (var article in _latestArticles)
            {
                var currentArticleDelay = articleDelay;
                <MudItem xs="12" sm="6" md="4">
                    <MudCard Elevation="2" Class="h-100" data-aos="flip-left" data-aos-delay="@(currentArticleDelay)" data-aos-duration="800">
                        <MudCardMedia Image="@article.ImageUrl" Height="200" />
                        <MudCardContent>
                            <MudText Typo="Typo.subtitle1">@article.Category</MudText>
                            <MudText Typo="Typo.h5" Class="mb-2">@article.Title</MudText>
                            <MudText Typo="Typo.body2">@article.PublishDate.ToString("yyyy-MM-dd")</MudText>
                            <MudText Typo="Typo.body2" Class="mt-3 line-clamp-3">@GetArticleSummary(article.Content)</MudText>
                        </MudCardContent>
                        <MudCardActions>
                            <MudButton Variant="Variant.Text" Color="Color.Primary" Href="@($"/post/{article.Id}")">اقرأ المزيد</MudButton>
                        </MudCardActions>
                    </MudCard>
                </MudItem>
                articleDelay += 200;
            }
        }
        else
        {
            <MudItem xs="12">
                <MudAlert Severity="Severity.Info" Class="my-2" data-aos="fade-in" data-aos-duration="600">لم يتم العثور على مقالات.</MudAlert>
            </MudItem>
        }
    </MudGrid>
</MudContainer>



<!-- Our Services Section -->
<div class="py-16" style="background-color: white;">
    <MudContainer MaxWidth="MaxWidth.Large">
        <div class="d-flex justify-space-between align-center mb-8" data-aos="fade-right" data-aos-duration="800" data-aos-once="false" data-aos-mirror="true">
            <MudText Typo="Typo.h4">خدماتنا</MudText>
            <MudButton Variant="Variant.Text" Color="Color.Primary" Href="/ourServices" data-aos="fade-left" data-aos-delay="200" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">عرض جميع الخدمات</MudButton>
        </div>

        @if (_servicesLoading)
        {
            <div class="d-flex justify-center align-center" style="min-height: 300px;">
                <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                <MudText Typo="Typo.h6" Class="mr-4">جاري تحميل الخدمات...</MudText>
            </div>
        }
        else if (_services?.Any() == true)
        {
            <MudGrid>
                @{
                    var activeServices = _services.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder).Take(3).ToList();
                    var serviceDelay = 0;
                }

                @foreach (var service in activeServices)
                {
                    var currentServiceDelay = serviceDelay;
                    <MudItem xs="12" md="4">
                        <MudCard Elevation="2" Class="h-100" data-aos="fade-up" data-aos-delay="@(currentServiceDelay)" data-aos-duration="800" data-aos-once="false" data-aos-mirror="true">
                            @if (!string.IsNullOrEmpty(service.MainImageUrl))
                            {
                                <MudCardMedia Image="@service.MainImageUrl" Height="200" />
                            }
                            else
                            {
                                <div class="d-flex justify-center align-center" style="height: 200px; background-color: #f5f5f5;">
                                    <MudIcon Icon="@Icons.Material.Filled.BusinessCenter" Size="Size.Large" Color="Color.Default" />
                                </div>
                            }
                            <MudCardContent>
                                <MudText Typo="Typo.h6" Class="mb-3">@service.Title</MudText>
                                <MudText Typo="Typo.body2" Class="mb-4 line-clamp-3">@service.PrimaryDescription</MudText>

                                @if (!string.IsNullOrEmpty(service.Features))
                                {
                                    var features = service.Features.Split(';', StringSplitOptions.RemoveEmptyEntries).Take(2);
                                    <div class="d-flex flex-column gap-1 mb-4">
                                        @foreach (var feature in features)
                                        {
                                            <MudText Typo="Typo.body2" Class="d-flex align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.Check" Color="Color.Success" Size="Size.Small" Class="mr-2" />
                                                @feature.Trim()
                                            </MudText>
                                        }
                                    </div>
                                }
                            </MudCardContent>
                            <MudCardActions>
                                <MudButton Variant="Variant.Text" Color="Color.Primary" Href="/ourServices">تفاصيل أكثر</MudButton>
                                <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/contact">طلب الخدمة</MudButton>
                            </MudCardActions>
                        </MudCard>
                    </MudItem>
                    serviceDelay += 200;
                }
            </MudGrid>
        }
        else
        {
            <!-- No services available -->
            <div class="text-center py-8" data-aos="fade-in" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">
                <MudIcon Icon="@Icons.Material.Filled.BusinessCenter" Size="Size.Large" Color="Color.Default" Class="mb-4" />
                <MudText Typo="Typo.h6" Class="mb-2">لا توجد خدمات متاحة حالياً</MudText>
                <MudText Typo="Typo.body1" Color="Color.Secondary">نعمل على إضافة خدمات جديدة قريباً. تابعونا للحصول على آخر التحديثات.</MudText>
            </div>
        }

        <!-- Why Choose Us Section (Compact Version) -->
        <div class="mt-16">
            <MudText Typo="Typo.h5" Align="Align.Center" Class="mb-8" data-aos="fade-up" data-aos-duration="800" data-aos-once="false" data-aos-mirror="true">لماذا تختارنا؟</MudText>

            <MudGrid>
                <MudItem xs="12" sm="6" md="3">
                    <div class="d-flex flex-column align-center text-center pa-4" data-aos="zoom-in" data-aos-delay="100" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">
                        <MudIcon Icon="@Icons.Material.Filled.Star" Color="Color.Primary" Size="Size.Large" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2">جودة عالية</MudText>
                        <MudText Typo="Typo.body2">نقدم خدمات ذات جودة عالية تلبي أعلى المعايير العالمية.</MudText>
                    </div>
                </MudItem>

                <MudItem xs="12" sm="6" md="3">
                    <div class="d-flex flex-column align-center text-center pa-4" data-aos="zoom-in" data-aos-delay="200" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">
                        <MudIcon Icon="@Icons.Material.Filled.Timer" Color="Color.Primary" Size="Size.Large" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2">سرعة في التنفيذ</MudText>
                        <MudText Typo="Typo.body2">نلتزم بمواعيد التسليم ونسعى لإنجاز المشاريع في الوقت المناسب.</MudText>
                    </div>
                </MudItem>

                <MudItem xs="12" sm="6" md="3">
                    <div class="d-flex flex-column align-center text-center pa-4" data-aos="zoom-in" data-aos-delay="300" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">
                        <MudIcon Icon="@Icons.Material.Filled.Support" Color="Color.Primary" Size="Size.Large" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2">دعم متواصل</MudText>
                        <MudText Typo="Typo.body2">نوفر دعماً فنياً على مدار الساعة لضمان استمرارية العمل.</MudText>
                    </div>
                </MudItem>

                <MudItem xs="12" sm="6" md="3">
                    <div class="d-flex flex-column align-center text-center pa-4" data-aos="zoom-in" data-aos-delay="400" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">
                        <MudIcon Icon="@Icons.Material.Filled.PriceCheck" Color="Color.Primary" Size="Size.Large" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2">أسعار تنافسية</MudText>
                        <MudText Typo="Typo.body2">نقدم خدماتنا بأسعار تنافسية مع الحفاظ على مستوى الجودة العالي.</MudText>
                    </div>
                </MudItem>
            </MudGrid>
        </div>
    </MudContainer>
</div>

<!-- Call to Action Section -->
<div class="py-12" style="background-color: var(--mud-palette-primary); color: white;" data-aos="fade-up" data-aos-duration="1000" data-aos-once="false" data-aos-mirror="true">
    <MudContainer MaxWidth="MaxWidth.Large">
        <MudGrid Justify="Justify.Center" Class="text-center">
            <MudItem xs="12" md="8">
                <MudText Typo="Typo.h4" Class="mb-4" data-aos="bounce-in" data-aos-delay="200" data-aos-duration="800" data-aos-once="false" data-aos-mirror="true">تواصل معنا اليوم!</MudText>
                <MudText Typo="Typo.body1" Class="mb-6" data-aos="fade-up" data-aos-delay="400" data-aos-duration="800" data-aos-once="false" data-aos-mirror="true">مستعدون للإجابة على جميع استفساراتكم وتقديم عروض الأسعار المناسبة لكم.</MudText>
                <MudButton Variant="Variant.Filled" Color="Color.Secondary" Size="Size.Large" Href="/contact" data-aos="pulse" data-aos-delay="600" data-aos-duration="800" data-aos-once="false" data-aos-mirror="true">اتصل بنا</MudButton>
            </MudItem>
        </MudGrid>
    </MudContainer>
</div>

