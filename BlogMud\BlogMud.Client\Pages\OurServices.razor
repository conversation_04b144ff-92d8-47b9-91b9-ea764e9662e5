@page "/ourServices"
@using BlogMud.Shared.DTOs
@using BlogMud.Shared.Models
@inject HttpClient Http

<PageTitle>خدماتنا | @(_companyInfo?.Name ?? "شركتنا")</PageTitle>

<!-- Image Slideshows Carousel Section -->
@if (_slideshowsLoading)
{
    <div class="d-flex justify-center align-center" style="min-height: 250px;">
        <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
        <MudText Typo="Typo.h6" Class="mr-4">جاري تحميل العروض المرئية...</MudText>
    </div>
}
else if (_slideshows?.Any() == true)
{
    <div class="hero-slider position-relative overflow-hidden" data-aos="fade-in" data-aos-duration="800" data-aos-once="false" data-aos-mirror="true">
        <MudCarousel Class="mud-width-full" Style="height:250px;" ShowArrows="true" ShowDelimiters="true" AutoCycle="true" TData="object">
                @{
                    var allImages = new List<(string ImageUrl, string Title, string Description, string? LinkUrl, string? LinkText)>();
                    foreach (var slideshow in _slideshows.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder))
                    {
                        foreach (var imageUrl in slideshow.Images)
                        {
                            allImages.Add((imageUrl, slideshow.Title, slideshow.Description, slideshow.LinkUrl, slideshow.LinkText));
                        }
                    }
                }

            @foreach (var (imageUrl, title, description, linkUrl, linkText) in allImages)
            {
                <MudCarouselItem Transition="Transition.Slide">
                    <MudImage Src="@imageUrl" Alt="@title" ObjectFit="ObjectFit.Fill" Width="100" Height="100" Class="absolute-fill" />
                    <div class="d-flex flex-column justify-center align-center h-100" style="background: rgba(0,0,0,0.4); position: absolute; top: 0; left: 0; right: 0; bottom: 0;">
                        <MudContainer Class="text-center">
                            <MudText Typo="Typo.h3" Color="Color.Surface" Class="mb-4" data-aos="fade-up" data-aos-delay="200">@title</MudText>
                            @if (!string.IsNullOrEmpty(description))
                            {
                                <MudText Color="Color.Surface" Class="mb-8" data-aos="fade-up" data-aos-delay="400">@description</MudText>
                            }
                            @if (!string.IsNullOrEmpty(linkUrl) && !string.IsNullOrEmpty(linkText))
                            {
                                <MudButton Variant="Variant.Filled" Color="Color.Primary" Size="Size.Large" Href="@linkUrl" data-aos="fade-up" data-aos-delay="600">@linkText</MudButton>
                            }
                        </MudContainer>
                    </div>
                </MudCarouselItem>
            }
        </MudCarousel>
    </div>
}

<!-- Services Section -->
<MudContainer MaxWidth="MaxWidth.Large" Class="py-16 services-section">
    @if (_loading)
    {
        <div class="d-flex justify-center align-center" style="min-height: 400px;">
            <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
            <MudText Typo="Typo.h6" Class="mr-4">جاري تحميل الخدمات...</MudText>
        </div>
    }
    else if (_services?.Any() == true)
    {
        <!-- Services Header -->
        <div class="text-center mb-12" data-aos="fade-up" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">
            <MudText Typo="Typo.h3" Class="mb-4">خدماتنا المتميزة</MudText>
            <MudText Typo="Typo.body1" Color="Color.Secondary">نقدم مجموعة شاملة من الخدمات المتخصصة لتلبية احتياجاتكم</MudText>
        </div>

        <MudGrid>
            @{
                var activeServices = _services.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder).ToList();
            }

            @for (int i = 0; i < activeServices.Count; i++)
            {
                var service = activeServices[i];
                var isEven = i % 2 == 0;
                var delay = (i * 200) + 100; // تأخير متدرج للخدمات

                <MudItem xs="12" Class="mb-12">
                    <MudCard Elevation="0" Class="service-card"
                             data-aos="@(isEven ? "fade-right" : "fade-left")"
                             data-aos-delay="@delay"
                             data-aos-duration="700"
                             data-aos-once="false"
                             data-aos-mirror="true">
                        <MudGrid>
                            @if (isEven)
                            {
                                <!-- Image on the left for even-indexed services -->
                                <MudItem xs="12" md="6" Style="min-height: 400px;" Class="service-image-container">
                                    @if (!string.IsNullOrEmpty(service.MainImageUrl))
                                    {
                                        <MudImage Src="@service.MainImageUrl" Alt="@service.Title" ObjectFit="ObjectFit.Cover" Style="width:100%; height:100%" Class="service-image" />
                                    }
                                    else
                                    {
                                        <div class="d-flex justify-center align-center h-100 service-placeholder">
                                            <MudIcon Icon="@Icons.Material.Filled.Image" Size="Size.Large" Color="Color.Default" />
                                        </div>
                                    }
                                </MudItem>
                                <MudItem xs="12" md="6">
                                    <MudCardContent Class="d-flex flex-column justify-center h-100 pa-8 service-content">
                                        <MudText Typo="Typo.h4" Class="mb-4 service-title" data-aos="fade-up" data-aos-delay="@(delay + 100)">@service.Title</MudText>
                                        <MudText Typo="Typo.body1" Class="mb-4 service-description" data-aos="fade-up" data-aos-delay="@(delay + 200)">
                                            @service.PrimaryDescription
                                        </MudText>
                                        @if (!string.IsNullOrEmpty(service.DetailedDescription))
                                        {
                                            <MudText Typo="Typo.body1" Class="mb-6 service-detailed" data-aos="fade-up" data-aos-delay="@(delay + 300)">
                                                @service.DetailedDescription
                                            </MudText>
                                        }

                                        @if (!string.IsNullOrEmpty(service.Features))
                                        {
                                            <!-- Features -->
                                            <MudText Typo="Typo.h6" Class="mb-2 features-title" data-aos="fade-up" data-aos-delay="@(delay + 400)">المميزات:</MudText>
                                            <div class="d-flex flex-column gap-2 mb-6 features-list" data-aos="fade-up" data-aos-delay="@(delay + 500)">
                                                @{
                                                    var featureIndex = 0;
                                                }
                                                @foreach (var feature in service.Features.Split(';', StringSplitOptions.RemoveEmptyEntries))
                                                {
                                                    <MudText Typo="Typo.body2" Class="feature-item" data-aos="fade-left" data-aos-delay="@(delay + 600 + (featureIndex * 100))">
                                                        <MudIcon Icon="@Icons.Material.Filled.Check" Color="Color.Success" Size="Size.Small" Class="mr-2" />
                                                        @feature.Trim()
                                                    </MudText>
                                                    featureIndex++;
                                                }
                                            </div>
                                        }

                                        <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/contact" Class="service-button" data-aos="zoom-in" data-aos-delay="@(delay + 700)">طلب الخدمة</MudButton>
                                    </MudCardContent>
                                </MudItem>
                            }
                            else
                            {
                                <!-- Content on the left for odd-indexed services -->
                                <MudItem xs="12" md="6" order="2" orderMd="1">
                                    <MudCardContent Class="d-flex flex-column justify-center h-100 pa-8 service-content">
                                        <MudText Typo="Typo.h4" Class="mb-4 service-title" data-aos="fade-up" data-aos-delay="@(delay + 100)">@service.Title</MudText>
                                        <MudText Typo="Typo.body1" Class="mb-4 service-description" data-aos="fade-up" data-aos-delay="@(delay + 200)">
                                            @service.PrimaryDescription
                                        </MudText>
                                        @if (!string.IsNullOrEmpty(service.DetailedDescription))
                                        {
                                            <MudText Typo="Typo.body1" Class="mb-6 service-detailed" data-aos="fade-up" data-aos-delay="@(delay + 300)">
                                                @service.DetailedDescription
                                            </MudText>
                                        }

                                        @if (!string.IsNullOrEmpty(service.Features))
                                        {
                                            <!-- Features -->
                                            <MudText Typo="Typo.h6" Class="mb-2 features-title" data-aos="fade-up" data-aos-delay="@(delay + 400)">المميزات:</MudText>
                                            <div class="d-flex flex-column gap-2 mb-6 features-list" data-aos="fade-up" data-aos-delay="@(delay + 500)">
                                                @{
                                                    var featureIndex = 0;
                                                }
                                                @foreach (var feature in service.Features.Split(';', StringSplitOptions.RemoveEmptyEntries))
                                                {
                                                    <MudText Typo="Typo.body2" Class="feature-item" data-aos="fade-right" data-aos-delay="@(delay + 600 + (featureIndex * 100))">
                                                        <MudIcon Icon="@Icons.Material.Filled.Check" Color="Color.Success" Size="Size.Small" Class="mr-2" />
                                                        @feature.Trim()
                                                    </MudText>
                                                    featureIndex++;
                                                }
                                            </div>
                                        }

                                        <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/contact" Class="service-button" data-aos="zoom-in" data-aos-delay="@(delay + 700)">طلب الخدمة</MudButton>
                                    </MudCardContent>
                                </MudItem>
                                <MudItem xs="12" md="6" Style="min-height: 400px;" Class="service-image-container">
                                    @if (!string.IsNullOrEmpty(service.MainImageUrl))
                                    {
                                        <MudImage Src="@service.MainImageUrl" Alt="@service.Title" ObjectFit="ObjectFit.Cover" Style="width:100%; height:100%" Class="service-image" />
                                    }
                                    else
                                    {
                                        <div class="d-flex justify-center align-center h-100 service-placeholder">
                                            <MudIcon Icon="@Icons.Material.Filled.Image" Size="Size.Large" Color="Color.Default" />
                                        </div>
                                    }
                                </MudItem>
                            }
                        </MudGrid>
                    </MudCard>
                </MudItem>
            }
        </MudGrid>
    }
    else
    {
        <!-- No services available -->
        <div class="text-center py-16 no-services-section" data-aos="fade-up" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">
            <MudIcon Icon="@Icons.Material.Filled.BusinessCenter" Size="Size.Large" Color="Color.Default" Class="mb-4" data-aos="zoom-in" data-aos-delay="200" />
            <MudText Typo="Typo.h5" Class="mb-2" data-aos="fade-up" data-aos-delay="400">لا توجد خدمات متاحة حالياً</MudText>
            <MudText Typo="Typo.body1" Color="Color.Secondary" data-aos="fade-up" data-aos-delay="600">نعمل على إضافة خدمات جديدة قريباً. تابعونا للحصول على آخر التحديثات.</MudText>
        </div>
    }
</MudContainer>

<!-- Why Choose Us Section -->
<div class="py-16 why-choose-us-section" style="background-color: #ffffff;">
    <MudContainer MaxWidth="MaxWidth.Large">
        <MudText Typo="Typo.h4" Align="Align.Center" Class="mb-12 section-title" data-aos="fade-up" data-aos-duration="600" data-aos-once="false" data-aos-mirror="true">لماذا تختارنا؟</MudText>

        <MudGrid>
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="0" Class="h-100 why-choose-card" data-aos="flip-up" data-aos-delay="100" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                    <MudCardContent Class="d-flex flex-column align-center text-center">
                        <MudIcon Icon="@Icons.Material.Filled.Star" Color="Color.Primary" Size="Size.Large" Class="feature-icon" data-aos="zoom-in" data-aos-delay="200" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2 feature-title" data-aos="fade-up" data-aos-delay="300">جودة عالية</MudText>
                        <MudText Typo="Typo.body2" Class="feature-description" data-aos="fade-up" data-aos-delay="400">نقدم خدمات ذات جودة عالية تلبي أعلى المعايير العالمية وتحقق رضا العملاء.</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="0" Class="h-100 why-choose-card" data-aos="flip-up" data-aos-delay="200" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                    <MudCardContent Class="d-flex flex-column align-center text-center">
                        <MudIcon Icon="@Icons.Material.Filled.Timer" Color="Color.Primary" Size="Size.Large" Class="feature-icon" data-aos="zoom-in" data-aos-delay="300" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2 feature-title" data-aos="fade-up" data-aos-delay="400">سرعة في التنفيذ</MudText>
                        <MudText Typo="Typo.body2" Class="feature-description" data-aos="fade-up" data-aos-delay="500">نلتزم بمواعيد التسليم المحددة ونسعى دائماً لإنجاز المشاريع في الوقت المناسب.</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="0" Class="h-100 why-choose-card" data-aos="flip-up" data-aos-delay="300" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                    <MudCardContent Class="d-flex flex-column align-center text-center">
                        <MudIcon Icon="@Icons.Material.Filled.Support" Color="Color.Primary" Size="Size.Large" Class="feature-icon" data-aos="zoom-in" data-aos-delay="400" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2 feature-title" data-aos="fade-up" data-aos-delay="500">دعم متواصل</MudText>
                        <MudText Typo="Typo.body2" Class="feature-description" data-aos="fade-up" data-aos-delay="600">نوفر دعماً فنياً على مدار الساعة لضمان استمرارية العمل وحل أي مشكلات قد تواجه العملاء.</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="0" Class="h-100 why-choose-card" data-aos="flip-up" data-aos-delay="400" data-aos-duration="700" data-aos-once="false" data-aos-mirror="true">
                    <MudCardContent Class="d-flex flex-column align-center text-center">
                        <MudIcon Icon="@Icons.Material.Filled.PriceCheck" Color="Color.Primary" Size="Size.Large" Class="feature-icon" data-aos="zoom-in" data-aos-delay="500" />
                        <MudText Typo="Typo.h6" Class="mt-4 mb-2 feature-title" data-aos="fade-up" data-aos-delay="600">أسعار تنافسية</MudText>
                        <MudText Typo="Typo.body2" Class="feature-description" data-aos="fade-up" data-aos-delay="700">نقدم خدماتنا بأسعار تنافسية مناسبة لجميع العملاء مع الحفاظ على مستوى الجودة العالي.</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    </MudContainer>
</div>

<!-- Call to Action Section -->
<div class="py-12 cta-section" style="background-color: var(--mud-palette-primary); color: white;" data-aos="fade-up" data-aos-duration="800" data-aos-once="false" data-aos-mirror="true">
    <MudContainer MaxWidth="MaxWidth.Large">
        <MudGrid Justify="Justify.Center" Class="text-center">
            <MudItem xs="12" md="8">
                <MudText Typo="Typo.h4" Class="mb-4 cta-title" data-aos="fade-up" data-aos-delay="200">جاهزون لتقديم أفضل الخدمات لكم</MudText>
                <MudText Typo="Typo.body1" Class="mb-6 cta-description" data-aos="fade-up" data-aos-delay="400">تواصل معنا اليوم للاستفسار عن خدماتنا أو لطلب عرض سعر مناسب لاحتياجاتكم.</MudText>
                <MudButton Variant="Variant.Filled" Color="Color.Secondary" Size="Size.Large" Href="/contact" Class="cta-button" data-aos="zoom-in" data-aos-delay="600">تواصل معنا الآن</MudButton>
            </MudItem>
        </MudGrid>
    </MudContainer>
</div>

<style>
    /* Clean and Simple Design for OurServices Page */

    /* General Page Styling */
    .services-section {
        background-color: #ffffff;
        min-height: 100vh;
    }

    /* Hero Slider */
    .hero-slider {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .absolute-fill {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    /* Service Cards */
    .service-card {
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        overflow: hidden;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .service-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    }

    .service-image-container {
        overflow: hidden;
        border-radius: 16px 0 0 16px;
    }

    .service-image {
        transition: transform 0.3s ease;
    }

    .service-card:hover .service-image {
        transform: scale(1.05);
    }

    .service-placeholder {
        background-color: #f8f9fa;
        border-radius: 16px;
    }

    .service-content {
        padding: 2rem;
    }

    .service-title {
        color: var(--mud-palette-text-primary);
        font-weight: 700;
    }

    .service-description,
    .service-detailed {
        color: var(--mud-palette-text-secondary);
        line-height: 1.6;
    }

    .features-title {
        color: var(--mud-palette-primary);
        font-weight: 600;
    }

    .feature-item {
        transition: all 0.3s ease;
        padding: 0.25rem 0;
    }

    .feature-item:hover {
        transform: translateX(5px);
        color: var(--mud-palette-primary);
    }

    .service-button {
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .service-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(var(--mud-palette-primary-rgb), 0.3);
    }

    /* Why Choose Us Section */
    .why-choose-us-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .why-choose-card {
        background: #ffffff;
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .why-choose-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
    }

    .feature-icon {
        transition: all 0.3s ease;
    }

    .why-choose-card:hover .feature-icon {
        transform: scale(1.1) rotate(5deg);
    }

    .feature-title {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
    }

    .feature-description {
        color: var(--mud-palette-text-secondary);
        line-height: 1.6;
    }

    /* Call to Action Section */
    .cta-section {
        background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-primary-darken) 100%);
        position: relative;
        overflow: hidden;
    }

    .cta-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, transparent 50%);
    }

    .cta-title {
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .cta-description {
        opacity: 0.95;
        line-height: 1.6;
    }

    .cta-button {
        border-radius: 12px;
        padding: 16px 32px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .cta-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    }

    /* No Services Section */
    .no-services-section {
        background: #ffffff;
        border-radius: 16px;
        padding: 3rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .service-content {
            padding: 1.5rem;
        }

        .service-image-container {
            border-radius: 16px 16px 0 0;
            min-height: 250px !important;
        }

        .why-choose-card {
            margin-bottom: 1rem;
        }

        .cta-section {
            padding: 2rem 0;
        }
    }

    /* RTL Support */
    [dir="rtl"] .feature-item:hover {
        transform: translateX(-5px);
    }

    [dir="rtl"] .service-image-container {
        border-radius: 0 16px 16px 0;
    }

    @@media (max-width: 768px) {
        [dir="rtl"] .service-image-container {
            border-radius: 16px 16px 0 0;
        }
    }

    /* AOS Animation Support */
    [data-aos] {
        pointer-events: none;
    }

    [data-aos].aos-animate {
        pointer-events: auto;
    }

    /* Accessibility */
    @@media (prefers-reduced-motion: reduce) {
        * {
            transition: none !important;
            animation: none !important;
        }
    }

    /* Focus States */
    .service-button:focus,
    .cta-button:focus {
        outline: 2px solid var(--mud-palette-primary);
        outline-offset: 2px;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>

@code {
    private CompanyInfo _companyInfo;
    private List<ServiceDto> _services = new();
    private List<SidertMoveServicesDto> _slideshows = new();
    private bool _loading = true;
    private bool _slideshowsLoading = true;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Load company info, services, and slideshows concurrently
            var companyInfoTask = LoadCompanyInfo();
            var servicesTask = LoadServices();
            var slideshowsTask = LoadSlideshows();

            await Task.WhenAll(companyInfoTask, servicesTask, slideshowsTask);
        }
        catch (Exception ex)
        {
            // Handle error if needed
            Console.WriteLine($"Error loading page data: {ex.Message}");
        }
        finally
        {
            _loading = false;
            _slideshowsLoading = false;
            StateHasChanged();
        }

        await base.OnInitializedAsync();
    }

    private async Task LoadCompanyInfo()
    {
        try
        {
            _companyInfo = await Http.GetFromJsonAsync<CompanyInfo>("/api/CompanyInfo/1");
        }
        catch
        {
            // Si no hay un endpoint para CompanyInfo, usar datos por defecto
            _companyInfo = new CompanyInfo
            {
                Name = "شركتنا",
                Vision = "أن نكون الشركة الرائدة في مجال عملنا",
                Mission = "توفير خدمات عالية الجودة لعملائنا",
                AboutUs = "نحن شركة رائدة في مجال تكنولوجيا المعلومات.",
                Phone = "+966123456789",
                Email = "<EMAIL>",
                Address = "الرياض، المملكة العربية السعودية"
            };
        }
    }

    private async Task LoadServices()
    {
        try
        {
            _services = await Http.GetFromJsonAsync<List<ServiceDto>>("api/Services") ?? new List<ServiceDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading services: {ex.Message}");
            _services = new List<ServiceDto>();
        }
    }

    private async Task LoadSlideshows()
    {
        try
        {
            _slideshows = await Http.GetFromJsonAsync<List<SidertMoveServicesDto>>("api/SidertMoveServices/active") ?? new List<SidertMoveServicesDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading slideshows: {ex.Message}");
            _slideshows = new List<SidertMoveServicesDto>();
        }
    }
}
