using BlogMud.Shared.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

// Alias to avoid namespace conflict
using ClientModel = BlogMud.Shared.Models.Client;

namespace BlogMud.Data
{
    public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : IdentityDbContext<ApplicationUser>(options)
    {
        public DbSet<Article> Articles { get; set; }
        public DbSet<Branch> Branches { get; set; }
        public DbSet<ContactMessage> ContactMessages { get; set; }
        public DbSet<CompanyInfo> CompanyInfo { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<ClientModel> Clients { get; set; }
        public DbSet<AnimatedGif> AnimatedGifs { get; set; }
        public DbSet<Service> Services { get; set; }
        public DbSet<SidertMoveServices> SidertMoveServices { get; set; }
        public DbSet<NewsSiderMove> NewsSiderMoves { get; set; }
        public DbSet<AboutUs> AboutUs { get; set; }
        public DbSet<ProductionCapacityItem> ProductionCapacityItems { get; set; }
        public DbSet<SiderAboutUs> SiderAboutUs { get; set; }
        public DbSet<UserNotification> UserNotifications { get; set; }
        public DbSet<NewsletterSubscription> NewsletterSubscriptions { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Add any model configuration here

            // Seed data
            builder.Entity<CompanyInfo>().HasData(
                new CompanyInfo
                {
                    Id = 1,
                    Name = "شركتنا",
                    Vision = "أن نكون الشركة الرائدة في مجال عملنا",
                    Mission = "توفير خدمات عالية الجودة لعملائنا",
                    AboutUs = "نحن شركة رائدة في مجال [مجال الشركة]، تأسست عام [سنة التأسيس]، ونقدم خدمات متميزة للعملاء.",
                    Phone = "+966123456789",
                    Email = "<EMAIL>",
                    Address = "الرياض، المملكة العربية السعودية",
                    LogoUrl = "/images/logo.png",
                    FacebookUrl = "https://facebook.com/company",
                    TwitterUrl = "https://twitter.com/company",
                    InstagramUrl = "https://instagram.com/company",
                    LinkedInUrl = "https://linkedin.com/in/company"
                }
            );

            // Seed categories
            builder.Entity<Category>().HasData(
                new Category { Id = 1, Name = "أخبار", Description = "أخبار الشركة والفعاليات", ArticleCount = 0 },
                new Category { Id = 2, Name = "مقالات", Description = "مقالات متنوعة", ArticleCount = 0 },
                new Category { Id = 3, Name = "خدماتنا", Description = "خدمات الشركة", ArticleCount = 0 }
            );
        }
    }
}
