@page "/news/{Id:int}"

@using System.Text.RegularExpressions

<PageTitle>@(article?.Title ?? "تحميل...") | @(_companyInfo?.Name ?? "شركتنا")</PageTitle>

@if (isLoading)
{
    <div class="d-flex justify-center my-16 article-loading"
         data-aos="fade-in"
         data-aos-duration="600"
         data-aos-once="false"
         data-aos-mirror="true">
        <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
        <MudText Typo="Typo.h6" Class="mr-4 loading-text">جاري تحميل المقال...</MudText>
    </div>
}
else if (article == null)
{
    <MudContainer MaxWidth="MaxWidth.Large" Class="py-16 error-container">
        <MudAlert Severity="Severity.Error"
                  Class="my-8 error-alert"
                  data-aos="fade-in"
                  data-aos-duration="600"
                  data-aos-once="false"
                  data-aos-mirror="true">
            عذراً، لم يتم العثور على المقال المطلوب.
        </MudAlert>
        <MudButton Variant="Variant.Outlined"
                   Color="Color.Primary"
                   Href="/news"
                   Class="back-to-news-button"
                   data-aos="fade-up"
                   data-aos-delay="200"
                   data-aos-duration="600"
                   data-aos-once="false"
                   data-aos-mirror="true">
            العودة إلى صفحة الأخبار
        </MudButton>
    </MudContainer>
}
else
{
    <!-- Hero Section with Article Image -->
    <div class="position-relative hero-section"
         style="background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url('@article.ImageUrl') no-repeat center center; background-size: cover; height: 400px;"
         data-aos="fade-in"
         data-aos-duration="800"
         data-aos-once="false"
         data-aos-mirror="true">
        <div class="d-flex flex-column justify-end h-100 pb-8 hero-content">
            <MudContainer MaxWidth="MaxWidth.Large">
                <MudChip T="string"
                         Color="Color.Primary"
                         Size="Size.Small"
                         Class="mb-2 article-category-chip"
                         data-aos="fade-right"
                         data-aos-delay="300"
                         data-aos-duration="600"
                         data-aos-once="false"
                         data-aos-mirror="true">@article.CategoryName</MudChip>
                <MudText Typo="Typo.h3"
                         Color="Color.Surface"
                         Class="article-hero-title"
                         data-aos="fade-up"
                         data-aos-delay="500"
                         data-aos-duration="700"
                         data-aos-once="false"
                         data-aos-mirror="true">@article.Title</MudText>
                <MudText Typo="Typo.subtitle1"
                         Color="Color.Surface"
                         Class="mt-2 article-hero-intro"
                         data-aos="fade-up"
                         data-aos-delay="700"
                         data-aos-duration="700"
                         data-aos-once="false"
                         data-aos-mirror="true">@article.Introduction</MudText>
                <div class="d-flex align-center mt-4 article-hero-meta"
                     data-aos="fade-up"
                     data-aos-delay="900"
                     data-aos-duration="600"
                     data-aos-once="false"
                     data-aos-mirror="true">
                    <MudIcon Icon="@Icons.Material.Filled.CalendarToday" Color="Color.Surface" Size="Size.Small" Class="mr-2 meta-icon" />
                    <MudText Typo="Typo.body2" Color="Color.Surface" Class="meta-text">@article.PublishDate.ToString("yyyy-MM-dd")</MudText>
                    <MudDivider Vertical="true" FlexItem="true" Class="mx-4 meta-divider" />
                    <MudIcon Icon="@Icons.Material.Filled.Person" Color="Color.Surface" Size="Size.Small" Class="mr-2 meta-icon" />
                    <MudText Typo="Typo.body2" Color="Color.Surface" Class="meta-text">@article.ClientName</MudText>
                </div>
            </MudContainer>
        </div>
    </div>

    <MudContainer MaxWidth="MaxWidth.Large" Class="py-16 main-content-container">
        <MudGrid Class="article-grid">
            <MudItem xs="12" md="8" Class="main-content-item">
                <!-- Article Content -->
                <MudPaper Elevation="2"
                          Class="pa-6 article-content-paper"
                          data-aos="fade-up"
                          data-aos-delay="100"
                          data-aos-duration="700"
                          data-aos-once="false"
                          data-aos-mirror="true">
                    <!-- Video Section - Only show if VideoUrl exists -->
                    @if (!string.IsNullOrEmpty(article.VideoUrl))
                    {
                        <div class="video-container mb-6 article-video"
                             data-aos="zoom-in"
                             data-aos-delay="300"
                             data-aos-duration="800"
                             data-aos-once="false"
                             data-aos-mirror="true">
                            <video controls width="100%" style="max-height: 500px;" class="video-player">
                                <source src="@article.VideoUrl" type="video/mp4" />
                                متصفحك لا يدعم تشغيل الفيديو.
                            </video>
                        </div>
                    }

                    <!-- Article Content -->
                    <div class="article-content main-article-text"
                         data-aos="fade-up"
                         data-aos-delay="500"
                         data-aos-duration="700"
                         data-aos-once="false"
                         data-aos-mirror="true">
                        @((MarkupString)FormatContent(article.Content))
                    </div>

                    <!-- Image Carousel/Slider -->
                    @if (!string.IsNullOrEmpty(article.ImageCarousel))
                    {
                        <MudPaper Elevation="1"
                                  Class="mt-8 pa-0 image-carousel-container"
                                  data-aos="fade-up"
                                  data-aos-delay="700"
                                  data-aos-duration="700"
                                  data-aos-once="false"
                                  data-aos-mirror="true">
                            <img src="@article.ImageCarousel"
                                 alt="صورة المقال"
                                 class="rounded main-carousel-image"
                                 style="width: 100%;" />

                            @if (additionalImages.Count > 0)
                            {
                                <div class="d-flex flex-wrap gap-2 mt-4 additional-images"
                                     data-aos="fade-up"
                                     data-aos-delay="900"
                                     data-aos-duration="600"
                                     data-aos-once="false"
                                     data-aos-mirror="true">
                                    @foreach (var additionalImage in additionalImages)
                                    {
                                        <img src="@additionalImage"
                                             alt="صورة إضافية"
                                             class="rounded additional-image"
                                             style="width: 100px; height: 100px; object-fit: cover;" />
                                    }
                                </div>
                            }
                        </MudPaper>
                    }

                    <!-- Share Buttons -->
                    <MudDivider Class="my-6 share-divider" />
                    <MudText Typo="Typo.subtitle1"
                             Class="mb-2 share-title"
                             data-aos="fade-right"
                             data-aos-delay="1100"
                             data-aos-duration="600"
                             data-aos-once="false"
                             data-aos-mirror="true">مشاركة المقال</MudText>
                    <div class="d-flex gap-2 share-buttons"
                         data-aos="fade-up"
                         data-aos-delay="1300"
                         data-aos-duration="600"
                         data-aos-once="false"
                         data-aos-mirror="true">
                        <MudIconButton Icon="@Icons.Custom.Brands.Facebook"
                                       Color="Color.Primary"
                                       Variant="Variant.Outlined"
                                       Class="share-button facebook-button" />
                        <MudIconButton Icon="@Icons.Custom.Brands.Twitter"
                                       Color="Color.Info"
                                       Variant="Variant.Outlined"
                                       Class="share-button twitter-button" />
                        <MudIconButton Icon="@Icons.Custom.Brands.WhatsApp"
                                       Color="Color.Success"
                                       Variant="Variant.Outlined"
                                       Class="share-button whatsapp-button" />
                        <MudIconButton Icon="@Icons.Material.Filled.Link"
                                       Color="Color.Default"
                                       Variant="Variant.Outlined"
                                       Class="share-button link-button" />
                    </div>
                </MudPaper>
            </MudItem>

            <MudItem xs="12" md="4" Class="sidebar-item">
                <!-- Sidebar -->
                <MudPaper Elevation="2"
                          Class="pa-6 sidebar-paper"
                          data-aos="fade-left"
                          data-aos-delay="200"
                          data-aos-duration="700"
                          data-aos-once="false"
                          data-aos-mirror="true">
                    <MudText Typo="Typo.h6"
                             Class="mb-4 sidebar-title"
                             data-aos="fade-down"
                             data-aos-delay="400"
                             data-aos-duration="600"
                             data-aos-once="false"
                             data-aos-mirror="true">مقالات ذات صلة</MudText>

                    @if (relatedArticles.Count > 0)
                    {
                        @for (int i = 0; i < relatedArticles.Count; i++)
                        {
                            var relatedArticle = relatedArticles[i];
                            var currentDelay = 600 + (i * 200);
                            <MudCard Elevation="1"
                                     Class="mb-4 related-article-card"
                                     data-aos="fade-up"
                                     data-aos-delay="@currentDelay"
                                     data-aos-duration="600"
                                     data-aos-once="false"
                                     data-aos-mirror="true">
                                <MudCardHeader Class="related-article-header">
                                    <CardHeaderContent>
                                        <MudText Typo="Typo.body1" Class="related-article-title">@relatedArticle.Title</MudText>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardActions Class="related-article-actions">
                                    <MudButton Variant="Variant.Text"
                                               Color="Color.Primary"
                                               OnClick="@(() => NavigateToArticle(relatedArticle.Id))"
                                               Class="related-article-button">
                                        قراءة المزيد
                                    </MudButton>
                                </MudCardActions>
                            </MudCard>
                        }
                    }
                    else
                    {
                        <MudText Typo="Typo.body2"
                                 Class="no-related-articles"
                                 data-aos="fade-in"
                                 data-aos-delay="600"
                                 data-aos-duration="600"
                                 data-aos-once="false"
                                 data-aos-mirror="true">لا توجد مقالات ذات صلة حاليًا.</MudText>
                    }
                </MudPaper>
            </MudItem>
        </MudGrid>
    </MudContainer>
}

@code {
    [Parameter] public int Id { get; set; }

    private ArticleDto? article;
    private CompanyInfo? _companyInfo;
    private bool isLoading = true;
    private List<ArticleDto> relatedArticles = new List<ArticleDto>();
    private List<string> additionalImages = new List<string>();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Load company info
            try
            {
                _companyInfo = await Http.GetFromJsonAsync<CompanyInfo>("/api/CompanyInfo/1");
            }
            catch
            {
                // Default company info if API fails
                _companyInfo = new CompanyInfo
                {
                    Name = "شركتنا",
                    Vision = "أن نكون الشركة الرائدة في مجال عملنا",
                    Mission = "توفير خدمات عالية الجودة لعملائنا",
                    AboutUs = "نحن شركة رائدة في مجال تكنولوجيا المعلومات.",
                    Phone = "+966123456789",
                    Email = "<EMAIL>",
                    Address = "الرياض، المملكة العربية السعودية"
                };
            }

            await LoadArticle();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading article: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (Id > 0)
        {
            await LoadArticle();
        }
    }

    private async Task LoadArticle()
    {
        try
        {
            isLoading = true;

            // Load the article
            article = await Http.GetFromJsonAsync<ArticleDto>($"api/Article/{Id}");

            if (article != null)
            {
                // Load related articles (same category)
                await LoadRelatedArticles();

                // Load additional carousel images
                await LoadAdditionalImages();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading article: {ex.Message}");
            article = null;
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task LoadRelatedArticles()
    {
        try
        {
            // Get all articles
            var allArticles = await Http.GetFromJsonAsync<List<ArticleDto>>("api/Article");

            // Filter related articles (same category, excluding current article)
            relatedArticles = allArticles
                .Where(a => a.CategoryId == article.CategoryId && a.Id != article.Id)
                .Take(3)
                .ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading related articles: {ex.Message}");
        }
    }

    private async Task LoadAdditionalImages()
    {
        try
        {
            // In a real implementation, you would load additional carousel images
            // For now, we'll just use the main carousel image
            if (!string.IsNullOrEmpty(article.ImageCarousel))
            {
                // Extract the folder and article ID pattern from the ImageCarousel URL
                string folderPath = Path.GetDirectoryName(article.ImageCarousel.Replace('/', Path.DirectorySeparatorChar));
                string fileName = Path.GetFileName(article.ImageCarousel);

                // For demonstration, we'll just add a placeholder image
                // In a real implementation, you would query the server for all images with the article ID pattern
                additionalImages.Add("/images/default-carousel.jpg");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading additional images: {ex.Message}");
        }
    }

    private void NavigateToArticle(int articleId)
    {
        _Navigation.NavigateTo($"/news/{articleId}");
    }

    private string FormatContent(string content)
    {
        if (string.IsNullOrEmpty(content))
            return string.Empty;

        // Remove any existing video tags from the content
        // This is for backward compatibility with articles that have videos embedded in content
        var videoRegex = new Regex(@"<div class=""video-container"">.*?</div>", RegexOptions.Singleline);
        content = videoRegex.Replace(content, string.Empty);

        // Add paragraph tags if needed
        if (!content.Contains("<p>"))
        {
            content = "<p>" + content.Replace("\n\n", "</p><p>").Replace("\r\n\r\n", "</p><p>") + "</p>";
        }

        return content;
    }
}

<style>
    /* Base Styles */
    .article-loading {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 12px;
        padding: 2rem;
        margin: 2rem 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .loading-text {
        margin-right: 1rem;
        color: #666;
    }

    .error-container {
        background-color: #ffffff;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .error-alert {
        border-radius: 8px;
        border: 1px solid rgba(244, 67, 54, 0.2);
    }

    .back-to-news-button {
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .back-to-news-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(149, 55, 53, 0.2);
    }

    /* Hero Section */
    .hero-section {
        border-radius: 0 0 20px 20px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .hero-content {
        background: linear-gradient(135deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.4) 100%);
    }

    .article-category-chip {
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(149, 55, 53, 0.3);
    }

    .article-hero-title {
        text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
        font-weight: 700;
        line-height: 1.3;
    }

    .article-hero-intro {
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        max-width: 800px;
        line-height: 1.5;
    }

    .article-hero-meta {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 0.75rem 1rem;
        backdrop-filter: blur(10px);
    }

    .meta-icon {
        filter: drop-shadow(1px 1px 1px rgba(0,0,0,0.5));
    }

    .meta-text {
        text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
        font-weight: 500;
    }

    .meta-divider {
        background-color: rgba(255, 255, 255, 0.3);
    }

    /* Main Content */
    .main-content-container {
        background-color: #ffffff;
        min-height: 600px;
    }

    .article-grid {
        gap: 2rem;
    }

    .main-content-item {
        padding-right: 1rem;
    }

    .sidebar-item {
        padding-left: 1rem;
    }

    .article-content-paper {
        background: #ffffff;
        border-radius: 16px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .article-content-paper:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    /* Video Section */
    .article-video {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .video-container {
        position: relative;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        height: 0;
        overflow: hidden;
        border-radius: 12px;
    }

    .video-container video,
    .video-player {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 12px;
    }

    /* Article Content */
    .main-article-text {
        line-height: 1.8;
        font-size: 1.1rem;
        color: #333;
    }

    .main-article-text p {
        margin-bottom: 1.5rem;
        text-align: justify;
    }

    .main-article-text h1,
    .main-article-text h2,
    .main-article-text h3,
    .main-article-text h4,
    .main-article-text h5,
    .main-article-text h6 {
        margin: 2rem 0 1rem 0;
        color: #2c3e50;
        font-weight: 600;
    }

    .main-article-text ul,
    .main-article-text ol {
        margin: 1rem 0;
        padding-right: 2rem;
    }

    .main-article-text li {
        margin-bottom: 0.5rem;
    }

    /* Image Carousel */
    .image-carousel-container {
        border-radius: 12px;
        overflow: hidden;
        background: #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .main-carousel-image {
        transition: transform 0.3s ease;
        border-radius: 12px;
    }

    .main-carousel-image:hover {
        transform: scale(1.02);
    }

    .additional-images {
        padding: 1rem;
    }

    .additional-image {
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    }

    .additional-image:hover {
        transform: scale(1.1);
        border-color: var(--mud-palette-primary);
        box-shadow: 0 4px 15px rgba(149, 55, 53, 0.3);
    }

    /* Share Section */
    .share-divider {
        margin: 2rem 0;
        background-color: rgba(0, 0, 0, 0.1);
    }

    .share-title {
        color: #333;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .share-buttons {
        gap: 0.75rem;
    }

    .share-button {
        transition: all 0.3s ease;
        border-radius: 8px;
    }

    .share-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .facebook-button:hover {
        box-shadow: 0 4px 15px rgba(59, 89, 152, 0.4);
    }

    .twitter-button:hover {
        box-shadow: 0 4px 15px rgba(29, 161, 242, 0.4);
    }

    .whatsapp-button:hover {
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    }

    .link-button:hover {
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
    }

    /* Sidebar */
    .sidebar-paper {
        background: #ffffff;
        border-radius: 16px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        position: sticky;
        top: 2rem;
    }

    .sidebar-paper:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .sidebar-title {
        color: #333;
        font-weight: 700;
        border-bottom: 2px solid var(--mud-palette-primary);
        padding-bottom: 0.5rem;
    }

    .related-article-card {
        border-radius: 12px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        background: #ffffff;
    }

    .related-article-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        border-color: var(--mud-palette-primary);
    }

    .related-article-header {
        padding: 1rem 1rem 0.5rem;
    }

    .related-article-title {
        font-weight: 600;
        color: #333;
        line-height: 1.4;
        transition: color 0.3s ease;
    }

    .related-article-card:hover .related-article-title {
        color: var(--mud-palette-primary);
    }

    .related-article-actions {
        padding: 0.5rem 1rem 1rem;
    }

    .related-article-button {
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .related-article-button:hover {
        transform: translateX(-4px);
    }

    .no-related-articles {
        color: #666;
        text-align: center;
        padding: 2rem;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* RTL Support */
    [dir="rtl"] .loading-text {
        margin-right: 0;
        margin-left: 1rem;
    }

    [dir="rtl"] .main-content-item {
        padding-right: 0;
        padding-left: 1rem;
    }

    [dir="rtl"] .sidebar-item {
        padding-left: 0;
        padding-right: 1rem;
    }

    [dir="rtl"] .meta-icon {
        margin-right: 0;
        margin-left: 0.5rem;
    }

    [dir="rtl"] .main-article-text ul,
    [dir="rtl"] .main-article-text ol {
        padding-right: 0;
        padding-left: 2rem;
    }

    [dir="rtl"] .related-article-button:hover {
        transform: translateX(4px);
    }

    [dir="rtl"] .share-buttons {
        flex-direction: row-reverse;
    }

    /* AOS Animation Support */
    [data-aos] {
        pointer-events: none;
    }

    [data-aos].aos-animate {
        pointer-events: auto;
    }

    /* Custom AOS animations for ArticleDetail page */
    .article-content-paper[data-aos],
    .sidebar-paper[data-aos],
    .related-article-card[data-aos] {
        transition: transform 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .hero-section {
            height: 300px !important;
            border-radius: 0;
        }

        .article-hero-title {
            font-size: 1.5rem !important;
        }

        .article-hero-intro {
            font-size: 0.9rem;
        }

        .article-hero-meta {
            flex-direction: column;
            gap: 0.5rem;
            align-items: flex-start !important;
        }

        .meta-divider {
            display: none;
        }

        .main-content-container {
            padding: 1rem 0;
        }

        .article-grid {
            gap: 1rem;
        }

        .main-content-item,
        .sidebar-item {
            padding: 0;
        }

        .article-content-paper,
        .sidebar-paper {
            padding: 1.5rem;
            border-radius: 12px;
        }

        .main-article-text {
            font-size: 1rem;
        }

        .share-buttons {
            flex-wrap: wrap;
            justify-content: center;
        }

        .sidebar-paper {
            position: static;
            margin-top: 2rem;
        }

        /* Reduce animation duration on mobile */
        [data-aos] {
            animation-duration: 0.5s !important;
        }
    }

    @@media (max-width: 480px) {
        .hero-section {
            height: 250px !important;
        }

        .article-hero-title {
            font-size: 1.25rem !important;
        }

        .article-content-paper,
        .sidebar-paper {
            padding: 1rem;
        }

        .main-article-text {
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .additional-images {
            padding: 0.5rem;
        }

        .additional-image {
            width: 80px !important;
            height: 80px !important;
        }

        .share-title {
            font-size: 1rem !important;
        }
    }

    /* Tablet Responsive */
    @@media (min-width: 769px) and (max-width: 1024px) {
        .hero-section {
            height: 350px !important;
        }

        .main-content-container {
            padding: 2rem 0;
        }

        .article-content-paper,
        .sidebar-paper {
            padding: 2rem;
        }

        .sidebar-paper {
            position: static;
        }
    }

    /* Desktop Enhancements */
    @@media (min-width: 1025px) {
        .hero-section {
            border-radius: 0 0 24px 24px;
        }

        .article-content-paper:hover {
            transform: translateY(-2px);
        }

        .related-article-card:hover {
            transform: translateY(-6px);
        }

        .main-content-container {
            padding: 3rem 0;
        }
    }

    /* Accessibility Enhancements */
    @@media (prefers-reduced-motion: reduce) {
        * {
            transition: none !important;
            animation: none !important;
        }

        .main-carousel-image:hover,
        .additional-image:hover,
        .share-button:hover,
        .related-article-card:hover {
            transform: none;
        }

        [data-aos] {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }

    /* High Contrast Mode */
    @@media (prefers-contrast: high) {
        .article-content-paper,
        .sidebar-paper,
        .related-article-card {
            border: 2px solid #000;
        }

        .hero-section {
            border: 2px solid #000;
        }

        .share-button {
            border: 2px solid #000;
        }
    }

    /* Focus States for Accessibility */
    .article-content-paper:focus-within,
    .sidebar-paper:focus-within,
    .related-article-card:focus-within,
    .share-button:focus {
        outline: 2px solid var(--mud-palette-primary);
        outline-offset: 2px;
    }

    /* Print Styles */
    @@media print {
        .hero-section,
        .share-buttons,
        .sidebar-paper {
            display: none;
        }

        .article-content-paper {
            box-shadow: none;
            border: 1px solid #000;
            break-inside: avoid;
        }

        .main-article-text {
            color: #000;
            font-size: 12pt;
            line-height: 1.5;
        }
    }

    /* Loading Animation */
    @@keyframes pulse {
        0% {
            opacity: 1;
        }
        50% {
            opacity: 0.5;
        }
        100% {
            opacity: 1;
        }
    }

    .article-loading {
        animation: pulse 2s infinite;
    }

    /* Smooth Scrolling */
    html {
        scroll-behavior: smooth;
    }

    /* Custom Scrollbar */
    .main-article-text::-webkit-scrollbar {
        width: 8px;
    }

    .main-article-text::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .main-article-text::-webkit-scrollbar-thumb {
        background: var(--mud-palette-primary);
        border-radius: 4px;
    }

    .main-article-text::-webkit-scrollbar-thumb:hover {
        background: #c62828;
    }
</style>
