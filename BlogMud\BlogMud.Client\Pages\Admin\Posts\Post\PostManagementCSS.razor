﻿﻿<style>
    /* PostManagement Component Scoped Styles */

    /* إخفاء الجدول العادي في الشاشات الصغيرة */
    .regular-table {
        display: block;
    }

    .mobile-cards {
        display: none;
    }

    /* عرض البطاقات في الشاشات الصغيرة */
    @@media (max-width: 768px) {
        .regular-table {
            display: none;
        }

        .mobile-cards {
            display: block;
        }
    }

    /* تحسينات إضافية للجداول */
    .regular-table .mud-table {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-radius: 8px;
        overflow: hidden;
    }

    /* تنسيق عمود الترقيم */
    .mud-table-cell:first-child {
        font-weight: 600;
        color: var(--mud-palette-primary);
        text-align: center;
        width: 60px;
        min-width: 60px;
    }

    /* تنسيق البطاقات المصغرة للهاتف المحمول */
    .mobile-cards { 
        padding: 0 2px; 
    }
    
    .mobile-card {
        border-radius: 6px;
        transition: all 0.2s ease;
        border: 1px solid var(--mud-palette-divider);
        background: var(--mud-palette-surface);
        margin-bottom: 6px;
    }

    .mobile-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .article-number {
        font-weight: 600;
        font-size: 0.7rem;
        padding: 1px 6px;
        min-height: 20px;
        min-width: 24px;
    }

    .article-actions {
        display: flex;
        gap: 2px;
    }

    .article-title {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
        font-size: 0.9rem;
        line-height: 1.2;
        word-break: break-word;
        margin-bottom: 6px;
    }

    .article-info {
        direction: rtl;
    }

    .info-row {
        display: flex;
        align-items: center;
        gap: 4px;
        direction: rtl;
        margin-bottom: 4px;
        font-size: 0.8rem;
    }

    .info-icon {
        color: var(--mud-palette-primary);
        flex-shrink: 0;
    }

    .info-label {
        color: var(--mud-palette-text-secondary);
        font-weight: 500;
        min-width: 50px;
        flex-shrink: 0;
    }

    .info-value {
        color: var(--mud-palette-text-primary);
        font-weight: 400;
        word-break: break-word;
    }

    /* تحسينات للشاشات الصغيرة جداً */
    @@media (max-width: 480px) {
        .mobile-card .mud-card-content {
            padding: 6px !important;
        }

        .article-title {
            font-size: 0.85rem;
        }

        .info-row {
            flex-wrap: wrap;
            font-size: 0.75rem;
        }

        .info-label {
            min-width: 45px;
        }
    }

    /* دعم الوضع المظلم */
    @@media (prefers-color-scheme: dark) {
        .mobile-card {
            background: var(--mud-palette-dark-surface);
            border-color: var(--mud-palette-dark-divider);
        }

        .article-title {
            color: var(--mud-palette-dark-text-primary);
        }

        .info-label {
            color: var(--mud-palette-dark-text-secondary);
        }

        .info-value {
            color: var(--mud-palette-dark-text-primary);
        }
    }

    /* تحسينات الحركة */
    @@media (prefers-reduced-motion: reduce) {
        .mobile-card {
            transition: none;
        }

        .mobile-card:hover {
            transform: none;
        }
    }

    /* تحسينات للطباعة */
    @@media print {
        .mobile-cards {
            display: block !important;
        }

        .regular-table {
            display: none !important;
        }

        .mobile-card {
            break-inside: avoid;
            box-shadow: none;
            border: 1px solid #000;
            margin-bottom: 1rem;
        }

        .article-actions {
            display: none;
        }
    }

    /* AOS Animation Support for PostManagement */
    [data-aos] {
        pointer-events: none;
    }

    [data-aos].aos-animate {
        pointer-events: auto;
    }

    /* Custom AOS animations for PostManagement page */
    .regular-table[data-aos],
    .mobile-card[data-aos] {
        transition: transform 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
    }

    .mobile-card[data-aos]:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    /* Enhanced hover effects for AOS animated elements */
    .regular-table[data-aos] .mud-table {
        transition: all 0.3s ease;
    }

    .regular-table[data-aos].aos-animate .mud-table:hover {
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    }

    /* Smooth transitions for loading states */
    .d-flex.justify-center[data-aos] {
        transition: opacity 0.5s ease;
    }

    /* Enhanced button animations */
    .mud-button[data-aos] {
        transition: all 0.3s ease;
    }

    .mud-button[data-aos]:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    /* Accessibility - Respect user's motion preferences */
    @@media (prefers-reduced-motion: reduce) {
        [data-aos] {
            animation: none !important;
            transition: none !important;
        }
    }
</style>