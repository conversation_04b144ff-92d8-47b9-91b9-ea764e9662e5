using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    public class Branch
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Address { get; set; }
        
        [StringLength(100)]
        public string Phone { get; set; }
        
        [StringLength(100)]
        public string Email { get; set; }
        
        // For Google Maps
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        
        // Additional info
        [StringLength(500)]
        public string Description { get; set; }
        
        public bool IsActive { get; set; } = true;
    }
}
