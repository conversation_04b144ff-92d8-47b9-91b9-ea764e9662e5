using BlogMud.Data;
using BlogMud.Shared.Models;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

namespace BlogMud.Services
{
    public interface INewsletterService
    {
        Task<bool> SubscribeAsync(string email, string? fullName = null, string? userId = null);
        Task<bool> UnsubscribeAsync(string email);
        Task<bool> ConfirmSubscriptionAsync(string token);
        Task<List<NewsletterSubscription>> GetActiveSubscriptionsAsync();
        Task SendNewsletterAsync(string subject, string content);
        Task<bool> IsSubscribedAsync(string email);
    }

    public class NewsletterService : INewsletterService
    {
        private readonly ApplicationDbContext _context;
        private readonly EmailService _emailService;
        private readonly ILogger<NewsletterService> _logger;

        public NewsletterService(
            ApplicationDbContext context,
            EmailService emailService,
            ILogger<NewsletterService> logger)
        {
            _context = context;
            _emailService = emailService;
            _logger = logger;
        }

        public async Task<bool> SubscribeAsync(string email, string? fullName = null, string? userId = null)
        {
            try
            {
                // Check if already subscribed
                var existingSubscription = await _context.NewsletterSubscriptions
                    .FirstOrDefaultAsync(s => s.Email == email);

                if (existingSubscription != null)
                {
                    if (existingSubscription.IsActive)
                    {
                        return false; // Already subscribed
                    }
                    else
                    {
                        // Reactivate subscription
                        existingSubscription.IsActive = true;
                        existingSubscription.SubscribedAt = DateTime.Now;
                        existingSubscription.UnsubscribedAt = null;
                        existingSubscription.ConfirmationToken = GenerateToken();
                        existingSubscription.EmailConfirmed = false;
                    }
                }
                else
                {
                    // Create new subscription
                    var subscription = new NewsletterSubscription
                    {
                        Email = email,
                        FullName = fullName,
                        UserId = userId,
                        ConfirmationToken = GenerateToken(),
                        EmailConfirmed = false
                    };

                    _context.NewsletterSubscriptions.Add(subscription);
                }

                await _context.SaveChangesAsync();

                // Send confirmation email
                var confirmationLink = $"https://localhost:7009/newsletter/confirm?token={existingSubscription?.ConfirmationToken ?? GenerateToken()}";
                var emailBody = GetSubscriptionConfirmationTemplate(fullName ?? email, confirmationLink);
                await _emailService.SendEmailAsync(email, "تأكيد الاشتراك في النشرة الإخبارية - BlogMud", emailBody);

                _logger.LogInformation("Newsletter subscription created for {Email}", email);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to subscribe {Email} to newsletter", email);
                return false;
            }
        }

        public async Task<bool> UnsubscribeAsync(string email)
        {
            try
            {
                var subscription = await _context.NewsletterSubscriptions
                    .FirstOrDefaultAsync(s => s.Email == email && s.IsActive);

                if (subscription != null)
                {
                    subscription.IsActive = false;
                    subscription.UnsubscribedAt = DateTime.Now;
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Newsletter subscription cancelled for {Email}", email);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to unsubscribe {Email} from newsletter", email);
                return false;
            }
        }

        public async Task<bool> ConfirmSubscriptionAsync(string token)
        {
            try
            {
                var subscription = await _context.NewsletterSubscriptions
                    .FirstOrDefaultAsync(s => s.ConfirmationToken == token && !s.EmailConfirmed);

                if (subscription != null)
                {
                    subscription.EmailConfirmed = true;
                    subscription.ConfirmedAt = DateTime.Now;
                    subscription.ConfirmationToken = null;
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Newsletter subscription confirmed for {Email}", subscription.Email);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to confirm newsletter subscription with token {Token}", token);
                return false;
            }
        }

        public async Task<List<NewsletterSubscription>> GetActiveSubscriptionsAsync()
        {
            return await _context.NewsletterSubscriptions
                .Where(s => s.IsActive && s.EmailConfirmed)
                .ToListAsync();
        }

        public async Task SendNewsletterAsync(string subject, string content)
        {
            try
            {
                var subscriptions = await GetActiveSubscriptionsAsync();

                foreach (var subscription in subscriptions)
                {
                    try
                    {
                        var emailBody = GetNewsletterTemplate(subscription.FullName ?? subscription.Email, content, subscription.Email);
                        await _emailService.SendEmailAsync(subscription.Email, subject, emailBody);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send newsletter to {Email}", subscription.Email);
                    }
                }

                _logger.LogInformation("Newsletter sent to {Count} subscribers", subscriptions.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send newsletter");
            }
        }

        public async Task<bool> IsSubscribedAsync(string email)
        {
            return await _context.NewsletterSubscriptions
                .AnyAsync(s => s.Email == email && s.IsActive && s.EmailConfirmed);
        }

        private string GenerateToken()
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[32];
            rng.GetBytes(bytes);
            return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
        }

        private string GetSubscriptionConfirmationTemplate(string name, string confirmationLink)
        {
            return $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تأكيد الاشتراك في النشرة الإخبارية</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; direction: rtl; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; padding: 20px 0; border-bottom: 2px solid #007bff; }}
        .logo {{ font-size: 24px; font-weight: bold; color: #007bff; }}
        .content {{ padding: 30px 0; }}
        .button {{ display: inline-block; padding: 12px 30px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
        .footer {{ text-align: center; padding: 20px 0; border-top: 1px solid #eee; color: #666; font-size: 14px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>BlogMud</div>
        </div>
        <div class='content'>
            <h2>مرحباً {name}!</h2>
            <p>شكراً لك على الاشتراك في النشرة الإخبارية لموقع BlogMud. لتأكيد اشتراكك، يرجى النقر على الرابط أدناه:</p>
            <div style='text-align: center;'>
                <a href='{confirmationLink}' class='button'>تأكيد الاشتراك</a>
            </div>
            <p>إذا لم تقم بالاشتراك في النشرة الإخبارية، يرجى تجاهل هذا البريد الإلكتروني.</p>
            <p>مع أطيب التحيات،<br>فريق BlogMud</p>
        </div>
        <div class='footer'>
            <p>© 2024 BlogMud. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html>";
        }

        private string GetNewsletterTemplate(string name, string content, string email)
        {
            var unsubscribeLink = $"https://localhost:7009/newsletter/unsubscribe?email={email}";
            
            return $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>النشرة الإخبارية - BlogMud</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; direction: rtl; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; padding: 20px 0; border-bottom: 2px solid #28a745; }}
        .logo {{ font-size: 24px; font-weight: bold; color: #28a745; }}
        .content {{ padding: 30px 0; }}
        .footer {{ text-align: center; padding: 20px 0; border-top: 1px solid #eee; color: #666; font-size: 14px; }}
        .unsubscribe {{ font-size: 12px; color: #999; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>BlogMud</div>
        </div>
        <div class='content'>
            <h2>مرحباً {name}!</h2>
            {content}
            <p>مع أطيب التحيات،<br>فريق BlogMud</p>
        </div>
        <div class='footer'>
            <p>© 2024 BlogMud. جميع الحقوق محفوظة.</p>
            <p class='unsubscribe'>
                إذا كنت لا ترغب في تلقي هذه الرسائل، يمكنك 
                <a href='{unsubscribeLink}'>إلغاء الاشتراك</a>
            </p>
        </div>
    </div>
</body>
</html>";
        }
    }
}
