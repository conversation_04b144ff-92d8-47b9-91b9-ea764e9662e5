@page "/dev/email-test"
@using BlogMud.Data
@using BlogMud.Services
@using Microsoft.AspNetCore.Identity
@using System.ComponentModel.DataAnnotations
@inject IEmailSender<ApplicationUser> EmailSender
@inject ILogger<EmailTest> Logger
@inject IConfiguration Configuration

<PageTitle>اختبار الإيميل - BlogMud</PageTitle>

<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-envelope-open-text me-2"></i>
                        اختبار نظام الإيميل
                    </h3>
                </div>
                <div class="card-body">

                    <!-- Email Service Status -->
                    <div class="alert @(isRealEmailService ? "alert-success" : "alert-warning")" role="alert">
                        <h5 class="alert-heading">
                            <i class="fas @(isRealEmailService ? "fa-check-circle" : "fa-exclamation-triangle") me-2"></i>
                            حالة خدمة الإيميل
                        </h5>
                        <p class="mb-0">
                            @if (isRealEmailService)
                            {
                                <strong>✅ خدمة الإيميل الحقيقية مفعلة</strong><br>
                                <small>سيتم إرسال الإيميلات فعلياً إلى عناوين البريد الإلكتروني</small>
                            }
                            else
                            {
                                <strong>⚠️ خدمة الإيميل التطويرية مفعلة</strong><br>
                                <small>الإيميلات محاكاة فقط - لن تصل إلى البريد الإلكتروني الفعلي</small><br>
                                <small>لتفعيل الإرسال الحقيقي، راجع ملف GMAIL_SETUP_INSTRUCTIONS.md</small>
                            }
                        </p>
                    </div>

                    <!-- SMTP Configuration Display -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">إعدادات SMTP الحالية</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>خادم SMTP:</strong></td>
                                            <td>@emailSettings.SmtpServer</td>
                                        </tr>
                                        <tr>
                                            <td><strong>المنفذ:</strong></td>
                                            <td>@emailSettings.SmtpPort</td>
                                        </tr>
                                        <tr>
                                            <td><strong>البريد المرسل:</strong></td>
                                            <td>@emailSettings.SenderEmail</td>
                                        </tr>
                                        <tr>
                                            <td><strong>اسم المرسل:</strong></td>
                                            <td>@emailSettings.SenderName</td>
                                        </tr>
                                        <tr>
                                            <td><strong>SSL مفعل:</strong></td>
                                            <td>@(emailSettings.EnableSsl ? "نعم" : "لا")</td>
                                        </tr>
                                        <tr>
                                            <td><strong>كلمة المرور:</strong></td>
                                            <td>
                                                @if (string.IsNullOrEmpty(emailSettings.Password))
                                                {
                                                    <span class="text-danger">❌ غير محددة</span>
                                                }
                                                else if (emailSettings.Password == "your-gmail-app-password-here")
                                                {
                                                    <span class="text-warning">⚠️ تحتاج تحديث</span>
                                                }
                                                else
                                                {
                                                    <span class="text-success">✅ محددة</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>حالة الخدمة:</strong></td>
                                            <td>
                                                @if (isRealEmailService)
                                                {
                                                    <span class="badge bg-success">خدمة إيميل حقيقية</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-warning">خدمة تطويرية</span>
                                                }
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- Test Email Form -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">إرسال إيميل تجريبي</h6>
                                </div>
                                <div class="card-body">
                                    <EditForm Model="testEmailModel" OnValidSubmit="SendTestEmail">
                                        <DataAnnotationsValidator />

                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني:</label>
                                            <InputText @bind-Value="testEmailModel.Email" class="form-control" placeholder="<EMAIL>" />
                                            <ValidationMessage For="() => testEmailModel.Email" class="text-danger" />
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">الاسم الكامل:</label>
                                            <InputText @bind-Value="testEmailModel.FullName" class="form-control" placeholder="اسم المستخدم" />
                                            <ValidationMessage For="() => testEmailModel.FullName" class="text-danger" />
                                        </div>

                                        <button type="submit" class="btn btn-primary" disabled="@isSending">
                                            @if (isSending)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                                <span>جاري الإرسال...</span>
                                            }
                                            else
                                            {
                                                <i class="fas fa-paper-plane me-2"></i>
                                                <span>إرسال إيميل تجريبي</span>
                                            }
                                        </button>
                                    </EditForm>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Results -->
                    @if (!string.IsNullOrEmpty(resultMessage))
                    {
                        <div class="alert @(isSuccess ? "alert-success" : "alert-danger")" role="alert">
                            <h6 class="alert-heading">
                                <i class="fas @(isSuccess ? "fa-check-circle" : "fa-exclamation-circle") me-2"></i>
                                نتيجة الإرسال
                            </h6>
                            <p class="mb-0">@resultMessage</p>
                        </div>
                    }

                    <!-- Quick Links -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6>روابط سريعة:</h6>
                            <div class="btn-group" role="group">
                                <a href="/dev/emails" class="btn btn-outline-primary">
                                    <i class="fas fa-list me-2"></i>
                                    عرض الإيميلات المرسلة
                                </a>
                                <a href="/Account/ResendEmailConfirmation" class="btn btn-outline-secondary">
                                    <i class="fas fa-redo me-2"></i>
                                    إعادة إرسال تأكيد الإيميل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private TestEmailModel testEmailModel = new();
    private bool isSending = false;
    private string resultMessage = "";
    private bool isSuccess = false;
    private bool isRealEmailService = false;
    private EmailSettings emailSettings = new();

    protected override void OnInitialized()
    {
        // Check which email service is being used
        isRealEmailService = EmailSender.GetType() == typeof(EmailService);

        // Load email settings
        Configuration.GetSection("EmailSettings").Bind(emailSettings);
    }

    private async Task SendTestEmail()
    {
        isSending = true;
        resultMessage = "";

        try
        {
            // Create a dummy user for testing
            var testUser = new ApplicationUser
            {
                FullName = testEmailModel.FullName,
                Email = testEmailModel.Email,
                UserName = testEmailModel.Email
            };

            // Generate a test confirmation link
            var testLink = "https://localhost:7009/Account/ConfirmEmail?test=true";

            await EmailSender.SendConfirmationLinkAsync(testUser, testEmailModel.Email, testLink);

            resultMessage = $"تم إرسال الإيميل التجريبي بنجاح إلى {testEmailModel.Email}";
            isSuccess = true;

            Logger.LogInformation("Test email sent successfully to {Email}", testEmailModel.Email);
        }
        catch (Exception ex)
        {
            resultMessage = $"فشل في إرسال الإيميل: {ex.Message}";
            isSuccess = false;

            Logger.LogError(ex, "Failed to send test email to {Email}", testEmailModel.Email);
        }
        finally
        {
            isSending = false;
        }
    }

    public class TestEmailModel
    {
        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string Email { get; set; } = "<EMAIL>";

        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        public string FullName { get; set; } = "أحمد فرنشي";
    }
}
