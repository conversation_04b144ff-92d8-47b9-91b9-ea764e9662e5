using BlogMud.Services;
using BlogMud.Data;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace BlogMud.Controllers
{
    /// <summary>
    /// Development-only controller for testing email functionality
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class DevelopmentController : ControllerBase
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<DevelopmentController> _logger;

        public DevelopmentController(IWebHostEnvironment environment, ILogger<DevelopmentController> logger)
        {
            _environment = environment;
            _logger = logger;
        }

        /// <summary>
        /// Get all sent emails (development only)
        /// </summary>
        [HttpGet("emails")]
        public IActionResult GetSentEmails()
        {
            if (!_environment.IsDevelopment())
            {
                return NotFound();
            }

            var emails = DevelopmentEmailService.GetSentEmails();
            return Ok(emails);
        }

        /// <summary>
        /// Clear all sent emails (development only)
        /// </summary>
        [HttpDelete("emails")]
        public IActionResult ClearSentEmails()
        {
            if (!_environment.IsDevelopment())
            {
                return NotFound();
            }

            DevelopmentEmailService.ClearSentEmails();
            _logger.LogInformation("Development emails cleared");
            return Ok(new { message = "All sent emails cleared" });
        }

        /// <summary>
        /// Get the latest confirmation email link (development only)
        /// </summary>
        [HttpGet("emails/latest-confirmation")]
        public IActionResult GetLatestConfirmationEmail()
        {
            if (!_environment.IsDevelopment())
            {
                return NotFound();
            }

            var emails = DevelopmentEmailService.GetSentEmails();
            var confirmationEmail = emails
                .Where(e => e.Subject.Contains("تأكيد"))
                .OrderByDescending(e => e.SentAt)
                .FirstOrDefault();

            if (confirmationEmail == null)
            {
                return NotFound(new { message = "No confirmation emails found" });
            }

            // Extract confirmation link from email body
            string? confirmationLink = null;
            if (confirmationEmail.Body.Contains("href='"))
            {
                var startIndex = confirmationEmail.Body.IndexOf("href='") + 6;
                var endIndex = confirmationEmail.Body.IndexOf("'", startIndex);
                if (endIndex > startIndex)
                {
                    confirmationLink = confirmationEmail.Body.Substring(startIndex, endIndex - startIndex);
                }
            }

            return Ok(new
            {
                email = confirmationEmail,
                confirmationLink = confirmationLink
            });
        }

        /// <summary>
        /// Manually confirm a user's email address (development only)
        /// </summary>
        [HttpPost("confirm-email/{email}")]
        public async Task<IActionResult> ManuallyConfirmEmail(string email, [FromServices] UserManager<ApplicationUser> userManager)
        {
            if (!_environment.IsDevelopment())
            {
                return NotFound();
            }

            try
            {
                var user = await userManager.FindByEmailAsync(email);
                if (user == null)
                {
                    return NotFound(new { message = $"User with email {email} not found" });
                }

                if (user.EmailConfirmed)
                {
                    return Ok(new { message = $"Email {email} is already confirmed", user = new { user.Email, user.FullName, user.EmailConfirmed } });
                }

                // Manually confirm the email
                user.EmailConfirmed = true;
                var result = await userManager.UpdateAsync(user);

                if (result.Succeeded)
                {
                    _logger.LogInformation("Email manually confirmed for user: {Email}", email);
                    return Ok(new {
                        message = $"Email {email} has been manually confirmed",
                        user = new { user.Email, user.FullName, user.EmailConfirmed },
                        loginUrl = "/Account/Login"
                    });
                }
                else
                {
                    return BadRequest(new { message = "Failed to confirm email", errors = result.Errors });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error manually confirming email for {Email}", email);
                return StatusCode(500, new { message = "Internal server error", error = ex.Message });
            }
        }
    }
}
