@using BlogMud.Shared.DTOs

<MudContainer MaxWidth="MaxWidth.Large">
    @if (Service != null)
    {
        <MudGrid>
            <!-- معلومات أساسية -->
            <MudItem xs="12" md="8">
                <MudPaper Class="pa-4 mb-3">
                    <MudText Typo="Typo.h5" Class="mb-3">@Service.Title</MudText>
                    
                    <MudDivider Class="mb-3" />
                    
                    <MudText Typo="Typo.subtitle1" Class="mb-2">الوصف الأساسي:</MudText>
                    <MudText Typo="Typo.body1" Class="mb-3">@Service.PrimaryDescription</MudText>
                    
                    @if (!string.IsNullOrEmpty(Service.DetailedDescription))
                    {
                        <MudText Typo="Typo.subtitle1" Class="mb-2">الوصف التفصيلي:</MudText>
                        <MudText Typo="Typo.body1" Class="mb-3">@Service.DetailedDescription</MudText>
                    }
                    
                    @if (!string.IsNullOrEmpty(Service.Features))
                    {
                        <MudText Typo="Typo.subtitle1" Class="mb-2">المميزات:</MudText>
                        <MudList T="bool">
                            @foreach (var feature in Service.Features.Split(';', StringSplitOptions.RemoveEmptyEntries))
                            {
                                <MudListItem Icon="@Icons.Material.Filled.CheckCircle" IconColor="Color.Success">
                                    <MudText>@feature.Trim()</MudText>
                                </MudListItem>
                            }
                        </MudList>
                    }
                </MudPaper>
            </MudItem>
            
            <!-- معلومات جانبية -->
            <MudItem xs="12" md="4">
                <MudPaper Class="pa-4 mb-3">
                    <MudText Typo="Typo.h6" Class="mb-3">معلومات الخدمة</MudText>

                    <MudList T="int" Dense="true">
                        <MudListItem>
                            <div class="d-flex justify-space-between">
                                <MudText Typo="Typo.body2">المعرف:</MudText>
                                <MudChip Size="Size.Small" Color="Color.Primary">@Service.Id</MudChip>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex justify-space-between">
                                <MudText Typo="Typo.body2">ترتيب العرض:</MudText>
                                <MudChip Size="Size.Small" Color="Color.Info">@Service.DisplayOrder</MudChip>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex justify-space-between">
                                <MudText Typo="Typo.body2">الحالة:</MudText>
                                <MudChip Size="Size.Small" Color="@(Service.IsActive ? Color.Success : Color.Error)">
                                    @(Service.IsActive ? "نشط" : "غير نشط")
                                </MudChip>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex justify-space-between">
                                <MudText Typo="Typo.body2">تاريخ الإنشاء:</MudText>
                                <MudText Typo="Typo.body2">@Service.CreatedAt.ToString("yyyy-MM-dd")</MudText>
                            </div>
                        </MudListItem>
                        @if (Service.LastModifiedAt.HasValue)
                        {
                            <MudListItem>
                                <div class="d-flex justify-space-between">
                                    <MudText Typo="Typo.body2">آخر تعديل:</MudText>
                                    <MudText Typo="Typo.body2">@Service.LastModifiedAt.Value.ToString("yyyy-MM-dd")</MudText>
                                </div>
                            </MudListItem>
                        }
                    </MudList>
                </MudPaper>
            </MudItem>
            
            <!-- الصورة الرئيسية -->
            @if (!string.IsNullOrEmpty(Service.MainImageUrl))
            {
                <MudItem xs="12" md="6">
                    <MudPaper Class="pa-4 mb-3">
                        <MudText Typo="Typo.h6" Class="mb-3">الصورة الرئيسية</MudText>
                        <MudImage Src="@Service.MainImageUrl" 
                                 Alt="@Service.Title" 
                                 Class="rounded"
                                 Style="width: 100%; max-height: 300px; object-fit: cover;" />
                    </MudPaper>
                </MudItem>
            }
            
            <!-- رابط الفيديو -->
            @if (!string.IsNullOrEmpty(Service.VideoUrl))
            {
                <MudItem xs="12" md="6">
                    <MudPaper Class="pa-4 mb-3">
                        <MudText Typo="Typo.h6" Class="mb-3">رابط الفيديو</MudText>
                        <MudLink Href="@Service.VideoUrl" Target="_blank" Color="Color.Primary">
                            <MudIcon Icon="@Icons.Material.Filled.PlayCircle" Class="mr-2" />
                            مشاهدة الفيديو
                        </MudLink>
                    </MudPaper>
                </MudItem>
            }
            
            <!-- دوار الصور -->
            @if (!string.IsNullOrEmpty(Service.ImageCarousel))
            {
                <MudItem xs="12">
                    <MudPaper Class="pa-4 mb-3">
                        <MudText Typo="Typo.h6" Class="mb-3">دوار الصور</MudText>
                        <MudCarousel Class="mud-width-full" 
                                    Style="height: 400px;"
                                    ShowArrows="true" 
                                    ShowBullets="true" 
                                    EnableSwipeGesture="true" 
                                    AutoCycle="false"
                                    TData="object">
                            @foreach (var imageUrl in Service.ImageCarousel.Split(';', StringSplitOptions.RemoveEmptyEntries))
                            {
                                <MudCarouselItem Transition="Transition.Slide" Color="Color.Default">
                                    <div class="d-flex justify-center align-center" style="height: 100%;">
                                        <MudImage Src="@imageUrl.Trim()" 
                                                 Alt="صورة دوار" 
                                                 Class="rounded"
                                                 Style="max-width: 100%; max-height: 100%; object-fit: contain;" />
                                    </div>
                                </MudCarouselItem>
                            }
                        </MudCarousel>
                    </MudPaper>
                </MudItem>
            }
        </MudGrid>
    }
    else
    {
        <div class="d-flex align-center justify-center" style="height: 200px;">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
            <MudText Class="ml-3">جاري تحميل البيانات...</MudText>
        </div>
    }
</MudContainer>

<div class="d-flex justify-end mt-4">
    <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="Close">إغلاق</MudButton>
</div>

@code {
    [CascadingParameter] public required IMudDialogInstance MudDialog { get; set; }

    [Parameter]
    public ServiceDto Service { get; set; }

    private void Close()
    {
        MudDialog.Close();
    }
}
