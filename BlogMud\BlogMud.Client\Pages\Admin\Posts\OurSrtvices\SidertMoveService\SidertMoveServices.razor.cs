using BlogMud.Shared.DTOs;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using MudBlazor;
using System.Net.Http.Json;

namespace BlogMud.Client.Pages.Admin.Posts.OurSrtvices.SidertMoveService;

public partial class SidertMoveServices : ComponentBase
{
    [Inject]
    private HttpClient Https { get; set; }

    [Inject]
    private ISnackbar Snackbars { get; set; }

    [Inject]
    private IDialogService DialogServices { get; set; }

    // Carousel settings
    private bool arrows = true;
    private bool bullets = true;
    private bool enableSwipeGesture = true;
    private bool autocycle = true;
    private Transition transition = Transition.Slide;

    // Data management
    private List<SidertMoveServicesDto> _sidertMoveServices = new();
    private bool _loading = true;
    private string _searchString = "";

    // Image modal
    private bool _showImageModal = false;
    private string _modalImageUrl = "";
    private string _modalImageTitle = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadSidertMoveServices();
    }

    private async Task LoadSidertMoveServices()
    {
        try
        {
            _loading = true;
            _sidertMoveServices = await Https.GetFromJsonAsync<List<SidertMoveServicesDto>>("api/SidertMoveServices") ?? new List<SidertMoveServicesDto>();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbars.Add($"خطأ في تحميل الشرائح المتحركة: {ex.Message}", Severity.Error);
            Console.WriteLine($"Error loading sidert move services: {ex}");
        }
        finally
        {
            _loading = false;
            StateHasChanged();
        }
    }

    private bool FilterFunc(SidertMoveServicesDto sidertMoveService)
    {
        if (string.IsNullOrWhiteSpace(_searchString))
            return true;

        if (sidertMoveService.Title?.Contains(_searchString, StringComparison.OrdinalIgnoreCase) == true)
            return true;

        if (sidertMoveService.Description?.Contains(_searchString, StringComparison.OrdinalIgnoreCase) == true)
            return true;



        if (sidertMoveService.Notes?.Contains(_searchString, StringComparison.OrdinalIgnoreCase) == true)
            return true;

        return false;
    }



    private async Task OpenCreateDialog()
    {
        try
        {
            var newSidertMoveService = new SidertMoveServicesDto
            {
                Title = "",
                Description = "",
                ImageUrls = "",
                Duration = 5,
                DisplayOrder = 0,
                IsActive = true,
                LinkUrl = "",
                LinkText = "",
                Notes = ""
            };

            var parameters = new DialogParameters<SidertMoveServiceEditForm>
            {
                { x => x.SidertMoveService, newSidertMoveService },
                { x => x.IsCreateMode, true }
            };

            var options = new DialogOptions()
            {
                MaxWidth = MaxWidth.Large,
                FullWidth = true,
                CloseButton = true,
                BackdropClick = false,
                CloseOnEscapeKey = false
            };

            var dialog = await DialogServices.ShowAsync<SidertMoveServiceEditForm>(
                "إضافة شريحة متحركة جديدة",
                parameters,
                options);

            var result = await dialog.Result;

            if (!result.Canceled && result.Data is SidertMoveServicesDto createdSidertMoveService)
            {
                await HandleCreateSidertMoveService(createdSidertMoveService);
            }
        }
        catch (Exception ex)
        {
            Snackbars.Add($"خطأ في فتح نافذة الإضافة: {ex.Message}", Severity.Error);
            Console.WriteLine($"Error opening create dialog: {ex}");
        }
    }

    private async Task OpenEditDialog(SidertMoveServicesDto sidertMoveService)
    {
        try
        {
            // Get fresh data from the server to avoid stale data issues
            var freshData = await Https.GetFromJsonAsync<SidertMoveServicesDto>($"api/SidertMoveServices/{sidertMoveService.Id}");
            if (freshData == null)
            {
                Snackbars.Add("لم يتم العثور على الشريحة المتحركة", Severity.Error);
                return;
            }

            // Create a completely new instance to avoid reference issues
            var editingSidertMoveService = new SidertMoveServicesDto
            {
                Id = freshData.Id,
                Title = freshData.Title ?? "",
                Description = freshData.Description ?? "",
                ImageUrls = freshData.ImageUrls ?? "",
                Duration = freshData.Duration,
                DisplayOrder = freshData.DisplayOrder,
                IsActive = freshData.IsActive,
                CreatedAt = freshData.CreatedAt,
                LastModifiedAt = freshData.LastModifiedAt,
                LinkUrl = freshData.LinkUrl ?? "",
                LinkText = freshData.LinkText ?? "",
                Notes = freshData.Notes ?? ""
            };

            var parameters = new DialogParameters<SidertMoveServiceEditForm>
            {
                { x => x.SidertMoveService, editingSidertMoveService },
                { x => x.IsCreateMode, false }
            };

            var options = new DialogOptions()
            {
                MaxWidth = MaxWidth.Large,
                FullWidth = true,
                CloseButton = true,
                BackdropClick = false,
                CloseOnEscapeKey = false
            };

            var dialog = await DialogServices.ShowAsync<SidertMoveServiceEditForm>(
                "تعديل الشريحة المتحركة",
                parameters,
                options);

            var result = await dialog.Result;

            if (!result.Canceled && result.Data is SidertMoveServicesDto updatedSidertMoveService)
            {
                await HandleSaveSidertMoveService(updatedSidertMoveService);
            }
        }
        catch (Exception ex)
        {
            Snackbars.Add($"خطأ في تحميل بيانات الشريحة المتحركة: {ex.Message}", Severity.Error);
            Console.WriteLine($"Error loading sidert move service data: {ex}");
        }
    }

    private async Task OpenDetailsDialog(SidertMoveServicesDto sidertMoveService)
    {
        try
        {
            var parameters = new DialogParameters<SidertMoveServiceDetailsDialog>
            {
                { x => x.SidertMoveService, sidertMoveService }
            };

            var options = new DialogOptions()
            {
                MaxWidth = MaxWidth.Large,
                FullWidth = true,
                CloseButton = true
            };

            await DialogServices.ShowAsync<SidertMoveServiceDetailsDialog>(
                "تفاصيل الشريحة المتحركة",
                parameters,
                options);
        }
        catch (Exception ex)
        {
            Snackbars.Add($"خطأ في عرض تفاصيل الشريحة المتحركة: {ex.Message}", Severity.Error);
            Console.WriteLine($"Error showing sidert move service details: {ex}");
        }
    }

    private async Task HandleCreateSidertMoveService(SidertMoveServicesDto newSidertMoveService)
    {
        try
        {
            // Ensure we're not sending null values
            newSidertMoveService.Title ??= "";
            newSidertMoveService.Description ??= "";
            newSidertMoveService.ImageUrls ??= "";
            newSidertMoveService.LinkUrl ??= "";
            newSidertMoveService.LinkText ??= "";
            newSidertMoveService.Notes ??= "";

            var response = await Https.PostAsJsonAsync("api/SidertMoveServices", newSidertMoveService);

            if (response.IsSuccessStatusCode)
            {
                var createdSidertMoveService = await response.Content.ReadFromJsonAsync<SidertMoveServicesDto>();
                if (createdSidertMoveService != null)
                {
                    _sidertMoveServices.Add(createdSidertMoveService);
                    StateHasChanged();
                    Snackbars.Add("تم إنشاء الشريحة المتحركة بنجاح", Severity.Success);
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Snackbars.Add($"خطأ في إنشاء الشريحة المتحركة: {errorContent}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbars.Add($"خطأ في إنشاء الشريحة المتحركة: {ex.Message}", Severity.Error);
            Console.WriteLine($"Error creating sidert move service: {ex}");
        }
    }

    private async Task HandleSaveSidertMoveService(SidertMoveServicesDto updatedSidertMoveService)
    {
        try
        {
            // Ensure we're not sending null values
            updatedSidertMoveService.Title ??= "";
            updatedSidertMoveService.Description ??= "";
            updatedSidertMoveService.ImageUrls ??= "";
            updatedSidertMoveService.LinkUrl ??= "";
            updatedSidertMoveService.LinkText ??= "";
            updatedSidertMoveService.Notes ??= "";

            // Add the current timestamp for LastModifiedAt
            updatedSidertMoveService.LastModifiedAt = DateTime.Now;

            // Send the update request
            var response = await Https.PutAsJsonAsync($"api/SidertMoveServices/{updatedSidertMoveService.Id}", updatedSidertMoveService);

            if (response.IsSuccessStatusCode)
            {
                // Find and update the sidert move service in the local list
                var index = _sidertMoveServices.FindIndex(s => s.Id == updatedSidertMoveService.Id);
                if (index >= 0)
                {
                    _sidertMoveServices[index] = updatedSidertMoveService;
                    StateHasChanged();
                }

                Snackbars.Add("تم تحديث الشريحة المتحركة بنجاح", Severity.Success);
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Snackbars.Add($"خطأ في تحديث الشريحة المتحركة: {errorContent}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbars.Add($"خطأ في تحديث الشريحة المتحركة: {ex.Message}", Severity.Error);
            Console.WriteLine($"Error updating sidert move service: {ex}");
        }
    }

    private async Task DeleteSidertMoveService(int sidertMoveServiceId)
    {
        try
        {
            var sidertMoveService = _sidertMoveServices.FirstOrDefault(s => s.Id == sidertMoveServiceId);
            if (sidertMoveService == null)
            {
                Snackbars.Add("لم يتم العثور على الشريحة المتحركة", Severity.Error);
                return;
            }

            var parameters = new DialogParameters
            {
                { "ContentText", $"هل أنت متأكد من حذف الشريحة المتحركة '{sidertMoveService.Title}'؟ هذا الإجراء لا يمكن التراجع عنه." },
                { "ButtonText", "حذف" },
                { "Color", Color.Error }
            };

            var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.ExtraSmall };

            var dialog = await DialogServices.ShowAsync<BlogMud.Client.Components.ConfirmationDialog>("تأكيد الحذف", parameters, options);
            var result = await dialog.Result;

            if (!result.Canceled)
            {
                var response = await Https.DeleteAsync($"api/SidertMoveServices/{sidertMoveServiceId}");

                if (response.IsSuccessStatusCode)
                {
                    _sidertMoveServices.RemoveAll(s => s.Id == sidertMoveServiceId);
                    StateHasChanged();
                    Snackbars.Add("تم حذف الشريحة المتحركة بنجاح", Severity.Success);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Snackbars.Add($"خطأ في حذف الشريحة المتحركة: {errorContent}", Severity.Error);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbars.Add($"خطأ في حذف الشريحة المتحركة: {ex.Message}", Severity.Error);
            Console.WriteLine($"Error deleting sidert move service: {ex}");
        }
    }

    // Image modal methods
    private void ShowImageModal(string imageUrl, string title)
    {
        _modalImageUrl = imageUrl;
        _modalImageTitle = title;
        _showImageModal = true;
        StateHasChanged();
    }

    private void HideImageModal()
    {
        _showImageModal = false;
        _modalImageUrl = "";
        _modalImageTitle = "";
        StateHasChanged();
    }
}