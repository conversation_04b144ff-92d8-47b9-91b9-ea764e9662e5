@page "/admin/client"


@attribute [Authorize(Roles = "Admin")]
@layout BlogMud.Client.Layout.AdminLayout

<PageTitle>إدارة العملاء</PageTitle>

<ClientManagerCSS />

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-6">
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h4">إدارة العملاء</MudText>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
            OnClick="@(() => OpenClientDialog(null))">
            إضافة عميل جديد
        </MudButton>
    </div>

    @if (isLoading)
    {
        <div class="d-flex justify-center my-8">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
        </div>
    }
    else if (clients.Count == 0)
    {
        <MudAlert Severity="Severity.Info" Class="my-4">لا يوجد عملاء متاحين. أضف عميلاً جديداً للبدء.</MudAlert>
    }
    else
    {
        <!-- الجدول العادي للشاشات الكبيرة -->
        <div class="regular-table">
            <MudTable Items="@clients" Hover="true" Striped="true" Bordered="true" Class="mb-8">
                <HeaderContent>
                    <MudTh>ت</MudTh>
                    <MudTh>اسم العميل</MudTh>
                    <MudTh>البريد الإلكتروني</MudTh>
                    <MudTh>رقم الهاتف</MudTh>
                    <MudTh>العنوان</MudTh>
                    <MudTh>الحالة</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="ت">@(clients.IndexOf(context) + 1)</MudTd>
                    <MudTd DataLabel="اسم العميل">@context.Name</MudTd>
                    <MudTd DataLabel="البريد الإلكتروني" Class="client-email">@context.Email</MudTd>
                    <MudTd DataLabel="رقم الهاتف">@context.Phone</MudTd>
                    <MudTd DataLabel="العنوان" Class="client-address">@context.Address</MudTd>
                    <MudTd DataLabel="الحالة">
                        @if (context.IsActive)
                        {
                            <MudChip T="string" Color="Color.Success" Size="Size.Small" Class="client-status-active">نشط</MudChip>
                        }
                        else
                        {
                            <MudChip T="string" Color="Color.Error" Size="Size.Small" Class="client-status-inactive">غير نشط</MudChip>
                        }
                    </MudTd>
                    <MudTd DataLabel="الإجراءات">
                        <MudIconButton Icon="@Icons.Material.Filled.Edit" Color="Color.Primary"
                            OnClick="@(() => OpenClientDialog(context))" />
                        <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error"
                            OnClick="@(() => ConfirmDelete(context))" />
                    </MudTd>
                </RowTemplate>
                <PagerContent>
                    <MudTablePager PageSizeOptions="new int[] { 5, 10, 15 }" />
                </PagerContent>
            </MudTable>
        </div>

        <!-- تخطيط البطاقات المصغرة للشاشات الصغيرة (التابلت والهاتف) -->
        <div class="mobile-cards">
            @foreach (var client in clients)
            {
                <MudCard Class="mobile-card" Elevation="1">
                    <MudCardContent Class="compact-content">
                        <!-- رقم العميل والإجراءات -->
                        <div class="client-header">
                            <MudChip T="string" Color="Color.Primary" Size="Size.Small" Class="client-number">
                                @(clients.IndexOf(client) + 1)
                            </MudChip>
                            <div class="client-actions">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                             Color="Color.Primary"
                                             Size="Size.Small"
                                             OnClick="@(() => OpenClientDialog(client))" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                             Color="Color.Error"
                                             Size="Size.Small"
                                             OnClick="@(() => ConfirmDelete(client))" />
                            </div>
                        </div>

                        <!-- اسم العميل -->
                        <div class="client-name">
                            @(client.Name ?? "غير محدد")
                        </div>

                        <!-- معلومات العميل -->
                        @if (!string.IsNullOrEmpty(client.Email))
                        {
                            <div class="client-info">
                                <MudIcon Icon="@Icons.Material.Filled.Email" Size="Size.Small" Class="info-icon" />
                                <span class="info-label">البريد:</span>
                                <span>@client.Email</span>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(client.Phone))
                        {
                            <div class="client-info">
                                <MudIcon Icon="@Icons.Material.Filled.Phone" Size="Size.Small" Class="info-icon" />
                                <span class="info-label">الهاتف:</span>
                                <span>@client.Phone</span>
                            </div>
                        }

                        <!-- حالة العميل -->
                        <div class="client-info">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">الحالة:</span>
                            @if (client.IsActive)
                            {
                                <MudChip T="string" Color="Color.Success" Size="Size.Small" Class="status-chip">نشط</MudChip>
                            }
                            else
                            {
                                <MudChip T="string" Color="Color.Error" Size="Size.Small" Class="status-chip">غير نشط</MudChip>
                            }
                        </div>
                    </MudCardContent>
                </MudCard>
            }
        </div>
    }
</MudContainer>

@code {
    #region الخصائص والمتغيرات

    /// <summary>
    /// قائمة العملاء المعروضة في الصفحة
    /// </summary>
    private List<ClientDto> clients = new List<ClientDto>();

    /// <summary>
    /// مؤشر على حالة تحميل البيانات
    /// </summary>
    private bool isLoading = true;
    #endregion

    #region دوال دورة الحياة

    /// <summary>
    /// تهيئة مكونات الصفحة عند بدء التشغيل
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بتحميل قائمة العملاء من الخادم عند تحميل الصفحة
    /// </remarks>
    protected override async Task OnInitializedAsync()
    {
        await LoadClients();
    }
    #endregion

    #region طلبات البيانات

    /// <summary>
    /// تحميل قائمة العملاء من الخادم
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بتعيين مؤشر التحميل إلى true قبل البدء في التحميل
    /// ويستخدم HttpClient مباشرة بدلاً من GetFromJsonAsync للحصول على معلومات تشخيصية أكثر
    /// في حالة نجاح الطلب، يتم تحميل قائمة العملاء
    /// في حالة فشل الطلب، يتم عرض رسالة خطأ تتضمن رمز الحالة ومحتوى الخطأ
    /// وإعادة تعيين مؤشر التحميل إلى false بعد الانتهاء من التحميل بغض النظر عن النتيجة
    /// </remarks>
    private async Task LoadClients()
    {
        try
        {
            isLoading = true;

            // استخدام HttpClient مباشرة بدلاً من GetFromJsonAsync للحصول على معلومات تشخيصية أكثر
            var response = await Http.GetAsync("api/Clients");

            if (response.IsSuccessStatusCode)
            {
                clients = await response.Content.ReadFromJsonAsync<List<ClientDto>>() ?? new List<ClientDto>();
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _Snackbar.Add($"حدث خطأ أثناء تحميل العملاء: {response.StatusCode} - {errorContent}", Severity.Error);
                clients = new List<ClientDto>();
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء تحميل العملاء: {ex.Message}", Severity.Error);
            clients = new List<ClientDto>();
        }
        finally
        {
            isLoading = false;
        }
    }
    #endregion

    #region الأحداث والتفاعلات

    /// <summary>
    /// فتح مربع حوار إضافة أو تعديل عميل
    /// </summary>
    /// <param name="client">العميل المراد تعديله، أو null لإضافة عميل جديد</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// إذا تم تمرير عميل موجود، يتم فتح مربع الحوار في وضع التعديل
    /// وإلا يتم فتحه في وضع الإضافة
    /// يتم نسخ جميع خصائص العميل إلى كائن جديد لتجنب التعديل المباشر على الكائن الأصلي
    /// بعد إغلاق مربع الحوار، إذا لم يتم إلغاء العملية، يتم إعادة تحميل قائمة العملاء
    /// </remarks>
    private async Task OpenClientDialog(ClientDto client)
    {
        var parameters = new DialogParameters();

        if (client != null)
        {
            parameters.Add("ClientDto", new ClientDto
            {
                Id = client.Id,
                Name = client.Name,
                Email = client.Email,
                Phone = client.Phone,
                Address = client.Address,
                Notes = client.Notes,
                IsActive = client.IsActive,
                CreatedAt = client.CreatedAt
            });
        }

        var dialog = await DialogService.ShowAsync<ClientDialogComponent>(
        client == null ? "إضافة عميل جديد" : "تعديل العميل",
        parameters
        );

        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadClients();
        }
    }

    /// <summary>
    /// عرض مربع حوار تأكيد حذف عميل
    /// </summary>
    /// <param name="client">العميل المراد حذفه</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يعرض مربع حوار تأكيد قبل حذف العميل
    /// إذا تم تأكيد الحذف، يتم استدعاء دالة DeleteClient لحذف العميل
    /// </remarks>
    private async Task ConfirmDelete(ClientDto client)
    {
        var parameters = new DialogParameters
{
{ "ContentText", $"هل أنت متأكد من حذف العميل '{client.Name}'؟" },
{ "ButtonText", "حذف" },
{ "Color", Color.Error }
};

        var dialog = await DialogService.ShowAsync<BlogMud.Client.Components.ConfirmationDialog>("تأكيد الحذف", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await DeleteClient(client);
        }
    }

    /// <summary>
    /// حذف عميل من قاعدة البيانات
    /// </summary>
    /// <param name="client">العميل المراد حذفه</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بإرسال طلب حذف إلى الخادم
    /// إذا نجحت العملية، يتم إزالة العميل من القائمة المحلية وعرض رسالة نجاح
    /// وإلا يتم عرض رسالة الخطأ المستلمة من الخادم
    /// </remarks>
    private async Task DeleteClient(ClientDto client)
    {
        try
        {
            var response = await Http.DeleteAsync($"api/Clients/{client.Id}");

            if (response.IsSuccessStatusCode)
            {
                clients.Remove(client);
                _Snackbar.Add("تم حذف العميل بنجاح", Severity.Success);
            }
            else
            {
                var errorMessage = await response.Content.ReadAsStringAsync();
                _Snackbar.Add(errorMessage, Severity.Error);
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء حذف العميل: {ex.Message}", Severity.Error);
        }
    }
    #endregion
}
