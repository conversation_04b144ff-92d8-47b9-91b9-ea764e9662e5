using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;

namespace BlogMud.Data.Repositories
{
    /// <summary>
    /// واجهة مستودع بيانات صفحة "من نحن"
    /// </summary>
    public interface IAboutUsRepository : IRepository<AboutUs>
    {
        /// <summary>
        /// الحصول على المحتوى النشط لصفحة "من نحن"
        /// </summary>
        /// <returns>قائمة بالمحتوى النشط مرتبة حسب ترتيب العرض</returns>
        Task<IEnumerable<AboutUs>> GetActiveAboutUsAsync();

        /// <summary>
        /// الحصول على أول محتوى نشط لصفحة "من نحن"
        /// </summary>
        /// <returns>أول محتوى نشط أو null إذا لم يوجد</returns>
        Task<AboutUs?> GetFirstActiveAboutUsAsync();

        /// <summary>
        /// الحصول على محتوى صفحة "من نحن" مع عناصر الطاقة الإنتاجية
        /// </summary>
        /// <param name="id">معرف المحتوى</param>
        /// <returns>محتوى صفحة "من نحن" مع عناصر الطاقة الإنتاجية</returns>
        Task<AboutUs?> GetAboutUsWithProductionItemsAsync(int id);
    }
}
