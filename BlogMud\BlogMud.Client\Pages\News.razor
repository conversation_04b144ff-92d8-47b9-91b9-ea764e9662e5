@page "/news"

<PageTitle>الأخبار | @(_companyInfo?.Name ?? "شركتنا")</PageTitle>

<!-- News Carousel Section -->
@if (_slideshowsLoading)
{
    <div class="d-flex justify-center align-center news-loading-section"
         style="height: 250px; background-color: #f5f5f5;"
         data-aos="fade-in"
         data-aos-duration="600"
         data-aos-once="false"
         data-aos-mirror="true">
        <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
        <MudText Typo="Typo.h6" Class="mr-4 loading-text">جاري تحميل الشرائح...</MudText>
    </div>
}
else if (_newsSlideshows?.Any() == true)
{
    <div class="hero-slider position-relative overflow-hidden"
         data-aos="fade-in"
         data-aos-duration="800"
         data-aos-once="false"
         data-aos-mirror="true">
        <MudCarousel Class="mud-width-full" Style="height:250px;" ShowArrows="true" ShowDelimiters="true" AutoCycle="true" TData="object">
                @{
                    var allImages = new List<(string ImageUrl, string Title, string Description, string? LinkUrl, string? LinkText)>();
                    foreach (var slideshow in _newsSlideshows.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder))
                    {
                        foreach (var imageUrl in slideshow.Images)
                        {
                            allImages.Add((imageUrl, slideshow.Title, slideshow.Description, slideshow.LinkUrl, slideshow.LinkText));
                        }
                    }
                }

            @foreach (var (imageUrl, title, description, linkUrl, linkText) in allImages)
            {
                <MudCarouselItem Transition="Transition.Slide">
                    <MudImage Src="@imageUrl" Alt="@title" ObjectFit="ObjectFit.Fill" Width="100" Height="100" Class="absolute-fill" />
                    <div class="d-flex flex-column justify-center align-center h-100 carousel-overlay">
                        <MudContainer Class="text-center">
                            <MudText Typo="Typo.h3" Color="Color.Surface" Class="mb-4 carousel-title"
                                     data-aos="fade-down"
                                     data-aos-delay="300"
                                     data-aos-duration="700"
                                     data-aos-once="false"
                                     data-aos-mirror="true">@title</MudText>
                            @if (!string.IsNullOrEmpty(description))
                            {
                                <MudText Color="Color.Surface" Class="mb-8 carousel-description"
                                         data-aos="fade-up"
                                         data-aos-delay="500"
                                         data-aos-duration="700"
                                         data-aos-once="false"
                                         data-aos-mirror="true">@description</MudText>
                            }
                            @if (!string.IsNullOrEmpty(linkUrl) && !string.IsNullOrEmpty(linkText))
                            {
                                <MudButton Variant="Variant.Filled" Color="Color.Primary" Size="Size.Large" Href="@linkUrl"
                                           Class="carousel-button"
                                           data-aos="zoom-in"
                                           data-aos-delay="700"
                                           data-aos-duration="600"
                                           data-aos-once="false"
                                           data-aos-mirror="true">@linkText</MudButton>
                            }
                        </MudContainer>
                    </div>
                </MudCarouselItem>
            }
        </MudCarousel>
    </div>
}

<!-- Articles Filter & Search -->
@* <MudContainer MaxWidth="MaxWidth.Large" Class="py-8 filter-search-section"> *@
@*     <MudGrid data-aos="fade-up" *@
@*              data-aos-duration="700" *@
@*              data-aos-delay="100" *@
@*              data-aos-once="false" *@
@*              data-aos-mirror="true"> *@
@*         <MudItem xs="12" sm="8" Class="search-field-container"> *@
@*             <MudTextField @bind-Value="_searchText" *@
@*                           Label="بحث في المقالات" *@
@*                           Variant="Variant.Outlined" *@
@*                           Adornment="Adornment.End" *@
@*                           AdornmentIcon="@Icons.Material.Filled.Search" *@
@*                           OnAdornmentClick="HandleSearch" *@
@*                           Immediate="true" *@
@*                           OnKeyDown="HandleKeyDown" *@
@*                           Class="search-field" /> *@
@*         </MudItem> *@
@*         <MudItem xs="12" sm="4" Class="category-filter-container"> *@
@*             <MudSelect T="string" *@
@*                        @bind-Value="_selectedCategory" *@
@*                        Label="تصفية حسب التصنيف" *@
@*                        AnchorOrigin="Origin.BottomCenter" *@
@*                        Variant="Variant.Outlined" *@
@*                        Class="category-filter"> *@
@*                 <MudSelectItem Value="@string.Empty">الكل</MudSelectItem> *@
@*                 @foreach (var category in _categories) *@
@*                 { *@
@*                     <MudSelectItem Value="@category">@category</MudSelectItem> *@
@*                 } *@
@*             </MudSelect> *@
@*         </MudItem> *@
@*     </MudGrid> *@
@* </MudContainer> *@

<!-- Articles Listing -->
<MudContainer MaxWidth="MaxWidth.Large" Class="py-8 articles-listing-section">
    @if (_loading)
    {
        <div class="d-flex justify-center my-8 articles-loading"
             data-aos="fade-in"
             data-aos-duration="600"
             data-aos-once="false"
             data-aos-mirror="true">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
        </div>
    }
    else if (_articles != null && _articles.Any())
    {
        <MudGrid Class="articles-grid">
            @{
                var articleDelay = 0;
            }
            @foreach (var article in _articles)
            {
                var currentDelay = articleDelay;
                <MudItem xs="12" sm="6" md="4" Class="mb-6 article-item">
                    <MudCard Elevation="2"
                             Class="h-100 article-card"
                             data-aos="fade-up"
                             data-aos-delay="@currentDelay"
                             data-aos-duration="700"
                             data-aos-once="false"
                             data-aos-mirror="true">
                        <MudCardMedia Image="@article.ImageUrl" Height="200" Class="article-image" />
                        <MudCardContent Class="pb-2 article-content">
                            <div class="d-flex justify-space-between align-center mb-2 article-meta">
                                <MudChip T="string" Size="Size.Small" Color="Color.Primary" Class="article-category">@article.Category</MudChip>
                                <MudText Typo="Typo.caption" Class="article-date">@article.PublishDate.ToString("yyyy-MM-dd")</MudText>
                            </div>
                            <MudText Typo="Typo.h5" Class="mb-2 line-clamp-2 article-title">@article.Title</MudText>
                            <MudText Typo="Typo.body2" Class="line-clamp-3 article-summary">@GetArticleSummary(article.Content)</MudText>
                        </MudCardContent>
                        <MudCardActions Class="article-actions">
                            <MudButton Variant="Variant.Text"
                                       Color="Color.Primary"
                                       OnClick="@(() => NavigateToArticle(article.Id))"
                                       Class="read-more-button">اقرأ المزيد</MudButton>
                        </MudCardActions>
                    </MudCard>
                </MudItem>
                articleDelay += 100;
            }
        </MudGrid>

        <!-- Pagination -->
        <div class="d-flex justify-center mt-8 pagination-container"
             data-aos="fade-up"
             data-aos-delay="200"
             data-aos-duration="600"
             data-aos-once="false"
             data-aos-mirror="true">
            <MudPagination Count="@((int)Math.Ceiling((double)_totalItemsCount / _pageSize))"
                          @bind-Selected="_currentPage"
                          Color="Color.Primary"
                          Class="news-pagination" />
        </div>
    }
    else
    {
        <MudAlert Severity="Severity.Info"
                  Class="my-8 no-articles-alert"
                  data-aos="fade-in"
                  data-aos-duration="600"
                  data-aos-once="false"
                  data-aos-mirror="true">
            لم يتم العثور على مقالات @(!string.IsNullOrEmpty(_searchText) ? $"تحتوي على \"{_searchText}\"" : "")
            @(!string.IsNullOrEmpty(_selectedCategory) ? $"في تصنيف \"{_selectedCategory}\"" : "").
        </MudAlert>
    }
</MudContainer>

<!-- Subscribe Section -->
<div class="py-12 subscribe-section" style="background-color: #ffffff;">
    <MudContainer MaxWidth="MaxWidth.Large">
        <MudGrid Justify="Justify.Center" Class="text-center"
                 data-aos="fade-up"
                 data-aos-duration="800"
                 data-aos-delay="100"
                 data-aos-once="false"
                 data-aos-mirror="true">
            <MudItem xs="12" md="8" Class="subscribe-content">
                <MudText Typo="Typo.h4"
                         Class="mb-4 subscribe-title"
                         data-aos="fade-down"
                         data-aos-delay="200"
                         data-aos-duration="700"
                         data-aos-once="false"
                         data-aos-mirror="true">اشترك في نشرتنا الإخبارية</MudText>
                <MudText Typo="Typo.body1"
                         Class="mb-6 subscribe-description"
                         data-aos="fade-up"
                         data-aos-delay="400"
                         data-aos-duration="700"
                         data-aos-once="false"
                         data-aos-mirror="true">احصل على آخر الأخبار والتحديثات عن أنشطة الشركة مباشرة إلى بريدك الإلكتروني.</MudText>
                <MudPaper Elevation="2"
                          Class="d-flex pa-2 subscribe-form"
                          Style="max-width: 500px; margin: 0 auto; background-color: white; border-radius: 8px;"
                          data-aos="zoom-in"
                          data-aos-delay="600"
                          data-aos-duration="600"
                          data-aos-once="false"
                          data-aos-mirror="true">
                    <MudTextField @bind-Value="_subscribeEmail"
                                  Placeholder="أدخل بريدك الإلكتروني"
                                  Variant="Variant.Text"
                                  Class="flex-grow-1 subscribe-email-field" />
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               OnClick="HandleSubscribe"
                               Class="ml-2 subscribe-button">اشترك</MudButton>
                </MudPaper>
            </MudItem>
        </MudGrid>
    </MudContainer>
</div>

<style>
    /* Base Styles */
    .absolute-fill {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    /* News Loading Section */
    .news-loading-section {
        background: linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%);
        border-radius: 8px;
        margin: 1rem 0;
    }

    .loading-text {
        margin-right: 1rem;
    }

    /* Hero Slider Enhancements */
    .hero-slider {
        border-radius: 0 0 16px 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .carousel-overlay {
        background: linear-gradient(135deg, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0.3) 100%);
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .carousel-title {
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        font-weight: 600;
    }

    .carousel-description {
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        max-width: 600px;
        margin: 0 auto;
    }

    .carousel-button {
        box-shadow: 0 4px 15px rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .carousel-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(149, 55, 53, 0.4);
    }

    /* Filter & Search Section */
    .filter-search-section {
        background-color: #ffffff;
        border-radius: 12px;
        margin: 2rem 0;
        padding: 2rem 1rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .search-field-container,
    .category-filter-container {
        padding: 0 0.5rem;
    }

    .search-field,
    .category-filter {
        transition: all 0.3s ease;
    }

    .search-field:focus-within,
    .category-filter:focus-within {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(149, 55, 53, 0.1);
    }

    /* Articles Section */
    .articles-listing-section {
        background-color: #ffffff;
        min-height: 400px;
    }

    .articles-grid {
        margin-top: 1rem;
    }

    .article-item {
        transition: all 0.3s ease;
    }

    .article-card {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
        background: #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .article-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    }

    .article-image {
        transition: transform 0.3s ease;
    }

    .article-card:hover .article-image {
        transform: scale(1.05);
    }

    .article-content {
        padding: 1.5rem;
    }

    .article-meta {
        margin-bottom: 1rem;
    }

    .article-category {
        font-weight: 600;
    }

    .article-date {
        color: #666;
        font-size: 0.85rem;
    }

    .article-title {
        font-weight: 600;
        color: #333;
        line-height: 1.4;
    }

    .article-summary {
        color: #666;
        line-height: 1.6;
    }

    .article-actions {
        padding: 0 1.5rem 1.5rem;
    }

    .read-more-button {
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .read-more-button:hover {
        transform: translateX(-4px);
    }

    /* Pagination */
    .pagination-container {
        margin-top: 3rem;
        padding: 2rem 0;
    }

    .news-pagination {
        background: #ffffff;
        border-radius: 8px;
        padding: 0.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    /* Subscribe Section */
    .subscribe-section {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        margin-top: 3rem;
    }

    .subscribe-content {
        max-width: 600px;
        margin: 0 auto;
    }

    .subscribe-title {
        color: #333;
        font-weight: 700;
        margin-bottom: 1.5rem;
    }

    .subscribe-description {
        color: #666;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    .subscribe-form {
        border: 2px solid rgba(149, 55, 53, 0.1);
        transition: all 0.3s ease;
    }

    .subscribe-form:hover {
        border-color: rgba(149, 55, 53, 0.2);
        box-shadow: 0 4px 20px rgba(149, 55, 53, 0.1);
    }

    .subscribe-email-field {
        border: none;
    }

    .subscribe-button {
        border-radius: 6px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .subscribe-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(149, 55, 53, 0.3);
    }

    /* Text Utilities */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* No Articles Alert */
    .no-articles-alert {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border: 1px solid rgba(149, 55, 53, 0.1);
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
    }

    /* RTL Support */
    [dir="rtl"] .loading-text {
        margin-right: 0;
        margin-left: 1rem;
    }

    [dir="rtl"] .carousel-button:hover {
        transform: translateY(-2px);
    }

    [dir="rtl"] .read-more-button:hover {
        transform: translateX(4px);
    }

    [dir="rtl"] .article-meta {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .subscribe-form {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .subscribe-button {
        margin-left: 0;
        margin-right: 0.5rem;
    }

    [dir="rtl"] .search-field-container,
    [dir="rtl"] .category-filter-container {
        text-align: right;
    }

    /* AOS Animation Support */
    [data-aos] {
        pointer-events: none;
    }

    [data-aos].aos-animate {
        pointer-events: auto;
    }

    /* Custom AOS animations for News page */
    .article-card[data-aos] {
        transition: transform 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
    }

    .article-card[data-aos]:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .hero-slider {
            border-radius: 0;
        }

        .carousel-title {
            font-size: 1.5rem !important;
        }

        .carousel-description {
            font-size: 0.9rem;
            margin-bottom: 1.5rem !important;
        }

        .filter-search-section {
            padding: 1.5rem 1rem;
            margin: 1rem 0;
        }

        .search-field-container,
        .category-filter-container {
            margin-bottom: 1rem;
        }

        .article-card {
            margin-bottom: 1.5rem;
        }

        .subscribe-section {
            padding: 2rem 0;
        }

        .subscribe-form {
            flex-direction: column;
            gap: 1rem;
        }

        .subscribe-button {
            margin: 0;
            width: 100%;
        }

        /* Reduce animation duration on mobile */
        [data-aos] {
            animation-duration: 0.5s !important;
        }
    }

    @@media (max-width: 480px) {
        .filter-search-section {
            padding: 1rem 0.5rem;
        }

        .article-content {
            padding: 1rem;
        }

        .article-actions {
            padding: 0 1rem 1rem;
        }

        .subscribe-title {
            font-size: 1.25rem !important;
        }

        .subscribe-description {
            font-size: 0.9rem;
        }
    }

    /* Tablet Responsive */
    @@media (min-width: 769px) and (max-width: 1024px) {
        .filter-search-section {
            padding: 2rem 1.5rem;
        }

        .article-card {
            height: 100%;
        }

        .subscribe-section {
            padding: 3rem 0;
        }
    }

    /* Desktop Enhancements */
    @@media (min-width: 1025px) {
        .hero-slider {
            border-radius: 0 0 20px 20px;
        }

        .filter-search-section {
            padding: 2.5rem 2rem;
        }

        .article-card:hover {
            transform: translateY(-10px);
        }

        .subscribe-section {
            padding: 4rem 0;
        }
    }

    /* Accessibility Enhancements */
    @@media (prefers-reduced-motion: reduce) {
        * {
            transition: none !important;
            animation: none !important;
        }

        .article-card:hover,
        .carousel-button:hover,
        .subscribe-button:hover {
            transform: none;
        }

        [data-aos] {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }

    /* High Contrast Mode */
    @@media (prefers-contrast: high) {
        .article-card {
            border: 2px solid #000;
        }

        .subscribe-form {
            border: 2px solid #000;
        }

        .hero-slider {
            border: 2px solid #000;
        }
    }

    /* Focus States for Accessibility */
    .article-card:focus-within,
    .search-field:focus-within,
    .category-filter:focus-within,
    .subscribe-form:focus-within {
        outline: 2px solid var(--mud-palette-primary);
        outline-offset: 2px;
    }

    /* Print Styles */
    @@media print {
        .hero-slider,
        .subscribe-section,
        .pagination-container {
            display: none;
        }

        .article-card {
            break-inside: avoid;
            box-shadow: none;
            border: 1px solid #000;
        }
    }
</style>


