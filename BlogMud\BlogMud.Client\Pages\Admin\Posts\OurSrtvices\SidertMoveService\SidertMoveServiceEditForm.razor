@using BlogMud.Shared.DTOs
@using Microsoft.AspNetCore.Components.Forms
@inject HttpClient Http
@inject ISnackbar Snackbar

<MudDialog Class="service-edit-dialog" MaxWidth="MaxWidth.Large">
    <TitleContent>
        <div class="dialog-title-container">
            <MudIcon Icon="@(IsCreateMode ? Icons.Material.Filled.Add : Icons.Material.Filled.Edit)"
                     Class="dialog-title-icon" />
            <MudText Typo="Typo.h5" Class="dialog-title-text">
                @(IsCreateMode ? "إضافة شريحة خدمة جديدة" : "تعديل شريحة الخدمة")
            </MudText>
        </div>
    </TitleContent>
    <DialogContent>
        <div class="dialog-content-container">
            <MudForm @ref="_form" Model="SidertMoveService" @bind-IsValid="@_success" Class="slider-form">

                <!-- Tabbed Interface -->
                <MudTabs Elevation="1" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6" @bind-ActivePanelIndex="_activeTabIndex">

                    <!-- Tab 1: Basic Information -->
                    <MudTabPanel Text="المعلومات الأساسية" Icon="@Icons.Material.Filled.Info">
                        <MudGrid Spacing="3" Class="form-grid">
                            <!-- العنوان -->
                            <MudItem xs="12">
                                <MudTextField @bind-Value="SidertMoveService.Title"
                                             Label="عنوان الشريحة"
                                             Required="true"
                                             RequiredError="العنوان مطلوب"
                                             MaxLength="100"
                                             Counter="100"
                                             HelperText="الحد الأقصى 100 حرف"
                                             Variant="Variant.Outlined"
                                             Class="form-field"
                                             Immediate="true" />
                            </MudItem>

                            <!-- الوصف -->
                            <MudItem xs="12">
                                <MudTextField @bind-Value="SidertMoveService.Description"
                                             Label="وصف الشريحة"
                                             Lines="3"
                                             MaxLength="500"
                                             Counter="500"
                                             HelperText="الحد الأقصى 500 حرف"
                                             Variant="Variant.Outlined"
                                             Class="form-field" />
                            </MudItem>

                            <!-- ترتيب العرض ومدة العرض -->
                            <MudItem xs="12" sm="6">
                                <MudNumericField @bind-Value="SidertMoveService.DisplayOrder"
                                                Label="ترتيب العرض"
                                                Min="0"
                                                Max="999"
                                                HideSpinButtons="false"
                                                Variant="Variant.Outlined"
                                                Class="form-field"
                                                HelperText="ترتيب ظهور الشريحة" />
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <MudNumericField @bind-Value="SidertMoveService.Duration"
                                                Label="مدة العرض (ثانية)"
                                                Min="1"
                                                Max="60"
                                                HideSpinButtons="false"
                                                Variant="Variant.Outlined"
                                                Class="form-field"
                                                HelperText="مدة عرض الشريحة بالثواني" />
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                    <!-- Tab 2: Media -->
                    <MudTabPanel Text="الوسائط" Icon="@Icons.Material.Filled.PhotoLibrary">
                        <MudGrid Spacing="3" Class="media-grid">

                            <!-- Image Upload Section -->
                            <MudItem xs="12">
                                <MudPaper Class="upload-section image-upload" Elevation="0">
                                    <div class="upload-header">
                                        <MudIcon Icon="@Icons.Material.Filled.PhotoLibrary" Class="upload-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="upload-title">صور الشريحة</MudText>
                                    </div>

                                    <div class="upload-content">
                                        <MudFileUpload T="IReadOnlyList<IBrowserFile>"
                                                      Accept=".png, .jpg, .jpeg, .gif, .webp"
                                                      FilesChanged="UploadMultipleImages"
                                                      MaximumFileCount="10"
                                                      Class="file-upload">
                                            <ActivatorContent>
                                                <MudButton Variant="Variant.Outlined"
                                                           Color="Color.Primary"
                                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                                           FullWidth="true"
                                                           Class="upload-button">
                                                    رفع صور متعددة (حد أقصى 10)
                                                </MudButton>
                                            </ActivatorContent>
                                        </MudFileUpload>

                                        @if (_imageUploading)
                                        {
                                            <div class="upload-progress">
                                                <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="mt-2" />
                                                <MudText Typo="Typo.caption" Class="mt-1 upload-status">جاري رفع الصور...</MudText>
                                            </div>
                                        }

                                        <!-- عرض الصور المرفوعة -->
                                        @if (SidertMoveService.Images.Any())
                                        {
                                            <div class="uploaded-images-section">
                                                <MudText Typo="Typo.subtitle2" Class="mb-2 images-count">
                                                    الصور المرفوعة (@SidertMoveService.Images.Count صورة)
                                                </MudText>
                                                <MudGrid Spacing="2">
                                                    @foreach (var (imageUrl, index) in SidertMoveService.Images.Select((url, i) => (url, i)))
                                                    {
                                                        <MudItem xs="6" sm="4" md="3">
                                                            <MudCard Class="image-card" Elevation="2">
                                                                <MudCardMedia Image="@imageUrl" Height="120" Class="image-preview" />
                                                                <MudCardActions Class="image-actions">
                                                                    <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                                                  Color="Color.Error"
                                                                                  Size="Size.Small"
                                                                                  OnClick="@(() => RemoveImage(index))"
                                                                                  Title="حذف الصورة"
                                                                                  Class="delete-image-btn" />
                                                                </MudCardActions>
                                                            </MudCard>
                                                        </MudItem>
                                                    }
                                                </MudGrid>
                                            </div>
                                        }
                                    </div>
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                    <!-- Tab 3: Settings -->
                    <MudTabPanel Text="الإعدادات" Icon="@Icons.Material.Filled.Settings">
                        <MudGrid Spacing="3" Class="settings-grid">

                            <!-- رابط اختياري -->
                            <MudItem xs="12" md="8">
                                <MudTextField @bind-Value="SidertMoveService.LinkUrl"
                                             Label="رابط اختياري (URL)"
                                             MaxLength="500"
                                             Counter="500"
                                             Placeholder="https://example.com"
                                             Variant="Variant.Outlined"
                                             Class="form-field"
                                             HelperText="رابط يتم الانتقال إليه عند النقر على الشريحة" />
                            </MudItem>

                            <!-- نص الرابط -->
                            <MudItem xs="12" md="4">
                                <MudTextField @bind-Value="SidertMoveService.LinkText"
                                             Label="نص الرابط"
                                             MaxLength="100"
                                             Counter="100"
                                             Placeholder="اقرأ المزيد"
                                             Variant="Variant.Outlined"
                                             Class="form-field"
                                             HelperText="النص الذي يظهر على زر الرابط" />
                            </MudItem>

                            <!-- ملاحظات -->
                            <MudItem xs="12">
                                <MudTextField @bind-Value="SidertMoveService.Notes"
                                             Label="ملاحظات إضافية"
                                             Lines="3"
                                             MaxLength="1000"
                                             Counter="1000"
                                             Variant="Variant.Outlined"
                                             Class="form-field"
                                             HelperText="ملاحظات داخلية للمراجعة" />
                            </MudItem>

                            <!-- حالة النشاط -->
                            <MudItem xs="12">
                                <MudPaper Class="form-section status-section" Elevation="0">
                                    <div class="status-header">
                                        <MudIcon Icon="@Icons.Material.Filled.Visibility" Class="status-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="status-title">حالة النشر</MudText>
                                    </div>
                                    <div class="status-content">
                                        <MudSwitch @bind-Value="SidertMoveService.IsActive"
                                                  Label="نشط"
                                                  Color="Color.Primary"
                                                  Class="status-switch" />
                                        <MudText Typo="Typo.caption" Color="Color.Secondary" Class="status-description">
                                            عند التفعيل، ستظهر هذه الشريحة في صفحة الخدمات
                                        </MudText>
                                    </div>
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                </MudTabs>

            </MudForm>
        </div>
    </DialogContent>
    <DialogActions>
        <div class="dialog-actions">
            <div class="action-buttons">
                <MudButton Variant="Variant.Text"
                           Color="Color.Default"
                           OnClick="Cancel"
                           StartIcon="@Icons.Material.Filled.Cancel"
                           Class="cancel-button">
                    إلغاء
                </MudButton>
                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           OnClick="Submit"
                           Disabled="@(!_success)"
                           StartIcon="@(IsCreateMode ? Icons.Material.Filled.Add : Icons.Material.Filled.Save)"
                           Class="submit-button">
                    @(IsCreateMode ? "إنشاء الشريحة" : "حفظ التعديلات")
                </MudButton>
            </div>
        </div>
    </DialogActions>
</MudDialog>

<SidertMoveServiceEditFormCSS />

@code {
    #region الخصائص والمتغيرات

    /// <summary>
    /// مرجع إلى نموذج MudForm المستخدم للتحقق من صحة البيانات
    /// </summary>
    private MudForm _form;

    /// <summary>
    /// مؤشر على حالة رفع الصور
    /// </summary>
    private bool _imageUploading = false;

    /// <summary>
    /// مؤشر على صحة النموذج
    /// </summary>
    private bool _success = true;

    /// <summary>
    /// فهرس التبويب النشط (0 = المعلومات الأساسية، 1 = الوسائط، 2 = الإعدادات)
    /// </summary>
    private int _activeTabIndex = 0;

    /// <summary>
    /// مثيل مربع الحوار MudDialog الذي يتم تمريره من خلال CascadingParameter
    /// </summary>
    [CascadingParameter] public required IMudDialogInstance MudDialog { get; set; }

    /// <summary>
    /// بيانات شريحة الخدمة التي يتم تعديلها أو إنشاؤها
    /// </summary>
    [Parameter]
    public SidertMoveServicesDto SidertMoveService { get; set; }

    /// <summary>
    /// مؤشر على ما إذا كان الوضع هو إنشاء شريحة جديدة أم تعديل موجودة
    /// </summary>
    [Parameter]
    public bool IsCreateMode { get; set; } = false;

    #endregion

    #region الأحداث والتفاعلات

    /// <summary>
    /// حفظ بيانات الشريحة
    /// </summary>
    private async Task Submit()
    {
        if (_form != null)
            await _form.Validate();
        if (!_success) return;

        try
        {
            // التحقق من الحقول المطلوبة
            if (string.IsNullOrEmpty(SidertMoveService.Title))
            {
                Snackbar.Add("يجب إدخال عنوان الشريحة", Severity.Error);
                _activeTabIndex = 0; // الانتقال إلى تبويب المعلومات الأساسية
                return;
            }

            if (string.IsNullOrEmpty(SidertMoveService.ImageUrls))
            {
                Snackbar.Add("يجب إضافة صور للشريحة", Severity.Error);
                _activeTabIndex = 1; // الانتقال إلى تبويب الوسائط
                return;
            }

            // التحقق من طول العنوان
            if (SidertMoveService.Title.Length > 100)
            {
                Snackbar.Add("يجب أن لا يتجاوز عنوان الشريحة 100 حرف", Severity.Error);
                _activeTabIndex = 0;
                return;
            }

            // التحقق من صحة الرابط إذا تم إدخاله
            if (!string.IsNullOrEmpty(SidertMoveService.LinkUrl))
            {
                if (!Uri.TryCreate(SidertMoveService.LinkUrl, UriKind.Absolute, out _))
                {
                    Snackbar.Add("الرابط المدخل غير صحيح", Severity.Error);
                    _activeTabIndex = 2; // الانتقال إلى تبويب الإعدادات
                    return;
                }
            }

            MudDialog.Close(DialogResult.Ok(SidertMoveService));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"حدث خطأ أثناء حفظ الشريحة: {ex.Message}", Severity.Error);
        }
    }

    /// <summary>
    /// إلغاء العملية وإغلاق مربع الحوار
    /// </summary>
    private void Cancel()
    {
        MudDialog.Cancel();
    }

    #endregion

    #region معالجة الملفات

    /// <summary>
    /// تحميل مجموعة من ملفات الصور للشريحة
    /// </summary>
    /// <param name="files">قائمة ملفات الصور المراد تحميلها</param>
    /// <returns>مهمة غير متزامنة</returns>
    private async Task UploadMultipleImages(IReadOnlyList<IBrowserFile> files)
    {
        if (files == null || files.Count == 0)
            return;

        try
        {
            _imageUploading = true;
            StateHasChanged();

            var uploadedUrls = new List<string>();
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
            const long maxFileSize = 5 * 1024 * 1024; // 5MB

            // التحقق من عدد الصور الحالية + الجديدة
            var currentImagesCount = SidertMoveService.Images.Count();
            if (currentImagesCount + files.Count > 10)
            {
                Snackbar.Add($"لا يمكن رفع أكثر من 10 صور. لديك حالياً {currentImagesCount} صورة", Severity.Warning);
                return;
            }

            foreach (var file in files)
            {
                // Validate file size
                if (file.Size > maxFileSize)
                {
                    Snackbar.Add($"الملف {file.Name} كبير جداً. الحد الأقصى 5 ميجابايت", Severity.Warning);
                    continue;
                }

                // Validate file extension
                var extension = Path.GetExtension(file.Name).ToLowerInvariant();
                if (!allowedExtensions.Contains(extension))
                {
                    Snackbar.Add($"نوع الملف {file.Name} غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WebP", Severity.Warning);
                    continue;
                }

                // Create form data
                using var content = new MultipartFormDataContent();
                using var fileStream = file.OpenReadStream(maxFileSize);
                using var streamContent = new StreamContent(fileStream);
                content.Add(streamContent, "file", file.Name);

                // Upload file to the specific directory for SiderMoveOurServicesImg
                var response = await Http.PostAsync("api/upload/sidermove-slideshow", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    uploadedUrls.Add(result.Trim('"')); // Remove quotes from JSON string
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    Snackbar.Add($"خطأ في رفع الصورة {file.Name}: {error}", Severity.Warning);
                }
            }

            if (uploadedUrls.Any())
            {
                // Append to existing images
                var existingImages = SidertMoveService.Images.ToList();
                existingImages.AddRange(uploadedUrls);
                SidertMoveService.ImageUrls = string.Join(";", existingImages);

                Snackbar.Add($"تم رفع {uploadedUrls.Count} صورة بنجاح", Severity.Success);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في رفع الصور: {ex.Message}", Severity.Error);
        }
        finally
        {
            _imageUploading = false;
            StateHasChanged();
        }
    }

    /// <summary>
    /// حذف صورة من قائمة الصور المرفوعة
    /// </summary>
    /// <param name="index">فهرس الصورة المراد حذفها</param>
    private void RemoveImage(int index)
    {
        try
        {
            var images = SidertMoveService.Images.ToList();
            if (index >= 0 && index < images.Count)
            {
                var removedImageUrl = images[index];
                images.RemoveAt(index);
                SidertMoveService.ImageUrls = string.Join(";", images);
                StateHasChanged();
                Snackbar.Add("تم حذف الصورة بنجاح", Severity.Success);

                // يمكن إضافة منطق لحذف الملف من الخادم هنا إذا لزم الأمر
                // await DeleteImageFromServer(removedImageUrl);
            }
            else
            {
                Snackbar.Add("فهرس الصورة غير صحيح", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف الصورة: {ex.Message}", Severity.Error);
        }
    }

    #endregion

}
