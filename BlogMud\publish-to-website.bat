@echo off
echo ========================================
echo Publishing BlogMud to ahmedakear.runasp.net
echo ========================================

echo Step 1: Cleaning previous builds...
dotnet clean ./BlogMud/BlogMud.csproj --configuration Release

echo Step 2: Removing old obj and bin folders...
rmdir /s /q BlogMud\obj BlogMud\bin BlogMud.Client\obj BlogMud.Client\bin BlogMud.Shared\obj BlogMud.Shared\bin 2>nul

echo Step 3: Restoring packages...
dotnet restore ./BlogMud/BlogMud.csproj

echo Step 4: Building project...
dotnet build ./BlogMud/BlogMud.csproj --configuration Release

echo Step 5: Publishing project...
dotnet publish ./BlogMud/BlogMud.csproj --configuration Release

echo ========================================
echo Local publishing completed successfully!
echo ========================================
echo.
echo To publish to the website:
echo 1. Open Visual Studio
echo 2. Right-click on BlogMud project
echo 3. Select "Publish"
echo 4. Choose "WebDeploy" profile
echo 5. Click "Publish"
echo.
echo Note: You may need to enter the password: Aa123456
echo ========================================
pause
