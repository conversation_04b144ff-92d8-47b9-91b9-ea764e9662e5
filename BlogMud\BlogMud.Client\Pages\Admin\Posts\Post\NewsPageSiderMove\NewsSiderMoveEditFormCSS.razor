<style>
    /* NewsSiderMoveEditForm - Component Scoped Styles */

    /* Dialog Container */
    .news-slider-dialog {
        direction: rtl;
        font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Helvetica', 'Arial', sans-serif;
    }

        .news-slider-dialog .mud-dialog-content {
            padding: 0;
            max-height: 80vh;
            overflow-y: auto;
        }

    /* Dialog Title */
    .dialog-title-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem 0;
    }

    .dialog-title-icon {
        color: var(--mud-palette-primary);
        font-size: 1.5rem;
    }

    .dialog-title-text {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
        margin: 0;
    }

    /* Dialog Content */
    .dialog-content-container {
        padding: 1.5rem;
        background: var(--mud-palette-background-grey);
    }

    /* Form Styling */
    .slider-form {
        width: 100%;
    }

    .form-grid {
        gap: 1.5rem;
    }

    .form-field {
        width: 100%;
    }

        .form-field .mud-input-control {
            border-radius: 8px;
        }

        .form-field .mud-input-control-input-container {
            border-radius: 8px;
        }

    /* Tab Styling */
    .news-slider-dialog .mud-tabs {
        margin-bottom: 1.5rem;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

        .news-slider-dialog .mud-tabs .mud-tab {
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            border-radius: 8px 8px 0 0;
        }

            .news-slider-dialog .mud-tabs .mud-tab:hover {
                background: var(--mud-palette-action-hover);
            }

            .news-slider-dialog .mud-tabs .mud-tab.mud-tab-active {
                background: var(--mud-palette-primary);
                color: white;
                font-weight: 600;
            }

        .news-slider-dialog .mud-tabs .mud-tab-panel {
            background: white;
            border-radius: 0 0 12px 12px;
            min-height: 300px;
        }

    /* Upload Section */
    .upload-section {
        background: white;
        border: 2px dashed var(--mud-palette-divider);
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

        .upload-section:hover {
            border-color: var(--mud-palette-primary);
            background: var(--mud-palette-primary-lighten-5);
        }

    .upload-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .upload-icon {
        color: var(--mud-palette-primary);
        font-size: 1.25rem;
    }

    .upload-title {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
        margin: 0;
    }

    .upload-content {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .upload-button {
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 2px solid var(--mud-palette-primary);
    }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

    /* Upload Progress */
    .upload-progress {
        margin-top: 1rem;
    }

    .upload-status {
        color: var(--mud-palette-text-secondary);
        font-style: italic;
    }

    /* Uploaded Images Section */
    .uploaded-images-section {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid var(--mud-palette-divider);
    }

    .images-count {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
    }

    .image-card {
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid var(--mud-palette-divider);
    }

        .image-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

    .image-preview {
        border-radius: 8px 8px 0 0;
        object-fit: cover;
    }

    .image-actions {
        padding: 0.5rem;
        justify-content: center;
        background: var(--mud-palette-background-grey);
    }

    .delete-image-btn {
        transition: all 0.3s ease;
    }

        .delete-image-btn:hover {
            transform: scale(1.1);
        }

    /* Form Section */
    .form-section {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border: 1px solid var(--mud-palette-divider);
        transition: all 0.3s ease;
    }

        .form-section:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

    /* Status Section */
    .status-section {
        border: 2px solid var(--mud-palette-info);
        background: var(--mud-palette-info-lighten-5);
    }

    .status-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .status-icon {
        color: var(--mud-palette-info);
        font-size: 1.25rem;
    }

    .status-title {
        color: var(--mud-palette-text-primary);
        font-weight: 600;
        margin: 0;
    }

    .status-content {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .status-switch {
        align-self: flex-start;
    }

    .status-description {
        margin-top: 0.5rem;
        font-style: italic;
    }

    /* Dialog Actions */
    .dialog-actions {
        padding: 1rem 1.5rem;
        background: var(--mud-palette-background-grey);
        border-top: 1px solid var(--mud-palette-divider);
    }

    .action-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        align-items: center;
    }

    .cancel-button {
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
        min-width: 120px;
    }

        .cancel-button:hover {
            background: var(--mud-palette-action-hover);
        }

    .submit-button {
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        min-width: 140px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

        .submit-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .submit-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

    /* Responsive Design */

    /* Mobile Phones (up to 599px) */
    @@media (max-width: 599px) {
        /* Full Screen Dialog for Mobile */
        .news-slider-dialog {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            max-width: 100vw !important;
            max-height: 100vh !important;
            margin: 0 !important;
            border-radius: 0 !important;
        }

        .news-slider-dialog .mud-dialog-container {
            width: 100vw !important;
            height: 100vh !important;
            max-width: 100vw !important;
            max-height: 100vh !important;
            margin: 0 !important;
            border-radius: 0 !important;
        }

        .news-slider-dialog .mud-dialog {
            width: 100vw !important;
            height: 100vh !important;
            max-width: 100vw !important;
            max-height: 100vh !important;
            margin: 0 !important;
            border-radius: 0 !important;
            display: flex !important;
            flex-direction: column !important;
        }

        .news-slider-dialog .mud-dialog-content {
            flex: 1 !important;
            max-height: none !important;
            height: auto !important;
            overflow-y: auto !important;
            padding: 0 !important;
        }

        .dialog-content-container {
            padding: 1rem;
            height: 100%;
            overflow-y: auto;
        }

        .form-section {
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .upload-section {
            padding: 1rem;
        }

        .image-preview {
            height: 100px;
        }

        .action-buttons {
            flex-direction: column-reverse;
            gap: 0.75rem;
        }

        .cancel-button,
        .submit-button {
            width: 100%;
            min-width: unset;
        }

        /* Mobile Tab Adjustments */
        .news-slider-dialog .mud-tabs .mud-tab {
            font-size: 0.8rem;
            padding: 0.4rem 0.75rem;
            height: 38px;
            min-height: 38px;
        }

        .news-slider-dialog .mud-tabs .mud-tab .mud-icon {
            font-size: 0.9rem;
        }

        /* Dialog Actions Fixed at Bottom */
        .news-slider-dialog .mud-dialog-actions {
            position: sticky !important;
            bottom: 0 !important;
            background: var(--mud-palette-background-grey) !important;
            border-top: 1px solid var(--mud-palette-divider) !important;
            z-index: 1000 !important;
        }
    }

    /* Tablets (600px to 959px) */
    @@media (min-width: 600px) and (max-width: 959px) {
        .dialog-content-container {
            padding: 1.25rem;
        }

        .form-section {
            padding: 1.25rem;
        }

        .image-preview {
            height: 110px;
        }

        /* Tablet Tab Adjustments */
        .news-slider-dialog .mud-tabs .mud-tab {
            font-size: 0.875rem;
            padding: 0.45rem 0.875rem;
            height: 40px;
            min-height: 40px;
        }
    }

    /* Desktop (960px and up) */
    @@media (min-width: 960px) {
        .news-slider-dialog .mud-dialog-content {
            max-height: 85vh;
        }

        .dialog-content-container {
            padding: 2rem;
        }

        .form-section {
            padding: 2rem;
        }

        .upload-section {
            padding: 1.5rem;
        }
    }

    /* High Contrast Mode Support */
    @@media (prefers-contrast: high) {
        .form-section, .upload-section {
            border-width: 3px;
            border-style: solid;
        }

        .image-preview {
            border: 2px solid var(--mud-palette-text-primary);
        }
    }

    /* Reduced Motion Support */
    @@media (prefers-reduced-motion: reduce) {
        .form-section, .upload-button, .image-preview, .submit-button {
            transition: none;
        }

        .upload-button:hover,
        .submit-button:hover:not(:disabled) {
            transform: none;
        }
    }

    /* Dark Theme Support */
    @@media (prefers-color-scheme: dark) {
        .upload-section {
            background: var(--mud-palette-dark-surface);
            border-color: var(--mud-palette-dark-divider);
        }

        .form-section {
            background: var(--mud-palette-dark-surface);
            border-color: var(--mud-palette-dark-divider);
        }

        .uploaded-images-section {
            border-top-color: var(--mud-palette-dark-divider);
        }
    }

    /* Touch Target Optimization for Mobile */
    .upload-button,
    .cancel-button,
    .submit-button {
        min-height: 44px;
        min-width: 44px;
    }

    /* Mobile Full Screen Enhancements */
    @@media (max-width: 599px) {
        /* Prevent body scroll when dialog is open */
        body:has(.news-slider-dialog) {
            overflow: hidden !important;
        }

        /* Enhanced mobile scrolling */
        .news-slider-dialog .dialog-content-container {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }

        /* Mobile-optimized tab navigation */
        .news-slider-dialog .mud-tabs {
            position: sticky;
            top: 0;
            z-index: 100;
            background: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Improved mobile form spacing */
        .news-slider-dialog .mud-grid .mud-item {
            padding: 0.5rem !important;
        }

        /* Mobile-friendly image grid */
        .uploaded-images-section .mud-grid {
            gap: 0.5rem !important;
        }

        .uploaded-images-section .mud-item {
            padding: 0.25rem !important;
        }

        /* Enhanced mobile touch interactions */
        .news-slider-dialog .mud-button,
        .news-slider-dialog .mud-icon-button {
            min-height: 44px;
            min-width: 44px;
        }

        /* Mobile status section optimization */
        .status-section {
            padding: 1rem !important;
            margin-bottom: 1rem !important;
        }

        /* Mobile upload section improvements */
        .upload-section {
            border-radius: 8px !important;
            margin-bottom: 1rem !important;
        }
    }

    /* Success States */
    .upload-section.success {
        border-color: var(--mud-palette-success);
        background: var(--mud-palette-success-lighten-5);
    }

    /* Smooth Animations */
    .form-section,
    .upload-section,
    .image-card,
    .uploaded-images-section {
        animation: fadeInUp 0.3s ease-out;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Improved Scrollbar for Dialog Content */
    .news-slider-dialog .mud-dialog-content::-webkit-scrollbar {
        width: 8px;
    }

    .news-slider-dialog .mud-dialog-content::-webkit-scrollbar-track {
        background: var(--mud-palette-background-grey);
        border-radius: 4px;
    }

    .news-slider-dialog .mud-dialog-content::-webkit-scrollbar-thumb {
        background: var(--mud-palette-divider);
        border-radius: 4px;
    }

        .news-slider-dialog .mud-dialog-content::-webkit-scrollbar-thumb:hover {
            background: var(--mud-palette-text-secondary);
        }

    /* RTL Specific Adjustments */
    [dir="rtl"] .dialog-title-container {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .upload-header {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .status-header {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .action-buttons {
        flex-direction: row-reverse;
    }

    /* Print Styles */
    @@media print {
        .dialog-actions {
            display: none;
        }

        .upload-section {
            border-style: solid;
        }
    }

    /* Focus States for Accessibility */
    .upload-button:focus,
    .cancel-button:focus,
    .submit-button:focus {
        outline: 2px solid var(--mud-palette-primary);
        outline-offset: 2px;
    }

    /* Loading States */
    .upload-section.loading {
        opacity: 0.7;
        pointer-events: none;
    }

    /* Error States */
    .form-field.error .mud-input-control {
        border-color: var(--mud-palette-error);
    }

    /* Validation Messages */
    .mud-input-helper-text.mud-input-error {
        color: var(--mud-palette-error);
        font-weight: 500;
    }

    /* Mobile Viewport Meta Support */
    @@media (max-width: 599px) {
        /* Ensure proper mobile viewport handling */
        .news-slider-dialog {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            text-size-adjust: 100%;
        }

        /* Mobile keyboard handling */
        .news-slider-dialog .mud-input-control-input-container input,
        .news-slider-dialog .mud-input-control-input-container textarea {
            font-size: 16px !important; /* Prevents zoom on iOS */
        }

        /* Mobile safe area support */
        .news-slider-dialog .dialog-content-container {
            padding-top: max(1rem, env(safe-area-inset-top));
            padding-bottom: max(1rem, env(safe-area-inset-bottom));
            padding-left: max(1rem, env(safe-area-inset-left));
            padding-right: max(1rem, env(safe-area-inset-right));
        }

        .news-slider-dialog .mud-dialog-actions {
            padding-bottom: max(1rem, env(safe-area-inset-bottom));
            padding-left: max(1.5rem, env(safe-area-inset-left));
            padding-right: max(1.5rem, env(safe-area-inset-right));
        }

        /* Mobile orientation support */
        @@media (orientation: landscape) {
            .news-slider-dialog .dialog-content-container {
                padding-top: 0.5rem;
                padding-bottom: 0.5rem;
            }
        }

        /* Mobile accessibility improvements */
        .news-slider-dialog .mud-tabs .mud-tab {
            position: relative;
        }

        .news-slider-dialog .mud-tabs .mud-tab:focus {
            outline: 2px solid var(--mud-palette-primary);
            outline-offset: -2px;
        }

        /* Mobile performance optimizations */
        .news-slider-dialog * {
            -webkit-tap-highlight-color: transparent;
        }

        .news-slider-dialog .image-card {
            will-change: transform;
        }

        .news-slider-dialog .upload-button {
            will-change: transform;
        }
    }
</style>
