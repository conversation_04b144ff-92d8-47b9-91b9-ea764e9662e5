/* About Us Edit Form - Component Scoped Styles */

/* Main Container Styles */
.about-us-edit-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.about-us-edit-container-mobile {
    padding: 0.5rem;
}

/* Header Styles */
.about-us-header {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--mud-palette-primary-lighten-5) 0%, var(--mud-palette-primary-lighten-4) 100%);
}

.about-us-header-container-mobile {
    padding: 1rem;
    margin-bottom: 1rem;
}

/* Expansion Panels Styles */
.about-us-expansion-panels {
    margin-bottom: 1rem;
}

.about-us-expansion-panels-mobile {
    margin-bottom: 0.5rem;
}

.about-us-panel {
    margin-bottom: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.about-us-panel:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Upload and Preview Styles */
.about-us-upload-paper {
    padding: 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.about-us-upload-button {
    min-height: 44px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.about-us-upload-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.about-us-preview-paper {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 6px;
    background-color: var(--mud-palette-grey-lighten-5);
    border: 1px solid var(--mud-palette-grey-lighten-3);
}

.about-us-placeholder-paper {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 6px;
    background-color: var(--mud-palette-grey-lighten-5);
    border: 2px dashed var(--mud-palette-grey-lighten-2);
}

.about-us-logo-preview {
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.about-us-logo-preview:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Text Field Styles */
.about-us-text-field {
    transition: all 0.3s ease;
}

.about-us-text-field:focus-within {
    transform: translateY(-1px);
}

/* Switch Styles */
.about-us-switch {
    min-height: 44px;
}

/* Production Items Styles */
.production-items-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.production-item-card {
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
}

.production-item-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Inline Add Item Form Styles */
.add-item-form {
    background-color: var(--mud-palette-grey-lighten-5);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid var(--mud-palette-grey-lighten-3);
    animation: slideDown 0.3s ease-in-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 1000px;
    }
}

/* Mobile Card Styles */
.about-us-mobile-card {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.about-us-mobile-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Action Button Styles */
.about-us-action-button {
    min-height: 44px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.about-us-action-button:hover {
    transform: translateY(-2px);
}

.about-us-add-button {
    min-height: 44px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.about-us-add-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.about-us-action-icon-button {
    min-width: 44px;
    min-height: 44px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.about-us-action-icon-button:hover {
    transform: scale(1.1);
}

/* Mobile Action Styles */
.about-us-mobile-action-button {
    min-height: 44px;
    border-radius: 6px;
}

.about-us-mobile-action-bar {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-start;
}

/* Metadata Styles */
.about-us-metadata-paper {
    padding: 1.5rem;
    border-radius: 8px;
    background-color: var(--mud-palette-grey-lighten-5);
}

.metadata-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Accessibility Improvements */
.about-us-action-button:focus,
.about-us-add-button:focus,
.about-us-upload-button:focus {
    outline: 2px solid var(--mud-palette-primary);
    outline-offset: 2px;
}

/* RTL Support */
[dir="rtl"] .about-us-edit-container {
    text-align: right;
}

[dir="rtl"] .about-us-mobile-action-bar {
    justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 767px) {
    .about-us-edit-container {
        padding: 0.5rem;
    }

    .about-us-header {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .about-us-upload-paper,
    .about-us-metadata-paper {
        padding: 1rem;
    }

    .production-item-card {
        margin-bottom: 1rem;
    }

    .about-us-mobile-action-bar {
        justify-content: center;
    }
}

@media (min-width: 768px) and (max-width: 1023px) {
    .about-us-edit-container {
        padding: 1rem;
    }

    .about-us-header {
        padding: 1.25rem;
    }
}

@media (min-width: 1024px) {
    .about-us-edit-container {
        padding: 1.5rem;
    }

    .about-us-header {
        padding: 2rem;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .about-us-panel,
    .about-us-upload-paper,
    .about-us-preview-paper,
    .production-item-card,
    .about-us-mobile-card {
        border: 2px solid var(--mud-palette-text-primary);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .about-us-panel,
    .about-us-upload-button,
    .about-us-logo-preview,
    .production-item-card,
    .about-us-mobile-card,
    .about-us-action-button,
    .about-us-add-button,
    .about-us-action-icon-button {
        transition: none;
    }

    .about-us-upload-button:hover,
    .about-us-logo-preview:hover,
    .production-item-card:hover,
    .about-us-mobile-card:hover,
    .about-us-action-button:hover,
    .about-us-add-button:hover,
    .about-us-action-icon-button:hover {
        transform: none;
    }

    .add-item-form {
        animation: none;
    }

    .fade-in,
    .slide-in {
        animation: none;
    }
}
