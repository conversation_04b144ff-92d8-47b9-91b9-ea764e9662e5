﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <base href="/"/>
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet"/>
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet"/>
    <link rel="icon" type="image/ico" href="favicon.ico"/>
    @* aos *@
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <HeadOutlet @rendermode="RenderModeForPage"/>
</head>

<body>
<Routes @rendermode="RenderModeForPage"/>
<script src="_framework/blazor.web.js"></script>
<script src="_content/MudBlazor/MudBlazor.min.js"></script>
<script src="_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js"></script>
@* aos *@
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
    // Initialize AOS with custom settings for repeating animations
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: false,        // Allow animations to repeat on every scroll
        mirror: true,       // Enable animations when scrolling back up
        offset: 100,
        delay: 0,
        anchorPlacement: 'top-bottom'  // Trigger animations when element top reaches viewport bottom
    });

    // Refresh AOS on Blazor navigation
    if (window.Blazor) {
        window.Blazor.addEventListener('enhancedload', function () {
            setTimeout(function() {
                AOS.refresh();
            }, 100);
        });
    }

    // Fallback for manual refresh
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            AOS.refresh();
        }, 500);
    });
</script>
</body>

</html>

@code {
    [CascadingParameter] private HttpContext HttpContext { get; set; } = default!;

    private IComponentRenderMode? RenderModeForPage =>
        HttpContext.Request.Path.StartsWithSegments("/account/profile")
            ? InteractiveServer
            : HttpContext.Request.Path.StartsWithSegments("/Account")
                ? null
                : HttpContext.Request.Path.StartsWithSegments("/admin")
                    ? InteractiveWebAssembly
                    : InteractiveAuto;

}
