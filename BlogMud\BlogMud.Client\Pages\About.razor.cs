using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using Microsoft.AspNetCore.Components;
using System.Net.Http.Json;

namespace BlogMud.Client.Pages
{
    /// <summary>
    /// صفحة "من نحن" مع عرض الشرائح المتحركة المدارة من لوحة الإدارة
    /// </summary>
    public partial class About : ComponentBase
    {
        #region الحقول والخصائص

        /// <summary>
        /// معلومات الشركة
        /// </summary>
        private CompanyInfo _companyInfo;

        /// <summary>
        /// قائمة الشرائح المتحركة لصفحة "من نحن"
        /// </summary>
        private List<SiderAboutUsDto> _slideshows = new List<SiderAboutUsDto>();

        /// <summary>
        /// مؤشر على حالة تحميل الشرائح المتحركة
        /// </summary>
        private bool _slideshowsLoading = true;

        /// <summary>
        /// بيانات صفحة "من نحن" المدارة من لوحة الإدارة
        /// </summary>
        private AboutUsDto? _aboutUsData;

        /// <summary>
        /// مؤشر على حالة تحميل بيانات "من نحن"
        /// </summary>
        private bool _aboutUsLoading = true;

        #endregion

        #region الحقن والخدمات

        // HttpClient متاح عالمياً من خلال _Imports.razor

        #endregion

        #region دوال دورة الحياة

        /// <summary>
        /// تهيئة مكونات الصفحة عند بدء التشغيل
        /// </summary>
        /// <returns>مهمة غير متزامنة</returns>
        protected override async Task OnInitializedAsync()
        {
            // تحميل معلومات الشركة والشرائح المتحركة وبيانات "من نحن" بشكل متوازي
            var companyInfoTask = LoadCompanyInfo();
            var slideshowsTask = LoadSlideshows();
            var aboutUsTask = LoadAboutUsData();

            await Task.WhenAll(companyInfoTask, slideshowsTask, aboutUsTask);
        }

        #endregion

        #region دوال تحميل البيانات

        /// <summary>
        /// تحميل معلومات الشركة
        /// </summary>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task LoadCompanyInfo()
        {
            try
            {
                // محاولة الحصول على بيانات الشركة من واجهة برمجة التطبيقات
                try
                {
                    _companyInfo = await Http.GetFromJsonAsync<CompanyInfo>("/api/CompanyInfo/1");
                }
                catch
                {
                    // في حالة عدم وجود نقطة نهاية لمعلومات الشركة، استخدام البيانات الافتراضية
                    _companyInfo = GetDefaultCompanyInfo();
                }
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، استخدام البيانات الاحتياطية
                Console.WriteLine($"Error loading company info: {ex.Message}");
                _companyInfo = GetDefaultCompanyInfo();
            }
        }

        /// <summary>
        /// تحميل الشرائح المتحركة النشطة من الخادم
        /// </summary>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task LoadSlideshows()
        {
            try
            {
                _slideshowsLoading = true;
                StateHasChanged();

                // تحميل الشرائح النشطة فقط من واجهة برمجة التطبيقات
                _slideshows = await Http.GetFromJsonAsync<List<SiderAboutUsDto>>("api/SiderAboutUs/active") ?? new List<SiderAboutUsDto>();
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، عرض قائمة فارغة
                Console.WriteLine($"Error loading SiderAboutUs slideshows: {ex.Message}");
                _slideshows = new List<SiderAboutUsDto>();
            }
            finally
            {
                _slideshowsLoading = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// تحميل بيانات صفحة "من نحن" المدارة من لوحة الإدارة
        /// </summary>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task LoadAboutUsData()
        {
            try
            {
                _aboutUsLoading = true;
                StateHasChanged();

                // تحميل أول محتوى نشط لصفحة "من نحن" من واجهة برمجة التطبيقات
                _aboutUsData = await Http.GetFromJsonAsync<AboutUsDto>("api/AboutUs/first-active");
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، عرض null (سيتم استخدام البيانات الافتراضية)
                Console.WriteLine($"Error loading AboutUs data: {ex.Message}");
                _aboutUsData = null;
            }
            finally
            {
                _aboutUsLoading = false;
                StateHasChanged();
            }
        }

        #endregion

        #region دوال مساعدة

        /// <summary>
        /// الحصول على معلومات الشركة الافتراضية
        /// </summary>
        /// <returns>كائن معلومات الشركة الافتراضي</returns>
        private CompanyInfo GetDefaultCompanyInfo()
        {
            return new CompanyInfo
            {
                Name = "شركتنا",
                Vision = "أن نكون الشركة الرائدة في مجال عملنا",
                Mission = "توفير خدمات عالية الجودة لعملائنا",
                AboutUs = "نحن شركة رائدة في مجال تكنولوجيا المعلومات، تأسست في عام 2010. نسعى دائماً لتقديم أفضل الخدمات لعملائنا والمساهمة في تطوير مجال الأعمال والتكنولوجيا.",
                Phone = "+966123456789",
                Email = "<EMAIL>",
                Address = "الرياض، المملكة العربية السعودية"
            };
        }

        #endregion
    }
}
