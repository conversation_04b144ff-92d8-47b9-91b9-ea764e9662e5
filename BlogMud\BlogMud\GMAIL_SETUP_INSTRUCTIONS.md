# إعداد Gmail لإرسال الإيميلات - تعليمات مفصلة

## الخطوة 1: تفعيل التحقق بخطوتين في Gmail

1. اذهب إلى [Google Account Settings](https://myaccount.google.com/)
2. انق<PERSON> على "الأمان" (Security) في القائمة الجانبية
3. في قسم "تسجيل الدخول إلى Google"، انقر على "التحقق بخطوتين"
4. اتبع التعليمات لتفعيل التحقق بخطوتين

## الخطوة 2: إنشاء App Password

1. بعد تفعيل التحقق بخطوتين، ارجع إلى صفحة الأمان
2. انقر على "كلمات مرور التطبيقات" (App passwords)
3. <PERSON><PERSON><PERSON><PERSON> "التطبيق" → "أخرى (اسم مخصص)"
4. ا<PERSON><PERSON><PERSON> "BlogMud Email System"
5. انقر على "إنشاء"
6. **انسخ كلمة المرور المكونة من 16 رقم** (مثل: abcd efgh ijkl mnop)

## الخطوة 3: تحديث إعدادات التطبيق

1. افتح ملف `appsettings.Development.json`
2. استبدل `"your-gmail-app-password-here"` بكلمة المرور التي نسختها
3. تأكد من أن البريد الإلكتروني صحيح: `<EMAIL>`

```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderName": "BlogMud System",
    "Username": "<EMAIL>",
    "Password": "abcd efgh ijkl mnop",
    "EnableSsl": true
  }
}
```

## الخطوة 4: إعادة تشغيل التطبيق

1. أوقف التطبيق (Ctrl+C)
2. شغل التطبيق مرة أخرى:
   ```bash
   dotnet run --launch-profile https
   ```

## الخطوة 5: اختبار الإيميل

1. اذهب إلى: https://localhost:7009/Account/ResendEmailConfirmation
2. أدخل بريدك الإلكتروني: `<EMAIL>`
3. انقر على "RESEND"
4. تحقق من بريدك الإلكتروني - يجب أن تصل رسالة التأكيد

## ملاحظات مهمة:

- **لا تشارك App Password مع أحد**
- **احتفظ بنسخة احتياطية من App Password**
- **إذا لم تعمل، تأكد من أن التحقق بخطوتين مفعل**
- **تأكد من عدم وجود مسافات في App Password**

## استكشاف الأخطاء:

### إذا لم تصل الإيميلات:
1. تحقق من مجلد الرسائل غير المرغوب فيها (Spam)
2. تأكد من صحة App Password
3. تحقق من logs التطبيق للأخطاء

### إذا ظهرت أخطاء SMTP:
1. تأكد من أن التحقق بخطوتين مفعل
2. تأكد من أن App Password صحيح
3. تحقق من اتصال الإنترنت
