<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-BlogMud-351051ed-475a-4c7d-b8b3-3b3105994395</UserSecretsId>
    <Platforms>AnyCPU;x86</Platforms>
  </PropertyGroup>

  <!-- إعدادات خاصة لمنصة x86 -->
  <PropertyGroup Condition="'$(Platform)' == 'x86'">
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>

  <!-- إعدادات خاصة لـ win-x86 runtime -->
  <PropertyGroup Condition="'$(RuntimeIdentifier)' == 'win-x86'">
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>


  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.*" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="8.*" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.*" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Extensions.MudBlazor.StaticInput" Version="3.2.0" />
    <PackageReference Include="MudBlazor" Version="8.8.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BlogMud.Client\BlogMud.Client.csproj" />
    <ProjectReference Include="..\BlogMud.Shared\BlogMud.Shared.csproj" />
  </ItemGroup>


  <ItemGroup>
    <Folder Include="wwwroot\AboutUsImg\SiderAboutUsImg\" />
    <Folder Include="wwwroot\AboutUsImg\ProductionCapacity\" />
    <Folder Include="wwwroot\imeg\" />
    <Folder Include="wwwroot\AnimatedGifsSider\" />
    <Folder Include="wwwroot\NewsSiderMoveImg\" />
    <Folder Include="wwwroot\OurServImg\img\" />
    <Folder Include="wwwroot\OurServImg\SiderMoveOurServicesImg\" />
    <Folder Include="wwwroot\OurServImg\Sider\" />
    <Folder Include="wwwroot\OurServImg\viedo\" />
    <Folder Include="wwwroot\Sider\" />
    <Folder Include="wwwroot\Video\" />
  </ItemGroup>

</Project>
