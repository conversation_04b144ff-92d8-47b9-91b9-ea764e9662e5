@implements IDisposable

@using BlogMud.Shared.Enums
@using Microsoft.AspNetCore.Components.Routing
@inject NavigationManager NavigationManager

@if (Mode == NavMenuMode.Horizontal)
{
    <div class="d-flex">
        <MudStack Row="true">
            <MudLink Href="/" Match="NavLinkMatch.All" Typo="Typo.body1" Color="Color.Inherit" Underline="Underline.None" Class="px-4 py-2">
                الرئيسية
            </MudLink>
            <MudLink Href="/about" Match="NavLinkMatch.Prefix" Typo="Typo.body1" Color="Color.Inherit" Underline="Underline.None" Class="px-4 py-2">
                من نحن
            </MudLink>
            <MudLink Href="/ourServices" Match="NavLinkMatch.Prefix" Typo="Typo.body1" Color="Color.Inherit" Underline="Underline.None" Class="px-4 py-2">
                خدماتنا
            </MudLink>
            <MudLink Href="/news" Match="NavLinkMatch.Prefix" Typo="Typo.body1" Color="Color.Inherit" Underline="Underline.None" Class="px-4 py-2">
                الأخبار
            </MudLink>
        @*     <MudLink Href="/branches" Match="NavLinkMatch.Prefix" Typo="Typo.body1" Color="Color.Inherit" Underline="Underline.None" Class="px-4 py-2">
                الفروع
            </MudLink> *@
            <MudLink Href="/contact" Match="NavLinkMatch.Prefix" Typo="Typo.body1" Color="Color.Inherit" Underline="Underline.None" Class="px-4 py-2">
                اتصل بنا
            </MudLink>
            <AuthorizeView Roles="Admin">
                <MudLink Href="/admin" Match="NavLinkMatch.Prefix" Typo="Typo.body1" Color="Color.Inherit" Underline="Underline.None" Class="px-4 py-2">
                    لوحة التحكم
                </MudLink>
            </AuthorizeView>
        </MudStack>
    </div>
}
else
{
    <MudNavMenu Color="Color.Inherit" Rounded="true" Class="pa-2">
        <MudNavLink Href="/" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Home">الرئيسية</MudNavLink>
        <MudNavLink Href="/about" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Info">من نحن</MudNavLink>
        <MudNavLink Href="/ourServices" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Business">خدماتنا</MudNavLink>
        <MudNavLink Href="/news" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Article">الأخبار</MudNavLink>
        @* <MudNavLink Href="/branches" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.LocationOn">الفروع</MudNavLink> *@
        <MudNavLink Href="/contact" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Mail">اتصل بنا</MudNavLink>
        <AuthorizeView Roles="Admin">
            <MudNavLink Href="/admin" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Dashboard">لوحة التحكم</MudNavLink>
        </AuthorizeView>
    </MudNavMenu>
}


@code {
    private string? currentUrl;
    
    [Parameter]
    public NavMenuMode Mode { get; set; } = NavMenuMode.Vertical;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}


