<style>
    /* PostManagementDialog - Component Scoped Styles */

    /* Dialog Container */
    .post-management-dialog {
        direction: rtl;
        font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Helvetica', 'Arial', sans-serif;
        border-radius: 16px;
        box-shadow: 0 24px 48px rgba(0, 0, 0, 0.15);
    }

        .post-management-dialog .mud-dialog-content {
            padding: 0;
            max-height: 85vh;
            overflow-y: auto;
            border-radius: 0 0 16px 16px;
        }

        .post-management-dialog .mud-dialog-title {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-primary-darken) 100%);
            color: white;
            border-radius: 16px 16px 0 0;
            margin: 0;
        }

    /* Dialog Title */
    .dialog-title-container {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0;
    }

    .dialog-title-icon {
        color: white;
        font-size: 1.75rem;
        opacity: 0.9;
    }

    .dialog-title-text {
        color: white;
        font-weight: 700;
        margin: 0;
        font-size: 1.5rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    /* Dialog Content */
    .dialog-content-container {
        padding: 2rem;
        background: var(--mud-palette-surface);
        min-height: 60vh;
    }

    /* Form Sections */
    .form-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border-radius: 12px;
        background: var(--mud-palette-surface);
        border: 1px solid var(--mud-palette-divider);
        transition: all 0.3s ease;
    }

        .form-section:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: var(--mud-palette-primary-lighten);
        }

    /* Section Titles */
    .section-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
        color: var(--mud-palette-text-primary);
        font-weight: 600;
        border-bottom: 2px solid var(--mud-palette-divider);
        padding-bottom: 0.75rem;
    }

    .section-icon {
        color: var(--mud-palette-primary);
        font-size: 1.25rem;
    }

    /* Form Grid */
    .form-grid {
        gap: 1.25rem;
    }

    /* Form Fields */
    .form-field {
        margin-bottom: 1rem;
    }

        .form-field .mud-input-control {
            border-radius: 8px;
        }

        .form-field .mud-input-outlined .mud-input-outlined-border {
            border-width: 1.5px;
            transition: border-color 0.2s ease;
        }

        .form-field .mud-input-outlined:hover .mud-input-outlined-border {
            border-color: var(--mud-palette-primary-lighten);
        }

        .form-field .mud-input-outlined.mud-input-focused .mud-input-outlined-border {
            border-width: 2px;
        }

    .content-field {
        min-height: 120px;
    }

    /* Tabbed Interface Styles */
    .post-management-dialog .mud-tabs {
        background: var(--mud-palette-surface);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
    }

    .post-management-dialog .mud-tabs .mud-tab {
        font-weight: 500;
        font-size: 0.8rem;
        padding: 0.4rem 0.875rem;
        height: 36px;
        min-height: 36px;
        border-radius: 6px 6px 0 0;
        transition: all 0.3s ease;
        color: var(--mud-palette-text-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.3rem;
    }

    .post-management-dialog .mud-tabs .mud-tab:hover {
        background: var(--mud-palette-primary-lighten-5);
        color: var(--mud-palette-primary);
    }

    .post-management-dialog .mud-tabs .mud-tab.mud-tab-active {
        background: var(--mud-palette-primary);
        color: var(--mud-palette-primary-text);
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .post-management-dialog .mud-tabs .mud-tab-panel {
        background: var(--mud-palette-surface);
        border-radius: 0 0 8px 8px;
        min-height: 400px;
        padding: 1.5rem;
    }

    /* Tab Container Styling */
    .post-management-dialog .mud-tabs {
        margin-bottom: 0;
    }

    .post-management-dialog .mud-tabs .mud-tabs-toolbar {
        background: var(--mud-palette-background-grey);
        border-radius: 8px 8px 0 0;
        padding: 0.25rem 0.5rem 0;
    }

    /* Tab Icons */
    .post-management-dialog .mud-tabs .mud-tab .mud-icon {
        font-size: 0.9rem;
        margin: 0;
    }

    /* Tab Text */
    .post-management-dialog .mud-tabs .mud-tab .mud-tab-text {
        font-size: 0.8rem;
        line-height: 1.2;
        white-space: nowrap;
    }

    /* Media Section */
    .media-section {
        background: linear-gradient(135deg, var(--mud-palette-primary-lighten-5) 0%, var(--mud-palette-surface) 100%);
    }

    .media-grid {
        gap: 1.5rem;
    }

    /* Upload Sections */
    .upload-section {
        padding: 1.25rem;
        border-radius: 8px;
        background: var(--mud-palette-surface);
        border: 2px dashed var(--mud-palette-divider);
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

        .upload-section:hover {
            border-color: var(--mud-palette-primary-lighten);
            background: var(--mud-palette-primary-lighten-5);
        }

    /* Upload Headers */
    .upload-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--mud-palette-divider);
    }

    .upload-icon {
        color: var(--mud-palette-primary);
        font-size: 1.1rem;
    }

    .upload-title {
        color: var(--mud-palette-text-primary);
        font-weight: 500;
        margin: 0;
    }

    /* Upload Content */
    .upload-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    /* Upload Buttons */
    .upload-button {
        min-height: 48px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

    /* Image Previews */
    .image-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        background: var(--mud-palette-background-grey);
        border-radius: 8px;
    }

    .preview-image {
        width: 100%;
        max-width: 200px;
        height: 120px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }

        .preview-image:hover {
            transform: scale(1.05);
        }

    .image-caption {
        color: var(--mud-palette-text-secondary);
        text-align: center;
        word-break: break-all;
        max-width: 100%;
    }

    /* Carousel Info */
    .carousel-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    .image-count-chip {
        font-size: 0.75rem;
    }

    /* Video Preview */
    .video-preview {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .video-success-alert {
        margin: 0;
        border-radius: 6px;
    }

    .video-container {
        display: flex;
        justify-content: center;
        background: var(--mud-palette-background-grey);
        border-radius: 8px;
        padding: 0.5rem;
    }

    .preview-video {
        width: 100%;
        max-width: 200px;
        height: 120px;
        border-radius: 6px;
    }

    .video-caption {
        color: var(--mud-palette-text-secondary);
        text-align: center;
        font-size: 0.75rem;
    }

    /* Notification Section */
    .notification-section {
        background: var(--mud-palette-info-lighten-5);
        border-color: var(--mud-palette-info-lighten);
    }

    .notification-alert {
        margin: 0;
        border-radius: 8px;
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .notification-spinner {
        flex-shrink: 0;
    }

    .notification-icon {
        flex-shrink: 0;
        color: var(--mud-palette-success);
    }

    .notification-text {
        margin: 0;
        font-weight: 500;
    }

    /* Dialog Actions */
    .dialog-actions {
        padding: 1.5rem 2rem 2rem;
        background: var(--mud-palette-surface);
        border-top: 1px solid var(--mud-palette-divider-light);
        border-radius: 0 0 16px 16px;
    }

    .action-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        align-items: center;
    }

    .cancel-button {
        min-width: 100px;
        height: 40px;
        border-radius: 10px;
        font-weight: 500;
        font-size: 0.875rem;
        padding: 0 1.5rem;
        transition: all 0.2s ease;
        border: 1px solid var(--mud-palette-divider);
    }

        .cancel-button:hover {
            background-color: var(--mud-palette-action-hover);
            border-color: var(--mud-palette-text-secondary);
        }

    .submit-button {
        min-width: 120px;
        height: 40px;
        border-radius: 10px;
        font-weight: 600;
        font-size: 0.875rem;
        padding: 0 1.75rem;
        box-shadow: 0 2px 8px rgba(var(--mud-palette-primary-rgb), 0.25);
        transition: all 0.2s ease;
        background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-primary-darken) 100%);
    }

        .submit-button:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(var(--mud-palette-primary-rgb), 0.35);
        }

        .submit-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }

    /* Responsive Design */

    /* Mobile Phones (up to 599px) */
    @@media (max-width: 599px) {
        .post-management-dialog .mud-dialog-title {
            padding: 1rem 1.5rem;
        }

        .dialog-title-text {
            font-size: 1.25rem;
        }

        .dialog-title-icon {
            font-size: 1.5rem;
        }

        .dialog-content-container {
            padding: 1.5rem;
            min-height: 50vh;
        }

        .dialog-actions {
            padding: 1rem 1.5rem 1.5rem;
        }

        .form-section {
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 1rem;
            margin-bottom: 1rem;
        }

        .media-grid .mud-item {
            margin-bottom: 1rem;
        }

        .upload-section {
            padding: 1rem;
        }

        .preview-image {
            max-width: 150px;
            height: 100px;
        }

        .preview-video {
            max-width: 150px;
            height: 100px;
        }

        .action-buttons {
            flex-direction: column-reverse;
            gap: 0.75rem;
        }

        .cancel-button,
        .submit-button {
            width: 100%;
            min-width: unset;
            height: 44px;
            font-size: 1rem;
        }

    /* Mobile Tab Adjustments */
    .post-management-dialog .mud-tabs .mud-tab {
        font-size: 0.75rem;
        padding: 0.35rem 0.65rem;
        height: 34px;
        min-height: 34px;
    }

    .post-management-dialog .mud-tabs .mud-tab .mud-icon {
        font-size: 0.8rem;
    }

    .post-management-dialog .mud-tabs .mud-tab .mud-tab-text {
        font-size: 0.75rem;
    }

    }

    /* Tablets (600px to 959px) */
    @@media (min-width: 600px) and (max-width: 959px) {
        .post-management-dialog .mud-dialog-title {
            padding: 1.25rem 1.75rem;
        }

        .dialog-title-text {
            font-size: 1.375rem;
        }

        .dialog-content-container {
            padding: 1.75rem;
            min-height: 55vh;
        }

        .dialog-actions {
            padding: 1.25rem 1.75rem 1.75rem;
        }

        .form-section {
            padding: 1.5rem;
        }

        .media-grid {
            gap: 1.5rem;
        }

        .preview-image {
            max-width: 200px;
            height: 120px;
        }

        .preview-video {
            max-width: 200px;
            height: 120px;
        }

        .action-buttons {
            gap: 1rem;
        }

        .cancel-button,
        .submit-button {
            min-width: 110px;
            height: 42px;
        }

    /* Tablet Tab Adjustments */
    .post-management-dialog .mud-tabs .mud-tab {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
        height: 34px;
        min-height: 34px;
    }

    .post-management-dialog .mud-tabs .mud-tab .mud-icon {
        font-size: 0.85rem;
    }

    .post-management-dialog .mud-tabs .mud-tab .mud-tab-text {
        font-size: 0.8rem;
    }

    }

    /* Desktop (960px and up) */
    @@media (min-width: 960px) {
        .post-management-dialog .mud-dialog-content {
            max-height: 90vh;
        }

        .post-management-dialog .mud-dialog-title {
            padding: 1.5rem 2rem;
        }

        .dialog-content-container {
            padding: 2.5rem;
            min-height: 65vh;
        }

        .dialog-actions {
            padding: 1.5rem 2rem 2rem;
        }

        .form-section {
            padding: 2rem;
        }

        .media-grid {
            gap: 2rem;
        }

        .upload-section {
            padding: 1.5rem;
        }

        .action-buttons {
            gap: 1.25rem;
        }

        .cancel-button {
            min-width: 120px;
            height: 44px;
        }

        .submit-button {
            min-width: 140px;
            height: 44px;
        }

        /* Desktop Tab Adjustments */
        .post-management-dialog .mud-tabs .mud-tab {
            font-size: 0.8rem;
            padding: 0.375rem 0.875rem;
            height: 34px;
            min-height: 34px;
        }

        .post-management-dialog .mud-tabs .mud-tab .mud-icon {
            font-size: 0.85rem;
        }

        .post-management-dialog .mud-tabs .mud-tab .mud-tab-text {
            font-size: 0.8rem;
        }
    }

    /* High Contrast Mode Support */
    @@media (prefers-contrast: high) {
        .form-section, .upload-section

    {
        border-width: 3px;
        border-style: solid;
    }

    .preview-image,
    .preview-video {
        border: 2px solid var(--mud-palette-text-primary);
    }

    }

    /* Reduced Motion Support */
    @@media (prefers-reduced-motion: reduce) {
        .form-section, .upload-button, .preview-image, .submit-button

    {
        transition: none;
    }

    .upload-button:hover,
    .submit-button:hover:not(:disabled) {
        transform: none;
    }

    }

    /* Dark Theme Support */
    @@media (prefers-color-scheme: dark) {
        .upload-section

    {
        background: var(--mud-palette-dark-surface);
        border-color: var(--mud-palette-dark-divider);
    }

    .image-preview,
    .video-container {
        background: var(--mud-palette-dark-background);
    }

    }

    /* Touch Target Optimization for Mobile */
    .upload-button,
    .cancel-button,
    .submit-button {
        min-height: 44px;
        min-width: 44px;
    }

    /* Focus Management for Accessibility */
    .form-field .mud-input-control:focus-within {
        outline: 2px solid var(--mud-palette-primary);
        outline-offset: 2px;
        border-radius: 4px;
    }

    .upload-button:focus,
    .cancel-button:focus,
    .submit-button:focus {
        outline: 2px solid var(--mud-palette-primary);
        outline-offset: 2px;
    }

    /* Tab Focus States */
    .post-management-dialog .mud-tabs .mud-tab:focus {
        outline: 2px solid var(--mud-palette-primary);
        outline-offset: 2px;
        border-radius: 6px;
    }

    .post-management-dialog .mud-tabs .mud-tab:focus-visible {
        outline: 2px solid var(--mud-palette-primary);
        outline-offset: 2px;
        border-radius: 6px;
    }

    /* Loading States */
    .form-section.loading {
        opacity: 0.7;
        pointer-events: none;
    }

    .upload-button.uploading {
        position: relative;
        color: transparent;
    }

        .upload-button.uploading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

    @@keyframes spin {
        0%

    {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }

    }

    /* Error States */
    .form-field.error .mud-input-control {
        border-color: var(--mud-palette-error);
        box-shadow: 0 0 0 1px var(--mud-palette-error);
    }

    .upload-section.error {
        border-color: var(--mud-palette-error);
        background: var(--mud-palette-error-lighten-5);
    }

    /* Success States */
    .upload-section.success {
        border-color: var(--mud-palette-success);
        background: var(--mud-palette-success-lighten-5);
    }

    /* Smooth Animations */
    .form-section,
    .upload-section,
    .image-preview,
    .video-preview {
        animation: fadeInUp 0.3s ease-out;
    }

    @@keyframes fadeInUp {
        from

    {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }

    }

    /* Improved Scrollbar for Dialog Content */
    .post-management-dialog .mud-dialog-content::-webkit-scrollbar {
        width: 8px;
    }

    .post-management-dialog .mud-dialog-content::-webkit-scrollbar-track {
        background: var(--mud-palette-background-grey);
        border-radius: 4px;
    }

    .post-management-dialog .mud-dialog-content::-webkit-scrollbar-thumb {
        background: var(--mud-palette-divider);
        border-radius: 4px;
    }

        .post-management-dialog .mud-dialog-content::-webkit-scrollbar-thumb:hover {
            background: var(--mud-palette-text-secondary);
        }

    /* RTL Specific Adjustments */
    [dir="rtl"] .dialog-title-container {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .section-title {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .upload-header {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .notification-content {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .action-buttons {
        flex-direction: row-reverse;
    }

    /* Print Styles */
    @@media print {
        .dialog-actions

    {
        display: none;
    }

    .upload-section {
        border-style: solid;
    }

    .preview-image,
    .preview-video {
        max-width: 100px;
        height: 60px;
    }

    .form-section {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    }

    /* تمييز الحقول المفقودة */
    .highlighted-field {
        animation: highlightField 3s ease-in-out;
        border: 2px solid var(--mud-palette-error) !important;
        box-shadow: 0 0 10px rgba(244, 67, 54, 0.3) !important;
    }

    @@keyframes highlightField {
        0% {
            transform: scale(1);
            box-shadow: 0 0 0 rgba(244, 67, 54, 0.7);
        }
        25% {
            transform: scale(1.02);
            box-shadow: 0 0 20px rgba(244, 67, 54, 0.7);
        }
        50% {
            transform: scale(1);
            box-shadow: 0 0 15px rgba(244, 67, 54, 0.5);
        }
        75% {
            transform: scale(1.01);
            box-shadow: 0 0 10px rgba(244, 67, 54, 0.3);
        }
        100% {
            transform: scale(1);
            box-shadow: 0 0 5px rgba(244, 67, 54, 0.2);
        }
    }

    /* تمييز خاص لحقول التحميل */
    .upload-section.highlighted-field {
        background-color: rgba(244, 67, 54, 0.05) !important;
        border-color: var(--mud-palette-error) !important;
    }

    /* تأثير النبض للحقول المميزة */
    .highlighted-field::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, transparent, rgba(244, 67, 54, 0.1), transparent);
        border-radius: inherit;
        animation: pulse 2s infinite;
        z-index: -1;
    }

    @@keyframes pulse {
        0% {
            opacity: 0.5;
        }
        50% {
            opacity: 1;
        }
        100% {
            opacity: 0.5;
        }
    }
</style>