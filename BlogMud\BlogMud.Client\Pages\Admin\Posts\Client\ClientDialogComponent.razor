
<MudDialog>
    <DialogContent>
        <MudForm @ref="form" @bind-IsValid="@success">
            <MudTextField @bind-Value="clientDto.Name" Label="اسم العميل" Required="true"
                RequiredError="يجب إدخال اسم العميل"
                          MaxLength="100"
                          Counter="100"
                          HelperText="الحد الأقصى 100 حرف" />

            <MudTextField @bind-Value="clientDto.Email" Label="البريد الإلكتروني" Required="true"
                RequiredError="يجب إدخال البريد الإلكتروني" Class="mt-3"
                Validation="@(new EmailAddressAttribute() { ErrorMessage = "البريد الإلكتروني غير صالح" })"
                          MaxLength="100"
                          Counter="100"
                          HelperText="الحد الأقصى 100 حرف" />

            <MudNumericField @bind-Value="clientDto.Phone" Label="رقم الهاتف" Required="true"
                RequiredError="يجب إدخال رقم الهاتف" Class="mt-3"
                             MaxLength="12"
                             Counter="12"
                             HelperText="الحد الأقصى 12 رقم" />

            <MudTextField @bind-Value="clientDto.Address" Label="العنوان" Class="mt-3"
                          MaxLength="200"
                          Counter="200"
                          HelperText="الحد الأقصى 200 حرف" />

            <MudTextField @bind-Value="clientDto.Notes" Label="ملاحظات" Lines="3" Class="mt-3"
                          MaxLength="500"
                          Counter="500"
                          HelperText="الحد الأقصى 500 حرف" />

            <MudSwitch T="bool" @bind-Checked="clientDto.IsActive" Label="نشط" Color="Color.Primary" Class="mt-3" />
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">إلغاء</MudButton>
        <MudButton Color="Color.Primary" OnClick="Submit" Disabled="@(!success)">حفظ</MudButton>
    </DialogActions>
</MudDialog>

@code {
    #region الخصائص والمتغيرات

    /// <summary>
    /// مثيل مربع الحوار MudDialog الذي يتم تمريره من خلال CascadingParameter
    /// </summary>
    /// <remarks>
    /// يستخدم للتحكم في إغلاق مربع الحوار وإرجاع النتائج
    /// </remarks>
    [CascadingParameter] public required IMudDialogInstance MudDialog { get; set; }

    /// <summary>
    /// بيانات العميل التي يتم تمريرها إلى مربع الحوار عند التعديل
    /// </summary>
    /// <remarks>
    /// إذا كانت القيمة null، فهذا يعني أننا نقوم بإنشاء عميل جديد
    /// </remarks>
    [Parameter] public ClientDto? ClientDto { get; set; }

    /// <summary>
    /// نموذج بيانات العميل الذي يتم تعديله أو إنشاؤه
    /// </summary>
    private ClientDto clientDto = new ClientDto();

    /// <summary>
    /// مؤشر على صحة النموذج
    /// </summary>
    private bool success;

    /// <summary>
    /// مرجع إلى نموذج MudForm المستخدم للتحقق من صحة البيانات
    /// </summary>
    private MudForm? form;
    #endregion

    #region دوال دورة الحياة

    /// <summary>
    /// تهيئة مكونات الصفحة عند بدء التشغيل
    /// </summary>
    /// <remarks>
    /// إذا كان هناك عميل موجود (تعديل)، يتم نسخ بياناته إلى النموذج الحالي
    /// وإلا يتم تعيين قيم افتراضية للعميل الجديد مثل تاريخ الإنشاء وحالة النشاط
    /// </remarks>
    protected override void OnInitialized()
    {
        if (ClientDto != null)
        {
            clientDto = new ClientDto
            {
                Id = ClientDto.Id,
                Name = ClientDto.Name,
                Email = ClientDto.Email,
                Phone = ClientDto.Phone,
                Address = ClientDto.Address,
                Notes = ClientDto.Notes,
                IsActive = ClientDto.IsActive,
                CreatedAt = ClientDto.CreatedAt
            };
        }
        else
        {
            clientDto.CreatedAt = DateTime.Now;
            clientDto.IsActive = true;
        }
    }
    #endregion

    #region الأحداث والتفاعلات

    /// <summary>
    /// إلغاء العملية وإغلاق مربع الحوار
    /// </summary>
    private void Cancel()
    {
        MudDialog.Cancel();
    }

    /// <summary>
    /// حفظ بيانات العميل
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بالتحقق من صحة النموذج أولاً، ثم إرسال البيانات إلى الخادم
    /// إذا كان معرف العميل أكبر من صفر، يتم تحديث عميل موجود، وإلا يتم إنشاء عميل جديد
    /// </remarks>
    private async Task Submit()
    {
        // التحقق من صحة النموذج
        if (form != null)
            await form.Validate();
        if (!success) return;

        try
        {
            HttpResponseMessage response;

            // تحديد ما إذا كان تحديث أو إنشاء جديد
            if (clientDto.Id > 0)
            {
                // تحديث عميل موجود
                response = await Http.PutAsJsonAsync($"api/Clients/{clientDto.Id}", clientDto);
            }
            else
            {
                // إنشاء عميل جديد
                response = await Http.PostAsJsonAsync("api/Clients", clientDto);
            }

            // معالجة الاستجابة
            if (response.IsSuccessStatusCode)
            {
                _Snackbar.Add("تم حفظ العميل بنجاح", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _Snackbar.Add($"فشل في حفظ العميل: {errorContent}", Severity.Error);
                Console.WriteLine($"Error saving client: {errorContent}");
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء حفظ العميل: {ex.Message}", Severity.Error);
        }
    }
    #endregion
}
