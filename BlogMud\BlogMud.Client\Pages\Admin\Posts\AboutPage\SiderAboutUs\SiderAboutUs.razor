﻿@page "/admin/siderAboutUs"
@attribute [Authorize(Roles = "Admin")]
@layout BlogMud.Client.Layout.AdminLayout
@using BlogMud.Shared.DTOs
@using MudBlazor

<PageTitle>إدارة الشرائح المتحركة لصفحة "من نحن"</PageTitle>
<SiderAboutUsCSS />
<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="sider-aboutus-container">
    <!-- عنوان الصفحة المحسن -->
    <div class="page-header-section">
        <MudPaper Class="page-header-card" Elevation="0">
            <div class="header-content">
                <div class="header-icon-wrapper">
                    <MudIcon Icon="@Icons.Material.Filled.ViewCarousel" Class="header-icon" />
                </div>
                <div class="header-text">
                    <MudText Typo="Typo.h4" Class="header-title">إدارة الشرائح المتحركة لصفحة "من نحن"</MudText>
                    <MudText Typo="Typo.body1" Class="header-subtitle">
                        إدارة عروض الصور المتحركة التي تظهر في صفحة "من نحن" بتصميم أنيق ومتجاوب
                    </MudText>
                </div>
            </div>
            <div class="header-stats">
                <div class="stat-item">
                    <MudIcon Icon="@Icons.Material.Filled.Slideshow" Class="stat-icon" />
                    <div class="stat-content">
                        <MudText Typo="Typo.h6" Class="stat-number">@_siderAboutUs.Count</MudText>
                        <MudText Typo="Typo.caption" Class="stat-label">إجمالي الشرائح</MudText>
                    </div>
                </div>
                <div class="stat-item">
                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="stat-icon active" />
                    <div class="stat-content">
                        <MudText Typo="Typo.h6" Class="stat-number">@_siderAboutUs.Count(s => s.IsActive)</MudText>
                        <MudText Typo="Typo.caption" Class="stat-label">الشرائح النشطة</MudText>
                    </div>
                </div>
            </div>
        </MudPaper>
    </div>

    <!-- معاينة الشرائح المتحركة المحسنة -->
    @if (_siderAboutUs.Any())
    {
        <div class="preview-section">
            <MudPaper Class="preview-card" Elevation="0">
                <div class="preview-header">
                    <div class="preview-title-wrapper">
                        <MudIcon Icon="@Icons.Material.Filled.Preview" Class="preview-icon" />
                        <MudText Typo="Typo.h6" Class="preview-title">معاينة الشرائح المتحركة</MudText>
                    </div>
                    <div class="carousel-controls">
                        <MudIconButton Icon="@(autocycle? Icons.Material.Filled.Pause : Icons.Material.Filled.PlayArrow)"
                                       Color="Color.Primary"
                                       Size="Size.Small"
                                       OnClick="() => autocycle = !autocycle"
                                       Title="@(autocycle ? "إيقاف التشغيل التلقائي" : "تشغيل تلقائي")" />
                        <MudIconButton Icon="@(arrows? Icons.Material.Filled.VisibilityOff : Icons.Material.Filled.Visibility)"
                                       Color="Color.Secondary"
                                       Size="Size.Small"
                                       OnClick="() => arrows = !arrows"
                                       Title="@(arrows ? "إخفاء الأسهم" : "إظهار الأسهم")" />
                    </div>
                </div>

                @if (_siderAboutUs.Any(s => s.IsActive))
                {
                    <div class="carousel-container">
                        <MudCarousel Class="enhanced-carousel"
                                     ShowArrows="@arrows"
                                     ShowBullets="@bullets"
                                     EnableSwipeGesture="@enableSwipeGesture"
                                     AutoCycle="@autocycle"
                                     TData="object"
                                     AutoCycleTime="TimeSpan.FromSeconds(4)"
                                     BulletsColor="Color.Primary">
                            @foreach (var slideshow in _siderAboutUs.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder))
                            {
                                @if (slideshow.Images.Any())
                                {
                                    @foreach (var imageUrl in slideshow.Images)
                                    {
                                        <MudCarouselItem Transition="transition">
                                            <div class="carousel-item">
                                                <div class="slide-content">
                                                    <div class="slide-image-wrapper">
                                                        <img src="@imageUrl"
                                                             alt="@slideshow.Title"
                                                             class="slide-image"
                                                             @onclick="() => ShowImageModal(imageUrl, slideshow.Title)"
                                                             @onclick:stopPropagation="true"
                                                             title="انقر لعرض الصورة بحجم كامل" />
                                                        <div class="slide-overlay"></div>
                                                    </div>
                                                    <div class="slide-info">
                                                        <MudText Typo="Typo.h6" Class="slide-title">@slideshow.Title</MudText>
                                                        @if (!string.IsNullOrEmpty(slideshow.Description))
                                                        {
                                                            <MudText Typo="Typo.body2" Class="slide-description">@slideshow.Description</MudText>
                                                        }
                                                        @if (!string.IsNullOrEmpty(slideshow.LinkUrl) && !string.IsNullOrEmpty(slideshow.LinkText))
                                                        {
                                                            <MudButton Href="@slideshow.LinkUrl"
                                                                       Target="_blank"
                                                                       Color="Color.Primary"
                                                                       Variant="Variant.Filled"
                                                                       Size="Size.Small"
                                                                       StartIcon="@Icons.Material.Filled.OpenInNew"
                                                                       Class="slide-link-btn">
                                                                @slideshow.LinkText
                                                            </MudButton>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        </MudCarouselItem>
                                    }
                                }
                            }
                        </MudCarousel>
                    </div>
                }
                else
                {
                    <div class="no-active-slides">
                        <MudIcon Icon="@Icons.Material.Filled.Image" Class="no-slides-icon" />
                        <MudText Typo="Typo.h6" Class="no-slides-title">لا توجد شرائح نشطة</MudText>
                        <MudText Typo="Typo.body2" Class="no-slides-subtitle">قم بتفعيل بعض الشرائح لرؤية المعاينة</MudText>
                    </div>
                }
            </MudPaper>
        </div>
    }

    <!-- أدوات التحكم المحسنة -->
    <div class="controls-section">
        <MudPaper Class="controls-card" Elevation="0">
            <div class="controls-container">
                <div class="controls-left">
                    <div class="action-buttons">
                        <MudButton Color="Color.Primary"
                                   StartIcon="@Icons.Material.Filled.Add"
                                   OnClick="OpenCreateDialog"
                                   Variant="Variant.Filled"
                                   Size="Size.Large"
                                   Class="primary-action-btn">
                            <span class="button-text-full">إضافة شريحة متحركة جديدة</span>
                            <span class="button-text-short">إضافة</span>
                        </MudButton>

                        <MudButton Color="Color.Secondary"
                                   StartIcon="@Icons.Material.Filled.Refresh"
                                   OnClick="LoadSiderAboutUs"
                                   Variant="Variant.Outlined"
                                   Size="Size.Large"
                                   Class="secondary-action-btn"
                                   Loading="_loading">
                            تحديث
                        </MudButton>
                    </div>
                </div>

                <div class="controls-right">
                    <!-- فلتر الحالة -->
                    <MudSelect @bind-Value="_statusFilter"
                               Label="حالة الشريحة"
                               Variant="Variant.Outlined"
                               Class="status-filter-select"
                               Style="min-width: 150px; margin-left: 1rem;">
                        <MudSelectItem Value="@("all")">جميع الشرائح</MudSelectItem>
                        <MudSelectItem Value="@("active")">نشطة فقط</MudSelectItem>
                        <MudSelectItem Value="@("inactive")">غير نشطة فقط</MudSelectItem>
                    </MudSelect>

                    <!-- شريط البحث المحسن -->
                    <div class="search-wrapper">
                        <MudTextField @bind-Value="_searchString"
                                      Placeholder="البحث في الشرائح المتحركة..."
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Search"
                                      IconSize="Size.Medium"
                                      Variant="Variant.Outlined"
                                      Class="enhanced-search-field"
                                      Immediate="true"
                                      DebounceInterval="300">
                        </MudTextField>
                        @if (!string.IsNullOrEmpty(_searchString))
                        {
                            <MudIconButton Icon="@Icons.Material.Filled.Clear"
                                           Color="Color.Default"
                                           Size="Size.Small"
                                           OnClick="() => _searchString = string.Empty"
                                           Class="search-clear-btn"
                                           Title="مسح البحث" />
                        }
                    </div>
                </div>
            </div>
        </MudPaper>
    </div>

    @if (_loading)
    {
        <div class="loading-section">
            <MudPaper Class="loading-card" Elevation="0">
                <div class="loading-content">
                    <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                    <MudText Typo="Typo.h6" Class="loading-text">جاري تحميل الشرائح المتحركة...</MudText>
                    <MudText Typo="Typo.body2" Class="loading-subtitle">يرجى الانتظار</MudText>
                </div>
            </MudPaper>
        </div>
    }
    else if (_siderAboutUs.Count == 0)
    {
        <div class="empty-state-section">
            <MudPaper Class="empty-state-card" Elevation="0">
                <div class="empty-state-content">
                    <div class="empty-state-icon-wrapper">
                        <MudIcon Icon="@Icons.Material.Filled.ViewCarousel" Class="empty-state-icon" />
                    </div>
                    <MudText Typo="Typo.h5" Class="empty-state-title">لا توجد شرائح متحركة</MudText>
                    <MudText Typo="Typo.body1" Class="empty-state-subtitle">
                        ابدأ بإضافة شرائح متحركة جديدة لعرضها في صفحة "من نحن"
                    </MudText>
                    <MudButton Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Add"
                               OnClick="OpenCreateDialog"
                               Variant="Variant.Filled"
                               Size="Size.Large"
                               Class="empty-state-action-btn">
                        إضافة أول شريحة متحركة
                    </MudButton>
                </div>
            </MudPaper>
        </div>
    }
    else
    {
        <!-- الجدول العادي للشاشات الكبيرة -->
        <div class="regular-table">
            <MudPaper Class="pa-4">
                <MudTable Items="@_siderAboutUs" Loading="@_loading" Filter="FilterFunc" Dense="true" Hover="true" Bordered="true">
                    <HeaderContent>
                        <MudTh>ت</MudTh>
                        <MudTh>العنوان</MudTh>
                        <MudTh>عدد الصور</MudTh>
                        <MudTh>الترتيب</MudTh>
                        <MudTh>المدة (ثانية)</MudTh>
                        <MudTh>نشط</MudTh>
                        <MudTh>تاريخ الإنشاء</MudTh>
                        <MudTh>الإجراءات</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="ت">@(_siderAboutUs.IndexOf(context) + 1)</MudTd>
                        <MudTd>@(context.Title?.Length > 30 ? context.Title.Substring(0, 30) + "..." : context.Title)</MudTd>
                        <MudTd>
                            <MudChip T="string" Color="Color.Primary" Size="Size.Small">
                                @context.Images.Count صورة
                            </MudChip>
                        </MudTd>
                        <MudTd>@context.DisplayOrder</MudTd>
                        <MudTd>@context.Duration</MudTd>
                        <MudTd>
                            <MudChip T="string" Color="@(context.IsActive? Color.Success: Color.Default)" Size="Size.Small">
                                @(context.IsActive ? "نشط" : "غير نشط")
                            </MudChip>
                        </MudTd>
                        <MudTd>@context.CreatedAt.ToString("yyyy/MM/dd")</MudTd>
                        <MudTd>
                            <div class="d-flex gap-1">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                               Color="Color.Primary"
                                               Size="Size.Small"
                                               OnClick="@(() => OpenEditDialog(context))"
                                               Title="تعديل" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                               Color="Color.Error"
                                               Size="Size.Small"
                                               OnClick="@(() => DeleteSiderAboutUs(context.Id))"
                                               Title="حذف" />
                                <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                               Color="Color.Info"
                                               Size="Size.Small"
                                               OnClick="@(() => ViewSiderAboutUsDetails(context))"
                                               Title="عرض التفاصيل" />
                            </div>
                        </MudTd>
                    </RowTemplate>
                    <PagerContent>
                        <MudTablePager PageSizeOptions="new int[] { 10, 25, 50, 100 }" />
                    </PagerContent>
                </MudTable>
            </MudPaper>
        </div>

        <!-- تخطيط البطاقات المحسن للشاشات الصغيرة -->
        <div class="mobile-cards">
            @foreach (var siderAboutUs in _siderAboutUs.Where(FilterFunc))
            {
                <MudCard Class="mobile-card" Elevation="1">
                    <MudCardContent Class="compact-content">
                        <!-- رقم الشريحة والإجراءات -->
                        <div class="gif-header">
                            <MudChip T="string" Color="Color.Primary" Size="Size.Small" Class="gif-number">
                                @(_siderAboutUs.IndexOf(siderAboutUs) + 1)
                            </MudChip>
                            <div class="gif-actions">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                               Color="Color.Primary"
                                               Size="Size.Small"
                                               OnClick="@(() => OpenEditDialog(siderAboutUs))"
                                               Title="تعديل" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                               Color="Color.Error"
                                               Size="Size.Small"
                                               OnClick="@(() => DeleteSiderAboutUs(siderAboutUs.Id))"
                                               Title="حذف" />
                                <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                               Color="Color.Info"
                                               Size="Size.Small"
                                               OnClick="@(() => ViewSiderAboutUsDetails(siderAboutUs))"
                                               Title="عرض التفاصيل" />
                            </div>
                        </div>

                        <!-- عنوان الشريحة -->
                        <div class="gif-title">
                            @(siderAboutUs.Title ?? "غير محدد")
                        </div>

                        <!-- معلومات الشريحة -->
                        @if (!string.IsNullOrEmpty(siderAboutUs.Description))
                        {
                            <div class="gif-info">
                                <MudIcon Icon="@Icons.Material.Filled.Description" Size="Size.Small" Class="info-icon" />
                                <span class="info-label">الوصف:</span>
                                <span>@(siderAboutUs.Description.Length > 30 ? siderAboutUs.Description.Substring(0, 30) + "..." : siderAboutUs.Description)</span>
                            </div>
                        }
                        <div class="gif-info">
                            <MudIcon Icon="@Icons.Material.Filled.Image" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">عدد الصور:</span>
                            <span>@siderAboutUs.Images.Count</span>
                        </div>
                        <div class="gif-info">
                            <MudIcon Icon="@Icons.Material.Filled.Sort" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">الترتيب:</span>
                            <span>@siderAboutUs.DisplayOrder</span>
                        </div>
                        <div class="gif-info">
                            <MudIcon Icon="@Icons.Material.Filled.Timer" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">المدة:</span>
                            <span>@siderAboutUs.Duration ثانية</span>
                        </div>
                        <div class="gif-info">
                            <MudIcon Icon="@Icons.Material.Filled.DateRange" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">تاريخ الإنشاء:</span>
                            <span>@siderAboutUs.CreatedAt.ToString("yyyy/MM/dd")</span>
                        </div>

                        <!-- حالة الشريحة -->
                        <div class="gif-info">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">الحالة:</span>
                            @if (siderAboutUs.IsActive)
                            {
                                <MudChip T="string" Color="Color.Success" Size="Size.Small" Class="status-chip">نشط</MudChip>
                            }
                            else
                            {
                                <MudChip T="string" Color="Color.Error" Size="Size.Small" Class="status-chip">غير نشط</MudChip>
                            }
                        </div>
                    </MudCardContent>
                </MudCard>
            }
        </div>
    }
</MudContainer>

<!-- مودال عرض الصورة بحجم كامل -->
@if (_showImageModal)
{
    <div class="image-modal-overlay" @onclick="HideImageModal" @onkeydown="@(async (e) => { if (e.Key == "Escape") HideImageModal(); })" tabindex="0">
        <button class="image-modal-close" @onclick="HideImageModal" @onclick:stopPropagation="true" title="إغلاق">
            <MudIcon Icon="@Icons.Material.Filled.Close" />
        </button>
        <div class="image-modal-content-wrapper" @onclick:stopPropagation="true">
            <img src="@_modalImageUrl"
                 alt="@_modalImageTitle"
                 class="image-modal-content" />
            <div class="image-modal-title">@_modalImageTitle</div>
        </div>
    </div>
}
