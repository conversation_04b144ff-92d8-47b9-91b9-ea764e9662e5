using AutoMapper;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BlogMud.Controllers
{
    /// <summary>
    /// وحدة تحكم واجهة برمجة التطبيقات لإدارة رسائل الاتصال
    /// </summary>
    /// <remarks>
    /// توفر هذه الوحدة واجهات برمجة تطبيقات RESTful للتعامل مع رسائل الاتصال
    /// بما في ذلك عمليات الإنشاء والقراءة والتحديث والحذف
    /// تتطلب معظم العمليات صلاحيات المسؤول باستثناء إنشاء رسالة جديدة
    /// </remarks>
    [Route("api/[controller]")]
    [ApiController]
    public class ContactMessagesController : ControllerBase
    {
        #region المتغيرات والمنشئ

        private readonly IUnitOfWork _unitOfWork;
        private readonly IRepository<ContactMessage> _contactMessageRepository;
        private readonly ILogger<ContactMessagesController> _logger;
        private readonly IMapper _mapper;
        private readonly BlogMud.Services.EmailService _emailService;

        /// <summary>
        /// منشئ وحدة تحكم رسائل الاتصال
        /// </summary>
        /// <param name="unitOfWork">واجهة وحدة العمل للتعامل مع قاعدة البيانات</param>
        /// <param name="logger">خدمة تسجيل الأحداث</param>
        /// <param name="mapper">خدمة التحويل بين الكيانات ونماذج نقل البيانات</param>
        /// <param name="emailService">خدمة البريد الإلكتروني لإرسال الإشعارات</param>
        /// <remarks>
        /// يقوم بتهيئة المستودعات والخدمات اللازمة لإدارة رسائل الاتصال
        /// </remarks>
        public ContactMessagesController(IUnitOfWork unitOfWork, ILogger<ContactMessagesController> logger, IMapper mapper, BlogMud.Services.EmailService emailService)
        {
            _unitOfWork = unitOfWork;
            _contactMessageRepository = _unitOfWork.Repository<ContactMessage>();
            _logger = logger;
            _mapper = mapper;
            _emailService = emailService;
        }

        #endregion

        #region عمليات القراءة (GET)

        /// <summary>
        /// الحصول على جميع رسائل الاتصال
        /// </summary>
        /// <returns>قائمة بجميع رسائل الاتصال</returns>
        /// <response code="200">تم استرجاع قائمة رسائل الاتصال بنجاح</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// هذه الطريقة تقوم باسترجاع جميع رسائل الاتصال من قاعدة البيانات
        /// وتحويلها إلى نماذج نقل البيانات (DTOs) قبل إرجاعها للمستخدم
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// </remarks>
        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<ContactMessageDto>>> GetContactMessages()
        {
            try
            {
                _logger.LogInformation("Fetching all contact messages");
                var contactMessages = await _contactMessageRepository.GetAllAsync();
                var contactMessageDtos = _mapper.Map<IEnumerable<ContactMessageDto>>(contactMessages);
                _logger.LogInformation("Retrieved {Count} contact messages", contactMessageDtos.Count());
                return Ok(contactMessageDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving contact messages");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على رسالة اتصال محددة بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف رسالة الاتصال المطلوبة</param>
        /// <returns>رسالة الاتصال المطلوبة</returns>
        /// <response code="200">تم استرجاع رسالة الاتصال بنجاح</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="404">لم يتم العثور على رسالة الاتصال بالمعرف المحدد</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تقوم هذه الطريقة بالبحث عن رسالة اتصال بناءً على المعرف المقدم
        /// وإرجاع نموذج نقل البيانات (DTO) المقابل إذا تم العثور عليه
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// </remarks>
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ContactMessageDto>> GetContactMessage(int id)
        {
            try
            {
                _logger.LogInformation("Fetching contact message with ID: {ContactMessageId}", id);
                var contactMessage = await _contactMessageRepository.GetByIdAsync(id);

                if (contactMessage == null)
                {
                    _logger.LogWarning("Contact message not found with ID: {ContactMessageId}", id);
                    return NotFound();
                }

                var contactMessageDto = _mapper.Map<ContactMessageDto>(contactMessage);
                return Ok(contactMessageDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving contact message with ID: {ContactMessageId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #endregion

        #region عمليات التعديل (PUT/POST/DELETE)

        /// <summary>
        /// تحديث رسالة اتصال موجودة
        /// </summary>
        /// <param name="id">معرف رسالة الاتصال المراد تحديثها</param>
        /// <param name="contactMessageDto">نموذج نقل البيانات المحتوي على المعلومات المحدثة</param>
        /// <returns>استجابة HTTP بدون محتوى في حالة نجاح التحديث</returns>
        /// <response code="204">تم تحديث رسالة الاتصال بنجاح</response>
        /// <response code="400">معرف الرسالة في المسار لا يتطابق مع المعرف في النموذج</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="404">لم يتم العثور على رسالة الاتصال بالمعرف المحدد</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// وتقوم بالتحقق من وجود الرسالة قبل تحديثها
        /// ثم تستخدم AutoMapper لتحديث الكيان الموجود بالبيانات الجديدة
        /// </remarks>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> PutContactMessage(int id, ContactMessageDto contactMessageDto)
        {
            if (id != contactMessageDto.Id)
            {
                return BadRequest("Contact message ID mismatch");
            }

            try
            {
                _logger.LogInformation("Updating contact message with ID: {ContactMessageId}", id);

                // First check if the contact message exists
                var existingContactMessage = await _contactMessageRepository.GetByIdAsync(id);
                if (existingContactMessage == null)
                {
                    _logger.LogWarning("Contact message not found with ID: {ContactMessageId}", id);
                    return NotFound();
                }

                // Use AutoMapper to map from DTO to existing entity
                _mapper.Map(contactMessageDto, existingContactMessage);

                // Update in the repository
                _contactMessageRepository.Update(existingContactMessage);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Contact message updated successfully: {ContactMessageId}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating contact message: {ContactMessageId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء رسالة اتصال جديدة
        /// </summary>
        /// <param name="contactMessageDto">نموذج نقل البيانات المحتوي على معلومات رسالة الاتصال الجديدة</param>
        /// <returns>رسالة الاتصال التي تم إنشاؤها مع رابط للوصول إليها</returns>
        /// <response code="201">تم إنشاء رسالة الاتصال بنجاح</response>
        /// <response code="400">البيانات المقدمة غير صالحة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// هذه الطريقة متاحة للمستخدمين العاديين (لا تتطلب صلاحيات خاصة)
        /// وتقوم بتعيين قيم افتراضية لبعض الحقول:
        /// - تاريخ الإرسال: الوقت الحالي
        /// - حالة القراءة: غير مقروءة (IsRead = false)
        /// - حالة الرد: لم يتم الرد (IsResponded = false)
        /// </remarks>
        [HttpPost]
        public async Task<ActionResult<ContactMessageDto>> PostContactMessage(ContactMessageDto contactMessageDto)
        {
            try
            {
                _logger.LogInformation("Creating new contact message from: {Name}", contactMessageDto.Name);

                // Set submission date
                contactMessageDto.SubmissionDate = DateTime.Now;
                contactMessageDto.IsRead = false;
                contactMessageDto.IsResponded = false;

                // Convert DTO to domain model using AutoMapper
                var contactMessage = _mapper.Map<ContactMessage>(contactMessageDto);

                await _contactMessageRepository.AddAsync(contactMessage);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Contact message created successfully with ID: {ContactMessageId}", contactMessage.Id);

                // Send email notification to admin
                try
                {
                    await _emailService.SendContactNotificationAsync(contactMessage);
                    _logger.LogInformation("Contact notification email sent successfully for message ID: {ContactMessageId}", contactMessage.Id);
                }
                catch (Exception emailEx)
                {
                    _logger.LogError(emailEx, "Failed to send contact notification email for message ID: {ContactMessageId}", contactMessage.Id);
                    // Don't fail the entire operation if email sending fails
                }

                // Convert the created contact message back to DTO using AutoMapper
                var createdContactMessageDto = _mapper.Map<ContactMessageDto>(contactMessage);

                return CreatedAtAction("GetContactMessage", new { id = createdContactMessageDto.Id }, createdContactMessageDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating contact message from: {Name}", contactMessageDto.Name);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف رسالة اتصال
        /// </summary>
        /// <param name="id">معرف رسالة الاتصال المراد حذفها</param>
        /// <returns>استجابة HTTP بدون محتوى في حالة نجاح الحذف</returns>
        /// <response code="204">تم حذف رسالة الاتصال بنجاح</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="404">لم يتم العثور على رسالة الاتصال بالمعرف المحدد</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// وتقوم بالتحقق من وجود الرسالة قبل حذفها
        /// </remarks>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteContactMessage(int id)
        {
            try
            {
                _logger.LogInformation("Deleting contact message with ID: {ContactMessageId}", id);

                var contactMessage = await _contactMessageRepository.GetByIdAsync(id);
                if (contactMessage == null)
                {
                    _logger.LogWarning("Contact message not found with ID: {ContactMessageId}", id);
                    return NotFound();
                }

                _contactMessageRepository.Remove(contactMessage);
                await _unitOfWork.SaveAsync();
                _logger.LogInformation("Contact message deleted successfully: {ContactMessageId}", id);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting contact message with ID: {ContactMessageId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #endregion
    }
}
