using System.Linq.Expressions;

namespace BlogMud.Shared.Repositories
{
    public interface IRepository<T> where T : class
    {
        // Get Methods
        T GetById(object id);
        Task<T> GetByIdAsync(object id);
        IEnumerable<T> GetAll(
            Expression<Func<T, bool>> filter = null,
            Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
            string includeProperties = "",
            bool tracked = true);
        Task<IEnumerable<T>> GetAllAsync(
            Expression<Func<T, bool>> filter = null,
            Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
            string includeProperties = "",
            bool tracked = true);
        T GetFirstOrDefault(
            Expression<Func<T, bool>> filter = null,
            string includeProperties = "",
            bool tracked = true);
        Task<T> GetFirstOrDefaultAsync(
            Expression<Func<T, bool>> filter = null,
            string includeProperties = "",
            bool tracked = true);

        // Add Methods
        void Add(T entity);
        Task AddAsync(T entity);
        void AddRange(IEnumerable<T> entities);
        Task AddRangeAsync(IEnumerable<T> entities);

        // Remove Methods
        void Remove(T entity);
        void RemoveRange(IEnumerable<T> entities);

        // Update Methods
        void Update(T entity);
    }
}
