using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    /// <summary>
    /// نموذج الشرائح المتحركة لصفحة "من نحن"
    /// نظام منفصل لإدارة عروض الصور المتعددة في صفحة من نحن
    /// </summary>
    public class SiderAboutUs
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// عنوان الشريحة أو العرض
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// وصف الشريحة أو العرض
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// روابط الصور المتعددة مفصولة بفاصلة منقوطة (;)
        /// مسار الحفظ: wwwroot/AboutUsImg/SiderAboutUsImg/
        /// </summary>
        [Required]
        [StringLength(2000)]
        public string ImageUrls { get; set; } = string.Empty;

        /// <summary>
        /// مدة العرض بالثواني لكل صورة في الشريحة
        /// </summary>
        public int Duration { get; set; } = 5;

        /// <summary>
        /// ترتيب عرض الشريحة
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// حالة الشريحة (نشطة أو غير نشطة)
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// رابط اختياري للانتقال عند النقر على الشريحة
        /// </summary>
        [StringLength(255)]
        public string LinkUrl { get; set; } = string.Empty;

        /// <summary>
        /// نص الرابط المعروض
        /// </summary>
        [StringLength(100)]
        public string LinkText { get; set; } = string.Empty;

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ إنشاء الشريحة
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }
    }
}
