/* Contact Page Responsive Styles */

/* Hero Section */
.hero-section {
    position: relative;
    background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url('/images/contact-hero.jpg') no-repeat center center;
    background-size: cover;
    height: 300px;
}

/* Contact Form Container */
.contact-form-container {
    padding: 2rem;
}

/* Contact Info Cards */
.contact-info-card {
    padding: 2rem;
    margin-bottom: 1.5rem;
}

.contact-info-item {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.contact-info-item:last-child {
    margin-bottom: 0;
}

/* Social Media Icons */
.social-icons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Working Hours */
.working-hours {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.working-hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Map Container */
.map-container {
    height: 400px;
    margin-bottom: -80px;
    z-index: 1;
    position: relative;
}

#map {
    height: 100%;
    width: 100%;
    background-color: #e9ecef;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 1.1rem;
}

#map::before {
    content: "خريطة الموقع - قريباً";
}

/* Form Validation Styles */
.mud-input-error {
    border-color: #dc3545 !important;
}

.mud-input-error-text {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Submit Button Loading State */
.submit-button-loading {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Touch Target Optimization for Mobile */
.mud-button {
    min-height: 44px;
    min-width: 44px;
}

.mud-icon-button {
    min-height: 44px;
    min-width: 44px;
}

/* Desktop Layout (>1024px) - Multi-column */
@media (min-width: 1024px) {
    .contact-container {
        padding: 4rem 0;
    }
    
    .contact-form-container {
        padding: 3rem;
    }
    
    .contact-info-card {
        padding: 3rem;
    }
    
    .hero-section {
        height: 350px;
    }
    
    /* Enhanced spacing for desktop */
    .contact-info-item {
        margin-bottom: 1.5rem;
    }
    
    .working-hours {
        gap: 0.75rem;
    }
}

/* Tablet Layout (768px-1024px) - Adaptive */
@media (min-width: 768px) and (max-width: 1023px) {
    .contact-container {
        padding: 3rem 0;
    }
    
    .contact-form-container {
        padding: 2.5rem;
    }
    
    .contact-info-card {
        padding: 2.5rem;
    }
    
    .hero-section {
        height: 320px;
    }
    
    /* Adaptive grid for tablet */
    .contact-grid {
        gap: 2rem;
    }
    
    /* Stack contact info cards on tablet */
    .contact-info-section {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }
}

/* Mobile Layout (<768px) - Single-column */
@media (max-width: 767px) {
    .contact-container {
        padding: 2rem 0;
    }
    
    .contact-form-container {
        padding: 1.5rem;
    }
    
    .contact-info-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .hero-section {
        height: 250px;
    }
    
    /* Single column layout for mobile */
    .contact-grid {
        gap: 1.5rem;
    }
    
    /* Stack form fields vertically on mobile */
    .form-row {
        flex-direction: column;
    }
    
    /* Optimize contact info for mobile */
    .contact-info-item {
        margin-bottom: 1.25rem;
    }
    
    .working-hours-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    /* Social icons responsive */
    .social-icons {
        justify-content: center;
    }
    
    /* Map height adjustment for mobile */
    .map-container {
        height: 300px;
        margin-bottom: -60px;
    }
    
    /* Form button full width on mobile */
    .submit-button-mobile {
        width: 100%;
    }
}

/* RTL Support */
[dir="rtl"] .contact-info-item {
    flex-direction: row;
}

[dir="rtl"] .working-hours-item {
    text-align: right;
}

[dir="rtl"] .social-icons {
    justify-content: flex-start;
}

/* Accessibility Enhancements */
.contact-form-container:focus-within {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .hero-section {
        background: linear-gradient(rgba(0,0,0,0.8), rgba(0,0,0,0.8)), url('/images/contact-hero.jpg') no-repeat center center;
    }
    
    .contact-info-card {
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .hero-section {
        background-attachment: scroll;
    }
    
    .mud-button {
        transition: none;
    }
}

/* Print styles */
@media print {
    .hero-section {
        background: none;
        color: #000;
        border-bottom: 2px solid #000;
    }
    
    .map-container {
        display: none;
    }
    
    .social-icons {
        display: none;
    }
    
    .submit-button-mobile {
        display: none;
    }
}

/* Loading state animations */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.submit-button-loading .mud-progress-circular {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Form focus states */
.mud-input-control:focus-within {
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Success state styling */
.form-success {
    border: 2px solid #28a745;
    background-color: #d4edda;
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
}

/* Error state styling */
.form-error {
    border: 2px solid #dc3545;
    background-color: #f8d7da;
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
}
