using BlogMud.Client.Pages.Admin.Posts.Post.NewsPageSiderMove;
using BlogMud.Shared.DTOs;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Net.Http.Json;

namespace BlogMud.Client.Pages.Admin.Posts.Post.NewsPageSiderMove;

public partial class NewsSiderMove
{
    //[Inject] private HttpClient Http { get; set; } = default!;
    //[Inject] private ISnackbar _Snackbar { get; set; } = default!;
    /// <summary>
    /// مثيل مربع الحوار MudDialog الذي يتم تمريره من خلال CascadingParameter
    /// </summary>
    [CascadingParameter] public required IMudDialogInstance MudDialog { get; set; }

    #region المتغيرات الخاصة
    // متغيرات الكاروسيل
    private bool arrows = true;
    private bool bullets = true;
    private bool enableSwipeGesture = true;
    private bool autocycle = true;
    private bool fixedheader = false;

    private Transition transition = Transition.Slide;

    // متغير للتأكد من عرض الأسهم على الهاتف المحمول
    private bool forceShowArrowsOnMobile = true;

    private List<NewsSiderMoveDto> _newsSiderMoves = new();
    private bool _loading = false;
    private string _searchString = "";

    // متغيرات عرض الصورة الكاملة
    private bool _showImageModal = false;
    private string _modalImageUrl = "";
    private string _modalImageTitle = "";

    #endregion

    #region دورة حياة المكون

    protected override async Task OnInitializedAsync()
    {
        await LoadNewsSiderMoves();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && _showImageModal)
        {
            // Focus on modal overlay for keyboard events
            await Task.Delay(100);
            StateHasChanged();
        }
    }

    #endregion

    #region تحميل البيانات

    private async Task LoadNewsSiderMoves()
    {
        try
        {
            _loading = true;
            StateHasChanged();

            var response = await Http.GetFromJsonAsync<List<NewsSiderMoveDto>>("api/NewsSiderMove");
            if (response != null)
            {
                _newsSiderMoves = response;
            }
            else
            {
                _newsSiderMoves = new List<NewsSiderMoveDto>();
                _Snackbar.Add("لم يتم العثور على بيانات الشرائح المتحركة للأخبار", Severity.Warning);
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"خطأ في تحميل الشرائح المتحركة للأخبار: {ex.Message}", Severity.Error);
            _newsSiderMoves = new List<NewsSiderMoveDto>();
        }
        finally
        {
            _loading = false;
            StateHasChanged();
        }
    }

    #endregion

    #region البحث والتصفية

    private bool FilterFunc(NewsSiderMoveDto newsSiderMove)
    {
        if (string.IsNullOrWhiteSpace(_searchString))
            return true;

        var searchTerm = _searchString.ToLower();

        return newsSiderMove.Title.ToLower().Contains(searchTerm) ||
               (newsSiderMove.Description?.ToLower().Contains(searchTerm) ?? false) ||
               (newsSiderMove.LinkText?.ToLower().Contains(searchTerm) ?? false) ||
               (newsSiderMove.Notes?.ToLower().Contains(searchTerm) ?? false);
    }

    #endregion

    #region إدارة النوافذ المنبثقة

    private async Task OpenCreateDialog()
    {
        try
        {
            var newNewsSiderMove = new NewsSiderMoveDto
            {
                Title = "",
                Description = "",
                ImageUrls = "",
                Duration = 5,
                DisplayOrder = 0,
                IsActive = true,
                LinkUrl = "",
                LinkText = "",
                Notes = ""
            };

            var parameters = new DialogParameters<NewsSiderMoveEditForm>
            {
                { x => x.NewsSiderMove, newNewsSiderMove },
                { x => x.IsCreateMode, true }
            };

            var options = new DialogOptions()
            {
                MaxWidth = MaxWidth.Large,
                FullWidth = true,
                CloseButton = true,
                BackdropClick = false,
                CloseOnEscapeKey = false
            };

            var dialog = await DialogService.ShowAsync<NewsSiderMoveEditForm>(
                "إضافة شريحة متحركة جديدة للأخبار",
                parameters,
                options);

            var result = await dialog.Result;
            if (!result.Canceled && result.Data is NewsSiderMoveDto createdNewsSiderMove)
            {
                await HandleCreateNewsSiderMove(createdNewsSiderMove);
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"خطأ في فتح نافذة الإنشاء: {ex.Message}", Severity.Error);
        }
    }

    private async Task OpenEditDialog(NewsSiderMoveDto newsSiderMove)
    {
        try
        {
            // الحصول على البيانات الحديثة من الخادم
            var response = await Http.GetFromJsonAsync<NewsSiderMoveDto>($"api/NewsSiderMove/{newsSiderMove.Id}");
            if (response == null)
            {
                _Snackbar.Add("لم يتم العثور على الشريحة المتحركة للأخبار", Severity.Error);
                return;
            }

            var freshData = response;

            // Create a completely new instance to avoid reference issues
            var editingNewsSiderMove = new NewsSiderMoveDto
            {
                Id = freshData.Id,
                Title = freshData.Title ?? "",
                Description = freshData.Description ?? "",
                ImageUrls = freshData.ImageUrls ?? "",
                Duration = freshData.Duration,
                DisplayOrder = freshData.DisplayOrder,
                IsActive = freshData.IsActive,
                CreatedAt = freshData.CreatedAt,
                LastModifiedAt = freshData.LastModifiedAt,
                LinkUrl = freshData.LinkUrl ?? "",
                LinkText = freshData.LinkText ?? "",
                Notes = freshData.Notes ?? ""
            };

            var parameters = new DialogParameters<NewsSiderMoveEditForm>
            {
                { x => x.NewsSiderMove, editingNewsSiderMove },
                { x => x.IsCreateMode, false }
            };

            var options = new DialogOptions()
            {
                MaxWidth = MaxWidth.Large,
                FullWidth = true,
                CloseButton = true,
                BackdropClick = false,
                CloseOnEscapeKey = false
            };

            var dialog = await DialogService.ShowAsync<NewsSiderMoveEditForm>(
                $"تعديل الشريحة المتحركة للأخبار: {editingNewsSiderMove.Title}",
                parameters,
                options);

            var result = await dialog.Result;
            if (!result.Canceled && result.Data is NewsSiderMoveDto updatedNewsSiderMove)
            {
                await HandleSaveNewsSiderMove(updatedNewsSiderMove);
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"خطأ في فتح نافذة التعديل: {ex.Message}", Severity.Error);
        }
    }

    #endregion

    #region عمليات CRUD

    private async Task HandleCreateNewsSiderMove(NewsSiderMoveDto newNewsSiderMove)
    {
        try
        {
            // Ensure we're not sending null values
            newNewsSiderMove.Title ??= "";
            newNewsSiderMove.Description ??= "";
            newNewsSiderMove.ImageUrls ??= "";
            newNewsSiderMove.LinkUrl ??= "";
            newNewsSiderMove.LinkText ??= "";
            newNewsSiderMove.Notes ??= "";

            var response = await Http.PostAsJsonAsync("api/NewsSiderMove", newNewsSiderMove);

            if (response.IsSuccessStatusCode)
            {
                _Snackbar.Add("تم إنشاء الشريحة المتحركة للأخبار بنجاح", Severity.Success);
                await LoadNewsSiderMoves();
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _Snackbar.Add($"خطأ في إنشاء الشريحة المتحركة للأخبار: {errorContent}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"خطأ في إنشاء الشريحة المتحركة للأخبار: {ex.Message}", Severity.Error);
        }
    }

    private async Task HandleSaveNewsSiderMove(NewsSiderMoveDto updatedNewsSiderMove)
    {
        try
        {
            // Ensure we're not sending null values
            updatedNewsSiderMove.Title ??= "";
            updatedNewsSiderMove.Description ??= "";
            updatedNewsSiderMove.ImageUrls ??= "";
            updatedNewsSiderMove.LinkUrl ??= "";
            updatedNewsSiderMove.LinkText ??= "";
            updatedNewsSiderMove.Notes ??= "";

            // Add the current timestamp for LastModifiedAt
            updatedNewsSiderMove.LastModifiedAt = DateTime.Now;

            // Send the update request
            var response = await Http.PutAsJsonAsync($"api/NewsSiderMove/{updatedNewsSiderMove.Id}", updatedNewsSiderMove);

            if (response.IsSuccessStatusCode)
            {
                _Snackbar.Add("تم تحديث الشريحة المتحركة للأخبار بنجاح", Severity.Success);
                await LoadNewsSiderMoves();
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _Snackbar.Add($"خطأ في تحديث الشريحة المتحركة للأخبار: {errorContent}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"خطأ في تحديث الشريحة المتحركة للأخبار: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteNewsSiderMove(int id)
    {
        try
        {
            var newsSiderMove = _newsSiderMoves.FirstOrDefault(s => s.Id == id);
            if (newsSiderMove == null)
            {
                _Snackbar.Add("لم يتم العثور على الشريحة المتحركة للأخبار", Severity.Warning);
                return;
            }

            bool? result = await DialogService.ShowMessageBox(
                "تأكيد الحذف",
                $"هل أنت متأكد من حذف الشريحة المتحركة للأخبار '{newsSiderMove.Title}'؟\n\nسيتم حذف جميع الصور المرتبطة بها نهائياً.",
                yesText: "حذف", cancelText: "إلغاء");

            if (result == true)
            {
                var response = await Http.DeleteAsync($"api/NewsSiderMove/{id}");

                if (response.IsSuccessStatusCode)
                {
                    _Snackbar.Add("تم حذف الشريحة المتحركة للأخبار بنجاح", Severity.Success);
                    await LoadNewsSiderMoves();
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _Snackbar.Add($"خطأ في حذف الشريحة المتحركة للأخبار: {errorContent}", Severity.Error);
                }
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"خطأ في حذف الشريحة المتحركة للأخبار: {ex.Message}", Severity.Error);
        }
    }

    #endregion

    #region عرض الصورة الكاملة

    private void ShowImageModal(string imageUrl, string title)
    {
        try
        {
            if (string.IsNullOrEmpty(imageUrl))
            {
                _Snackbar.Add("رابط الصورة غير صحيح", Severity.Warning);
                return;
            }

            _modalImageUrl = imageUrl;
            _modalImageTitle = title ?? "صورة";
            _showImageModal = true;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"خطأ في عرض الصورة: {ex.Message}", Severity.Error);
        }
    }

    private void HideImageModal()
    {
        try
        {
            _showImageModal = false;
            _modalImageUrl = "";
            _modalImageTitle = "";
            StateHasChanged();
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"خطأ في إغلاق الصورة: {ex.Message}", Severity.Error);
        }
    }

    #endregion
}
