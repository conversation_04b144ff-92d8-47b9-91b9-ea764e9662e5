using BlogMud.Services;
using Microsoft.AspNetCore.Mvc;

namespace BlogMud.Controllers
{
    [Route("newsletter")]
    [ApiController]
    public class NewsletterController : ControllerBase
    {
        private readonly INewsletterService _newsletterService;
        private readonly ILogger<NewsletterController> _logger;

        public NewsletterController(INewsletterService newsletterService, ILogger<NewsletterController> logger)
        {
            _newsletterService = newsletterService;
            _logger = logger;
        }

        [HttpPost("subscribe")]
        public async Task<IActionResult> Subscribe([FromBody] SubscribeRequest request)
        {
            try
            {
                var result = await _newsletterService.SubscribeAsync(request.Email, request.FullName);
                
                if (result)
                {
                    return Ok(new { success = true, message = "تم الاشتراك بنجاح. يرجى التحقق من بريدك الإلكتروني لتأكيد الاشتراك." });
                }
                else
                {
                    return BadRequest(new { success = false, message = "البريد الإلكتروني مشترك بالفعل." });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error subscribing email {Email}", request.Email);
                return StatusCode(500, new { success = false, message = "حدث خطأ أثناء الاشتراك. يرجى المحاولة مرة أخرى." });
            }
        }

        [HttpGet("confirm")]
        public async Task<IActionResult> ConfirmSubscription([FromQuery] string token)
        {
            try
            {
                var result = await _newsletterService.ConfirmSubscriptionAsync(token);
                
                if (result)
                {
                    return Redirect("/?confirmed=true");
                }
                else
                {
                    return Redirect("/?confirmed=false");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming subscription with token {Token}", token);
                return Redirect("/?confirmed=error");
            }
        }

        [HttpGet("unsubscribe")]
        public async Task<IActionResult> Unsubscribe([FromQuery] string email)
        {
            try
            {
                var result = await _newsletterService.UnsubscribeAsync(email);
                
                if (result)
                {
                    return Redirect("/?unsubscribed=true");
                }
                else
                {
                    return Redirect("/?unsubscribed=false");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unsubscribing email {Email}", email);
                return Redirect("/?unsubscribed=error");
            }
        }

        [HttpPost("check-subscription")]
        public async Task<IActionResult> CheckSubscription([FromBody] CheckSubscriptionRequest request)
        {
            try
            {
                var isSubscribed = await _newsletterService.IsSubscribedAsync(request.Email);
                return Ok(new { isSubscribed });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking subscription for email {Email}", request.Email);
                return StatusCode(500, new { error = "حدث خطأ أثناء التحقق من الاشتراك." });
            }
        }
    }

    public class SubscribeRequest
    {
        public string Email { get; set; } = string.Empty;
        public string? FullName { get; set; }
    }

    public class CheckSubscriptionRequest
    {
        public string Email { get; set; } = string.Empty;
    }
}
