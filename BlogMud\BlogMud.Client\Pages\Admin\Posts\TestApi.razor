@page "/admin/posts/test"

@attribute [Authorize(Roles = "Admin")]
@layout BlogMud.Client.Layout.AdminLayout

<PageTitle>اختبار API</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-6">
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h4">اختبار API</MudText>
    </div>

    <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="TestPostsApi">
        اختبار API المنشورات
    </MudButton>

    <MudButton Variant="Variant.Filled" Color="Color.Secondary" OnClick="TestCategoriesApi" Class="ml-2">
        اختبار API الأقسام
    </MudButton>

    <MudDivider Class="my-4" />

    <MudText Typo="Typo.h5">نتيجة الاختبار:</MudText>
    <MudPaper Class="pa-4 mt-2" Elevation="3">
        <MudText>@testResult</MudText>
    </MudPaper>
</MudContainer>

@code {
    #region الخصائص والمتغيرات

    /// <summary>
    /// نتيجة اختبار API
    /// </summary>
    private string testResult = "";
    #endregion

    #region الأحداث والتفاعلات

    /// <summary>
    /// اختبار API المنشورات
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بإرسال طلب GET إلى نقطة نهاية API المنشورات
    /// وعرض نتيجة الاستجابة في واجهة المستخدم
    /// </remarks>
    private async Task TestPostsApi()
    {
        try
        {
            testResult = "جاري اختبار API المنشورات...";
            var response = await Http.GetAsync("api/Posts");

            testResult = $"استجابة API المنشورات: {response.StatusCode}\n";
            testResult += $"رأس الاستجابة: {response.Headers}\n";

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                testResult += $"محتوى الاستجابة: {content}";
            }
            else
            {
                testResult += $"خطأ: {response.ReasonPhrase}";
            }
        }
        catch (Exception ex)
        {
            testResult = $"حدث خطأ أثناء اختبار API المنشورات: {ex.Message}";
            if (ex.InnerException != null)
            {
                testResult += $"\nInner Exception: {ex.InnerException.Message}";
            }
        }
    }

    /// <summary>
    /// اختبار API الأقسام
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بإرسال طلب GET إلى نقطة نهاية API الأقسام
    /// وعرض نتيجة الاستجابة في واجهة المستخدم
    /// </remarks>
    private async Task TestCategoriesApi()
    {
        try
        {
            testResult = "جاري اختبار API الأقسام...";
            var response = await Http.GetAsync("api/Categories");

            testResult = $"استجابة API الأقسام: {response.StatusCode}\n";
            testResult += $"رأس الاستجابة: {response.Headers}\n";

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                testResult += $"محتوى الاستجابة: {content}";
            }
            else
            {
                testResult += $"خطأ: {response.ReasonPhrase}";
            }
        }
        catch (Exception ex)
        {
            testResult = $"حدث خطأ أثناء اختبار API الأقسام: {ex.Message}";
            if (ex.InnerException != null)
            {
                testResult += $"\nInner Exception: {ex.InnerException.Message}";
            }
        }
    }
    #endregion
}
