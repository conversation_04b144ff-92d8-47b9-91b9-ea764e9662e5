using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    public class UserNotification
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(1000)]
        public string Message { get; set; } = string.Empty;

        public NotificationType Type { get; set; }

        public bool IsRead { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? ReadAt { get; set; }

        // Optional: Link to related entity (e.g., Article ID for post notifications)
        public int? RelatedEntityId { get; set; }

        public string? RelatedEntityType { get; set; }
    }

    public enum NotificationType
    {
        General = 0,
        NewPost = 1,
        SystemUpdate = 2,
        Welcome = 3,
        AccountVerification = 4
    }
}
