﻿@page "/Account/Login"

@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using BlogMud.Data

@inject SignInManager<ApplicationUser> SignInManager
@inject ILogger<Login> Logger
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager

<PageTitle>تسجيل الدخول</PageTitle>
<LoginCSS />
<div class="login-container">
    <MudPaper Class="login-card" Elevation="0">
        <div class="login-header">
            <div class="login-header-content">
                <MudIcon Icon="@Icons.Material.Filled.AccountCircle" Class="welcome-icon" />
                <MudText Typo="Typo.h3" Class="mb-2" Style="font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    مرحباً بك
                </MudText>
                <MudText Typo="Typo.h6" Style="opacity: 0.95; font-weight: 400; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                    سجل دخولك للوصول إلى حسابك
                </MudText>
                <div style="margin-top: 1rem; opacity: 0.8;">
                    <MudIcon Icon="@Icons.Material.Filled.Security" Size="Size.Small" Style="margin: 0 0.5rem;" />
                    <MudIcon Icon="@Icons.Material.Filled.Verified" Size="Size.Small" Style="margin: 0 0.5rem;" />
                    <MudIcon Icon="@Icons.Material.Filled.Shield" Size="Size.Small" Style="margin: 0 0.5rem;" />
                </div>
            </div>
        </div>

        <MudGrid Spacing="0">
            <MudItem xs="12" md="6">
                <div class="login-form-section">
                    <StatusMessage Message="@errorMessage" />

                    <EditForm Model="Input" method="post" OnValidSubmit="LoginUser" FormName="login">
                        <DataAnnotationsValidator />

                        <MudText Typo="Typo.h6" Class="mb-4" Style="color: var(--mud-palette-primary); font-weight: 600; text-shadow: 0 1px 2px rgba(149,55,53,0.1);">
                            <MudIcon Icon="@Icons.Material.Filled.Person" Class="mr-2" Style="filter: drop-shadow(0 1px 2px rgba(149,55,53,0.2));" />
                            تسجيل الدخول بالحساب المحلي
                        </MudText>

                        <div class="form-input">
                            <MudStaticTextField For="@(() => Input.Email)" @bind-Value="Input.Email"
                                Label="البريد الإلكتروني" Placeholder="<EMAIL>"
                                Variant="Variant.Outlined" Margin="Margin.Normal"
                                Adornment="Adornment.Start" AdornmentIcon="@Icons.Material.Filled.Email"
                                UserAttributes="@(new() { { "autocomplete", "email" }, { "aria-required", "true" } } )" />
                        </div>

                        <div class="form-input">
                            <MudStaticTextField For="@(() => Input.Password)" @bind-Value="Input.Password"
                                Label="كلمة المرور" InputType="InputType.Password" Placeholder="أدخل كلمة المرور"
                                Variant="Variant.Outlined" Margin="Margin.Normal"
                                Adornment="Adornment.Start" AdornmentIcon="@Icons.Material.Filled.Lock"
                                UserAttributes="@(new() { { "autocomplete", "current-password" }, { "aria-required", "true" } } )" />
                        </div>

                        <div class="form-input">
                            <MudStaticCheckBox For="@(() => Input.RememberMe)" @bind-Value="Input.RememberMe"
                                Color="Color.Primary" Class="mb-3">
                                <MudText Typo="Typo.body2">تذكرني على هذا الجهاز</MudText>
                            </MudStaticCheckBox>
                        </div>

                        <MudStaticButton Variant="Variant.Filled" Color="Color.Primary"
                            FullWidth="true" FormAction="FormAction.Submit"
                            Class="login-button" Size="Size.Large">
                            <MudIcon Icon="@Icons.Material.Filled.Login" Class="mr-2" Style="filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2));" />
                            <span style="text-shadow: 0 1px 2px rgba(0,0,0,0.1);">تسجيل الدخول</span>
                        </MudStaticButton>
                    </EditForm>

                    <div class="login-links">
                        <MudStack Spacing="2">
                            <MudLink Href="Account/ForgotPassword" Color="Color.Primary">
                                <MudIcon Icon="@Icons.Material.Filled.Help" Size="Size.Small" Class="mr-2" />
                                نسيت كلمة المرور؟
                            </MudLink>
                            <MudLink Href="@(NavigationManager.GetUriWithQueryParameters("Account/Register", new Dictionary<string, object?> { ["ReturnUrl"] = ReturnUrl }))" Color="Color.Primary">
                                <MudIcon Icon="@Icons.Material.Filled.PersonAdd" Size="Size.Small" Class="mr-2" />
                                إنشاء حساب جديد
                            </MudLink>
                            <MudLink Href="Account/ResendEmailConfirmation" Color="Color.Primary">
                                <MudIcon Icon="@Icons.Material.Filled.Email" Size="Size.Small" Class="mr-2" />
                                إعادة إرسال تأكيد البريد الإلكتروني
                            </MudLink>
                        </MudStack>
                    </div>
                </div>
            </MudItem>

            <MudItem xs="12" md="6">
                <div class="login-external-section">
                    <MudText Typo="Typo.h6" Class="mb-4" Style="color: var(--mud-palette-secondary); font-weight: 600; text-shadow: 0 1px 2px rgba(89,89,89,0.1);">
                        <MudIcon Icon="@Icons.Material.Filled.Language" Class="mr-2" Style="filter: drop-shadow(0 1px 2px rgba(89,89,89,0.2));" />
                        تسجيل الدخول بخدمة أخرى
                    </MudText>

                    <ExternalLoginPicker />
                </div>
            </MudItem>
        </MudGrid>
    </MudPaper>
</div>

@code {
    private string? errorMessage;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (HttpMethods.IsGet(HttpContext.Request.Method))
        {
            // Clear the existing external cookie to ensure a clean login process
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);
        }
    }

    public async Task LoginUser()
    {
        // This doesn't count login failures towards account lockout
        // To enable password failures to trigger account lockout, set lockoutOnFailure: true
        var result = await SignInManager.PasswordSignInAsync(Input.Email, Input.Password, Input.RememberMe, lockoutOnFailure: false);
        if (result.Succeeded)
        {
            Logger.LogInformation("User logged in.");
            RedirectManager.RedirectTo(ReturnUrl);
        }
        else if (result.RequiresTwoFactor)
        {
            RedirectManager.RedirectTo(
                "Account/LoginWith2fa",
                new() { ["returnUrl"] = ReturnUrl, ["rememberMe"] = Input.RememberMe });
        }
        else if (result.IsLockedOut)
        {
            Logger.LogWarning("User account locked out.");
            RedirectManager.RedirectTo("Account/Lockout");
        }
        else if (result.IsNotAllowed)
        {
            Logger.LogWarning("User login not allowed - email not confirmed for {Email}", Input.Email);
            errorMessage = "يجب تأكيد البريد الإلكتروني قبل تسجيل الدخول. يرجى التحقق من بريدك الإلكتروني والنقر على رابط التأكيد.";
        }
        else
        {
            errorMessage = "خطأ: محاولة تسجيل دخول غير صحيحة.";
        }
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";

        [Required]
        [DataType(DataType.Password)]
        public string Password { get; set; } = "";

        [Display(Name = "Remember me?")]
        public bool RememberMe { get; set; }
    }
}
