using System;

namespace BlogMud.Shared.DTOs
{
    /// <summary>
    /// كائن نقل البيانات للشرائح المتحركة
    /// يستخدم لنقل بيانات عروض الصور المتعددة بين واجهة المستخدم والخادم
    /// </summary>
    public class SidertMoveServicesDto
    {
        public int Id { get; set; }

        /// <summary>
        /// عنوان الشريحة أو العرض
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// وصف الشريحة أو العرض
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// روابط الصور المتعددة مفصولة بفاصلة منقوطة (;)
        /// مسار الحفظ: wwwroot/OurServImg/SiderMoveOurServicesImg/
        /// </summary>
        public string ImageUrls { get; set; } = string.Empty;

        /// <summary>
        /// مدة العرض بالثواني لكل صورة في الشريحة
        /// </summary>
        public int Duration { get; set; } = 5;

        /// <summary>
        /// ترتيب ظهور الشريحة في العرض
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// حالة الشريحة (نشطة أو غير نشطة)
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ إنشاء الشريحة
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل للشريحة
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }

        /// <summary>
        /// رابط إضافي للانتقال عند النقر على الشريحة (اختياري)
        /// </summary>
        public string? LinkUrl { get; set; }

        /// <summary>
        /// نص الرابط أو الزر
        /// </summary>
        public string? LinkText { get; set; }

        /// <summary>
        /// ملاحظات إضافية للشريحة
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// قائمة الصور المستخرجة من ImageUrls للاستخدام في واجهة المستخدم
        /// </summary>
        public List<string> Images => string.IsNullOrEmpty(ImageUrls)
            ? new List<string>()
            : ImageUrls.Split(';', StringSplitOptions.RemoveEmptyEntries).ToList();
    }
}
