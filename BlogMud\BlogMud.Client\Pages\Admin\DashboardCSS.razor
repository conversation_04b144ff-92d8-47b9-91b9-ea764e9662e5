@using BlogMud.Shared.DTOs
@using System.Net.Http.Json

<style>
    /* Simple and Clean Dashboard Container */
    .dashboard-container {
        padding: 0;
        margin: 0;
        background: #ffffff;
        min-height: calc(100vh - 64px);
        position: relative;
    }

    /* Clean Header Section */
    .dashboard-header {
        background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-primary-darken) 100%);
        color: white;
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
        border-radius: 0 0 16px 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    /* Breadcrumb Section */
    .breadcrumb-section {
        margin-bottom: 1.5rem;
    }

    .dashboard-breadcrumbs {
        background: rgba(255, 255, 255, 0.15);
        border-radius: 8px;
        padding: 0.75rem 1rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .breadcrumb-link {
        color: rgba(255, 255, 255, 0.9);
        text-decoration: none;
        transition: color 0.3s ease;
        display: flex;
        align-items: center;
    }

        .breadcrumb-link:hover {
            color: white;
        }

    .breadcrumb-separator {
        color: rgba(255, 255, 255, 0.6);
        margin: 0 0.5rem;
    }

    /* Welcome Section */
    .welcome-section {
        text-align: start;
    }

    .welcome-icon-container {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        margin-bottom: 1rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .welcome-icon {
        font-size: 2rem !important;
        color: white;
    }

    .dashboard-title {
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .dashboard-subtitle {
        opacity: 0.95;
        font-weight: 400;
        line-height: 1.5;
    }

    /* Dashboard Status Chips */
    .dashboard-status {
        display: flex;
        gap: 0.75rem;
        margin-top: 1rem;
        flex-wrap: wrap;
    }

    .status-chip {
        background: rgba(255, 255, 255, 0.15) !important;
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white !important;
        font-weight: 500;
        transition: all 0.3s ease;
    }

        .status-chip:hover {
            background: rgba(255, 255, 255, 0.25) !important;
        }

    /* Quick Actions */
    .quick-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        flex-wrap: wrap;
    }

    .quick-action-btn {
        color: white;
        font-weight: 600;
        padding: 12px 24px;
        border-radius: 12px;
        transition: all 0.3s ease;
        min-height: 48px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .primary-action {
        background: rgba(255, 255, 255, 0.2);
    }

        .primary-action:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

    .secondary-action {
        background: rgba(255, 255, 255, 0.1);
    }

        .secondary-action:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

    /* Statistics Section */
    .statistics-section {
        padding: 0 1.5rem;
        margin-bottom: 3rem;
    }

    /* Section Header */
    .section-header {
        margin-bottom: 2rem;
        text-align: center;
    }

    .section-title {
        font-weight: 700;
        color: var(--mud-palette-text-primary);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .section-icon {
        color: var(--mud-palette-primary);
    }

    .section-subtitle {
        color: var(--mud-palette-text-secondary);
        opacity: 0.8;
        font-weight: 400;
    }

    /* Statistics Cards */
    .stat-card {
        border-radius: 16px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
        min-height: 200px;
        background: #ffffff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .enhanced-card {
        /* AOS animations will handle the entrance effects */
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-color);
    }

    /* Card Color Variables */
    .stat-card-primary {
        --card-color: var(--mud-palette-primary);
        --card-color-rgb: 149, 55, 53;
    }

    .stat-card-secondary {
        --card-color: var(--mud-palette-secondary);
        --card-color-rgb: 89, 89, 89;
    }

    .stat-card-info {
        --card-color: var(--mud-palette-info);
        --card-color-rgb: 33, 150, 243;
    }

    .stat-card-warning {
        --card-color: var(--mud-palette-warning);
        --card-color-rgb: 255, 152, 0;
    }

    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .stat-card-content {
        padding: 2rem 1.5rem;
        display: flex;
        flex-direction: column;
        height: 100%;
        gap: 1rem;
    }

    /* Stat Header */
    .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 0.5rem;
    }

    /* Stat Trend Indicators */
    .stat-trend {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .trend-icon {
        font-size: 1.2rem !important;
    }

    .trend-up {
        color: #4caf50;
    }

    .trend-down {
        color: #f44336;
    }

    .trend-stable {
        color: #ff9800;
    }

    /* Stat Icon Container */
    .stat-icon-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 64px;
        height: 64px;
        border-radius: 12px;
        background: var(--card-color);
        color: white;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }

    .stat-icon {
        font-size: 2.2rem !important;
    }

    .stat-card:hover .stat-icon-container {
        transform: scale(1.05);
    }

    /* Stat Details */
    .stat-details {
        flex: 1;
    }

    .stat-number {
        font-weight: 700;
        color: var(--card-color);
        margin-bottom: 0.5rem;
        font-size: 2.8rem;
        line-height: 1;
    }

    .stat-label {
        font-weight: 600;
        color: var(--mud-palette-text-primary);
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }

    .stat-description {
        color: var(--mud-palette-text-secondary);
        font-size: 0.875rem;
        line-height: 1.4;
        margin-bottom: 1rem;
    }

    /* Progress Bar */
    .stat-progress {
        margin: 1rem 0;
    }

    .progress-bar {
        border-radius: 4px;
        background: rgba(var(--card-color-rgb), 0.1);
        overflow: hidden;
    }

        .progress-bar .mud-progress-linear-bar {
            background: var(--card-color);
        }

    /* Stat Actions */
    .stat-actions {
        /* AOS will handle animations */
    }

    .stat-action-btn {
        font-weight: 600;
        border-radius: 8px;
        padding: 10px 20px;
        min-height: 40px;
        transition: all 0.3s ease;
        background: rgba(var(--card-color-rgb), 0.1);
        border: 1px solid rgba(var(--card-color-rgb), 0.2);
    }

        .stat-action-btn:hover {
            background: rgba(var(--card-color-rgb), 0.15);
            transform: translateY(-1px);
        }

    /* Management Section */
    .management-section {
        padding: 0 1.5rem;
        margin-bottom: 3rem;
    }

    .section-title {
        font-weight: 700;
        color: var(--mud-palette-text-primary);
        margin-bottom: 1.5rem;
        padding-left: 1rem;
        border-left: 4px solid var(--mud-palette-primary);
    }

    .management-card {
        border-radius: 12px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        min-height: 220px;
        background: #ffffff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

        .management-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

    .management-card-content {
        padding: 2rem 1.5rem;
        display: flex;
        flex-direction: column;
        height: 100%;
        text-align: center;
        gap: 1rem;
    }

    .management-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        border-radius: 16px;
        background: rgba(var(--mud-palette-primary), 0.1);
        margin: 0 auto 1rem;
    }

    .management-title {
        font-weight: 700;
        color: var(--mud-palette-text-primary);
        margin-bottom: 0.5rem;
    }

    .management-description {
        color: var(--mud-palette-text-secondary);
        flex: 1;
        margin-bottom: 1rem;
    }

    .management-actions {
        margin-top: auto;
    }

    .management-btn {
        border-radius: 8px;
        font-weight: 600;
        padding: 12px 24px;
        min-height: 44px;
        transition: all 0.3s ease;
    }

        .management-btn:hover {
            transform: translateY(-1px);
        }

    /* Activity Section */
    .activity-section {
        padding: 0 1.5rem 2rem;
    }

    .activity-card,
    .system-status-card {
        border-radius: 12px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        min-height: 400px;
        background: #ffffff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .activity-title,
    .system-title {
        font-weight: 700;
        color: var(--mud-palette-text-primary);
        display: flex;
        align-items: center;
    }

    .activity-content,
    .system-content {
        padding: 1rem 0;
    }

    .activity-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem;
        color: var(--mud-palette-text-secondary);
    }

    .activity-timeline {
        padding: 1rem 0;
    }

    .activity-item {
        padding: 0.5rem 0;
    }

    .activity-text {
        color: var(--mud-palette-text-primary);
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .activity-time {
        color: var(--mud-palette-text-secondary);
    }

    /* System Status */
    .system-status-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

        .system-status-item:last-child {
            border-bottom: none;
        }

    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        flex-shrink: 0;
    }

    .status-online {
        background: var(--mud-palette-success);
    }

    .status-warning {
        background: var(--mud-palette-warning);
    }

    .status-details {
        flex: 1;
    }

    .status-label {
        font-weight: 600;
        color: var(--mud-palette-text-primary);
        margin-bottom: 0.25rem;
    }

    .status-value {
        color: var(--mud-palette-text-secondary);
    }

    /* Responsive Design */
    @@media (max-width: 767px) {
        .dashboard-header {
            padding: 1.5rem 1rem;
            margin-bottom: 1.5rem;
            border-radius: 0 0 12px 12px;
        }

        .welcome-section {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .quick-actions {
            justify-content: center;
        }

        .quick-action-btn {
            width: 100%;
            max-width: 280px;
        }

        .statistics-section,
        .management-section,
        .activity-section {
            padding: 0 1rem;
        }

        .stat-card,
        .management-card {
            min-height: 160px;
        }

        .stat-card-content,
        .management-card-content {
            padding: 1.25rem;
        }

        .stat-icon-container {
            width: 50px;
            height: 50px;
        }

        .stat-number {
            font-size: 2rem;
        }

        .management-icon-wrapper {
            width: 60px;
            height: 60px;
        }

        .activity-card,
        .system-status-card {
            min-height: 300px;
        }
    }

    @@media (min-width: 768px) and (max-width: 1023px) {
        .dashboard-header {
            padding: 1.75rem 1.25rem;
        }

        .statistics-section,
        .management-section,
        .activity-section {
            padding: 0 1.25rem;
        }

        .stat-card,
        .management-card {
            min-height: 170px;
        }
    }

    @@media (min-width: 1024px) {
        .dashboard-header {
            padding: 2.5rem 2rem;
        }

        .statistics-section,
        .management-section,
        .activity-section {
            padding: 0 2rem;
        }
    }

    /* RTL Support */
    [dir="rtl"] .section-title {
        border-left: none;
        border-right: 4px solid var(--mud-palette-primary);
        padding-left: 0;
        padding-right: 1rem;
    }

    [dir="rtl"] .quick-actions {
        justify-content: flex-start;
    }

    [dir="rtl"] .activity-title,
    [dir="rtl"] .system-title {
        flex-direction: row-reverse;
    }

    /* Simple Transitions */
    * {
        transition: all 0.3s ease;
    }

    /* Accessibility */
    @@media (prefers-reduced-motion: reduce) {
        * {
            transition: none !important;
            animation: none !important;
        }
    }

    /* Focus States */
    .quick-action-btn:focus,
    .stat-action-btn:focus,
    .management-btn:focus {
        outline: 2px solid var(--mud-palette-primary);
        outline-offset: 2px;
    }

    /* AOS Animation Support */
    [data-aos] {
        pointer-events: none;
    }

    [data-aos].aos-animate {
        pointer-events: auto;
    }

    /* Custom AOS animations for dashboard */
    .dashboard-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .dashboard-card:hover {
        transform: translateY(-2px);
    }

</style>