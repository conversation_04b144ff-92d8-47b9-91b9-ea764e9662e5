using BlogMud.Shared.DTOs;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using MudBlazor;
using System.Net.Http.Headers;

namespace BlogMud.Client.Pages.Admin.Posts.AboutPage
{
    /// <summary>
    /// نموذج تعديل محتوى صفحة "من نحن" - محسن للاستجابة
    /// </summary>
    public partial class AboutUsEditForm : ComponentBase, IDisposable
    {
        #region المعاملات والخصائص

        private MudForm _form = default!;
        private MudForm _productionItemForm = default!;

        [CascadingParameter] public IMudDialogInstance? MudDialog { get; set; }

        [Parameter] public AboutUsDto AboutUs { get; set; } = new() { IsActive = true, DisplayOrder = 0 };

        [Parameter] public bool IsCreateMode { get; set; } = false;

        [Parameter] public bool IsViewMode { get; set; } = false;

        [Parameter] public EventCallback<AboutUsDto> OnSubmit { get; set; }

        [Parameter] public EventCallback OnCancel { get; set; }

        #endregion

        #region الحقن والخدمات

        // الخدمات متاحة عالمياً من خلال _Imports.razor

        #endregion

        #region متغيرات عناصر الطاقة الإنتاجية

        private bool _isProductionItemDialogOpen = false;
        private bool _isEditingProductionItem = false;
        private ProductionCapacityItemDto _currentProductionItem = new();
        private DialogOptions _dialogOptions = new() { MaxWidth = MaxWidth.Large, FullWidth = true, CloseOnEscapeKey = true };

        #endregion

        #region متغيرات الاستجابة والحالة

        private bool _isMobile = false;
        private bool _isTablet = false;
        private bool _isSubmitting = false;
        private bool _isSubmittingProductionItem = false;
        private double _formProgress = 0;
        private bool _showProductionItemsSection = false;
        private bool _showAddItemForm = false;
        private int activeTabIndex = 0;

        #endregion

        #region دوال دورة الحياة

        protected override void OnInitialized()
        {
            // إذا كان في وضع الإنشاء، تهيئة كائن جديد
            if (IsCreateMode)
            {
                AboutUs = new AboutUsDto
                {
                    IsActive = true,
                    DisplayOrder = 0,
                    CreatedAt = DateTime.Now,
                    ProductionCapacityItems = new List<ProductionCapacityItemDto>()
                };
            }
            // إذا كان في وضع التعديل أو العرض، إنشاء نسخة من الكائن الموجود
            else if (AboutUs != null)
            {
                Console.WriteLine($"AboutUsEditForm received AboutUs with ID: {AboutUs.Id} and {AboutUs.ProductionCapacityItems?.Count ?? 0} production items");
                AboutUs = new AboutUsDto
                {
                    Id = AboutUs.Id,
                    CompanyLogoUrl = AboutUs.CompanyLogoUrl,
                    CompanyDescription = AboutUs.CompanyDescription,
                    CompanyCapabilities = AboutUs.CompanyCapabilities,
                    ProductionCapacityItems = AboutUs.ProductionCapacityItems?.ToList() ?? new List<ProductionCapacityItemDto>(),
                    DisplayOrder = AboutUs.DisplayOrder,
                    IsActive = AboutUs.IsActive,
                    CreatedAt = AboutUs.CreatedAt,
                    LastModifiedAt = AboutUs.LastModifiedAt
                };
                Console.WriteLine($"AboutUsEditForm after copying: {AboutUs.ProductionCapacityItems.Count} production items");
            }

            // تحديث تقدم النموذج
            UpdateFormProgress();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await DetectScreenSize();
            }
            await base.OnAfterRenderAsync(firstRender);
        }

        private async Task DetectScreenSize()
        {
            try
            {
                // كشف حجم الشاشة للاستجابة
                var isMobile = await _JSRuntime.InvokeAsync<bool>("eval", new object[] { "window.matchMedia('(max-width: 767px)').matches" });
                var isTablet = await _JSRuntime.InvokeAsync<bool>("eval", new object[] { "window.matchMedia('(min-width: 768px) and (max-width: 1023px)').matches" });

                _isMobile = isMobile;
                _isTablet = isTablet;

                // تحديث خيارات النافذة المنبثقة بناءً على حجم الشاشة
                _dialogOptions = new DialogOptions
                {
                    MaxWidth = _isMobile ? MaxWidth.Small : MaxWidth.Large,
                    FullWidth = true,
                    CloseOnEscapeKey = true,
                    FullScreen = _isMobile
                };

                StateHasChanged();
            }
            catch
            {
                // في حالة الفشل، افتراض الجوال
                _isMobile = true;
                _isTablet = false;
                StateHasChanged();
            }
        }

        public void Dispose()
        {
            // تنظيف الموارد
        }

        #endregion

        #region دوال التحقق من صحة البيانات

        /// <summary>
        /// التحقق من صحة نموذج البيانات
        /// </summary>
        /// <param name="model">النموذج المراد التحقق منه</param>
        /// <returns>قائمة برسائل الخطأ</returns>
        private IEnumerable<string> ValidateModel(object model)
        {
            var errors = new List<string>();

            if (model is AboutUsDto aboutUs)
            {
                // التحقق من شعار الشركة
                if (string.IsNullOrWhiteSpace(aboutUs.CompanyLogoUrl))
                {
                    errors.Add("شعار الشركة مطلوب");
                }

                // التحقق من وصف الشركة
                if (string.IsNullOrWhiteSpace(aboutUs.CompanyDescription))
                {
                    errors.Add("وصف الشركة مطلوب");
                }
                else if (aboutUs.CompanyDescription.Length > 2000)
                {
                    errors.Add("وصف الشركة يجب أن يكون أقل من 2000 حرف");
                }

                // التحقق من قدرات الشركة
                if (string.IsNullOrWhiteSpace(aboutUs.CompanyCapabilities))
                {
                    errors.Add("قدرات الشركة مطلوبة");
                }
                else if (aboutUs.CompanyCapabilities.Length > 2000)
                {
                    errors.Add("قدرات الشركة يجب أن تكون أقل من 2000 حرف");
                }

                // التحقق من عناصر الطاقة الإنتاجية
                if (aboutUs.ProductionCapacityItems?.Count > 20)
                {
                    errors.Add("لا يمكن إضافة أكثر من 20 عنصر للطاقة الإنتاجية");
                }

                // التحقق من صحة كل عنصر طاقة إنتاجية
                if (aboutUs.ProductionCapacityItems?.Any() == true)
                {
                    for (int i = 0; i < aboutUs.ProductionCapacityItems.Count; i++)
                    {
                        var item = aboutUs.ProductionCapacityItems[i];

                        if (string.IsNullOrWhiteSpace(item.Title))
                        {
                            errors.Add($"عنوان العنصر رقم {i + 1} مطلوب");
                        }
                        else if (item.Title.Length > 200)
                        {
                            errors.Add($"عنوان العنصر رقم {i + 1} يجب أن يكون أقل من 200 حرف");
                        }

                        if (string.IsNullOrWhiteSpace(item.Description))
                        {
                            errors.Add($"وصف العنصر رقم {i + 1} مطلوب");
                        }
                        else if (item.Description.Length > 1000)
                        {
                            errors.Add($"وصف العنصر رقم {i + 1} يجب أن يكون أقل من 1000 حرف");
                        }
                    }
                }

                // التحقق من ترتيب العرض
                if (aboutUs.DisplayOrder < 0)
                {
                    errors.Add("ترتيب العرض يجب أن يكون رقماً موجباً");
                }
            }

            return errors;
        }

        #endregion

        #region دوال رفع الملفات

        /// <summary>
        /// رفع شعار الشركة
        /// </summary>
        /// <param name="file">ملف الشعار المراد رفعه</param>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task UploadCompanyLogo(IBrowserFile file)
        {
            if (file == null) return;

            try
            {
                // التحقق من نوع الملف
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".svg" };
                var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    _Snackbar.Add("نوع الملف غير مدعوم. يُسمح فقط بملفات JPG, JPEG, PNG, GIF, SVG", Severity.Error);
                    return;
                }

                // التحقق من حجم الملف (5 ميجابايت كحد أقصى)
                if (file.Size > 5 * 1024 * 1024)
                {
                    _Snackbar.Add("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت", Severity.Error);
                    return;
                }

                // إنشاء نموذج لرفع الملف
                using var content = new MultipartFormDataContent();
                using var fileStream = file.OpenReadStream(maxAllowedSize: 5 * 1024 * 1024);
                using var streamContent = new StreamContent(fileStream);

                content.Add(streamContent, "file", file.Name);

                // إضافة مسار الشعار القديم للحذف
                if (!string.IsNullOrEmpty(AboutUs.CompanyLogoUrl))
                {
                    content.Add(new StringContent(AboutUs.CompanyLogoUrl), "oldImageUrl");
                }

                // رفع الملف إلى الخادم
                var response = await Http.PostAsync("api/upload/aboutus/logo", content);

                if (response.IsSuccessStatusCode)
                {
                    var uploadedFilePath = await response.Content.ReadAsStringAsync();
                    AboutUs.CompanyLogoUrl = uploadedFilePath.Trim('"'); // إزالة علامات الاقتباس
                    _Snackbar.Add("تم رفع شعار الشركة بنجاح", Severity.Success);
                    StateHasChanged();
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _Snackbar.Add($"فشل في رفع شعار الشركة: {error}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                _Snackbar.Add($"حدث خطأ أثناء رفع شعار الشركة: {ex.Message}", Severity.Error);
            }
        }

        #endregion

        #region دوال إدارة عناصر الطاقة الإنتاجية

        /// <summary>
        /// تبديل عرض قسم عناصر الطاقة الإنتاجية
        /// </summary>
        private void ToggleProductionItemsSection()
        {
            _showProductionItemsSection = !_showProductionItemsSection;
            StateHasChanged();
        }

        /// <summary>
        /// تبديل عرض نموذج إضافة العنصر
        /// </summary>
        private void ToggleAddItemForm()
        {
            _showAddItemForm = !_showAddItemForm;
            if (_showAddItemForm)
            {
                // إعداد عنصر جديد
                _currentProductionItem = new ProductionCapacityItemDto
                {
                    IsActive = true,
                    DisplayOrder = AboutUs.ProductionCapacityItems.Count
                };
                _isEditingProductionItem = false;
            }
            else
            {
                // إعادة تعيين النموذج
                CancelAddItemForm();
            }
            StateHasChanged();
        }

        /// <summary>
        /// إلغاء نموذج إضافة/تعديل العنصر
        /// </summary>
        private void CancelAddItemForm()
        {
            _showAddItemForm = false;
            _isEditingProductionItem = false;
            _currentProductionItem = new ProductionCapacityItemDto
            {
                IsActive = true,
                DisplayOrder = 0
            };
            StateHasChanged();
        }

        /// <summary>
        /// إضافة عنصر طاقة إنتاجية جديد (الطريقة القديمة - للحفاظ على التوافق)
        /// </summary>
        private void AddProductionCapacityItem()
        {
            if (AboutUs.ProductionCapacityItems.Count >= 20)
            {
                _Snackbar.Add("لا يمكن إضافة أكثر من 20 عنصر للطاقة الإنتاجية", Severity.Warning);
                return;
            }

            // استخدام النموذج المضمن بدلاً من النافذة المنفصلة
            ToggleAddItemForm();
        }

        /// <summary>
        /// تعديل عنصر طاقة إنتاجية موجود في النموذج المضمن
        /// </summary>
        /// <param name="item">العنصر المراد تعديله</param>
        private void EditProductionCapacityItemInline(ProductionCapacityItemDto item)
        {
            _currentProductionItem = new ProductionCapacityItemDto
            {
                Id = item.Id,
                Title = item.Title,
                Description = item.Description,
                ImageUrl = item.ImageUrl,
                IsActive = item.IsActive,
                DisplayOrder = item.DisplayOrder,
                CreatedAt = item.CreatedAt,
                LastModifiedAt = item.LastModifiedAt
            };
            _isEditingProductionItem = true;
            _showAddItemForm = true;

            // التمرير إلى النموذج
            StateHasChanged();
        }

        /// <summary>
        /// تعديل عنصر طاقة إنتاجية موجود
        /// </summary>
        /// <param name="item">العنصر المراد تعديله</param>
        private void EditProductionCapacityItem(ProductionCapacityItemDto item)
        {
            _currentProductionItem = new ProductionCapacityItemDto
            {
                Id = item.Id,
                Title = item.Title,
                Description = item.Description,
                ImageUrl = item.ImageUrl,
                IsActive = item.IsActive,
                DisplayOrder = item.DisplayOrder,
                CreatedAt = item.CreatedAt,
                LastModifiedAt = item.LastModifiedAt
            };
            _isEditingProductionItem = true;
            _isProductionItemDialogOpen = true;

            // إجبار إعادة الرسم لإظهار النافذة المنبثقة
            StateHasChanged();
        }

        /// <summary>
        /// حذف عنصر طاقة إنتاجية
        /// </summary>
        /// <param name="item">العنصر المراد حذفه</param>
        private void DeleteProductionCapacityItem(ProductionCapacityItemDto item)
        {
            AboutUs.ProductionCapacityItems.Remove(item);

            // إعادة ترقيم العناصر المتبقية
            for (int i = 0; i < AboutUs.ProductionCapacityItems.Count; i++)
            {
                AboutUs.ProductionCapacityItems[i].DisplayOrder = i;
            }

            _Snackbar.Add("تم حذف العنصر بنجاح", Severity.Success);
            StateHasChanged();
        }

        /// <summary>
        /// حفظ عنصر الطاقة الإنتاجية (إضافة أو تعديل)
        /// </summary>
        private async Task SaveProductionItem()
        {
            _isSubmittingProductionItem = true;
            StateHasChanged();

            try
            {
                // التحقق من صحة البيانات
                //if (string.IsNullOrWhiteSpace(_currentProductionItem.Title))
                //{
                //    _Snackbar.Add("عنوان العنصر مطلوب", Severity.Error);
                //    return;
                //}

                //if (string.IsNullOrWhiteSpace(_currentProductionItem.Description))
                //{
                //    _Snackbar.Add("وصف العنصر مطلوب", Severity.Error);
                //    return;
                //}

                // محاكاة معالجة
                await Task.Delay(500);

                if (_isEditingProductionItem)
                {
                    // تحديث العنصر الموجود
                    var existingItem = AboutUs.ProductionCapacityItems.FirstOrDefault(x => x.Id == _currentProductionItem.Id);
                    if (existingItem != null)
                    {
                        existingItem.Title = _currentProductionItem.Title;
                        existingItem.Description = _currentProductionItem.Description;
                        existingItem.ImageUrl = _currentProductionItem.ImageUrl;
                        existingItem.IsActive = _currentProductionItem.IsActive;
                        existingItem.LastModifiedAt = DateTime.Now;
                    }
                }
                else
                {
                    // إضافة عنصر جديد - إنشاء نسخة جديدة من الكائن لتجنب مشاركة المرجع
                    var newItem = new ProductionCapacityItemDto
                    {
                        Title = _currentProductionItem.Title,
                        Description = _currentProductionItem.Description,
                        ImageUrl = _currentProductionItem.ImageUrl,
                        IsActive = _currentProductionItem.IsActive,
                        DisplayOrder = AboutUs.ProductionCapacityItems.Count,
                        CreatedAt = DateTime.Now
                    };
                    AboutUs.ProductionCapacityItems.Add(newItem);
                }

                // إخفاء النموذج المضمن
                _showAddItemForm = false;

                // إعادة تعيين الكائن الحالي بكائن جديد فارغ
                _currentProductionItem = new ProductionCapacityItemDto
                {
                    IsActive = true,
                    DisplayOrder = 0
                };

                // إعادة تعيين حالة التعديل
                _isEditingProductionItem = false;

                // تحديث تقدم النموذج
                UpdateFormProgress();

                _Snackbar.Add(_isEditingProductionItem ? "تم تحديث العنصر بنجاح" : "تم إضافة العنصر بنجاح", Severity.Success);
            }
            finally
            {
                _isSubmittingProductionItem = false;
                StateHasChanged();
            }
        }



        /// <summary>
        /// رفع صورة عنصر الطاقة الإنتاجية
        /// </summary>
        /// <param name="file">ملف الصورة المراد رفعه</param>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task UploadProductionItemImage(IBrowserFile file)
        {
            if (file == null) return;

            try
            {
                // التحقق من نوع الملف
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    _Snackbar.Add("نوع الملف غير مدعوم. يُسمح فقط بملفات JPG, JPEG, PNG, GIF", Severity.Error);
                    return;
                }

                // التحقق من حجم الملف (5 ميجابايت كحد أقصى)
                if (file.Size > 5 * 1024 * 1024)
                {
                    _Snackbar.Add("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت", Severity.Error);
                    return;
                }

                // إنشاء نموذج لرفع الملف
                using var content = new MultipartFormDataContent();
                using var fileStream = file.OpenReadStream(maxAllowedSize: 5 * 1024 * 1024);
                using var streamContent = new StreamContent(fileStream);

                content.Add(streamContent, "file", file.Name);

                // إضافة مسار الصورة القديمة للحذف
                if (!string.IsNullOrEmpty(_currentProductionItem.ImageUrl))
                {
                    content.Add(new StringContent(_currentProductionItem.ImageUrl), "oldImageUrl");
                }

                // رفع الملف إلى الخادم
                var response = await Http.PostAsync("api/upload/aboutus/production-capacity-item", content);

                if (response.IsSuccessStatusCode)
                {
                    var uploadedFilePath = await response.Content.ReadAsStringAsync();
                    _currentProductionItem.ImageUrl = uploadedFilePath.Trim('"'); // إزالة علامات الاقتباس
                    _Snackbar.Add("تم رفع صورة العنصر بنجاح", Severity.Success);
                    StateHasChanged();
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _Snackbar.Add($"فشل في رفع صورة العنصر: {error}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                _Snackbar.Add($"حدث خطأ أثناء رفع صورة العنصر: {ex.Message}", Severity.Error);
            }
        }

        #endregion

        #region دوال النموذج

        /// <summary>
        /// إرسال النموذج
        /// </summary>
        private async Task Submit()
        {
            _isSubmitting = true;
            StateHasChanged();

            try
            {
                await Task.Delay(100); // محاكاة معالجة

                if (MudDialog != null)
                {
                    MudDialog.Close(DialogResult.Ok(AboutUs));
                }
                else if (OnSubmit.HasDelegate)
                {
                    await OnSubmit.InvokeAsync(AboutUs);
                }
            }
            finally
            {
                _isSubmitting = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// إلغاء النموذج
        /// </summary>
        private async Task Cancel()
        {
            if (MudDialog != null)
            {
                MudDialog.Cancel();
            }
            else if (OnCancel.HasDelegate)
            {
                await OnCancel.InvokeAsync();
            }
        }

        #endregion

        #region دوال مساعدة للتصميم المتجاوب

        /// <summary>
        /// تحديث تقدم النموذج
        /// </summary>
        private void UpdateFormProgress()
        {
            var totalFields = 4; // الحقول المطلوبة
            var completedFields = 0;

            if (!string.IsNullOrWhiteSpace(AboutUs.CompanyLogoUrl)) completedFields++;
            if (!string.IsNullOrWhiteSpace(AboutUs.CompanyDescription)) completedFields++;
            if (!string.IsNullOrWhiteSpace(AboutUs.CompanyCapabilities)) completedFields++;
            if (AboutUs.DisplayOrder >= 0) completedFields++;

            _formProgress = (double)completedFields / totalFields * 100;
            StateHasChanged();
        }

        /// <summary>
        /// تحديث تقدم النموذج (نسخة غير متزامنة للاستخدام مع OnBlur)
        /// </summary>
        private async Task UpdateFormProgressAsync()
        {
            await Task.Run(() => UpdateFormProgress());
        }

        /// <summary>
        /// إزالة شعار الشركة
        /// </summary>
        private void RemoveCompanyLogo()
        {
            AboutUs.CompanyLogoUrl = string.Empty;
            UpdateFormProgress();
            StateHasChanged();
        }

        /// <summary>
        /// إزالة صورة عنصر الطاقة الإنتاجية
        /// </summary>
        private void RemoveProductionItemImage()
        {
            _currentProductionItem.ImageUrl = string.Empty;
            StateHasChanged();
        }

        /// <summary>
        /// الحصول على عنوان النموذج
        /// </summary>
        private string GetFormTitle()
        {
            if (IsCreateMode) return "إضافة محتوى جديد";
            if (IsViewMode) return "عرض محتوى من نحن";
            return "تعديل محتوى من نحن";
        }

        /// <summary>
        /// الحصول على العنوان الفرعي للنموذج
        /// </summary>
        private string GetFormSubtitle()
        {
            if (IsCreateMode) return "إنشاء محتوى جديد لصفحة من نحن";
            if (IsViewMode) return "عرض تفاصيل محتوى صفحة من نحن";
            return "تحديث محتوى صفحة من نحن";
        }

        #endregion
    }
}
