using BlogMud.Data;
using BlogMud.Shared.Models;
using Microsoft.EntityFrameworkCore;

namespace BlogMud.Data.Repositories
{
    /// <summary>
    /// تنفيذ مستودع بيانات صفحة "من نحن"
    /// </summary>
    public class AboutUsRepository : Repository<AboutUs>, IAboutUsRepository
    {
        public AboutUsRepository(ApplicationDbContext context) : base(context)
        {
        }

        /// <summary>
        /// الحصول على المحتوى النشط لصفحة "من نحن"
        /// </summary>
        /// <returns>قائمة بالمحتوى النشط مرتبة حسب ترتيب العرض</returns>
        public async Task<IEnumerable<AboutUs>> GetActiveAboutUsAsync()
        {
            return await GetAllAsync(
                filter: a => a.IsActive,
                orderBy: q => q.OrderBy(a => a.DisplayOrder).ThenBy(a => a.CreatedAt),
                includeProperties: "ProductionCapacityItems"
            );
        }

        /// <summary>
        /// الحصول على أول محتوى نشط لصفحة "من نحن"
        /// </summary>
        /// <returns>أول محتوى نشط أو null إذا لم يوجد</returns>
        public async Task<AboutUs?> GetFirstActiveAboutUsAsync()
        {
            return await GetFirstOrDefaultAsync(
                filter: a => a.IsActive,
                includeProperties: "ProductionCapacityItems"
            );
        }

        /// <summary>
        /// الحصول على محتوى صفحة "من نحن" مع عناصر الطاقة الإنتاجية
        /// </summary>
        /// <param name="id">معرف المحتوى</param>
        /// <returns>محتوى صفحة "من نحن" مع عناصر الطاقة الإنتاجية</returns>
        public async Task<AboutUs?> GetAboutUsWithProductionItemsAsync(int id)
        {
            return await GetFirstOrDefaultAsync(
                filter: a => a.Id == id,
                includeProperties: "ProductionCapacityItems",
                tracked: true // تأكد من أن الكائن مُتتبع
            );
        }
    }
}
