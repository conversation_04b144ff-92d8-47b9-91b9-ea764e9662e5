using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;

namespace BlogMud.Data
{
    public static class DbInitializer
    {
        public static async Task Initialize(IServiceProvider serviceProvider, IConfiguration configuration)
        {
            using var scope = serviceProvider.CreateScope();
            var services = scope.ServiceProvider;
            var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();

            try
            {
                var context = services.GetRequiredService<ApplicationDbContext>();
                var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
                var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();

                // Asegurarse de que la base de datos está actualizada
                await context.Database.MigrateAsync();

                // Check if tables exist, if not create them
                if (!await context.Database.GetService<IRelationalDatabaseCreator>().HasTablesAsync())
                {
                    await context.Database.EnsureCreatedAsync();
                }
                else
                {
                    // Check if Categories table exists
                    try
                    {
                        // Try to query the Categories table
                        await context.Categories.FirstOrDefaultAsync();
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning(ex, "Categories table does not exist. Creating it...");

                        // Create Categories table
                        await context.Database.ExecuteSqlRawAsync(@"
                            IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Categories')
                            BEGIN
                                CREATE TABLE [dbo].[Categories] (
                                    [Id] INT IDENTITY(1,1) NOT NULL,
                                    [Name] NVARCHAR(100) NOT NULL,
                                    [Description] NVARCHAR(500) NULL,
                                    [ArticleCount] INT NOT NULL DEFAULT 0,
                                    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
                                );

                                -- Insert seed data
                                INSERT INTO [dbo].[Categories] ([Name], [Description], [ArticleCount])
                                VALUES
                                    (N'أخبار', N'أخبار الشركة والفعاليات', 0),
                                    (N'مقالات', N'مقالات متنوعة', 0),
                                    (N'خدماتنا', N'خدمات الشركة', 0);
                            END
                        ");

                        logger.LogInformation("Categories table created successfully");
                    }

                    // Check if Clients table exists
                    try
                    {
                        // Try to query the Clients table
                        await context.Clients.FirstOrDefaultAsync();
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning(ex, "Clients table does not exist. Creating it...");

                        // Create Clients table
                        await context.Database.ExecuteSqlRawAsync(@"
                            IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Clients')
                            BEGIN
                                CREATE TABLE [dbo].[Clients] (
                                    [Id] INT IDENTITY(1,1) NOT NULL,
                                    [Name] NVARCHAR(100) NOT NULL,
                                    [Email] NVARCHAR(100) NOT NULL,
                                    [Phone] NVARCHAR(20) NOT NULL,
                                    [Address] NVARCHAR(200) NULL,
                                    [Notes] NVARCHAR(500) NULL,
                                    [CreatedAt] DATETIME2 NOT NULL,
                                    [IsActive] BIT NOT NULL DEFAULT 1,
                                    CONSTRAINT [PK_Clients] PRIMARY KEY ([Id])
                                );

                                -- Insert seed data
                                INSERT INTO [dbo].[Clients] ([Name], [Email], [Phone], [Address], [Notes], [CreatedAt], [IsActive])
                                VALUES
                                    (N'شركة الرياض للتقنية', N'<EMAIL>', N'+966123456789', N'الرياض، المملكة العربية السعودية', N'عميل منذ 2020', GETDATE(), 1),
                                    (N'مؤسسة جدة للخدمات', N'<EMAIL>', N'+966987654321', N'جدة، المملكة العربية السعودية', N'عميل جديد', GETDATE(), 1),
                                    (N'شركة الدمام للاستشارات', N'<EMAIL>', N'+966555555555', N'الدمام، المملكة العربية السعودية', NULL, GETDATE(), 1);
                            END
                        ");

                        logger.LogInformation("Clients table created successfully");
                    }
                }

                logger.LogInformation("Inicializando roles y usuarios...");

                // Crear roles si no existen
                string[] roleNames = { "Admin", "User" };

                foreach (var roleName in roleNames)
                {
                    if (!await roleManager.RoleExistsAsync(roleName))
                    {
                        logger.LogInformation($"Creando rol: {roleName}");
                        await roleManager.CreateAsync(new IdentityRole(roleName));
                    }
                }

                // Leer configuración del administrador
                var adminSection = configuration.GetSection("AdminAccount");
                var adminEmail = adminSection["Email"] ?? "<EMAIL>";
                var adminPassword = adminSection["Password"] ?? "Admin@123456";
                var adminFullName = adminSection["FullName"] ?? "مدير النظام";

                // Verificar si existe el usuario administrador
                var adminUser = await userManager.FindByEmailAsync(adminEmail);

                if (adminUser == null)
                {
                    logger.LogInformation($"Creando usuario administrador: {adminEmail}");

                    // Crear el usuario administrador
                    adminUser = new ApplicationUser
                    {
                        UserName = adminEmail,
                        Email = adminEmail,
                        EmailConfirmed = true, // Para poder iniciar sesión sin confirmar email
                        FullName = adminFullName,
                        CreatedAt = DateTime.Now,
                        IsActive = true
                    };

                    // Usar la contraseña de la configuración
                    var password = adminPassword;
                    var result = await userManager.CreateAsync(adminUser, password);

                    if (result.Succeeded)
                    {
                        logger.LogInformation("Asignando rol de administrador");
                        await userManager.AddToRoleAsync(adminUser, "Admin");
                        logger.LogInformation("Usuario administrador creado correctamente");
                    }
                    else
                    {
                        var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                        logger.LogError($"Error al crear usuario administrador: {errors}");
                    }
                }

                logger.LogInformation("Inicialización de la base de datos completada");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Ocurrió un error durante la inicialización de la base de datos");
                throw;
            }
        }
    }
}
