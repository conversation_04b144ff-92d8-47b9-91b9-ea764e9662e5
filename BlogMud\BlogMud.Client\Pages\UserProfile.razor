@page "/account/profile"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using BlogMud.Shared.Models
@using BlogMud.Shared.DTOs
@using System.Net.Http.Json
@attribute [Authorize]
@rendermode InteractiveWebAssembly

@inject HttpClient Http
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>الملف الشخصي</PageTitle>

<style>
    .rtl-container {
        direction: rtl;
        text-align: right;
    }

    .profile-avatar {
        width: 120px;
        height: 120px;
    }

    .notification-item {
        border-radius: 8px;
        margin-bottom: 8px;
    }

    .unread-notification {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.08);
        border-right: 4px solid var(--mud-palette-primary);
    }

    .profile-card {
        background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-secondary) 100%);
        color: white;
    }

    .profile-card .mud-typography {
        color: white !important;
    }
</style>

@if (isLoading)
{
    <MudContainer MaxWidth="MaxWidth.Large" Class="mt-8 d-flex justify-center rtl-container">
        <div class="d-flex flex-column align-center">
            <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
            <MudText Typo="Typo.body1" Class="mt-4">جاري تحميل بيانات الملف الشخصي...</MudText>
        </div>
    </MudContainer>
}
else if (!string.IsNullOrEmpty(errorMessage))
{
    <MudContainer MaxWidth="MaxWidth.Large" Class="mt-8 rtl-container">
        <MudAlert Severity="Severity.Error" Class="mb-4">
            <MudIcon Icon="Icons.Material.Filled.Error" Class="ml-2" />
            @errorMessage
        </MudAlert>
        <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="@(() => NavigationManager.NavigateTo("/Account/Login"))">
            <MudIcon Icon="Icons.Material.Filled.Login" Class="ml-2" />
            العودة لتسجيل الدخول
        </MudButton>
    </MudContainer>
}
else if (user != null)
{
    <MudContainer MaxWidth="MaxWidth.Large" Class="mt-8 rtl-container">
    <MudGrid>
        @* Debug Info - Only show in development *@
        @if (showDebugInfo)
        {
            <MudItem xs="12">
                <MudAlert Severity="Severity.Info" Class="mb-4">
                    <strong>معلومات التطوير:</strong><br/>
                    معرف المستخدم: @user.Id<br/>
                    البريد الإلكتروني: @user.Email<br/>
                    الاسم الكامل: '@user.FullName'<br/>
                    تم تأكيد البريد: @(user.EmailConfirmed ? "نعم" : "لا")<br/>
                    تاريخ الإنشاء: @user.CreatedAt.ToString("dd/MM/yyyy")<br/>
                    نشط: @(user.IsActive ? "نعم" : "لا")
                </MudAlert>
            </MudItem>
        }

        <!-- User Info Card -->
        <MudItem xs="12" md="4">
            <MudPaper Elevation="3" Class="pa-6 profile-card">
                <div class="d-flex flex-column align-center">
                    <MudAvatar Size="Size.Large" Class="mb-4 profile-avatar">
                        @if (!string.IsNullOrEmpty(user.ProfilePicture))
                        {
                            <MudImage Src="@user.ProfilePicture" Alt="صورة الملف الشخصي" />
                        }
                        else
                        {
                            <MudIcon Icon="Icons.Material.Filled.Person" Size="Size.Large" />
                        }
                    </MudAvatar>

                    <MudText Typo="Typo.h5" Class="mb-2 text-center">
                        @(string.IsNullOrEmpty(user.FullName) ? "لا يوجد اسم" : user.FullName)
                    </MudText>
                    <MudText Typo="Typo.body2" Class="mb-4 text-center" Style="color: rgba(255,255,255,0.8);">
                        @user.Email
                    </MudText>

                    <MudChip T="string"
                             Color="@(user.EmailConfirmed ? Color.Success : Color.Warning)"
                             Size="Size.Small"
                             Class="mb-4"
                             Variant="Variant.Filled">
                        @if (user.EmailConfirmed)
                        {
                            <MudIcon Icon="Icons.Material.Filled.Verified" Size="Size.Small" Class="mr-1" />
                            <span>تم التحقق</span>
                        }
                        else
                        {
                            <MudIcon Icon="Icons.Material.Filled.Warning" Size="Size.Small" Class="mr-1" />
                            <span>في انتظار التحقق</span>
                        }
                    </MudChip>

                    <MudText Typo="Typo.caption" Class="text-center" Style="color: rgba(255,255,255,0.7);">
                        <MudIcon Icon="Icons.Material.Filled.CalendarToday" Size="Size.Small" Class="mr-1" />
                        عضو منذ: @user.CreatedAt.ToString("dd/MM/yyyy")
                    </MudText>
                </div>
            </MudPaper>
        </MudItem>

        <!-- Notifications -->
        <MudItem xs="12" md="8">
            <MudPaper Elevation="3" Class="pa-6">
                <div class="d-flex justify-space-between align-center mb-4">
                    <MudText Typo="Typo.h6">
                        <MudIcon Icon="Icons.Material.Filled.Notifications" Class="ml-2" />
                        الإشعارات
                    </MudText>
                    <div class="d-flex align-center gap-2">
                        <MudChip T="string" Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled">
                            @unreadCount إشعار غير مقروء
                        </MudChip>
                        @if (unreadCount > 0)
                        {
                            <MudButton Size="Size.Small"
                                       Variant="Variant.Text"
                                       Color="Color.Primary"
                                       OnClick="MarkAllAsRead"
                                       StartIcon="Icons.Material.Filled.DoneAll">
                                تحديد الكل كمقروء
                            </MudButton>
                        }
                    </div>
                </div>

                @if (notifications?.Any() == true)
                {
                    <MudList T="string" Class="pa-0">
                        @foreach (var notification in notifications)
                        {
                            <MudListItem T="string"
                                         Class="@($"notification-item {(!notification.IsRead ? "unread-notification" : "")}")"
                                         OnClick="@(() => MarkAsRead(notification.Id))"
                                         Style="cursor: pointer; padding: 16px;">
                                <div class="d-flex justify-space-between align-center">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-center mb-1">
                                            <MudIcon Icon="@GetNotificationIcon(notification.Type)"
                                                     Color="Color.Primary"
                                                     Size="Size.Small"
                                                     Class="ml-2" />
                                            <MudText Typo="Typo.subtitle2" Class="@(!notification.IsRead ? "font-weight-bold" : "")">
                                                @notification.Title
                                            </MudText>
                                        </div>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-2">
                                            @notification.Message
                                        </MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                                            <MudIcon Icon="Icons.Material.Filled.AccessTime" Size="Size.Small" Class="ml-1" />
                                            @notification.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                                        </MudText>
                                    </div>
                                    <div class="mr-4">
                                        @if (!notification.IsRead)
                                        {
                                            <MudIcon Icon="Icons.Material.Filled.Circle" Color="Color.Primary" Size="Size.Small" />
                                        }
                                    </div>
                                </div>
                            </MudListItem>
                            @if (notification != notifications.Last())
                            {
                                <MudDivider />
                            }
                        }
                    </MudList>
                }
                else
                {
                    <div class="text-center py-8">
                        <MudIcon Icon="Icons.Material.Filled.NotificationsNone" Size="Size.Large" Color="Color.Secondary" Class="mb-4" />
                        <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mb-2">
                            لا توجد إشعارات
                        </MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                            ستظهر الإشعارات الجديدة هنا عند وصولها
                        </MudText>
                    </div>
                }
            </MudPaper>
        </MudItem>

        <!-- Quick Actions -->
        <MudItem xs="12">
            <MudPaper Elevation="3" Class="pa-6">
                <MudText Typo="Typo.h6" Class="mb-4">
                    <MudIcon Icon="Icons.Material.Filled.Settings" Class="ml-2" />
                    إعدادات الحساب
                </MudText>
                <MudGrid>
                    <MudItem xs="12" sm="6" md="3">
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Primary"
                                   FullWidth="true"
                                   Href="/Account/Manage"
                                   StartIcon="Icons.Material.Filled.Edit">
                            تعديل الملف الشخصي
                        </MudButton>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Secondary"
                                   FullWidth="true"
                                   Href="/Account/Manage/Email"
                                   StartIcon="Icons.Material.Filled.Email">
                            إدارة البريد الإلكتروني
                        </MudButton>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Warning"
                                   FullWidth="true"
                                   Href="/Account/Manage/ChangePassword"
                                   StartIcon="Icons.Material.Filled.Lock">
                            تغيير كلمة المرور
                        </MudButton>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Info"
                                   FullWidth="true"
                                   Href="/Account/Manage/TwoFactorAuthentication"
                                   StartIcon="Icons.Material.Filled.Security">
                            المصادقة الثنائية
                        </MudButton>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </MudItem>
    </MudGrid>
</MudContainer>
}
else
{
    <MudContainer MaxWidth="MaxWidth.Large" Class="mt-8 rtl-container">
        <MudAlert Severity="Severity.Warning" Class="mb-4">
            <MudIcon Icon="Icons.Material.Filled.Warning" Class="ml-2" />
            لم يتم العثور على بيانات المستخدم. يرجى تسجيل الدخول مرة أخرى.
        </MudAlert>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@(() => NavigationManager.NavigateTo("/Account/Login"))">
            <MudIcon Icon="Icons.Material.Filled.Login" Class="ml-2" />
            تسجيل الدخول
        </MudButton>
    </MudContainer>
}

@code {
    private UserDto? user;
    private List<UserNotificationDto>? notifications;
    private int unreadCount;
    private bool isLoading = true;
    private string? errorMessage;
    private bool showDebugInfo = false; // Set to true for development

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Get authentication state using AuthenticationStateProvider
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var claimsPrincipal = authState.User;

            // Check if user is authenticated
            if (claimsPrincipal.Identity?.IsAuthenticated != true)
            {
                errorMessage = "المستخدم غير مصادق عليه. يرجى تسجيل الدخول للوصول إلى الملف الشخصي.";
                NavigationManager.NavigateTo("/Account/Login");
                return;
            }

            // Get user data from API
            await LoadUserData();

            // Load notifications
            await LoadNotifications();
        }
        catch (Exception ex)
        {
            errorMessage = $"حدث خطأ أثناء تحميل بيانات الملف الشخصي: {ex.Message}";
            Console.WriteLine($"UserProfile Error: {ex}"); // For debugging
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadUserData()
    {
        try
        {
            var response = await Http.GetAsync("/api/account/profile");
            if (response.IsSuccessStatusCode)
            {
                user = await response.Content.ReadFromJsonAsync<UserDto>();
            }
            else
            {
                errorMessage = "لم يتم العثور على بيانات المستخدم. يرجى تسجيل الدخول مرة أخرى.";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading user data: {ex.Message}");
            errorMessage = "حدث خطأ أثناء تحميل بيانات المستخدم.";
        }
    }

    private async Task LoadNotifications()
    {
        try
        {
            if (user != null)
            {
                var notificationsResponse = await Http.GetAsync($"/api/notifications/user/{user.Id}");
                if (notificationsResponse.IsSuccessStatusCode)
                {
                    notifications = await notificationsResponse.Content.ReadFromJsonAsync<List<UserNotificationDto>>();
                }

                var unreadResponse = await Http.GetAsync($"/api/notifications/user/{user.Id}/unread-count");
                if (unreadResponse.IsSuccessStatusCode)
                {
                    unreadCount = await unreadResponse.Content.ReadFromJsonAsync<int>();
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading notifications: {ex.Message}");
            // Initialize empty collections to prevent null reference errors
            notifications = new List<UserNotificationDto>();
            unreadCount = 0;
        }
    }

    private async Task MarkAsRead(int notificationId)
    {
        try
        {
            if (user != null)
            {
                var response = await Http.PostAsync($"/api/notifications/{notificationId}/mark-read", null);
                if (response.IsSuccessStatusCode)
                {
                    await LoadNotifications(); // Reload to update the UI
                    StateHasChanged();
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error marking notification as read: {ex.Message}");
            // Could show a snackbar notification here in the future
        }
    }

    private async Task MarkAllAsRead()
    {
        try
        {
            if (user != null)
            {
                var response = await Http.PostAsync($"/api/notifications/user/{user.Id}/mark-all-read", null);
                if (response.IsSuccessStatusCode)
                {
                    await LoadNotifications(); // Reload to update the UI
                    StateHasChanged();
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error marking all notifications as read: {ex.Message}");
            // Could show a snackbar notification here in the future
        }
    }

    private string GetNotificationIcon(NotificationType type)
    {
        return type switch
        {
            NotificationType.NewPost => Icons.Material.Filled.Article,
            NotificationType.Welcome => Icons.Material.Filled.EmojiEmotions,
            NotificationType.SystemUpdate => Icons.Material.Filled.Settings,
            NotificationType.AccountVerification => Icons.Material.Filled.Verified,
            NotificationType.General => Icons.Material.Filled.Info,
            _ => Icons.Material.Filled.Notifications
        };
    }
}