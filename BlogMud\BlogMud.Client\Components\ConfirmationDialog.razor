@using MudBlazor
@using MudBlazor.Interfaces

<MudDialog>
    <DialogContent>
        <MudText>@ContentText</MudText>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">إلغاء</MudButton>
        <MudButton Color="@Color" Variant="Variant.Filled" OnClick="Submit">@ButtonText</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] public required IMudDialogInstance MudDialog { get; set; }

    [Parameter] public required string ContentText { get; set; }
    [Parameter] public string ButtonText { get; set; } = "حذف";
    [Parameter] public Color Color { get; set; } = Color.Error;

    void Submit() => MudDialog.Close(DialogResult.Ok(true));
    void Cancel() => MudDialog.Cancel();
}
