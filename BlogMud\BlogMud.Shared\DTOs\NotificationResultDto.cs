namespace BlogMud.Shared.DTOs
{
    /// <summary>
    /// كائن نقل البيانات لنتيجة إرسال الإشعارات
    /// </summary>
    public class NotificationResultDto
    {
        /// <summary>
        /// مؤشر على نجاح العملية
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// عدد المستخدمين الذين تم إرسال الإشعارات إليهم
        /// </summary>
        public int UserCount { get; set; }

        /// <summary>
        /// رسالة الحالة
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// تفاصيل إضافية عن العملية (اختياري)
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// الوقت الذي تم فيه إرسال الإشعارات
        /// </summary>
        public DateTime SentAt { get; set; } = DateTime.Now;
    }
}
