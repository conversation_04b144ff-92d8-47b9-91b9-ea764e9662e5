/* Counter.razor Component Scoped Styles */

/* Container and Layout */
.mud-container {
    padding: 1rem;
}

/* Counter Section Styles */
.counter-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.counter-section .mud-text {
    color: white !important;
}

.counter-section .mud-button {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    transition: all 0.3s ease;
}

.counter-section .mud-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Contact Form Section Styles */
.contact-section {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
    border: 1px solid #e0e0e0;
}

/* Form Field Enhancements */
.mud-input-control {
    margin-bottom: 1rem;
}

.mud-input-control .mud-input {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.mud-input-control .mud-input:focus-within {
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    border-color: #667eea;
}

/* Submit Button Styles */
.submit-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 12px 32px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    min-height: 48px; /* Accessibility: 44px minimum touch target + padding */
}

.submit-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 24px rgba(102, 126, 234, 0.4);
}

.submit-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Loading State */
.submit-button .mud-progress-circular {
    color: white;
}

/* Responsive Design */

/* Desktop (>1024px) - Multi-column layout */
@media (min-width: 1024px) {
    .mud-container {
        padding: 2rem;
    }
    
    .contact-section {
        padding: 3rem;
    }
    
    .counter-section {
        padding: 3rem;
        margin-bottom: 2rem;
    }
}

/* Tablet (768px-1024px) - Adaptive layout */
@media (min-width: 768px) and (max-width: 1023px) {
    .mud-container {
        padding: 1.5rem;
    }
    
    .contact-section {
        padding: 2rem;
    }
    
    .counter-section {
        padding: 2rem;
        margin-bottom: 1.5rem;
    }
    
    /* Ensure form fields have adequate spacing on tablets */
    .mud-grid .mud-grid-item {
        padding: 0.5rem;
    }
}

/* Mobile (<768px) - Single-column layout */
@media (max-width: 767px) {
    .mud-container {
        padding: 1rem;
    }
    
    .contact-section {
        padding: 1.5rem;
    }
    
    .counter-section {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    /* Stack all form fields vertically on mobile */
    .mud-grid .mud-grid-item {
        width: 100% !important;
        flex-basis: 100% !important;
        max-width: 100% !important;
        padding: 0.25rem;
    }
    
    /* Ensure touch targets are at least 44px */
    .mud-button {
        min-height: 44px;
        padding: 12px 24px;
    }
    
    .mud-input {
        min-height: 44px;
    }
    
    /* Adjust text sizes for mobile */
    .mud-typography-h3 {
        font-size: 1.75rem;
    }
    
    .mud-typography-h4 {
        font-size: 1.5rem;
    }
}

/* Accessibility Enhancements */
.mud-button:focus-visible {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.mud-input:focus-visible {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .counter-section {
        background: #000000;
        color: #ffffff;
        border: 2px solid #ffffff;
    }
    
    .contact-section {
        border: 2px solid #000000;
    }
    
    .submit-button {
        background: #000000;
        color: #ffffff;
        border: 2px solid #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .mud-button,
    .mud-input-control .mud-input,
    .submit-button {
        transition: none;
    }
    
    .submit-button:hover:not(:disabled) {
        transform: none;
    }
}

/* RTL Support */
[dir="rtl"] .submit-button .mud-icon {
    margin-right: 0;
    margin-left: 0.5rem;
}

[dir="rtl"] .mud-progress-circular {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .contact-section {
        background: #1e1e1e;
        border-color: #333333;
        color: #ffffff;
    }
    
    .mud-input {
        background: #2d2d2d;
        border-color: #444444;
        color: #ffffff;
    }
}

/* Animation for form submission success */
@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.form-success {
    animation: successPulse 0.6s ease-in-out;
}

/* Smooth transitions for all interactive elements */
.mud-paper,
.mud-button,
.mud-input-control {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus management for better accessibility */
.mud-form .mud-input-control:focus-within {
    z-index: 1;
    position: relative;
}
