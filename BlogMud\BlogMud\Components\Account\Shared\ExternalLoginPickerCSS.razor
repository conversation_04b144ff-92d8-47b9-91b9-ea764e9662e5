@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using MudBlazor
@using BlogMud.Data
@using MudBlazor.StaticInput

<style>
    /* Enhanced External Login Buttons */
    .external-login-button {
        width: 100%;
        height: 52px;
        border-radius: 16px;
        margin-bottom: 16px;
        font-weight: 600;
        text-transform: none;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
        letter-spacing: 0.3px;
        box-shadow:
            0 4px 15px rgba(0, 0, 0, 0.1),
            0 2px 6px rgba(0, 0, 0, 0.05);
    }

        .external-login-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.2) 50%,
                transparent 100%);
            transition: left 0.6s ease;
        }

        .external-login-button:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow:
                0 8px 25px rgba(0, 0, 0, 0.2),
                0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
        }

            .external-login-button:hover::before {
                left: 100%;
            }

        .external-login-button:active {
            transform: translateY(-1px) scale(0.98);
        }

    /* Enhanced Provider-Specific Styling */
    .google-button {
        background: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);
        color: white;
        box-shadow:
            0 4px 15px rgba(66, 133, 244, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

        .google-button:hover {
            background: linear-gradient(135deg, #3367d6 0%, #2851a3 100%);
            box-shadow:
                0 8px 25px rgba(66, 133, 244, 0.4),
                0 4px 12px rgba(66, 133, 244, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

    .facebook-button {
        background: linear-gradient(135deg, #1877f2 0%, #166fe5 100%);
        color: white;
        box-shadow:
            0 4px 15px rgba(24, 119, 242, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

        .facebook-button:hover {
            background: linear-gradient(135deg, #166fe5 0%, #1461cc 100%);
            box-shadow:
                0 8px 25px rgba(24, 119, 242, 0.4),
                0 4px 12px rgba(24, 119, 242, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

    .microsoft-button {
        background: linear-gradient(135deg, #00a1f1 0%, #0078d4 100%);
        color: white;
        box-shadow:
            0 4px 15px rgba(0, 161, 241, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

        .microsoft-button:hover {
            background: linear-gradient(135deg, #0078d4 0%, #005a9e 100%);
            box-shadow:
                0 8px 25px rgba(0, 161, 241, 0.4),
                0 4px 12px rgba(0, 161, 241, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

    .github-button {
        background: linear-gradient(135deg, #333 0%, #24292e 100%);
        color: white;
        box-shadow:
            0 4px 15px rgba(51, 51, 51, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

        .github-button:hover {
            background: linear-gradient(135deg, #24292e 0%, #1b1f23 100%);
            box-shadow:
                0 8px 25px rgba(51, 51, 51, 0.4),
                0 4px 12px rgba(51, 51, 51, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

    .default-external-button {
        background: linear-gradient(135deg,
            var(--mud-palette-secondary) 0%,
            var(--mud-palette-secondary-darken) 100%);
        color: white;
        box-shadow:
            0 4px 15px rgba(89, 89, 89, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

        .default-external-button:hover {
            background: linear-gradient(135deg,
                var(--mud-palette-secondary-darken) 0%,
                #4a4a4a 100%);
            box-shadow:
                0 8px 25px rgba(89, 89, 89, 0.4),
                0 4px 12px rgba(89, 89, 89, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

    /* Enhanced Responsive Design for External Buttons */
    @@media (max-width: 600px) {
        .external-login-button {
            height: 48px;
            border-radius: 12px;
            margin-bottom: 12px;
        }
    }
</style>
