@using BlogMud.Shared.Enums
@using Microsoft.AspNetCore.Components.Routing

<div class="@GetNavClass()">
    <MudNavLink Href="/" Match="NavLinkMatch.All">الرئيسية</MudNavLink>
    <MudNavLink Href="/about">من نحن</MudNavLink>
    <MudNavLink Href="/services">خدماتنا</MudNavLink>
    <MudNavLink Href="/news">الأخبار</MudNavLink>
    <MudNavLink Href="/contact">اتصل بنا</MudNavLink>
    
    <!-- Añadimos AdminLink aquí -->
    <AdminLink />
</div>

@code {
    [Parameter]
    public NavMenuMode Mode { get; set; } = NavMenuMode.Horizontal;

    private string GetNavClass()
    {
        return Mode == NavMenuMode.Horizontal 
            ? "d-flex gap-4" 
            : "d-flex flex-column gap-2 pa-4";
    }
}
