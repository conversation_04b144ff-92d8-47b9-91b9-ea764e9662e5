using System;

namespace BlogMud.Shared.DTOs
{
    /// <summary>
    /// كائن نقل البيانات لعنصر الطاقة الإنتاجية
    /// يستخدم لنقل بيانات عناصر الطاقة الإنتاجية بين واجهة المستخدم والخادم
    /// </summary>
    public class ProductionCapacityItemDto
    {
        public int Id { get; set; }
        public int AboutUsId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? ImageUrl { get; set; }
        public int DisplayOrder { get; set; } = 0;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? LastModifiedAt { get; set; }
    }
}
