using BlogMud.Shared.Repositories;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace BlogMud.Data.Repositories
{
    /// <summary>
    /// Generic repository implementation for domain-specific repositories
    /// Extends the base Repository with additional domain-specific operations
    /// </summary>
    /// <typeparam name="T">The entity type</typeparam>
    public class GRepository<T> : Repository<T>, IGRepository<T> where T : class
    {
        private readonly ApplicationDbContext _db;
        private readonly DbSet<T> _dbSet;

        public GRepository(ApplicationDbContext db) : base(db)
        {
            _db = db;
            _dbSet = _db.Set<T>();
        }

        /// <summary>
        /// Gets entities with pagination support
        /// </summary>
        public IEnumerable<T> GetPaged(
            Expression<Func<T, bool>> filter = null,
            Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
            string includeProperties = "",
            int page = 1,
            int pageSize = 10,
            bool tracked = true)
        {
            IQueryable<T> query = tracked ? _dbSet : _dbSet.AsNoTracking();

            if (filter != null)
            {
                query = query.Where(filter);
            }

            foreach (var includeProperty in includeProperties.Split(
                new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                query = query.Include(includeProperty);
            }

            if (orderBy != null)
            {
                query = orderBy(query);
            }

            // Apply pagination
            return query.Skip((page - 1) * pageSize).Take(pageSize).ToList();
        }

        /// <summary>
        /// Gets entities with pagination support asynchronously
        /// </summary>
        public async Task<IEnumerable<T>> GetPagedAsync(
            Expression<Func<T, bool>> filter = null,
            Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
            string includeProperties = "",
            int page = 1,
            int pageSize = 10,
            bool tracked = true)
        {
            IQueryable<T> query = tracked ? _dbSet : _dbSet.AsNoTracking();

            if (filter != null)
            {
                query = query.Where(filter);
            }

            foreach (var includeProperty in includeProperties.Split(
                new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                query = query.Include(includeProperty);
            }

            if (orderBy != null)
            {
                query = orderBy(query);
            }

            // Apply pagination
            return await query.Skip((page - 1) * pageSize).Take(pageSize).ToListAsync();
        }

        /// <summary>
        /// Gets the total count of entities matching the filter
        /// </summary>
        public int Count(Expression<Func<T, bool>> filter = null)
        {
            IQueryable<T> query = _dbSet;

            if (filter != null)
            {
                query = query.Where(filter);
            }

            return query.Count();
        }

        /// <summary>
        /// Gets the total count of entities matching the filter asynchronously
        /// </summary>
        public async Task<int> CountAsync(Expression<Func<T, bool>> filter = null)
        {
            IQueryable<T> query = _dbSet;

            if (filter != null)
            {
                query = query.Where(filter);
            }

            return await query.CountAsync();
        }

        /// <summary>
        /// Checks if any entity matches the filter
        /// </summary>
        public bool Any(Expression<Func<T, bool>> filter = null)
        {
            IQueryable<T> query = _dbSet;

            if (filter != null)
            {
                return query.Any(filter);
            }

            return query.Any();
        }

        /// <summary>
        /// Checks if any entity matches the filter asynchronously
        /// </summary>
        public async Task<bool> AnyAsync(Expression<Func<T, bool>> filter = null)
        {
            IQueryable<T> query = _dbSet;

            if (filter != null)
            {
                return await query.AnyAsync(filter);
            }

            return await query.AnyAsync();
        }
    }
}
