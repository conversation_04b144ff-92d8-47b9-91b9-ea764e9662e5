@page "/dev/emails"
@using BlogMud.Services
@inject IJSRuntime JSRuntime
@inject ILogger<DevEmails> Logger

<PageTitle>Development Emails - BlogMud</PageTitle>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Development Email Testing</h2>
                <div>
                    <a href="/dev/email-test" class="btn btn-outline-primary me-2">
                        <i class="fas fa-vial"></i> Email Test
                    </a>
                    <button class="btn btn-primary me-2" @onclick="RefreshEmails">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                    <button class="btn btn-warning me-2" @onclick="ClearEmails">
                        <i class="fas fa-trash"></i> Clear All
                    </button>
                </div>
            </div>

            @if (loading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else if (emails?.Any() == true)
            {
                <div class="row">
                    @foreach (var email in emails.OrderByDescending(e => e.SentAt))
                    {
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <small class="text-muted">@email.SentAt.ToString("yyyy-MM-dd HH:mm:ss")</small>
                                    @if (email.Subject.Contains("تأكيد"))
                                    {
                                        <span class="badge bg-primary">Confirmation</span>
                                    }
                                    else if (email.Subject.Contains("إعادة تعيين"))
                                    {
                                        <span class="badge bg-warning">Password Reset</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-info">Other</span>
                                    }
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title">@email.Subject</h6>
                                    <p class="card-text">
                                        <strong>To:</strong> @email.To
                                    </p>

                                    @if (email.Subject.Contains("تأكيد"))
                                    {
                                        var confirmationLink = ExtractConfirmationLink(email.Body);
                                        if (!string.IsNullOrEmpty(confirmationLink))
                                        {
                                            <div class="mt-2">
                                                <button class="btn btn-success btn-sm me-2" @onclick="() => OpenConfirmationLink(confirmationLink)">
                                                    <i class="fas fa-external-link-alt"></i> Confirm Email
                                                </button>
                                                <button class="btn btn-warning btn-sm" @onclick="() => ManuallyConfirmEmail(email.To)">
                                                    <i class="fas fa-check-circle"></i> Manual Confirm
                                                </button>
                                            </div>
                                        }
                                    }

                                    <div class="mt-2">
                                        <button class="btn btn-outline-secondary btn-sm" @onclick="() => ViewEmailBody(email)">
                                            <i class="fas fa-eye"></i> View Content
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="alert alert-info text-center">
                    <h4>No emails sent yet</h4>
                    <p>Register a new user to see confirmation emails appear here.</p>
                </div>
            }
        </div>
    </div>
</div>

<!-- Email Content Modal -->
<div class="modal fade" id="emailModal" tabindex="-1" aria-labelledby="emailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailModalLabel">Email Content</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @if (selectedEmail != null)
                {
                    <div class="mb-3">
                        <strong>To:</strong> @selectedEmail.To<br>
                        <strong>Subject:</strong> @selectedEmail.Subject<br>
                        <strong>Sent:</strong> @selectedEmail.SentAt.ToString("yyyy-MM-dd HH:mm:ss")
                    </div>
                    <hr>
                    <div style="max-height: 400px; overflow-y: auto;">
                        @((MarkupString)selectedEmail.Body)
                    </div>
                }
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

@code {
    private List<EmailMessage>? emails;
    private EmailMessage? selectedEmail;
    private bool loading = true;

    protected override async Task OnInitializedAsync()
    {
        await RefreshEmails();
    }

    private async Task RefreshEmails()
    {
        loading = true;
        try
        {
            var baseUri = new Uri("https://localhost:7009");
            using var httpClient = new HttpClient { BaseAddress = baseUri };
            emails = await httpClient.GetFromJsonAsync<List<EmailMessage>>("api/Development/emails");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to load emails");
            emails = new List<EmailMessage>();
        }
        finally
        {
            loading = false;
        }
    }

    private async Task ClearEmails()
    {
        try
        {
            var baseUri = new Uri("https://localhost:7009");
            using var httpClient = new HttpClient { BaseAddress = baseUri };
            await httpClient.DeleteAsync("api/Development/emails");
            await RefreshEmails();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to clear emails");
        }
    }

    private async Task ViewEmailBody(EmailMessage email)
    {
        selectedEmail = email;
        await JSRuntime.InvokeVoidAsync("bootstrap.Modal.getOrCreateInstance", "#emailModal").AsTask();
        await JSRuntime.InvokeVoidAsync("bootstrap.Modal.getInstance", "#emailModal", "show");
    }

    private async Task OpenConfirmationLink(string confirmationLink)
    {
        await JSRuntime.InvokeVoidAsync("window.open", confirmationLink, "_blank");
    }

    private async Task ManuallyConfirmEmail(string email)
    {
        try
        {
            var baseUri = new Uri("https://localhost:7009");
            using var httpClient = new HttpClient { BaseAddress = baseUri };

            var response = await httpClient.PostAsync($"api/Development/confirm-email/{Uri.EscapeDataString(email)}", null);

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadAsStringAsync();
                Logger.LogInformation("Email manually confirmed: {Email}", email);

                // Show success message using JavaScript alert for now
                await JSRuntime.InvokeVoidAsync("alert", $"Email {email} has been manually confirmed! You can now log in.");
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                Logger.LogError("Failed to manually confirm email: {Error}", error);
                await JSRuntime.InvokeVoidAsync("alert", $"Failed to confirm email: {error}");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error manually confirming email for {Email}", email);
            await JSRuntime.InvokeVoidAsync("alert", $"Error: {ex.Message}");
        }
    }

    private string? ExtractConfirmationLink(string emailBody)
    {
        if (emailBody.Contains("href='"))
        {
            var startIndex = emailBody.IndexOf("href='") + 6;
            var endIndex = emailBody.IndexOf("'", startIndex);
            if (endIndex > startIndex)
            {
                return emailBody.Substring(startIndex, endIndex - startIndex);
            }
        }
        return null;
    }
}
