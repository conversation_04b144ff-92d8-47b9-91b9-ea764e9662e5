using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BlogMud.Services;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using AutoMapper;

namespace BlogMud.Controllers
{
    /// <summary>
    /// تحكم في إدارة الإشعارات
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")]
    public class NotificationController : ControllerBase
    {
        private readonly INotificationService _notificationService;
        private readonly IMapper _mapper;
        private readonly ILogger<NotificationController> _logger;

        public NotificationController(
            INotificationService notificationService,
            IMapper mapper,
            ILogger<NotificationController> logger)
        {
            _notificationService = notificationService;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// إرسال إشعارات للمستخدمين عند إنشاء مقال جديد
        /// </summary>
        /// <param name="articleDto">بيانات المقال الجديد</param>
        /// <returns>نتيجة إرسال الإشعارات</returns>
        /// <response code="200">تم إرسال الإشعارات بنجاح</response>
        /// <response code="400">بيانات المقال غير صالحة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="500">حدث خطأ أثناء إرسال الإشعارات</response>
        [HttpPost("send-new-post-notification")]
        public async Task<ActionResult<NotificationResultDto>> SendNewPostNotification([FromBody] ArticleDto articleDto)
        {
            try
            {
                _logger.LogInformation("Sending new post notifications for article: {ArticleTitle}", articleDto.Title);

                if (articleDto == null || articleDto.Id <= 0)
                {
                    return BadRequest("بيانات المقال غير صالحة");
                }

                // تحويل ArticleDto إلى Article model
                var article = _mapper.Map<Article>(articleDto);

                // إرسال الإشعارات باستخدام الخدمة الموجودة
                await _notificationService.SendNewPostNotificationAsync(article);

                // الحصول على عدد المستخدمين النشطين لإرجاع النتيجة
                var userCount = await _notificationService.GetActiveUsersCountAsync();

                var result = new NotificationResultDto
                {
                    Success = true,
                    UserCount = userCount,
                    Message = $"تم إرسال الإشعارات بنجاح إلى {userCount} مستخدم"
                };

                _logger.LogInformation("New post notifications sent successfully for article {ArticleId} to {UserCount} users", 
                    articleDto.Id, userCount);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send new post notifications for article {ArticleId}", articleDto.Id);
                
                var errorResult = new NotificationResultDto
                {
                    Success = false,
                    UserCount = 0,
                    Message = "حدث خطأ أثناء إرسال الإشعارات"
                };

                return StatusCode(500, errorResult);
            }
        }

        /// <summary>
        /// إرسال إشعار نظام لجميع المستخدمين
        /// </summary>
        /// <param name="request">بيانات الإشعار</param>
        /// <returns>نتيجة إرسال الإشعار</returns>
        /// <response code="200">تم إرسال الإشعار بنجاح</response>
        /// <response code="400">بيانات الإشعار غير صالحة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="500">حدث خطأ أثناء إرسال الإشعار</response>
        [HttpPost("send-system-notification")]
        public async Task<ActionResult<NotificationResultDto>> SendSystemNotification([FromBody] SystemNotificationRequest request)
        {
            try
            {
                _logger.LogInformation("Sending system notification: {Title}", request.Title);

                if (string.IsNullOrEmpty(request.Title) || string.IsNullOrEmpty(request.Message))
                {
                    return BadRequest("عنوان ومحتوى الإشعار مطلوبان");
                }

                // إرسال الإشعار النظام
                await _notificationService.SendSystemNotificationAsync(request.Title, request.Message, request.Type);

                // الحصول على عدد المستخدمين النشطين
                var userCount = await _notificationService.GetActiveUsersCountAsync();

                var result = new NotificationResultDto
                {
                    Success = true,
                    UserCount = userCount,
                    Message = $"تم إرسال الإشعار النظام بنجاح إلى {userCount} مستخدم"
                };

                _logger.LogInformation("System notification sent successfully to {UserCount} users", userCount);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send system notification");
                
                var errorResult = new NotificationResultDto
                {
                    Success = false,
                    UserCount = 0,
                    Message = "حدث خطأ أثناء إرسال الإشعار النظام"
                };

                return StatusCode(500, errorResult);
            }
        }
    }

    /// <summary>
    /// نموذج طلب إرسال إشعار نظام
    /// </summary>
    public class SystemNotificationRequest
    {
        /// <summary>
        /// عنوان الإشعار
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// محتوى الإشعار
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// نوع الإشعار
        /// </summary>
        public NotificationType Type { get; set; } = NotificationType.General;
    }
}
