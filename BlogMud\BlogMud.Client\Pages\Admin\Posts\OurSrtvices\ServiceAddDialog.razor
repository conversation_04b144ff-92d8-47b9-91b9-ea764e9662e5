@using BlogMud.Shared.DTOs
@using System.Net.Http.Json
@using System.ComponentModel.DataAnnotations
@inject HttpClient Http
@inject ISnackbar Snackbar

<MudDialog MaxWidth="MaxWidth.Large" FullWidth="true">
    <TitleContent>
        <div class="d-flex align-center">
            <MudIcon Icon="@Icons.Material.Filled.Add" Class="me-2" Color="Color.Primary" />
            <MudText Typo="Typo.h6">إضافة خدمة جديدة</MudText>
        </div>
    </TitleContent>
    
    <DialogContent>
        <MudContainer MaxWidth="MaxWidth.False" Class="pa-0">
            <MudForm @ref="_form" Model="@_newService">
                
                <!-- التبويبات الرئيسية -->
                <MudTabs Elevation="1" 
                         Rounded="true" 
                         PanelClass="pa-4" 
                         @bind-ActivePanelIndex="_activeTabIndex"
                         Class="mb-4">
                    
                    <!-- تبويب تفاصيل الخدمة -->
                    <MudTabPanel Text="تفاصيل الخدمة" 
                                Icon="@Icons.Material.Filled.Info"
                                BadgeData="@GetServiceDetailsBadge()"
                                BadgeColor="@GetServiceDetailsBadgeColor()">
                        
                        <MudGrid Spacing="3">
                            <!-- المعلومات الأساسية -->
                            <MudItem xs="12">
                                <MudText Typo="Typo.h6" Class="d-flex align-center mb-3">
                                    <MudIcon Icon="@Icons.Material.Filled.Article" Class="me-2" Color="Color.Primary" />
                                    المعلومات الأساسية
                                </MudText>
                                <MudDivider Class="mb-4" />
                            </MudItem>

                            <!-- العنوان وترتيب العرض -->
                            <MudItem xs="12" sm="8">
                                <MudTextField @bind-Value="_newService.Title"
                                              For="@(() => _newService.Title)"
                                              Label="عنوان الخدمة"
                                              Variant="Variant.Outlined"
                                              Margin="Margin.Dense"
                                              Required="true"
                                              RequiredError="عنوان الخدمة مطلوب"
                                              MaxLength="100"
                                              Counter="100"
                                              HelperText="أدخل عنواً واضحاً ومميزاً للخدمة"
                                              Adornment="Adornment.Start"
                                              AdornmentIcon="@Icons.Material.Filled.Title" />
                            </MudItem>

                            <MudItem xs="12" sm="4">
                                <MudNumericField @bind-Value="_newService.DisplayOrder"
                                                 For="@(() => _newService.DisplayOrder)"
                                                 Label="ترتيب العرض"
                                                 Variant="Variant.Outlined"
                                                 Margin="Margin.Dense"
                                                 Min="0"
                                                 Max="999"
                                                 HelperText="ترتيب ظهور الخدمة"
                                                 Adornment="Adornment.Start"
                                                 AdornmentIcon="@Icons.Material.Filled.Sort" />
                            </MudItem>

                            <!-- الوصف الأساسي -->
                            <MudItem xs="12">
                                <MudTextField @bind-Value="_newService.PrimaryDescription"
                                              For="@(() => _newService.PrimaryDescription)"
                                              Label="الوصف الأساسي"
                                              Variant="Variant.Outlined"
                                              Lines="3"
                                              MaxLines="5"
                                              Required="true"
                                              RequiredError="الوصف الأساسي مطلوب"
                                              MaxLength="500"
                                              Counter="500"
                                              HelperText="وصف مختصر وجذاب للخدمة (يظهر في البطاقات)"
                                              Adornment="Adornment.Start"
                                              AdornmentIcon="@Icons.Material.Filled.Description" />
                            </MudItem>

                            <!-- الوصف التفصيلي -->
                            <MudItem xs="12">
                                <MudTextField @bind-Value="_newService.DetailedDescription"
                                              For="@(() => _newService.DetailedDescription)"
                                              Label="الوصف التفصيلي"
                                              Variant="Variant.Outlined"
                                              Lines="5"
                                              MaxLines="10"
                                              MaxLength="2000"
                                              Counter="2000"
                                              HelperText="وصف شامل ومفصل للخدمة (يظهر في صفحة التفاصيل)"
                                              Adornment="Adornment.Start"
                                              AdornmentIcon="@Icons.Material.Filled.Notes" />
                            </MudItem>

                            <!-- المميزات -->
                            <MudItem xs="12">
                                <MudTextField @bind-Value="_newService.Features"
                                              For="@(() => _newService.Features)"
                                              Label="مميزات الخدمة"
                                              Variant="Variant.Outlined"
                                              Lines="3"
                                              MaxLines="5"
                                              MaxLength="1000"
                                              Counter="1000"
                                              HelperText="أدخل مميزات الخدمة مفصولة بفواصل منقوطة (;)"
                                              Placeholder="ميزة أولى; ميزة ثانية; ميزة ثالثة"
                                              Adornment="Adornment.Start"
                                              AdornmentIcon="@Icons.Material.Filled.Star" />
                            </MudItem>

                            <!-- حالة الخدمة -->
                            <MudItem xs="12">
                                <MudText Typo="Typo.h6" Class="d-flex align-center mb-3">
                                    <MudIcon Icon="@Icons.Material.Filled.Settings" Class="me-2" Color="Color.Primary" />
                                    إعدادات الخدمة
                                </MudText>
                                <MudDivider Class="mb-4" />
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <MudPaper Class="pa-4" Elevation="1">
                                    <div class="d-flex align-center justify-space-between">
                                        <div>
                                            <MudText Typo="Typo.subtitle1" Class="mb-1">حالة الخدمة</MudText>
                                            <MudText Typo="Typo.body2" Color="Color.Secondary">
                                                @(_newService.IsActive ? "الخدمة نشطة ومرئية للزوار" : "الخدمة غير نشطة ومخفية")
                                            </MudText>
                                        </div>
                                        <MudSwitch @bind-Value="_newService.IsActive"
                                                   For="@(() => _newService.IsActive)"
                                                   Color="Color.Success"
                                                   Size="Size.Large"
                                                   ThumbIcon="@(_newService.IsActive ? Icons.Material.Filled.Check : Icons.Material.Filled.Close)"
                                                   ThumbIconColor="@(_newService.IsActive ? Color.Success : Color.Error)" />
                                    </div>
                                </MudPaper>
                            </MudItem>

                            <!-- معاينة سريعة -->
                            <MudItem xs="12" sm="6">
                                <MudPaper Class="pa-4" Elevation="1">
                                    <MudText Typo="Typo.subtitle1" Class="mb-2">معاينة سريعة</MudText>
                                    <div class="d-flex flex-column gap-2">
                                        <div class="d-flex align-center">
                                            <MudIcon Icon="@Icons.Material.Filled.Title" Size="Size.Small" Class="me-2" />
                                            <MudText Typo="Typo.body2">
                                                @(!string.IsNullOrEmpty(_newService.Title) ? _newService.Title : "لا يوجد عنوان")
                                            </MudText>
                                        </div>
                                        <div class="d-flex align-center">
                                            <MudIcon Icon="@Icons.Material.Filled.Sort" Size="Size.Small" Class="me-2" />
                                            <MudText Typo="Typo.body2">ترتيب: @_newService.DisplayOrder</MudText>
                                        </div>
                                        <div class="d-flex align-center">
                                            <MudIcon Icon="@(_newService.IsActive ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff)" 
                                                     Size="Size.Small" 
                                                     Class="me-2" 
                                                     Color="@(_newService.IsActive ? Color.Success : Color.Error)" />
                                            <MudText Typo="Typo.body2">@(_newService.IsActive ? "نشطة" : "غير نشطة")</MudText>
                                        </div>
                                    </div>
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                    <!-- تبويب الوسائط -->
                    <MudTabPanel Text="الوسائط" 
                                Icon="@Icons.Material.Filled.PermMedia"
                                BadgeData="@GetMediaBadge()"
                                BadgeColor="@GetMediaBadgeColor()">
                        
                        <MudGrid Spacing="3">
                            <!-- عنوان القسم -->
                            <MudItem xs="12">
                                <MudText Typo="Typo.h6" Class="d-flex align-center mb-3">
                                    <MudIcon Icon="@Icons.Material.Filled.CloudUpload" Class="me-2" Color="Color.Primary" />
                                    تحميل الوسائط
                                </MudText>
                                <MudDivider Class="mb-4" />
                            </MudItem>

                            <!-- الصورة الرئيسية -->
                            <MudItem xs="12" md="6" lg="4">
                                <MudPaper Class="pa-4" Elevation="2" Style="min-height: 300px;">
                                    <div class="d-flex flex-column h-100">
                                        <MudText Typo="Typo.h6" Class="mb-2 d-flex align-center">
                                            <MudIcon Icon="@Icons.Material.Filled.Image" Class="me-2" Color="Color.Primary" />
                                            الصورة الرئيسية
                                        </MudText>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3">
                                            الصورة التي تظهر كغلاف للخدمة
                                        </MudText>

                                        <div class="flex-grow-1 d-flex flex-column justify-center">
                                            @if (!string.IsNullOrEmpty(_newService.MainImageUrl))
                                            {
                                                <div class="text-center mb-3">
                                                    <MudImage Src="@_newService.MainImageUrl" 
                                                             Alt="الصورة الرئيسية"
                                                             Class="rounded-lg shadow-sm"
                                                             Style="max-height: 150px; max-width: 100%; object-fit: cover;" />
                                                </div>
                                                <div class="d-flex gap-2">
                                                    <MudButton Variant="Variant.Outlined" 
                                                              Color="Color.Error" 
                                                              Size="Size.Small"
                                                              StartIcon="@Icons.Material.Filled.Delete"
                                                              OnClick="@(() => _newService.MainImageUrl = string.Empty)"
                                                              FullWidth="true">
                                                        حذف
                                                    </MudButton>
                                                </div>
                                            }
                                            else
                                            {
                                                <div class="text-center">
                                                    <MudIcon Icon="@Icons.Material.Filled.CloudUpload" 
                                                            Size="Size.Large" 
                                                            Color="Color.Secondary" 
                                                            Class="mb-2" />
                                                    <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3">
                                                        لم يتم رفع صورة رئيسية
                                                    </MudText>
                                                </div>
                                            }
                                        </div>

                                        <MudFileUpload T="IBrowserFile" 
                                                      Accept=".png,.jpg,.jpeg,.webp" 
                                                      FilesChanged="@UploadMainImage"
                                                      MaximumFileCount="1">
                                            <ActivatorContent>
                                                <MudButton Variant="Variant.Filled"
                                                          Color="Color.Primary"
                                                          StartIcon="@Icons.Material.Filled.Upload"
                                                          FullWidth="true"
                                                          Size="Size.Medium">
                                                    @(!string.IsNullOrEmpty(_newService.MainImageUrl) ? "تغيير الصورة" : "رفع صورة")
                                                </MudButton>
                                            </ActivatorContent>
                                        </MudFileUpload>
                                        
                                        <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mt-2 text-center">
                                            الحد الأقصى: 5 ميجابايت | PNG, JPG, JPEG, WebP
                                        </MudText>
                                    </div>
                                </MudPaper>
                            </MudItem>

                            <!-- صور الدوار -->
                            <MudItem xs="12" md="6" lg="4">
                                <MudPaper Class="pa-4" Elevation="2" Style="min-height: 300px;">
                                    <div class="d-flex flex-column h-100">
                                        <MudText Typo="Typo.h6" Class="mb-2 d-flex align-center">
                                            <MudIcon Icon="@Icons.Material.Filled.PhotoLibrary" Class="me-2" Color="Color.Secondary" />
                                            صور الدوار
                                        </MudText>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3">
                                            مجموعة صور تظهر في معرض الخدمة
                                        </MudText>

                                        <div class="flex-grow-1 d-flex flex-column justify-center">
                                            @if (!string.IsNullOrEmpty(_newService.ImageCarousel))
                                            {
                                                var carouselImages = _newService.ImageCarousel.Split(';', StringSplitOptions.RemoveEmptyEntries);
                                                <div class="text-center mb-3">
                                                    <MudChip T="string" 
                                                            Size="Size.Medium" 
                                                            Color="Color.Info"
                                                            Icon="@Icons.Material.Filled.Collections">
                                                        @carouselImages.Length صورة
                                                    </MudChip>
                                                </div>
                                                <div class="d-flex gap-2">
                                                    <MudButton Variant="Variant.Outlined" 
                                                              Color="Color.Info" 
                                                              Size="Size.Small"
                                                              StartIcon="@Icons.Material.Filled.Visibility"
                                                              OnClick="@(() => PreviewCarouselImages())"
                                                              FullWidth="true">
                                                        معاينة
                                                    </MudButton>
                                                    <MudButton Variant="Variant.Outlined" 
                                                              Color="Color.Error" 
                                                              Size="Size.Small"
                                                              StartIcon="@Icons.Material.Filled.Delete"
                                                              OnClick="@(() => _newService.ImageCarousel = string.Empty)"
                                                              FullWidth="true">
                                                        حذف الكل
                                                    </MudButton>
                                                </div>
                                            }
                                            else
                                            {
                                                <div class="text-center">
                                                    <MudIcon Icon="@Icons.Material.Filled.PhotoLibrary" 
                                                            Size="Size.Large" 
                                                            Color="Color.Secondary" 
                                                            Class="mb-2" />
                                                    <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3">
                                                        لم يتم رفع صور للدوار
                                                    </MudText>
                                                </div>
                                            }
                                        </div>

                                        <MudFileUpload T="IReadOnlyList<IBrowserFile>" 
                                                      Accept=".png,.jpg,.jpeg,.webp" 
                                                      FilesChanged="@UploadCarouselImages"
                                                      MaximumFileCount="10">
                                            <ActivatorContent>
                                                <MudButton Variant="Variant.Filled"
                                                          Color="Color.Secondary"
                                                          StartIcon="@Icons.Material.Filled.Upload"
                                                          FullWidth="true"
                                                          Size="Size.Medium">
                                                    رفع صور (حتى 10)
                                                </MudButton>
                                            </ActivatorContent>
                                        </MudFileUpload>
                                        
                                        <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mt-2 text-center">
                                            الحد الأقصى: 5 ميجابايت لكل صورة | حتى 10 صور
                                        </MudText>
                                    </div>
                                </MudPaper>
                            </MudItem>

                            <!-- الفيديو -->
                            <MudItem xs="12" md="12" lg="4">
                                <MudPaper Class="pa-4" Elevation="2" Style="min-height: 300px;">
                                    <div class="d-flex flex-column h-100">
                                        <MudText Typo="Typo.h6" Class="mb-2 d-flex align-center">
                                            <MudIcon Icon="@Icons.Material.Filled.VideoFile" Class="me-2" Color="Color.Tertiary" />
                                            فيديو الخدمة
                                        </MudText>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3">
                                            فيديو توضيحي أو تعريفي للخدمة
                                        </MudText>

                                        <div class="flex-grow-1 d-flex flex-column justify-center">
                                            @if (!string.IsNullOrEmpty(_newService.VideoUrl))
                                            {
                                                <div class="text-center mb-3">
                                                    <MudIcon Icon="@Icons.Material.Filled.PlayCircle" 
                                                            Size="Size.Large" 
                                                            Color="Color.Success" 
                                                            Class="mb-2" />
                                                    <MudText Typo="Typo.body2" Color="Color.Success">
                                                        تم رفع الفيديو بنجاح
                                                    </MudText>
                                                </div>
                                                <div class="d-flex gap-2">
                                                    <MudButton Variant="Variant.Outlined" 
                                                              Color="Color.Info" 
                                                              Size="Size.Small"
                                                              StartIcon="@Icons.Material.Filled.PlayArrow"
                                                              Href="@_newService.VideoUrl"
                                                              Target="_blank"
                                                              FullWidth="true">
                                                        تشغيل
                                                    </MudButton>
                                                    <MudButton Variant="Variant.Outlined" 
                                                              Color="Color.Error" 
                                                              Size="Size.Small"
                                                              StartIcon="@Icons.Material.Filled.Delete"
                                                              OnClick="@(() => _newService.VideoUrl = string.Empty)"
                                                              FullWidth="true">
                                                        حذف
                                                    </MudButton>
                                                </div>
                                            }
                                            else
                                            {
                                                <div class="text-center">
                                                    <MudIcon Icon="@Icons.Material.Filled.VideoFile" 
                                                            Size="Size.Large" 
                                                            Color="Color.Secondary" 
                                                            Class="mb-2" />
                                                    <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3">
                                                        لم يتم رفع فيديو
                                                    </MudText>
                                                </div>
                                            }
                                        </div>

                                        <MudFileUpload T="IBrowserFile" 
                                                      Accept=".mp4,.avi,.mov,.wmv,.webm" 
                                                      FilesChanged="@UploadVideo"
                                                      MaximumFileCount="1">
                                            <ActivatorContent>
                                                <MudButton Variant="Variant.Filled"
                                                          Color="Color.Tertiary"
                                                          StartIcon="@Icons.Material.Filled.Upload"
                                                          FullWidth="true"
                                                          Size="Size.Medium">
                                                    @(!string.IsNullOrEmpty(_newService.VideoUrl) ? "تغيير الفيديو" : "رفع فيديو")
                                                </MudButton>
                                            </ActivatorContent>
                                        </MudFileUpload>
                                        
                                        <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mt-2 text-center">
                                            الحد الأقصى: 50 ميجابايت | MP4, AVI, MOV, WMV, WebM
                                        </MudText>
                                    </div>
                                </MudPaper>
                            </MudItem>

                            <!-- الروابط اليدوية -->
                            <MudItem xs="12">
                                <MudExpansionPanels Elevation="1" Class="mt-4">
                                    <MudExpansionPanel Text="الروابط اليدوية (متقدم)" 
                                                      Icon="@Icons.Material.Filled.Link"
                                                      MaxHeight="400">
                                        <MudGrid Spacing="3" Class="mt-2">
                                            <MudItem xs="12">
                                                <MudAlert Severity="Severity.Info" Class="mb-3">
                                                    يمكنك إدخال روابط مباشرة للوسائط بدلاً من رفع الملفات. هذا مفيد للملفات المستضافة خارجياً.
                                                </MudAlert>
                                            </MudItem>
                                            
                                            <MudItem xs="12" md="6">
                                                <MudTextField @bind-Value="_manualMainImageUrl"
                                                              Label="رابط الصورة الرئيسية"
                                                              Variant="Variant.Outlined"
                                                              Margin="Margin.Dense"
                                                              HelperText="أدخل رابط مباشر للصورة"
                                                              Adornment="Adornment.End"
                                                              AdornmentIcon="@Icons.Material.Filled.Link"
                                                              AdornmentColor="Color.Primary" />
                                                <MudButton Variant="Variant.Text" 
                                                          Color="Color.Primary" 
                                                          Size="Size.Small"
                                                          StartIcon="@Icons.Material.Filled.Check"
                                                          OnClick="@(() => ApplyManualImageUrl())"
                                                          Class="mt-2">
                                                    تطبيق الرابط
                                                </MudButton>
                                            </MudItem>

                                            <MudItem xs="12" md="6">
                                                <MudTextField @bind-Value="_manualVideoUrl"
                                                              Label="رابط الفيديو"
                                                              Variant="Variant.Outlined"
                                                              Margin="Margin.Dense"
                                                              HelperText="أدخل رابط مباشر للفيديو"
                                                              Adornment="Adornment.End"
                                                              AdornmentIcon="@Icons.Material.Filled.Link"
                                                              AdornmentColor="Color.Tertiary" />
                                                <MudButton Variant="Variant.Text" 
                                                          Color="Color.Tertiary" 
                                                          Size="Size.Small"
                                                          StartIcon="@Icons.Material.Filled.Check"
                                                          OnClick="@(() => ApplyManualVideoUrl())"
                                                          Class="mt-2">
                                                    تطبيق الرابط
                                                </MudButton>
                                            </MudItem>

                                            <MudItem xs="12">
                                                <MudTextField @bind-Value="_manualCarouselUrls"
                                                              Label="روابط صور الدوار"
                                                              Variant="Variant.Outlined"
                                                              Lines="3"
                                                              HelperText="أدخل روابط الصور مفصولة بفواصل منقوطة (;)"
                                                              Placeholder="https://example.com/image1.jpg;https://example.com/image2.jpg"
                                                              Adornment="Adornment.End"
                                                              AdornmentIcon="@Icons.Material.Filled.Link"
                                                              AdornmentColor="Color.Secondary" />
                                                <MudButton Variant="Variant.Text" 
                                                          Color="Color.Secondary" 
                                                          Size="Size.Small"
                                                          StartIcon="@Icons.Material.Filled.Check"
                                                          OnClick="@(() => ApplyManualCarouselUrls())"
                                                          Class="mt-2">
                                                    تطبيق الروابط
                                                </MudButton>
                                            </MudItem>
                                        </MudGrid>
                                    </MudExpansionPanel>
                                </MudExpansionPanels>
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>
                </MudTabs>
            </MudForm>
        </MudContainer>
    </DialogContent>

    <DialogActions>
        <div class="d-flex justify-space-between align-center w-100">
            <!-- معلومات التقدم -->
            <div class="d-none d-sm-flex align-center gap-2">
                <MudChip T="string" Size="Size.Small" Color="@GetOverallProgressColor()" Variant="Variant.Text">
                    @GetOverallProgressText()
                </MudChip>
            </div>
            
            <!-- الأزرار -->
            <div class="d-flex gap-2">
                <MudButton OnClick="Cancel" 
                          Color="Color.Default"
                          Variant="Variant.Outlined"
                          StartIcon="@Icons.Material.Filled.Cancel">
                    إلغاء
                </MudButton>
                <MudButton OnClick="Save" 
                          Color="Color.Primary" 
                          Variant="Variant.Filled" 
                          StartIcon="@Icons.Material.Filled.Save"
                          Loading="_isSaving"
                          Disabled="_isSaving">
                    @(_isSaving ? "جاري الحفظ..." : "حفظ الخدمة")
                </MudButton>
            </div>
        </div>
    </DialogActions>
</MudDialog>

@code {
    private MudForm _form = new();
    private ServiceDto _newService = new() { IsActive = true, DisplayOrder = 0 };
    private int _activeTabIndex = 0;
    private bool _isSaving = false;

    // Manual URL inputs
    private string _manualMainImageUrl = string.Empty;
    private string _manualVideoUrl = string.Empty;
    private string _manualCarouselUrls = string.Empty;

    [CascadingParameter] public required IMudDialogInstance MudDialog { get; set; }

    #region Badge Methods
    private string GetServiceDetailsBadge()
    {
        var completedFields = 0;
        var totalFields = 4;

        if (!string.IsNullOrWhiteSpace(_newService.Title)) completedFields++;
        if (!string.IsNullOrWhiteSpace(_newService.PrimaryDescription)) completedFields++;
        if (!string.IsNullOrWhiteSpace(_newService.DetailedDescription)) completedFields++;
        if (!string.IsNullOrWhiteSpace(_newService.Features)) completedFields++;

        return $"{completedFields}/{totalFields}";
    }

    private Color GetServiceDetailsBadgeColor()
    {
        var badge = GetServiceDetailsBadge();
        return badge switch
        {
            "4/4" => Color.Success,
            "3/4" or "2/4" => Color.Warning,
            _ => Color.Error
        };
    }

    private string GetMediaBadge()
    {
        var mediaCount = 0;
        if (!string.IsNullOrWhiteSpace(_newService.MainImageUrl)) mediaCount++;
        if (!string.IsNullOrWhiteSpace(_newService.ImageCarousel)) mediaCount++;
        if (!string.IsNullOrWhiteSpace(_newService.VideoUrl)) mediaCount++;

        return mediaCount.ToString();
    }

    private Color GetMediaBadgeColor()
    {
        var count = int.Parse(GetMediaBadge());
        return count switch
        {
            >= 2 => Color.Success,
            1 => Color.Warning,
            _ => Color.Error
        };
    }

    private string GetOverallProgressText()
    {
        var hasRequiredFields = !string.IsNullOrWhiteSpace(_newService.Title) && 
                               !string.IsNullOrWhiteSpace(_newService.PrimaryDescription);
        var hasMedia = !string.IsNullOrWhiteSpace(_newService.MainImageUrl);

        if (hasRequiredFields && hasMedia) return "جاهز للحفظ";
        if (hasRequiredFields) return "يحتاج صورة رئيسية";
        return "يحتاج معلومات أساسية";
    }

    private Color GetOverallProgressColor()
    {
        var text = GetOverallProgressText();
        return text switch
        {
            "جاهز للحفظ" => Color.Success,
            "يحتاج صورة رئيسية" => Color.Warning,
            _ => Color.Error
        };
    }
    #endregion

    #region Manual URL Methods
    private void ApplyManualImageUrl()
    {
        if (!string.IsNullOrWhiteSpace(_manualMainImageUrl))
        {
            _newService.MainImageUrl = _manualMainImageUrl.Trim();
            _manualMainImageUrl = string.Empty;
            Snackbar.Add("تم تطبيق رابط الصورة الرئيسية", Severity.Success);
            StateHasChanged();
        }
    }

    private void ApplyManualVideoUrl()
    {
        if (!string.IsNullOrWhiteSpace(_manualVideoUrl))
        {
            _newService.VideoUrl = _manualVideoUrl.Trim();
            _manualVideoUrl = string.Empty;
            Snackbar.Add("تم تطبيق رابط الفيديو", Severity.Success);
            StateHasChanged();
        }
    }

    private void ApplyManualCarouselUrls()
    {
        if (!string.IsNullOrWhiteSpace(_manualCarouselUrls))
        {
            _newService.ImageCarousel = _manualCarouselUrls.Trim();
            _manualCarouselUrls = string.Empty;
            Snackbar.Add("تم تطبيق روابط صور الدوار", Severity.Success);
            StateHasChanged();
        }
    }

    private void PreviewCarouselImages()
    {
        // This could open a dialog to preview carousel images
        Snackbar.Add("معاينة الصور - قيد التطوير", Severity.Info);
    }
    #endregion

    #region Dialog Actions
    private async Task Save()
    {
        _isSaving = true;
        StateHasChanged();

        try
        {
            // Basic validation
            if (string.IsNullOrWhiteSpace(_newService.Title))
            {
                Snackbar.Add("عنوان الخدمة مطلوب", Severity.Error);
                _activeTabIndex = 0;
                return;
            }

            if (string.IsNullOrWhiteSpace(_newService.PrimaryDescription))
            {
                Snackbar.Add("الوصف الأساسي مطلوب", Severity.Error);
                _activeTabIndex = 0;
                return;
            }

            if (string.IsNullOrWhiteSpace(_newService.MainImageUrl))
            {
                Snackbar.Add("الصورة الرئيسية مطلوبة", Severity.Error);
                _activeTabIndex = 1; // Switch to media tab
                return;
            }

            MudDialog.Close(DialogResult.Ok(_newService));
        }
        finally
        {
            _isSaving = false;
            StateHasChanged();
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
    #endregion

    #region File Upload Methods
    private async Task UploadMainImage(IBrowserFile file)
    {
        if (file == null) return;

        try
        {
            // Validate file
            if (!await ValidateImageFile(file)) return;

            // Generate unique filename
            var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.Name).ToLowerInvariant()}";

            // Upload file
            var uploadedUrl = await UploadFile(file, "api/upload/service/image", fileName, 5 * 1024 * 1024);
            
            if (!string.IsNullOrEmpty(uploadedUrl))
            {
                _newService.MainImageUrl = uploadedUrl;
                Snackbar.Add("تم رفع الصورة الرئيسية بنجاح", Severity.Success);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في رفع الصورة الرئيسية: {ex.Message}", Severity.Error);
        }
    }

    private async Task UploadCarouselImages(IReadOnlyList<IBrowserFile> files)
    {
        if (files == null || files.Count == 0) return;

        try
        {
            var uploadedUrls = new List<string>();
            var successCount = 0;

            foreach (var file in files)
            {
                if (!await ValidateImageFile(file)) continue;

                var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.Name).ToLowerInvariant()}";
                var uploadedUrl = await UploadFile(file, "api/upload/service/slider", fileName, 5 * 1024 * 1024);
                
                if (!string.IsNullOrEmpty(uploadedUrl))
                {
                    uploadedUrls.Add(uploadedUrl);
                    successCount++;
                }
            }

            if (uploadedUrls.Any())
            {
                // Combine with existing images
                var existingImages = string.IsNullOrEmpty(_newService.ImageCarousel)
                    ? new List<string>()
                    : _newService.ImageCarousel.Split(';', StringSplitOptions.RemoveEmptyEntries).ToList();

                existingImages.AddRange(uploadedUrls);
                _newService.ImageCarousel = string.Join(";", existingImages);

                Snackbar.Add($"تم رفع {successCount} صورة بنجاح", Severity.Success);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في رفع صور الدوار: {ex.Message}", Severity.Error);
        }
    }

    private async Task UploadVideo(IBrowserFile file)
    {
        if (file == null) return;

        try
        {
            // Validate video file
            if (!await ValidateVideoFile(file)) return;

            var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.Name).ToLowerInvariant()}";
            var uploadedUrl = await UploadFile(file, "api/upload/service/video", fileName, 50 * 1024 * 1024);
            
            if (!string.IsNullOrEmpty(uploadedUrl))
            {
                _newService.VideoUrl = uploadedUrl;
                Snackbar.Add("تم رفع الفيديو بنجاح", Severity.Success);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في رفع الفيديو: {ex.Message}", Severity.Error);
        }
    }

    private async Task<bool> ValidateImageFile(IBrowserFile file)
    {
        // Check file size (max 5MB)
        if (file.Size > 5 * 1024 * 1024)
        {
            Snackbar.Add($"حجم الملف {file.Name} يتجاوز الحد الأقصى (5 ميجابايت)", Severity.Error);
            return false;
        }

        // Check file type
        var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".webp" };
        var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();

        if (!allowedExtensions.Contains(fileExtension))
        {
            Snackbar.Add($"نوع الملف {file.Name} غير مدعوم. الأنواع المدعومة: JPG, JPEG, PNG, WebP", Severity.Error);
            return false;
        }

        return true;
    }

    private async Task<bool> ValidateVideoFile(IBrowserFile file)
    {
        // Check file size (max 50MB)
        if (file.Size > 50 * 1024 * 1024)
        {
            Snackbar.Add($"حجم الملف {file.Name} يتجاوز الحد الأقصى (50 ميجابايت)", Severity.Error);
            return false;
        }

        // Check file type
        var allowedExtensions = new[] { ".mp4", ".avi", ".mov", ".wmv", ".webm" };
        var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();

        if (!allowedExtensions.Contains(fileExtension))
        {
            Snackbar.Add($"نوع الملف {file.Name} غير مدعوم. الأنواع المدعومة: MP4, AVI, MOV, WMV, WebM", Severity.Error);
            return false;
        }

        return true;
    }

    private async Task<string> UploadFile(IBrowserFile file, string endpoint, string fileName, long maxSize)
    {
        try
        {
            using var content = new MultipartFormDataContent();
            using var fileStream = file.OpenReadStream(maxAllowedSize: maxSize);
            using var streamContent = new StreamContent(fileStream);

            content.Add(streamContent, "file", fileName);

            var response = await Http.PostAsync(endpoint, content);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                Snackbar.Add($"خطأ في رفع الملف: {error}", Severity.Error);
                return string.Empty;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في رفع الملف: {ex.Message}", Severity.Error);
            return string.Empty;
        }
    }
    #endregion
}


