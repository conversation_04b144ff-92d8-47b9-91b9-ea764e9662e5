<style>
    /* SiderAboutUs Component - Beautiful & Elegant Styles */
    /* Matching NewsSiderMove Color Scheme */

    /* Container Styling */
    .sider-aboutus-container {
        margin-top: 2rem;
        padding: 0 1rem;
    }

    /* Page Header Section */
    .page-header-section {
        margin-bottom: 2rem;
    }

    .page-header-card {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        border-radius: 20px;
        padding: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(149, 55, 53, 0.3);
    }

    .page-header-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .header-content {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 1;
    }

    .header-icon-wrapper {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .header-icon {
        font-size: 2rem;
        color: white;
    }

    .header-title {
        color: white;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .header-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 400;
        line-height: 1.6;
    }

    .header-stats {
        display: flex;
        gap: 2rem;
        position: relative;
        z-index: 1;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        padding: 1rem 1.5rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
    }

    .stat-icon {
        font-size: 1.5rem;
        color: rgba(255, 255, 255, 0.9);
    }

    .stat-icon.active {
        color: #4caf50;
    }

    .stat-number {
        color: white;
        font-weight: 700;
        margin: 0;
    }

    .stat-label {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
    }

    /* Preview Section */
    .preview-section {
        margin-bottom: 2rem;
    }

    .preview-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .preview-card:hover {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(149, 55, 53, 0.1);
    }

    .preview-title-wrapper {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .preview-icon {
        color: #953735;
        font-size: 1.5rem;
    }

    .preview-title {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
    }

    .carousel-controls {
        display: flex;
        gap: 0.5rem;
    }

    .carousel-container {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        background: #f8f9fa;
        position: relative;
    }

    .carousel-container .mud-carousel {
        height: 400px;
        width: 100%;
    }

    .enhanced-carousel {
        height: 400px;
        border-radius: 16px;
        width: 100%;
    }

    .enhanced-carousel .mud-carousel-item {
        height: 400px;
        width: 100%;
    }

    .carousel-item {
        position: relative;
        height: 400px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .slide-content {
        position: relative;
        height: 100%;
        width: 100%;
        display: block;
    }

    .slide-image-wrapper {
        position: relative;
        height: 100%;
        width: 100%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .slide-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        transition: transform 0.3s ease;
        cursor: pointer;
    }

    .slide-image:hover {
        transform: scale(1.05);
    }

    .slide-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 40%;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        pointer-events: none;
        z-index: 1;
    }

    .slide-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 1.5rem 2rem;
        color: white;
        text-align: center;
        z-index: 2;
    }

    .slide-title {
        color: white;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    .slide-description {
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .slide-link-btn {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        font-weight: 600;
    }

    .slide-link-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }

    .no-active-slides {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .no-slides-icon {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }

    .no-slides-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .no-slides-subtitle {
        color: #6c757d;
    }

    /* Controls Section */
    .controls-section {
        margin-bottom: 2rem;
    }

    .controls-card {
        background: white;
        border-radius: 20px;
        padding: 1.5rem 2rem;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .controls-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 2rem;
    }

    .controls-left {
        flex: 0 0 auto;
    }

    .controls-right {
        flex: 1;
        display: flex;
        justify-content: flex-end;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
    }

    .primary-action-btn {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: none;
        box-shadow: 0 4px 16px rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .primary-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 24px rgba(149, 55, 53, 0.4);
    }

    .secondary-action-btn {
        border: 2px solid #953735;
        color: #953735;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: none;
        transition: all 0.3s ease;
    }

    .secondary-action-btn:hover {
        background: rgba(149, 55, 53, 0.1);
        transform: translateY(-2px);
    }

    .search-wrapper {
        position: relative;
        max-width: 400px;
        width: 100%;
    }

    .enhanced-search-field {
        border-radius: 12px;
    }

    .enhanced-search-field .mud-input-control {
        border-radius: 12px;
        border: 2px solid rgba(149, 55, 53, 0.2);
        transition: all 0.3s ease;
    }

    .enhanced-search-field .mud-input-control:hover {
        border-color: rgba(149, 55, 53, 0.4);
    }

    .enhanced-search-field .mud-input-control.mud-input-control-focused {
        border-color: #953735;
        box-shadow: 0 0 0 3px rgba(149, 55, 53, 0.1);
    }

    .search-clear-btn {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
    }

    .button-text-short {
        display: none;
    }

    .button-text-full {
        display: inline;
    }

    /* Loading Section */
    .loading-section {
        margin: 3rem 0;
    }

    .loading-card {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        text-align: center;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .loading-text {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
    }

    .loading-subtitle {
        color: #6c757d;
        margin: 0;
    }

    /* Empty State Section */
    .empty-state-section {
        margin: 3rem 0;
    }

    .empty-state-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 20px;
        padding: 4rem 2rem;
        text-align: center;
        border: 2px dashed rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .empty-state-card:hover {
        border-color: rgba(149, 55, 53, 0.5);
        transform: translateY(-2px);
    }

    .empty-state-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .empty-state-icon-wrapper {
        background: rgba(149, 55, 53, 0.1);
        border-radius: 50%;
        padding: 2rem;
        margin-bottom: 1rem;
    }

    .empty-state-icon {
        font-size: 4rem;
        color: #953735;
    }

    .empty-state-title {
        color: #2c3e50;
        font-weight: 700;
        margin: 0;
    }

    .empty-state-subtitle {
        color: #6c757d;
        max-width: 400px;
        line-height: 1.6;
        margin: 0;
    }

    .empty-state-action-btn {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        border-radius: 12px;
        padding: 1rem 2rem;
        font-weight: 600;
        text-transform: none;
        box-shadow: 0 4px 16px rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .empty-state-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 24px rgba(149, 55, 53, 0.4);
    }

    /* Table Enhancements */
    .regular-table {
        display: block;
    }

    .regular-table .mud-table {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .regular-table .mud-table-head {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
    }

    .regular-table .mud-table-head .mud-table-cell {
        color: white;
        font-weight: 600;
        border-bottom: none;
    }

    .regular-table .mud-table-row:hover {
        background: rgba(149, 55, 53, 0.05);
    }

    .mud-table-cell:first-child {
        font-weight: 600;
        color: #953735;
        text-align: center;
        width: 60px;
        min-width: 60px;
    }

    .mobile-cards {
        display: none;
    }

    /* Enhanced Mobile Cards - Redesigned */
    .mobile-cards {
        display: none;
        gap: 1.5rem;
    }

    .mobile-card {
        background: #ffffff !important;
        border-radius: 20px;
        transition: all 0.3s ease;
        border: 1px solid rgba(149, 55, 53, 0.15);
        box-shadow: 0 4px 20px rgba(149, 55, 53, 0.1);
        overflow: hidden;
        position: relative;
        margin-bottom: 1.5rem;
    }

    .mobile-card .mud-card {
        background: #ffffff !important;
    }

    .mobile-card .mud-card-content {
        background: #ffffff !important;
    }

    .mobile-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        z-index: 1;
    }

    .mobile-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 32px rgba(149, 55, 53, 0.2);
        border-color: rgba(149, 55, 53, 0.3);
    }

    .card-content {
        padding: 1.5rem !important;
        background: #ffffff !important;
        position: relative;
        z-index: 2;
    }

    /* Card Header */
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(149, 55, 53, 0.15);
        background: #ffffff;
    }

    .card-number-wrapper {
        display: flex;
        align-items: center;
    }

    .slide-number {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        color: white;
        font-weight: 700;
        min-width: 45px;
        height: 45px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(149, 55, 53, 0.3);
        font-size: 1.1rem;
    }

    .card-actions {
        display: flex;
        gap: 0.5rem;
    }

    .action-btn {
        border-radius: 10px;
        transition: all 0.3s ease;
        width: 40px;
        height: 40px;
    }

    .action-btn:hover {
        transform: scale(1.1);
    }

    .edit-btn:hover {
        background: rgba(25, 118, 210, 0.1);
    }

    .delete-btn:hover {
        background: rgba(244, 67, 54, 0.1);
    }

    /* Card Main Content */
    .card-main-content {
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
        background: #ffffff;
    }

    /* Title Section */
    .card-title-section {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 1rem;
    }

    .card-title {
        color: #2c3e50;
        font-weight: 700;
        line-height: 1.4;
        word-break: break-word;
        margin: 0;
        flex: 1;
    }

    .status-chip {
        flex-shrink: 0;
        font-weight: 600;
    }

    /* Description */
    .card-description {
        margin: -0.5rem 0 0 0;
    }

    .description-text {
        color: #6c757d;
        line-height: 1.6;
        margin: 0;
    }

    /* Info Grid */
    .card-info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1rem;
        border: 1px solid rgba(149, 55, 53, 0.1);
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        direction: rtl;
    }

    .info-icon-wrapper {
        background: rgba(149, 55, 53, 0.15);
        border-radius: 8px;
        padding: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .info-icon {
        color: #953735;
        font-size: 1rem;
    }

    .info-content {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        flex: 1;
        text-align: right;
    }

    .info-label {
        color: #6c757d;
        font-weight: 500;
        margin: 0;
        font-size: 0.75rem;
    }

    .info-value {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
        font-size: 0.9rem;
    }

    /* Image Modal Styles */
    .image-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
    }

    .image-modal-close {
        position: absolute;
        top: 2rem;
        right: 2rem;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        border-radius: 50%;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 10001;
    }

    .image-modal-close:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    .image-modal-close .mud-icon-root {
        color: white;
        font-size: 1.5rem;
    }

    .image-modal-content-wrapper {
        max-width: 90%;
        max-height: 90%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .image-modal-content {
        max-width: 100%;
        max-height: 80vh;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    }

    .image-modal-title {
        color: white;
        font-size: 1.2rem;
        font-weight: 600;
        text-align: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        max-width: 600px;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .regular-table {
            display: none;
        }

        .mobile-cards {
            display: block;
        }

        .sider-aboutus-container {
            padding: 0 0.5rem;
        }

        .page-header-card {
            padding: 1.5rem;
            margin: 0 0.5rem 1.5rem;
        }

        .header-content {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .header-stats {
            flex-direction: column;
            gap: 1rem;
            width: 100%;
        }

        .stat-item {
            justify-content: center;
            width: 100%;
        }

        .controls-container {
            flex-direction: column;
            gap: 1rem;
        }

        .controls-left,
        .controls-right {
            width: 100%;
        }

        .controls-right {
            flex-direction: column;
            align-items: stretch;
        }

        .status-filter-select {
            margin-left: 0 !important;
            margin-bottom: 1rem;
        }

        .action-buttons {
            flex-direction: column;
            width: 100%;
        }

        .primary-action-btn,
        .secondary-action-btn {
            width: 100%;
            justify-content: center;
        }

        .search-wrapper {
            max-width: 100%;
        }

        .button-text-short {
            display: inline;
        }

        .button-text-full {
            display: none;
        }
    }

    /* Animations and Transitions */
    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @@keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }

    .page-header-section {
        animation: fadeInUp 0.6s ease-out;
    }

    .preview-section {
        animation: fadeInUp 0.6s ease-out 0.1s both;
    }

    .controls-section {
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }

    .mobile-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .mobile-card:nth-child(2n) {
        animation-delay: 0.1s;
    }

    .mobile-card:nth-child(3n) {
        animation-delay: 0.2s;
    }

    /* Enhanced Carousel Styles */
    .enhanced-carousel .mud-carousel-item {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .enhanced-carousel .mud-carousel-arrows button {
        background: rgba(255, 255, 255, 0.9) !important;
        color: #953735 !important;
        border-radius: 50% !important;
        width: 48px !important;
        height: 48px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        transition: all 0.3s ease !important;
    }

    .enhanced-carousel .mud-carousel-arrows button:hover {
        background: white !important;
        transform: scale(1.1) !important;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
    }

    .enhanced-carousel .mud-carousel-bullets .mud-button {
        background: rgba(255, 255, 255, 0.5) !important;
        border-radius: 50% !important;
        width: 12px !important;
        height: 12px !important;
        min-width: 12px !important;
        margin: 0 4px !important;
        transition: all 0.3s ease !important;
    }

    .enhanced-carousel .mud-carousel-bullets .mud-button.mud-button-filled {
        background: #953735 !important;
        transform: scale(1.2) !important;
    }

    /* Smooth Scrolling */
    .sider-aboutus-container {
        scroll-behavior: smooth;
    }

    /* Performance Optimizations */
    .enhanced-carousel,
    .slide-image,
    .mobile-card,
    .preview-card,
    .controls-card {
        will-change: transform;
    }

    /* Additional Small Screen Optimizations */
    @@media (max-width: 480px) {
        .sider-aboutus-container {
            padding: 0 0.25rem;
        }

        .page-header-card {
            padding: 1rem;
            margin: 0 0.25rem 1rem;
        }

        .header-title {
            font-size: 1.5rem;
        }

        .stat-item {
            padding: 0.75rem 1rem;
        }

        .compact-content {
            padding: 1rem !important;
        }

        .gif-title {
            font-size: 1rem;
        }

        .gif-info {
            font-size: 0.85rem;
        }

        .image-modal-close {
            top: 1rem;
            right: 1rem;
            width: 40px;
            height: 40px;
        }

        .image-modal-title {
            font-size: 1rem;
        }
    }

    /* دعم الوضع المظلم المحسن - اختياري فقط */
    .dark-theme,
    [data-theme="dark"] {
        .page-header-card {
            background: linear-gradient(135deg, #953735 0%, #6d2a28 100%);
        }

        .preview-card,
        .controls-card,
        .loading-card {
            background: #1e1e1e !important;
            border-color: rgba(149, 55, 53, 0.3);
        }

        .mobile-card,
        .mobile-card .mud-card,
        .mobile-card .mud-card-content,
        .mobile-card .card-content,
        .mobile-card .card-header,
        .mobile-card .card-main-content {
            background: #1e1e1e !important;
            border-color: rgba(149, 55, 53, 0.3);
        }

        .card-header {
            border-bottom-color: rgba(149, 55, 53, 0.2);
        }

        .card-title {
            color: #ffffff !important;
        }

        .description-text {
            color: #b0b0b0 !important;
        }

        .card-info-grid {
            background: rgba(149, 55, 53, 0.1) !important;
            border-color: rgba(149, 55, 53, 0.2);
        }

        .info-icon-wrapper {
            background: rgba(149, 55, 53, 0.2) !important;
        }

        .info-label {
            color: #b0b0b0 !important;
        }

        .info-value {
            color: #ffffff !important;
        }

        .empty-state-card {
            background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%) !important;
            border-color: rgba(149, 55, 53, 0.3);
        }

        .empty-state-title {
            color: #ffffff !important;
        }

        .empty-state-subtitle {
            color: #b0b0b0 !important;
        }
    }

    /* Responsive Design - Mobile First */

    /* عرض البطاقات في الشاشات الصغيرة */
    @@media (max-width: 768px) {
        .regular-table {
            display: none;
        }

        .mobile-cards {
            display: block;
        }

        .sider-aboutus-container {
            padding: 0 0.5rem;
        }

        .page-header-card {
            padding: 1.5rem;
            margin: 0 0.5rem 1.5rem;
            border-radius: 16px;
        }

        .header-content {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .header-stats {
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .stat-item {
            flex: 1;
            min-width: 140px;
        }

        .preview-card,
        .controls-card {
            padding: 1.5rem;
            border-radius: 16px;
        }

        .enhanced-carousel {
            height: 300px;
        }

        .enhanced-carousel .mud-carousel {
            height: 300px;
        }

        .carousel-item {
            height: 300px;
        }

        .slide-image-wrapper {
            height: 300px;
        }

        .slide-image {
            max-height: 300px;
        }

        .controls-container {
            flex-direction: column;
            gap: 1.5rem;
        }

        .controls-left,
        .controls-right {
            width: 100%;
        }

        .action-buttons {
            justify-content: center;
        }

        .search-wrapper {
            max-width: none;
        }
    }

    /* تحسينات للشاشات الصغيرة جداً */
    @@media (max-width: 480px) {
        .sider-aboutus-container {
            padding: 0 0.25rem;
        }

        .page-header-card {
            padding: 1rem;
            margin: 0 0.25rem 1rem;
        }

        .header-title {
            font-size: 1.5rem;
        }

        .stat-item {
            padding: 0.75rem 1rem;
            min-width: 120px;
        }

        .button-text-short {
            display: inline;
        }

        .button-text-full {
            display: none;
        }

        .action-buttons {
            flex-direction: column;
            width: 100%;
        }

        .primary-action-btn,
        .secondary-action-btn {
            width: 100%;
            justify-content: center;
        }

        .card-content {
            padding: 1rem !important;
        }

        .card-header {
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
        }

        .slide-number {
            min-width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .action-btn {
            width: 36px;
            height: 36px;
        }

        .card-title {
            font-size: 1.1rem;
        }

        .card-info-grid {
            grid-template-columns: 1fr;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .info-item {
            gap: 0.5rem;
        }

        .info-icon-wrapper {
            padding: 0.4rem;
        }

        .enhanced-carousel {
            height: 250px;
        }

        .enhanced-carousel .mud-carousel {
            height: 250px;
        }

        .carousel-item {
            height: 250px;
        }

        .slide-image-wrapper {
            height: 250px;
        }

        .slide-image {
            max-height: 250px;
        }

        .slide-info {
            padding: 1rem;
        }

        .preview-card,
        .controls-card,
        .loading-card,
        .empty-state-card {
            margin: 0 0.25rem 1rem;
        }
    }

    /* Animations and Transitions */
    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @@keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }

    .page-header-section {
        animation: fadeInUp 0.6s ease-out;
    }

    .preview-section {
        animation: fadeInUp 0.6s ease-out 0.1s both;
    }

    .controls-section {
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }

    .mobile-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .mobile-card:nth-child(2n) {
        animation-delay: 0.1s;
    }

    .mobile-card:nth-child(2n+1) {
        animation-delay: 0.2s;
    }

    /* Additional Card Enhancements */
    .card-main-content > * {
        animation: slideInRight 0.4s ease-out;
    }

    .card-main-content > *:nth-child(2) {
        animation-delay: 0.1s;
    }

    .card-main-content > *:nth-child(3) {
        animation-delay: 0.2s;
    }

    .card-main-content > *:nth-child(4) {
        animation-delay: 0.3s;
    }

    .stat-item:hover {
        animation: pulse 0.6s ease-in-out;
    }

    /* تحسينات الحركة */
    @@media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }

        .mobile-card:hover,
        .preview-card:hover,
        .controls-card:hover,
        .primary-action-btn:hover,
        .secondary-action-btn:hover,
        .empty-state-action-btn:hover {
            transform: none;
        }
    }

    /* Smooth Scrolling */
    .sider-aboutus-container {
        scroll-behavior: smooth;
    }

    /* Performance Optimizations */
    .enhanced-carousel,
    .slide-image,
    .mobile-card,
    .preview-card,
    .controls-card {
        will-change: transform;
    }
</style>
