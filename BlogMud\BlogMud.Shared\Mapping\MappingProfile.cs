using AutoMapper;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;

// Alias to avoid namespace conflict
using ClientModel = BlogMud.Shared.Models.Client;

namespace BlogMud.Shared.Mapping
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // Article mappings
            CreateMap<Article, ArticleDto>()
                .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => src.Category != null ? src.Category.Name : null))
                .ForMember(dest => dest.ClientName, opt => opt.MapFrom(src => src.Client != null ? src.Client.Name : null));

            CreateMap<ArticleDto, Article>()
                .ForMember(dest => dest.Category, opt => opt.Ignore())
                .ForMember(dest => dest.Client, opt => opt.Ignore());

            // Category mappings
            CreateMap<Category, CategoryDto>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());

            // Client mappings
            CreateMap<ClientModel, ClientDto>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());

            // Branch mappings
            CreateMap<Branch, BranchDto>().ReverseMap();

            // ContactMessage mappings
            CreateMap<ContactMessage, ContactMessageDto>().ReverseMap();

            // CompanyInfo mappings
            CreateMap<CompanyInfo, CompanyInfoDto>().ReverseMap();

            // AnimatedGif mappings
            CreateMap<AnimatedGif, AnimatedGifDto>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());

            // Service mappings
            CreateMap<Service, ServiceDto>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());

            // SidertMoveServices mappings
            CreateMap<SidertMoveServices, SidertMoveServicesDto>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());

            // NewsSiderMove mappings
            CreateMap<NewsSiderMove, NewsSiderMoveDto>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());

            // ProductionCapacityItem mappings (يجب أن تكون قبل AboutUs لتجنب مشاكل التبعية)
            CreateMap<ProductionCapacityItem, ProductionCapacityItemDto>();
            CreateMap<ProductionCapacityItemDto, ProductionCapacityItem>()
                .ForMember(dest => dest.AboutUs, opt => opt.Ignore()); // تجنب المراجع الدائرية

            // AboutUs mappings
            CreateMap<AboutUs, AboutUsDto>()
                .ForMember(dest => dest.ProductionCapacityItems, opt => opt.MapFrom(src => src.ProductionCapacityItems));
            CreateMap<AboutUsDto, AboutUs>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.ProductionCapacityItems, opt => opt.MapFrom(src => src.ProductionCapacityItems));

            // SiderAboutUs mappings
            CreateMap<SiderAboutUs, SiderAboutUsDto>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());
        }
    }
}
