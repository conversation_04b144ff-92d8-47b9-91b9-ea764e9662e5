using BlogMud.Shared.DTOs;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using MudBlazor;
using System.Net.Http.Json;

namespace BlogMud.Client.Pages.Admin.Posts.AnimatedImg;

public partial class AnimatedGifs : ComponentBase
{
    [Inject]
    private HttpClient Https { get; set; }

    [Inject]
    private ISnackbar Snackbars { get; set; }

    [Inject]
    private IDialogService DialogServices { get; set; }

    private bool arrows = true;
    private bool bullets = true;
    private bool enableSwipeGesture = true;
    private bool autocycle = true;
    private Transition transition = Transition.Slide;

    private List<AnimatedGifDto> _animatedGifs = new();
    private bool _loading = true;
    private string _searchString = "";
    private bool _showImageModal = false;
    private string _modalImageUrl = "";
    private string _modalImageTitle = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadAnimatedGifs();
    }

    private async Task LoadAnimatedGifs()
    {
        try
        {
            _loading = true;
            _animatedGifs = await Https.GetFromJsonAsync<List<AnimatedGifDto>>("api/AnimatedGifs") ?? new List<AnimatedGifDto>();
        }
        catch (Exception ex)
        {
            Snackbars.Add($"Error loading animated gifs: {ex.Message}", Severity.Error);
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task OpenAddDialog()
    {
        var parameters = new DialogParameters();
        var options = new DialogOptions
        {
            CloseOnEscapeKey = true,
            MaxWidth = MaxWidth.Medium,
            FullWidth = true,
            CloseButton = true
        };

        var dialog = await DialogServices.ShowAsync<AddAnimatedGifDialog>(
            "إضافة صورة متحركة جديدة",
            parameters,
            options);

        var result = await dialog.Result;

        if (!result.Canceled && result.Data is AnimatedGifDto newGif)
        {
            await LoadAnimatedGifs(); // Refresh the list
        }
    }

    private async Task DeleteAnimatedGif(int id)
    {
        var parameters = new DialogParameters
        {
            { "ContentText", "Are you sure you want to delete this animated gif?" },
            { "ButtonText", "Delete" },
            { "Color", Color.Error }
        };

        var dialog = await DialogServices.ShowAsync<BlogMud.Client.Components.ConfirmationDialog>("Confirm Delete", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            try
            {
                var response = await Https.DeleteAsync($"api/AnimatedGifs/{id}");

                if (response.IsSuccessStatusCode)
                {
                    _animatedGifs.RemoveAll(g => g.Id == id);
                    Snackbars.Add("Animated gif deleted successfully", Severity.Success);
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    Snackbars.Add($"Error deleting animated gif: {error}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbars.Add($"Error deleting animated gif: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task OpenEditDialog(AnimatedGifDto animatedGif)
    {
        if (animatedGif == null || animatedGif.Id <= 0)
        {
            Snackbars.Add("خطأ: لم يتم العثور على بيانات العنصر", Severity.Error);
            return;
        }

        try
        {
            // Fetch fresh data from server
            var freshData = await Https.GetFromJsonAsync<AnimatedGifDto>($"api/AnimatedGifs/{animatedGif.Id}");

            if (freshData == null)
            {
                Snackbars.Add("خطأ: لم يتم العثور على بيانات العنصر", Severity.Error);
                return;
            }

            var parameters = new DialogParameters
            {
                ["AnimatedGif"] = freshData
            };

            var options = new DialogOptions
            {
                CloseOnEscapeKey = true,
                MaxWidth = MaxWidth.Medium,
                FullWidth = true,
                CloseButton = true
            };

            var dialog = await DialogServices.ShowAsync<AddAnimatedGifDialog>(
                "تعديل الصورة المتحركة",
                parameters,
                options);

            var result = await dialog.Result;

            if (!result.Canceled && result.Data is AnimatedGifDto updatedGif)
            {
                await LoadAnimatedGifs(); // Refresh the list
            }
        }
        catch (Exception ex)
        {
            Snackbars.Add($"خطأ في تحميل بيانات العنصر: {ex.Message}", Severity.Error);
        }
    }

    private bool FilterFunc(AnimatedGifDto animatedGif)
    {
        if (string.IsNullOrWhiteSpace(_searchString))
            return true;

        return animatedGif.Title?.Contains(_searchString, StringComparison.OrdinalIgnoreCase) == true ||
               animatedGif.Description?.Contains(_searchString, StringComparison.OrdinalIgnoreCase) == true;
    }

    private void ShowImageModal(string imageUrl, string title)
    {
        _modalImageUrl = imageUrl;
        _modalImageTitle = title;
        _showImageModal = true;
        StateHasChanged();
    }

    private void HideImageModal()
    {
        _showImageModal = false;
        _modalImageUrl = "";
        _modalImageTitle = "";
        StateHasChanged();
    }
}