using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using BlogMud.Shared.Repositories;

namespace BlogMud.Data.Repositories
{
    public class Repository<T> : IRepository<T> where T : class
    {
        private readonly ApplicationDbContext _db;
        private readonly DbSet<T> _dbSet;

        public Repository(ApplicationDbContext db)
        {
            _db = db;
            _dbSet = _db.Set<T>();
        }

        public void Add(T entity)
        {
            _dbSet.Add(entity);
        }

        public async Task AddAsync(T entity)
        {
            try
            {
                await _dbSet.AddAsync(entity);
            }
            catch (Exception ex)
            {
                // If the table doesn't exist, create it
                if (ex.Message.Contains("Invalid object name") && typeof(T).Name == "Category")
                {
                    // Create Categories table
                    await _db.Database.ExecuteSqlRawAsync(@"
                        IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Categories')
                        BEGIN
                            CREATE TABLE [dbo].[Categories] (
                                [Id] INT IDENTITY(1,1) NOT NULL,
                                [Name] NVARCHAR(100) NOT NULL,
                                [Description] NVARCHAR(500) NULL,
                                [ArticleCount] INT NOT NULL DEFAULT 0,
                                CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
                            );

                            -- Insert seed data
                            INSERT INTO [dbo].[Categories] ([Name], [Description], [ArticleCount])
                            VALUES
                                (N'أخبار', N'أخبار الشركة والفعاليات', 0),
                                (N'مقالات', N'مقالات متنوعة', 0),
                                (N'خدماتنا', N'خدمات الشركة', 0);
                        END
                    ");

                    // Try again
                    await _dbSet.AddAsync(entity);
                }
                else
                {
                    throw;
                }
            }
        }

        public void AddRange(IEnumerable<T> entities)
        {
            _dbSet.AddRange(entities);
        }

        public async Task AddRangeAsync(IEnumerable<T> entities)
        {
            await _dbSet.AddRangeAsync(entities);
        }

        public IEnumerable<T> GetAll(
            Expression<Func<T, bool>> filter = null,
            Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
            string includeProperties = "",
            bool tracked = true)
        {
            IQueryable<T> query = tracked ? _dbSet : _dbSet.AsNoTracking();

            if (filter != null)
            {
                query = query.Where(filter);
            }

            foreach (var includeProperty in includeProperties.Split(
                new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                query = query.Include(includeProperty);
            }

            if (orderBy != null)
            {
                return orderBy(query).ToList();
            }
            return query.ToList();
        }

        public async Task<IEnumerable<T>> GetAllAsync(
            Expression<Func<T, bool>> filter = null,
            Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
            string includeProperties = "",
            bool tracked = true)
        {
            try
            {
                IQueryable<T> query = tracked ? _dbSet : _dbSet.AsNoTracking();

                if (filter != null)
                {
                    query = query.Where(filter);
                }

                foreach (var includeProperty in includeProperties.Split(
                    new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
                {
                    query = query.Include(includeProperty);
                }

                if (orderBy != null)
                {
                    return await orderBy(query).ToListAsync();
                }
                return await query.ToListAsync();
            }
            catch (Exception ex)
            {
                // If the table doesn't exist, create it
                if (ex.Message.Contains("Invalid object name") && typeof(T).Name == "Category")
                {
                    // Create Categories table
                    await _db.Database.ExecuteSqlRawAsync(@"
                        IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Categories')
                        BEGIN
                            CREATE TABLE [dbo].[Categories] (
                                [Id] INT IDENTITY(1,1) NOT NULL,
                                [Name] NVARCHAR(100) NOT NULL,
                                [Description] NVARCHAR(500) NULL,
                                [ArticleCount] INT NOT NULL DEFAULT 0,
                                CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
                            );

                            -- Insert seed data
                            INSERT INTO [dbo].[Categories] ([Name], [Description], [ArticleCount])
                            VALUES
                                (N'أخبار', N'أخبار الشركة والفعاليات', 0),
                                (N'مقالات', N'مقالات متنوعة', 0),
                                (N'خدماتنا', N'خدمات الشركة', 0);
                        END
                    ");

                    // Try again
                    IQueryable<T> query = tracked ? _dbSet : _dbSet.AsNoTracking();

                    if (filter != null)
                    {
                        query = query.Where(filter);
                    }

                    foreach (var includeProperty in includeProperties.Split(
                        new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
                    {
                        query = query.Include(includeProperty);
                    }

                    if (orderBy != null)
                    {
                        return await orderBy(query).ToListAsync();
                    }
                    return await query.ToListAsync();
                }

                throw;
            }
        }

        public T GetById(object id)
        {
            return _dbSet.Find(id);
        }

        public async Task<T> GetByIdAsync(object id)
        {
            try
            {
                return await _dbSet.FindAsync(id);
            }
            catch (Exception ex)
            {
                // If the table doesn't exist, create it
                if (ex.Message.Contains("Invalid object name") && typeof(T).Name == "Category")
                {
                    // Create Categories table
                    await _db.Database.ExecuteSqlRawAsync(@"
                        IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Categories')
                        BEGIN
                            CREATE TABLE [dbo].[Categories] (
                                [Id] INT IDENTITY(1,1) NOT NULL,
                                [Name] NVARCHAR(100) NOT NULL,
                                [Description] NVARCHAR(500) NULL,
                                [ArticleCount] INT NOT NULL DEFAULT 0,
                                CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
                            );

                            -- Insert seed data
                            INSERT INTO [dbo].[Categories] ([Name], [Description], [ArticleCount])
                            VALUES
                                (N'أخبار', N'أخبار الشركة والفعاليات', 0),
                                (N'مقالات', N'مقالات متنوعة', 0),
                                (N'خدماتنا', N'خدمات الشركة', 0);
                        END
                    ");

                    // Try again
                    return await _dbSet.FindAsync(id);
                }

                throw;
            }
        }

        public T GetFirstOrDefault(
            Expression<Func<T, bool>> filter = null,
            string includeProperties = "",
            bool tracked = true)
        {
            IQueryable<T> query = tracked ? _dbSet : _dbSet.AsNoTracking();

            if (filter != null)
            {
                query = query.Where(filter);
            }

            foreach (var includeProperty in includeProperties.Split(
                new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                query = query.Include(includeProperty);
            }

            return query.FirstOrDefault();
        }

        public async Task<T> GetFirstOrDefaultAsync(
            Expression<Func<T, bool>> filter = null,
            string includeProperties = "",
            bool tracked = true)
        {
            IQueryable<T> query = tracked ? _dbSet : _dbSet.AsNoTracking();

            if (filter != null)
            {
                query = query.Where(filter);
            }

            foreach (var includeProperty in includeProperties.Split(
                new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                query = query.Include(includeProperty);
            }

            return await query.FirstOrDefaultAsync();
        }

        public void Remove(T entity)
        {
            _dbSet.Remove(entity);
        }

        public void RemoveRange(IEnumerable<T> entities)
        {
            _dbSet.RemoveRange(entities);
        }

        public void Update(T entity)
        {
            try
            {
                // Detach any existing entity with the same key to avoid tracking conflicts
                var keyValues = _db.Model.FindEntityType(typeof(T)).FindPrimaryKey().Properties
                    .Select(p => p.PropertyInfo.GetValue(entity)).ToArray();
                var existingEntity = _dbSet.Find(keyValues);

                if (existingEntity != null)
                {
                    _db.Entry(existingEntity).State = EntityState.Detached;
                }

                // Attach the entity and mark it as modified
                _dbSet.Attach(entity);
                _db.Entry(entity).State = EntityState.Modified;
            }
            catch (Exception ex)
            {
                // If the table doesn't exist, create it
                if (ex.Message.Contains("Invalid object name") && typeof(T).Name == "Category")
                {
                    // Create Categories table
                    _db.Database.ExecuteSqlRaw(@"
                        IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Categories')
                        BEGIN
                            CREATE TABLE [dbo].[Categories] (
                                [Id] INT IDENTITY(1,1) NOT NULL,
                                [Name] NVARCHAR(100) NOT NULL,
                                [Description] NVARCHAR(500) NULL,
                                [ArticleCount] INT NOT NULL DEFAULT 0,
                                CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
                            );

                            -- Insert seed data
                            INSERT INTO [dbo].[Categories] ([Name], [Description], [ArticleCount])
                            VALUES
                                (N'أخبار', N'أخبار الشركة والفعاليات', 0),
                                (N'مقالات', N'مقالات متنوعة', 0),
                                (N'خدماتنا', N'خدمات الشركة', 0);
                        END
                    ");

                    // Try again with the new approach
                    _db.Entry(entity).State = EntityState.Modified;
                }
                else
                {
                    throw;
                }
            }
        }
    }
}
