using System;

namespace BlogMud.Shared.DTOs
{
    /// <summary>
    /// كائن نقل البيانات للخدمات
    /// يستخدم لنقل بيانات الخدمات بين واجهة المستخدم والخادم
    /// </summary>
    public class ServiceDto
    {
        public int Id { get; set; }

        /// <summary>
        /// عنوان الخدمة
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// الوصف الأساسي للخدمة
        /// </summary>
        public string PrimaryDescription { get; set; }

        /// <summary>
        /// الوصف التفصيلي للخدمة
        /// </summary>
        public string? DetailedDescription { get; set; }

        /// <summary>
        /// الصورة الرئيسية للخدمة
        /// </summary>
        public string MainImageUrl { get; set; }

        /// <summary>
        /// شرائح الصور للخدمة (مفصولة بفواصل)
        /// </summary>
        public string? ImageCarousel { get; set; }

        /// <summary>
        /// رابط الفيديو الاختياري للخدمة
        /// </summary>
        public string? VideoUrl { get; set; }

        /// <summary>
        /// مميزات الخدمة (مفصولة بفواصل منقوطة)
        /// </summary>
        public string? Features { get; set; }

        /// <summary>
        /// ترتيب عرض الخدمة
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// حالة الخدمة (نشطة أو غير نشطة)
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ إنشاء الخدمة
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل للخدمة
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }
    }
}
