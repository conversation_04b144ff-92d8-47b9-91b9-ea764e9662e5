using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.IO;
using System.Threading.Tasks;

namespace BlogMud.Controllers
{
    /// <summary>
    /// وحدة تحكم واجهة برمجة التطبيقات لإدارة رفع الملفات
    /// </summary>
    /// <remarks>
    /// توفر هذه الوحدة واجهات برمجة تطبيقات RESTful للتعامل مع رفع أنواع مختلفة من الملفات
    /// مثل الصور وملفات الفيديو وصور عرض الشرائح
    /// تتطلب جميع العمليات صلاحيات المسؤول (Admin)
    /// </remarks>
    [Route("api/[controller]")]
    [ApiController]
    public class UploadController : ControllerBase
    {
        #region المتغيرات والمنشئ

        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<UploadController> _logger;
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// منشئ وحدة تحكم رفع الملفات
        /// </summary>
        /// <param name="environment">بيئة استضافة الويب للوصول إلى مسارات الملفات</param>
        /// <param name="logger">خدمة تسجيل الأحداث</param>
        /// <remarks>
        /// يقوم بتهيئة الخدمات اللازمة لإدارة رفع الملفات والوصول إلى مسارات الملفات
        /// </remarks>
        public UploadController(IWebHostEnvironment environment, ILogger<UploadController> logger, IServiceProvider serviceProvider)
        {
            _environment = environment;
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        #endregion

        #region عمليات رفع الملفات

        /// <summary>
        /// رفع ملف صورة
        /// </summary>
        /// <returns>معلومات الصورة التي تم رفعها بما في ذلك المسار المخزن واسم الملف الأصلي</returns>
        /// <response code="200">تم رفع الصورة بنجاح</response>
        /// <response code="400">لم يتم تقديم ملف أو تم تقديم نوع ملف غير صالح</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// وتقوم بالتحقق من نوع الملف المرفوع (يسمح فقط بملفات JPG و JPEG و PNG و GIF)
        /// ثم تقوم بإنشاء اسم فريد للملف وحفظه في مجلد "imeg" ضمن مجلد wwwroot
        /// </remarks>
        [HttpPost("image")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadImage()
        {
            try
            {
                var file = Request.Form.Files[0];
                if (file == null || file.Length == 0)
                    return BadRequest("No file was uploaded.");

                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                    return BadRequest("Invalid file type. Only JPG, JPEG, PNG, and GIF files are allowed.");

                // حذف الصورة القديمة إذا تم تقديم مسارها
                if (Request.Form.ContainsKey("oldImageUrl"))
                {
                    string oldImageUrl = Request.Form["oldImageUrl"];
                    if (!string.IsNullOrEmpty(oldImageUrl) && oldImageUrl.Contains("/imeg/"))
                    {
                        try
                        {
                            DeleteFileIfExists(oldImageUrl, "imeg");
                            _logger.LogInformation("Deleted old image: {OldImageUrl}", oldImageUrl);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error deleting old image: {OldImageUrl}", oldImageUrl);
                            // نستمر في تحميل الصورة الجديدة حتى لو فشل حذف الصورة القديمة
                        }
                    }
                }

                // Create a unique file name
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine(_environment.WebRootPath, "imeg");

                // Ensure directory exists
                if (!Directory.Exists(uploadPath))
                    Directory.CreateDirectory(uploadPath);

                var filePath = Path.Combine(uploadPath, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation("Image uploaded successfully: {FileName}", fileName);

                // Return the file path
                return Ok(new
                {
                    StoredFileName = $"/imeg/{fileName}",
                    OriginalFileName = file.FileName,
                    Successful = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading image");
                return StatusCode(500, new { ErrorMessage = $"Internal server error: {ex.Message}", Successful = false });
            }
        }

        /// <summary>
        /// رفع صورة لعرض الشرائح
        /// </summary>
        /// <returns>معلومات صورة عرض الشرائح التي تم رفعها بما في ذلك المسار المخزن واسم الملف الأصلي</returns>
        /// <response code="200">تم رفع صورة عرض الشرائح بنجاح</response>
        /// <response code="400">لم يتم تقديم ملف أو تم تقديم نوع ملف غير صالح</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// وتقوم بالتحقق من نوع الملف المرفوع (يسمح فقط بملفات JPG و JPEG و PNG و GIF)
        /// يمكن تقديم معرف المقال (articleId) أو معرف الجلسة (sessionId) كجزء من النموذج
        /// لإضافة بادئة إلى اسم الملف المخزن، مما يسمح بتجميع الصور حسب المقال أو الجلسة
        /// يتم حفظ الملف في مجلد "Sider" ضمن مجلد wwwroot
        /// </remarks>
        [HttpPost("slider")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadSliderImage()
        {
            try
            {
                var file = Request.Form.Files[0];
                if (file == null || file.Length == 0)
                    return BadRequest("No file was uploaded.");

                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                    return BadRequest("Invalid file type. Only JPG, JPEG, PNG, and GIF files are allowed.");

                // حذف الصورة القديمة إذا تم تقديم مسارها
                if (Request.Form.ContainsKey("oldImageUrl"))
                {
                    string oldImageUrl = Request.Form["oldImageUrl"];
                    if (!string.IsNullOrEmpty(oldImageUrl) && oldImageUrl.Contains("/Sider/"))
                    {
                        try
                        {
                            DeleteFileIfExists(oldImageUrl, "Sider");
                            _logger.LogInformation("Deleted old slider image: {OldImageUrl}", oldImageUrl);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error deleting old slider image: {OldImageUrl}", oldImageUrl);
                            // نستمر في تحميل الصورة الجديدة حتى لو فشل حذف الصورة القديمة
                        }
                    }
                }

                // Get article ID from request if available
                string articlePrefix = "";
                if (Request.Form.ContainsKey("articleId"))
                {
                    string articleId = Request.Form["articleId"];
                    if (!string.IsNullOrEmpty(articleId))
                    {
                        articlePrefix = $"article_{articleId}_";
                    }
                }
                else
                {
                    // If no article ID is provided, use a session ID to group uploads
                    if (Request.Form.ContainsKey("sessionId"))
                    {
                        string sessionId = Request.Form["sessionId"];
                        if (!string.IsNullOrEmpty(sessionId))
                        {
                            articlePrefix = $"session_{sessionId}_";
                        }
                    }
                }

                // Create a unique file name with prefix
                var fileName = $"{articlePrefix}{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine(_environment.WebRootPath, "Sider");

                // Ensure directory exists
                if (!Directory.Exists(uploadPath))
                    Directory.CreateDirectory(uploadPath);

                var filePath = Path.Combine(uploadPath, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation("Slider image uploaded successfully: {FileName}", fileName);

                // Return the file path
                return Ok(new
                {
                    StoredFileName = $"/Sider/{fileName}",
                    OriginalFileName = file.FileName,
                    Successful = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading slider image");
                return StatusCode(500, new { ErrorMessage = $"Internal server error: {ex.Message}", Successful = false });
            }
        }

        /// <summary>
        /// تحديث صور الجلسة وربطها بمقال محدد
        /// </summary>
        /// <param name="sessionId">معرف الجلسة المؤقتة التي تم رفع الصور بها</param>
        /// <param name="articleId">معرف المقال الذي سيتم ربط الصور به</param>
        /// <returns>عدد الصور التي تم تحديثها</returns>
        /// <response code="200">تم تحديث صور الجلسة بنجاح</response>
        /// <response code="400">معرف الجلسة أو معرف المقال غير صالح أو غير موجود</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="404">مجلد صور عرض الشرائح غير موجود</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// تقوم هذه الطريقة بتحديث أسماء ملفات الصور التي تم رفعها مسبقًا باستخدام معرف جلسة مؤقت
        /// وتغيير البادئة من "session_{sessionId}_" إلى "article_{articleId}_"
        /// مما يسمح بربط الصور المؤقتة بمقال محدد بعد حفظ المقال
        /// </remarks>
        [HttpPost("updateSessionImages")]
        [Authorize(Roles = "Admin")]
        public IActionResult UpdateSessionImages([FromQuery] string sessionId, [FromQuery] int articleId)
        {
            try
            {
                if (string.IsNullOrEmpty(sessionId) || articleId <= 0)
                {
                    return BadRequest("Session ID and Article ID are required");
                }

                var uploadPath = Path.Combine(_environment.WebRootPath, "Sider");
                if (!Directory.Exists(uploadPath))
                {
                    return NotFound("Slider directory not found");
                }

                // Get all files in the Sider directory
                string[] files = Directory.GetFiles(uploadPath);
                int updatedCount = 0;

                // Session prefix pattern
                string sessionPrefix = $"session_{sessionId}_";

                foreach (string file in files)
                {
                    string fileName = Path.GetFileName(file);

                    // Check if the file belongs to this session
                    if (fileName.StartsWith(sessionPrefix))
                    {
                        try
                        {
                            // Create new filename with article prefix
                            string newFileName = fileName.Replace(sessionPrefix, $"article_{articleId}_");
                            string newFilePath = Path.Combine(uploadPath, newFileName);

                            // Rename the file
                            System.IO.File.Move(file, newFilePath);
                            updatedCount++;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error renaming file: {FilePath}", file);
                        }
                    }
                }

                _logger.LogInformation("Updated {Count} carousel images for article ID: {ArticleId}", updatedCount, articleId);
                return Ok(new { UpdatedCount = updatedCount });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating session images");
                return StatusCode(500, new { ErrorMessage = $"Internal server error: {ex.Message}", Successful = false });
            }
        }

        /// <summary>
        /// رفع ملف فيديو
        /// </summary>
        /// <returns>معلومات الفيديو الذي تم رفعه بما في ذلك المسار المخزن واسم الملف الأصلي</returns>
        /// <response code="200">تم رفع الفيديو بنجاح</response>
        /// <response code="400">لم يتم تقديم ملف أو تم تقديم نوع ملف غير صالح</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// وتقوم بالتحقق من نوع الملف المرفوع (يسمح فقط بملفات MP4 و AVI و MOV و WMV)
        /// ثم تقوم بإنشاء اسم فريد للملف وحفظه في مجلد "Video" ضمن مجلد wwwroot
        /// </remarks>
        [HttpPost("video")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadVideo()
        {
            try
            {
                var file = Request.Form.Files[0];
                if (file == null || file.Length == 0)
                    return BadRequest("No file was uploaded.");

                // Validate file type
                var allowedExtensions = new[] { ".mp4", ".avi", ".mov", ".wmv" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                    return BadRequest("Invalid file type. Only MP4, AVI, MOV, and WMV files are allowed.");

                // حذف الفيديو القديم إذا تم تقديم مساره
                if (Request.Form.ContainsKey("oldVideoUrl"))
                {
                    string oldVideoUrl = Request.Form["oldVideoUrl"];
                    if (!string.IsNullOrEmpty(oldVideoUrl) && oldVideoUrl.Contains("/Video/"))
                    {
                        try
                        {
                            DeleteFileIfExists(oldVideoUrl, "Video");
                            _logger.LogInformation("Deleted old video: {OldVideoUrl}", oldVideoUrl);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error deleting old video: {OldVideoUrl}", oldVideoUrl);
                            // نستمر في تحميل الفيديو الجديد حتى لو فشل حذف الفيديو القديم
                        }
                    }
                }

                // Create a unique file name
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine(_environment.WebRootPath, "Video");

                // Ensure directory exists
                if (!Directory.Exists(uploadPath))
                    Directory.CreateDirectory(uploadPath);

                var filePath = Path.Combine(uploadPath, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation("Video uploaded successfully: {FileName}", fileName);

                // Return the file path
                return Ok(new
                {
                    StoredFileName = $"/Video/{fileName}",
                    OriginalFileName = file.FileName,
                    Successful = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading video");
                return StatusCode(500, new { ErrorMessage = $"Internal server error: {ex.Message}", Successful = false });
            }
        }

        /// <summary>
        /// رفع صور متعددة لعرض الشرائح المتحركة
        /// </summary>
        /// <returns>مسار الصورة التي تم رفعها</returns>
        /// <response code="200">تم رفع الصورة بنجاح</response>
        /// <response code="400">لم يتم تقديم ملف أو تم تقديم نوع ملف غير صالح</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// وتقوم بالتحقق من نوع الملف المرفوع (يسمح فقط بملفات JPG و JPEG و PNG و GIF و WEBP)
        /// ثم تقوم بإنشاء اسم فريد للملف وحفظه في مجلد "OurServImg/SiderMoveOurServicesImg" ضمن مجلد wwwroot
        /// </remarks>
        [HttpPost("sidermove-slideshow")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadSiderMoveSlideshow()
        {
            try
            {
                var file = Request.Form.Files[0];
                if (file == null || file.Length == 0)
                    return BadRequest("No file was uploaded.");

                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                    return BadRequest("Invalid file type. Only JPG, JPEG, PNG, GIF, and WEBP files are allowed.");

                // Create a unique file name
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine(_environment.WebRootPath, "OurServImg", "SiderMoveOurServicesImg");

                // Ensure directory exists
                if (!Directory.Exists(uploadPath))
                    Directory.CreateDirectory(uploadPath);

                var filePath = Path.Combine(uploadPath, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation("SiderMove slideshow image uploaded successfully: {FileName}", fileName);

                // Return the file path
                return Ok($"/OurServImg/SiderMoveOurServicesImg/{fileName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading SiderMove slideshow image");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// رفع صور للشرائح المتحركة للأخبار
        /// </summary>
        /// <returns>مسار الصورة التي تم رفعها</returns>
        /// <response code="200">تم رفع الصورة بنجاح</response>
        /// <response code="400">لم يتم تقديم ملف أو تم تقديم نوع ملف غير صالح</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// وتقوم بالتحقق من نوع الملف المرفوع (يسمح فقط بملفات JPG و JPEG و PNG و GIF و WEBP)
        /// ثم تقوم بإنشاء اسم فريد للملف وحفظه في مجلد "NewsSiderMoveImg" ضمن مجلد wwwroot
        /// </remarks>
        [HttpPost("news-slideshow")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadNewsSlideshowImage()
        {
            try
            {
                var file = Request.Form.Files[0];
                if (file == null || file.Length == 0)
                    return BadRequest("No file was uploaded.");

                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                    return BadRequest("Invalid file type. Only JPG, JPEG, PNG, GIF, and WEBP files are allowed.");

                // Create a unique file name
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine(_environment.WebRootPath, "NewsSiderMoveImg");

                // Ensure directory exists
                if (!Directory.Exists(uploadPath))
                    Directory.CreateDirectory(uploadPath);

                var filePath = Path.Combine(uploadPath, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation("News slideshow image uploaded successfully: {FileName}", fileName);

                // Return the file path
                return Ok($"/NewsSiderMoveImg/{fileName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading news slideshow image");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// رفع صورة متحركة
        /// </summary>
        /// <returns>مسار الصورة المتحركة التي تم رفعها</returns>
        /// <response code="200">تم رفع الصورة المتحركة بنجاح</response>
        /// <response code="400">لم يتم تقديم ملف أو تم تقديم نوع ملف غير صالح</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// وتقوم بالتحقق من نوع الملف المرفوع (يسمح فقط بملفات JPG و JPEG و PNG و GIF)
        /// ثم تقوم بإنشاء اسم فريد للملف وحفظه في مجلد "AnimatedGifsSider" ضمن مجلد wwwroot
        /// </remarks>
        [HttpPost("animatedgif")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadAnimatedGif()
        {
            try
            {
                var file = Request.Form.Files[0];
                if (file == null || file.Length == 0)
                    return BadRequest("No file was uploaded.");

                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                    return BadRequest("Invalid file type. Only JPG, JPEG, PNG, and GIF files are allowed.");

                // حذف الصورة القديمة إذا تم تقديم مسارها
                if (Request.Form.ContainsKey("oldImageUrl"))
                {
                    string oldImageUrl = Request.Form["oldImageUrl"];
                    if (!string.IsNullOrEmpty(oldImageUrl) && oldImageUrl.Contains("/AnimatedGifsSider/"))
                    {
                        try
                        {
                            DeleteFileIfExists(oldImageUrl, "AnimatedGifsSider");
                            _logger.LogInformation("Deleted old animated gif: {OldImageUrl}", oldImageUrl);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error deleting old animated gif: {OldImageUrl}", oldImageUrl);
                            // نستمر في تحميل الصورة الجديدة حتى لو فشل حذف الصورة القديمة
                        }
                    }
                }

                // Create a unique file name
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine(_environment.WebRootPath, "AnimatedGifsSider");

                // Ensure directory exists
                if (!Directory.Exists(uploadPath))
                    Directory.CreateDirectory(uploadPath);

                var filePath = Path.Combine(uploadPath, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation("Animated gif uploaded successfully: {FileName}", fileName);

                // Return the file path
                return Ok($"/AnimatedGifsSider/{fileName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading animated gif");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// رفع صورة رئيسية للخدمة
        /// </summary>
        /// <returns>مسار الملف المرفوع</returns>
        /// <response code="200">تم رفع الصورة بنجاح</response>
        /// <response code="400">خطأ في البيانات المرسلة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="500">خطأ داخلي في الخادم</response>
        [HttpPost("service/image")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadServiceImage()
        {
            try
            {
                var file = Request.Form.Files[0];
                if (file == null || file.Length == 0)
                    return BadRequest("No file was uploaded.");

                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                    return BadRequest("Invalid file type. Only JPG, JPEG, PNG, and GIF files are allowed.");

                // حذف الصورة القديمة إذا تم تقديم مسارها
                if (Request.Form.ContainsKey("oldImageUrl"))
                {
                    string oldImageUrl = Request.Form["oldImageUrl"];
                    if (!string.IsNullOrEmpty(oldImageUrl) && oldImageUrl.Contains("/OurServImg/img/"))
                    {
                        try
                        {
                            DeleteFileIfExists(oldImageUrl, "OurServImg/img");
                            _logger.LogInformation("Deleted old service image: {OldImageUrl}", oldImageUrl);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error deleting old service image: {OldImageUrl}", oldImageUrl);
                        }
                    }
                }

                // Create a unique file name
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine(_environment.WebRootPath, "OurServImg", "img");

                // Ensure directory exists
                if (!Directory.Exists(uploadPath))
                    Directory.CreateDirectory(uploadPath);

                var filePath = Path.Combine(uploadPath, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation("Service image uploaded successfully: {FileName}", fileName);

                // Return the file path
                return Ok($"/OurServImg/img/{fileName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading service image");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// رفع صور دوار للخدمة
        /// </summary>
        /// <returns>مسار الملف المرفوع</returns>
        /// <response code="200">تم رفع الصورة بنجاح</response>
        /// <response code="400">خطأ في البيانات المرسلة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="500">خطأ داخلي في الخادم</response>
        [HttpPost("service/slider")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadServiceSlider()
        {
            try
            {
                var file = Request.Form.Files[0];
                if (file == null || file.Length == 0)
                    return BadRequest("No file was uploaded.");

                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                    return BadRequest("Invalid file type. Only JPG, JPEG, PNG, and GIF files are allowed.");

                // Create a unique file name
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine(_environment.WebRootPath, "OurServImg", "Sider");

                // Ensure directory exists
                if (!Directory.Exists(uploadPath))
                    Directory.CreateDirectory(uploadPath);

                var filePath = Path.Combine(uploadPath, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation("Service slider image uploaded successfully: {FileName}", fileName);

                // Return the file path
                return Ok($"/OurServImg/Sider/{fileName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading service slider image");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// رفع فيديو للخدمة
        /// </summary>
        /// <returns>مسار الملف المرفوع</returns>
        /// <response code="200">تم رفع الفيديو بنجاح</response>
        /// <response code="400">خطأ في البيانات المرسلة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="500">خطأ داخلي في الخادم</response>
        [HttpPost("service/video")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadServiceVideo()
        {
            try
            {
                var file = Request.Form.Files[0];
                if (file == null || file.Length == 0)
                    return BadRequest("No file was uploaded.");

                // Validate file type
                var allowedExtensions = new[] { ".mp4", ".avi", ".mov", ".wmv", ".webm" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                    return BadRequest("Invalid file type. Only MP4, AVI, MOV, WMV, and WEBM files are allowed.");

                // حذف الفيديو القديم إذا تم تقديم مساره
                if (Request.Form.ContainsKey("oldVideoUrl"))
                {
                    string oldVideoUrl = Request.Form["oldVideoUrl"];
                    if (!string.IsNullOrEmpty(oldVideoUrl) && oldVideoUrl.Contains("/OurServImg/viedo/"))
                    {
                        try
                        {
                            DeleteFileIfExists(oldVideoUrl, "OurServImg/viedo");
                            _logger.LogInformation("Deleted old service video: {OldVideoUrl}", oldVideoUrl);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error deleting old service video: {OldVideoUrl}", oldVideoUrl);
                        }
                    }
                }

                // Create a unique file name
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine(_environment.WebRootPath, "OurServImg", "viedo");

                // Ensure directory exists
                if (!Directory.Exists(uploadPath))
                    Directory.CreateDirectory(uploadPath);

                var filePath = Path.Combine(uploadPath, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation("Service video uploaded successfully: {FileName}", fileName);

                // Return the file path
                return Ok($"/OurServImg/viedo/{fileName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading service video");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// تنظيف الملفات غير المستخدمة في مجلدات الخدمات
        /// </summary>
        /// <returns>عدد الملفات المحذوفة</returns>
        /// <response code="200">تم تنظيف الملفات بنجاح</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="500">خطأ داخلي في الخادم</response>
        [HttpPost("cleanup/services")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> CleanupUnusedServiceFiles()
        {
            try
            {
                _logger.LogInformation("Starting cleanup of unused service files");

                int deletedCount = 0;

                // الحصول على جميع الخدمات من قاعدة البيانات
                using var scope = _serviceProvider.CreateScope();
                var unitOfWork = scope.ServiceProvider.GetRequiredService<BlogMud.Shared.Repositories.IUnitOfWork>();
                var serviceRepository = unitOfWork.Repository<BlogMud.Shared.Models.Service>();
                var allServices = await serviceRepository.GetAllAsync();

                // جمع جميع الملفات المستخدمة
                var usedFiles = new HashSet<string>();

                foreach (var service in allServices)
                {
                    // إضافة الصورة الرئيسية
                    if (!string.IsNullOrEmpty(service.MainImageUrl))
                    {
                        usedFiles.Add(Path.GetFileName(service.MainImageUrl));
                    }

                    // إضافة صور الدوار
                    if (!string.IsNullOrEmpty(service.ImageCarousel))
                    {
                        var carouselImages = service.ImageCarousel.Split(';', StringSplitOptions.RemoveEmptyEntries);
                        foreach (var imageUrl in carouselImages)
                        {
                            usedFiles.Add(Path.GetFileName(imageUrl.Trim()));
                        }
                    }

                    // إضافة الفيديو
                    if (!string.IsNullOrEmpty(service.VideoUrl))
                    {
                        usedFiles.Add(Path.GetFileName(service.VideoUrl));
                    }
                }

                // تنظيف مجلد الصور الرئيسية
                var imgPath = Path.Combine(_environment.WebRootPath, "OurServImg", "img");
                if (Directory.Exists(imgPath))
                {
                    var imgFiles = Directory.GetFiles(imgPath);
                    foreach (var filePath in imgFiles)
                    {
                        var fileName = Path.GetFileName(filePath);
                        if (!usedFiles.Contains(fileName))
                        {
                            System.IO.File.Delete(filePath);
                            deletedCount++;
                            _logger.LogInformation("Deleted unused image file: {FileName}", fileName);
                        }
                    }
                }

                // تنظيف مجلد صور الدوار
                var siderPath = Path.Combine(_environment.WebRootPath, "OurServImg", "Sider");
                if (Directory.Exists(siderPath))
                {
                    var siderFiles = Directory.GetFiles(siderPath);
                    foreach (var filePath in siderFiles)
                    {
                        var fileName = Path.GetFileName(filePath);
                        if (!usedFiles.Contains(fileName))
                        {
                            System.IO.File.Delete(filePath);
                            deletedCount++;
                            _logger.LogInformation("Deleted unused slider file: {FileName}", fileName);
                        }
                    }
                }

                // تنظيف مجلد الفيديوهات
                var videoPath = Path.Combine(_environment.WebRootPath, "OurServImg", "viedo");
                if (Directory.Exists(videoPath))
                {
                    var videoFiles = Directory.GetFiles(videoPath);
                    foreach (var filePath in videoFiles)
                    {
                        var fileName = Path.GetFileName(filePath);
                        if (!usedFiles.Contains(fileName))
                        {
                            System.IO.File.Delete(filePath);
                            deletedCount++;
                            _logger.LogInformation("Deleted unused video file: {FileName}", fileName);
                        }
                    }
                }

                _logger.LogInformation("Cleanup completed. Deleted {DeletedCount} unused files", deletedCount);
                return Ok(new { DeletedCount = deletedCount, Message = $"تم حذف {deletedCount} ملف غير مستخدم" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during service files cleanup");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// رفع شعار الشركة لصفحة "من نحن"
        /// </summary>
        /// <returns>مسار الملف المرفوع</returns>
        /// <response code="200">تم رفع الشعار بنجاح</response>
        /// <response code="400">خطأ في البيانات المرسلة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="500">خطأ داخلي في الخادم</response>
        [HttpPost("aboutus/logo")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadAboutUsLogo()
        {
            try
            {
                var file = Request.Form.Files[0];
                if (file == null || file.Length == 0)
                    return BadRequest("No file was uploaded.");

                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".svg" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                    return BadRequest("Invalid file type. Only JPG, JPEG, PNG, GIF, and SVG files are allowed.");

                // حذف الشعار القديم إذا تم تقديم مساره
                if (Request.Form.ContainsKey("oldImageUrl"))
                {
                    string oldImageUrl = Request.Form["oldImageUrl"];
                    if (!string.IsNullOrEmpty(oldImageUrl) && oldImageUrl.Contains("/AboutUsImg/"))
                    {
                        try
                        {
                            DeleteFileIfExists(oldImageUrl, "AboutUsImg");
                            _logger.LogInformation("Deleted old AboutUs logo: {OldImageUrl}", oldImageUrl);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error deleting old AboutUs logo: {OldImageUrl}", oldImageUrl);
                        }
                    }
                }

                // Create a unique file name
                var fileName = $"logo_{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine(_environment.WebRootPath, "AboutUsImg");

                // Ensure directory exists
                if (!Directory.Exists(uploadPath))
                    Directory.CreateDirectory(uploadPath);

                var filePath = Path.Combine(uploadPath, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation("AboutUs logo uploaded successfully: {FileName}", fileName);

                // Return the file path
                return Ok($"/AboutUsImg/{fileName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading AboutUs logo");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// رفع صورة عنصر الطاقة الإنتاجية لصفحة "من نحن"
        /// </summary>
        /// <returns>مسار الملف المرفوع</returns>
        /// <response code="200">تم رفع الصورة بنجاح</response>
        /// <response code="400">خطأ في البيانات المرسلة</response>
        /// <response code="401">المستخدم غير مصرح له</response>
        /// <response code="500">خطأ داخلي في الخادم</response>
        [HttpPost("aboutus/production-capacity-item")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadProductionCapacityItemImage()
        {
            try
            {
                var file = Request.Form.Files[0];
                if (file == null || file.Length == 0)
                    return BadRequest("No file was uploaded.");

                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                    return BadRequest("Invalid file type. Only JPG, JPEG, PNG, and GIF files are allowed.");

                // حذف الصورة القديمة إذا تم تقديم مسارها
                if (Request.Form.ContainsKey("oldImageUrl"))
                {
                    string oldImageUrl = Request.Form["oldImageUrl"];
                    if (!string.IsNullOrEmpty(oldImageUrl) && oldImageUrl.Contains("/AboutUsImg/ProductionCapacity/"))
                    {
                        try
                        {
                            DeleteFileIfExists(oldImageUrl, "AboutUsImg/ProductionCapacity");
                            _logger.LogInformation("Deleted old production capacity item image: {OldImageUrl}", oldImageUrl);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error deleting old production capacity item image: {OldImageUrl}", oldImageUrl);
                        }
                    }
                }

                // Create a unique file name
                var fileName = $"production_item_{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine(_environment.WebRootPath, "AboutUsImg", "ProductionCapacity");

                // Ensure directory exists
                if (!Directory.Exists(uploadPath))
                    Directory.CreateDirectory(uploadPath);

                var filePath = Path.Combine(uploadPath, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation("Production capacity item image uploaded successfully: {FileName}", fileName);

                // Return the file path
                return Ok($"/AboutUsImg/ProductionCapacity/{fileName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading production capacity item image");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// رفع صور متعددة لعرض الشرائح المتحركة لصفحة "من نحن"
        /// </summary>
        /// <returns>مسار الصورة التي تم رفعها</returns>
        /// <response code="200">تم رفع الصورة بنجاح</response>
        /// <response code="400">لم يتم تقديم ملف أو تم تقديم نوع ملف غير صالح</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// وتقوم بالتحقق من نوع الملف المرفوع (يسمح فقط بملفات JPG و JPEG و PNG و GIF و WEBP)
        /// ثم تقوم بإنشاء اسم فريد للملف وحفظه في مجلد "AboutUsImg/SiderAboutUsImg" ضمن مجلد wwwroot
        /// </remarks>
        [HttpPost("sider-aboutus-slideshow")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UploadSiderAboutUsSlideshow()
        {
            try
            {
                var file = Request.Form.Files[0];
                if (file == null || file.Length == 0)
                    return BadRequest("No file was uploaded.");

                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                    return BadRequest("Invalid file type. Only JPG, JPEG, PNG, GIF, and WEBP files are allowed.");

                // Create a unique file name
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var uploadPath = Path.Combine(_environment.WebRootPath, "AboutUsImg", "SiderAboutUsImg");

                // Ensure directory exists
                if (!Directory.Exists(uploadPath))
                    Directory.CreateDirectory(uploadPath);

                var filePath = Path.Combine(uploadPath, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation("SiderAboutUs slideshow image uploaded successfully: {FileName}", fileName);

                // Return the file path
                return Ok($"/AboutUsImg/SiderAboutUsImg/{fileName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading SiderAboutUs slideshow image");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #endregion

        #region دوال مساعدة

        /// <summary>
        /// حذف جميع صور الشرائح المرتبطة بمقالة معينة
        /// </summary>
        /// <param name="articleId">معرف المقالة المراد حذف صور الشرائح المرتبطة بها</param>
        /// <returns>عدد الصور التي تم حذفها</returns>
        /// <response code="200">تم حذف صور الشرائح بنجاح</response>
        /// <response code="401">المستخدم غير مصرح له بالوصول</response>
        /// <response code="403">المستخدم ليس لديه صلاحية الإدارة</response>
        /// <response code="500">حدث خطأ أثناء معالجة الطلب</response>
        /// <remarks>
        /// تتطلب هذه العملية صلاحيات المسؤول (Admin)
        /// وتقوم بحذف جميع صور الشرائح المرتبطة بمقالة معينة من مجلد "Sider"
        /// </remarks>
        [HttpDelete("slider/{articleId}")]
        [Authorize(Roles = "Admin")]
        public IActionResult DeleteArticleSliderImages(int articleId)
        {
            try
            {
                if (articleId <= 0)
                {
                    return BadRequest("Article ID is required and must be greater than 0");
                }

                var uploadPath = Path.Combine(_environment.WebRootPath, "Sider");
                if (!Directory.Exists(uploadPath))
                {
                    return NotFound("Slider directory not found");
                }

                // Get all files in the Sider directory
                string[] files = Directory.GetFiles(uploadPath);
                int deletedCount = 0;

                // Article prefix pattern
                string articlePrefix = $"article_{articleId}_";

                foreach (string file in files)
                {
                    string fileName = Path.GetFileName(file);

                    // Check if the file belongs to this article
                    if (fileName.StartsWith(articlePrefix))
                    {
                        try
                        {
                            System.IO.File.Delete(file);
                            deletedCount++;
                            _logger.LogInformation("Deleted slider image: {FilePath}", file);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error deleting slider image: {FilePath}", file);
                        }
                    }
                }

                _logger.LogInformation("Deleted {Count} slider images for article ID: {ArticleId}", deletedCount, articleId);
                return Ok(new { DeletedCount = deletedCount });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting slider images for article ID: {ArticleId}", articleId);
                return StatusCode(500, new { ErrorMessage = $"Internal server error: {ex.Message}", Successful = false });
            }
        }

        /// <summary>
        /// دالة مساعدة لحذف ملف إذا كان موجودًا
        /// </summary>
        /// <param name="filePath">مسار الملف المراد حذفه (يمكن أن يكون URL كاملًا أو مسارًا نسبيًا)</param>
        /// <param name="folderName">اسم المجلد الذي يحتوي على الملف</param>
        private void DeleteFileIfExists(string filePath, string folderName)
        {
            try
            {
                _logger.LogInformation("Attempting to delete file from path: {FilePath} in folder: {FolderName}", filePath, folderName);

                // التعامل مع تنسيقات URL المختلفة
                // 1. URL كامل مع اسم النطاق: https://domain.com/folderName/filename.jpg
                // 2. URL نسبي مع شرطة مائلة في البداية: /folderName/filename.jpg
                // 3. URL نسبي بدون شرطة مائلة في البداية: folderName/filename.jpg
                // 4. اسم الملف فقط: filename.jpg

                string fileName;

                if (filePath.Contains($"/{folderName}/"))
                {
                    // استخراج اسم الملف من مسارات مثل "/folderName/filename.jpg" أو "https://domain.com/folderName/filename.jpg"
                    int folderIndex = filePath.LastIndexOf($"/{folderName}/") + folderName.Length + 2;
                    if (folderIndex < filePath.Length)
                    {
                        fileName = filePath.Substring(folderIndex);
                    }
                    else
                    {
                        _logger.LogWarning("Invalid file path format: {FilePath}", filePath);
                        return;
                    }
                }
                else
                {
                    // استخدام اسم الملف كما هو
                    fileName = Path.GetFileName(filePath);
                }

                if (string.IsNullOrEmpty(fileName))
                {
                    _logger.LogWarning("Could not extract filename from path: {FilePath}", filePath);
                    return;
                }

                // بناء المسار الفعلي الكامل
                string fullPath = Path.Combine(_environment.WebRootPath, folderName, fileName);
                _logger.LogInformation("Resolved file path for deletion: {FullPath}", fullPath);

                if (System.IO.File.Exists(fullPath))
                {
                    System.IO.File.Delete(fullPath);
                    _logger.LogInformation("Successfully deleted file: {FilePath}", fullPath);
                }
                else
                {
                    _logger.LogWarning("File not found for deletion: {FilePath}", fullPath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file: {FilePath} in folder: {FolderName}", filePath, folderName);
            }
        }

        #endregion
    }
}
