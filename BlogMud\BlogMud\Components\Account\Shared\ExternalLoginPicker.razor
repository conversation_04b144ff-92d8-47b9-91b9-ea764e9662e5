﻿@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using MudBlazor
@using BlogMud.Data
@using MudBlazor.StaticInput

@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityRedirectManager RedirectManager
<ExternalLoginPickerCSS />
@if (externalLogins.Length == 0)
{
    <MudAlert Variant="Variant.Outlined" Severity="Severity.Info" Class="mb-4">
        <MudText Typo="Typo.body2">
            لا توجد خدمات مصادقة خارجية مُكوَّنة حالياً.
        </MudText>
    </MudAlert>
    <MudPaper Elevation="1" Class="pa-4" Style="background-color: #f8f9fa; border-radius: 12px;">
        <MudText Typo="Typo.body2" Class="mb-3" Style="color: #6c757d;">
            <MudIcon Icon="@Icons.Material.Filled.Info" Size="Size.Small" Class="mr-2" />
            لتفعيل تسجيل الدخول بالخدمات الخارجية:
        </MudText>
        <MudText Typo="Typo.caption" Style="color: #6c757d;">
            راجع
            <MudLink Target="_blank" Href="https://go.microsoft.com/fwlink/?LinkID=532715" Color="Color.Primary">
                هذا المقال
            </MudLink>
            حول إعداد التطبيق لدعم تسجيل الدخول عبر الخدمات الخارجية
        </MudText>
    </MudPaper>
}
else
{
    <form class="form-horizontal" action="Account/PerformExternalLogin" method="post">
        <div>
            <AntiforgeryToken />
            <input type="hidden" name="ReturnUrl" value="@ReturnUrl" />

            <MudStack Spacing="3">
                @foreach (var provider in externalLogins)
                {
                    <button type="submit" name="provider" value="@provider.Name"
                            title="تسجيل الدخول باستخدام حساب @provider.DisplayName"
                            class="external-login-button @GetProviderButtonClass(provider.Name)">
                        <div style="display: flex; align-items: center; justify-content: center;">
                            @GetProviderIcon(provider.Name)
                            <span style="margin-right: 8px;">تسجيل الدخول بـ @GetProviderDisplayName(provider.DisplayName)</span>
                        </div>
                    </button>
                }
            </MudStack>
        </div>
    </form>
}

@code {
    private AuthenticationScheme[] externalLogins = [];

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        externalLogins = (await SignInManager.GetExternalAuthenticationSchemesAsync()).ToArray();
    }

    private string GetProviderButtonClass(string providerName)
    {
        return providerName.ToLower() switch
        {
            "google" => "google-button",
            "facebook" => "facebook-button",
            "microsoft" => "microsoft-button",
            "github" => "github-button",
            _ => "default-external-button"
        };
    }

    private RenderFragment GetProviderIcon(string providerName)
    {
        return providerName.ToLower() switch
        {
            "google" => @<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>,
            "facebook" => @<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
            </svg>,
            "microsoft" => @<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zM24 11.4H12.6V0H24v11.4z"/>
            </svg>,
            "github" => @<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>,
            _ => @<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
        };
    }

    private string GetProviderDisplayName(string displayName)
    {
        return displayName switch
        {
            "Google" => "جوجل",
            "Facebook" => "فيسبوك",
            "Microsoft" => "مايكروسوفت",
            "GitHub" => "جيت هاب",
            _ => displayName
        };
    }
}
