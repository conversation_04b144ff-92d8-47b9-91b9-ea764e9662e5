# Gmail App Password Configuration Script
# Run this script after getting your Gmail App Password

param(
    [Parameter(Mandatory=$true)]
    [string]$AppPassword
)

Write-Host "Updating Gmail App Password in appsettings.Development.json..." -ForegroundColor Green

$configPath = "appsettings.Development.json"

if (Test-Path $configPath) {
    $config = Get-Content $configPath -Raw | ConvertFrom-Json
    $config.EmailSettings.Password = $AppPassword
    
    $config | ConvertTo-Json -Depth 10 | Set-Content $configPath
    
    Write-Host "✅ Gmail App Password updated successfully!" -ForegroundColor Green
    Write-Host "📧 Email will now be sent to: <EMAIL>" -ForegroundColor Cyan
    Write-Host "🔄 Please restart the application for changes to take effect." -ForegroundColor Yellow
} else {
    Write-Host "❌ Configuration file not found: $configPath" -ForegroundColor Red
}

# Usage example:
# .\update-gmail-password.ps1 -AppPassword "abcd efgh ijkl mnop"
