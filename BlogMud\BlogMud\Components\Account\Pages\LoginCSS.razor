@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using BlogMud.Data

<style>
    /* Enhanced Background with Floating Particles */
    .login-container {
        min-height: calc(100vh - 120px);
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 1rem;
        position: relative;
        overflow: hidden;
        animation: backgroundShift 10s ease-in-out infinite;
    }

    /* Floating Particles Background */
    .login-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 20% 80%, rgba(149, 55, 53, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(149, 55, 53, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(149, 55, 53, 0.05) 0%, transparent 50%);
        animation: particleFloat 15s ease-in-out infinite;
        z-index: 0;
    }

    /* Enhanced Login Card with Glassmorphism */
    .login-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 24px;
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.1),
            0 8px 32px rgba(149, 55, 53, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        max-width: 1000px;
        width: 100%;
        position: relative;
        z-index: 1;
        animation: cardEntrance 0.8s ease-out;
    }

        .login-card:hover {
            box-shadow:
                0 30px 90px rgba(0, 0, 0, 0.15),
                0 12px 40px rgba(149, 55, 53, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transform: translateY(-4px) scale(1.01);
            border-color: rgba(149, 55, 53, 0.1);
        }

    /* Enhanced Header with Advanced Gradients */
    .login-header {
        background: linear-gradient(135deg,
            var(--mud-palette-primary) 0%,
            var(--mud-palette-primary-darken) 50%,
            #7a3f3d 100%);
        color: white;
        padding: 3rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.1);
    }

        .login-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background:
                radial-gradient(circle at 30% 30%, rgba(255,255,255,0.15) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(255,255,255,0.1) 0%, transparent 60%);
            animation: shimmer 4s ease-in-out infinite;
        }

        .login-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255,255,255,0.3) 50%,
                transparent 100%);
        }

    /* Enhanced Animations */
    @@keyframes shimmer {
        0%, 100% {
            transform: rotate(0deg) scale(1);
            opacity: 0.8;
        }
        50% {
            transform: rotate(180deg) scale(1.1);
            opacity: 1;
        }
    }

    @@keyframes backgroundShift {
        0%, 100% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
    }

    @@keyframes particleFloat {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.7;
        }
        33% {
            transform: translateY(-10px) rotate(120deg);
            opacity: 1;
        }
        66% {
            transform: translateY(5px) rotate(240deg);
            opacity: 0.8;
        }
    }

    @@keyframes cardEntrance {
        0% {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .login-header-content {
        position: relative;
        z-index: 1;
        animation: headerContentFade 1s ease-out 0.3s both;
    }

    /* Enhanced Form Section */
    .login-form-section {
        padding: 3rem 2rem;
        background: linear-gradient(145deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(248, 249, 250, 0.8) 100%);
        position: relative;
    }

        .login-form-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(149, 55, 53, 0.1) 50%,
                transparent 100%);
        }

    /* Enhanced External Section with Glassmorphism */
    .login-external-section {
        background: linear-gradient(145deg,
            rgba(248, 249, 250, 0.95) 0%,
            rgba(233, 236, 239, 0.9) 100%);
        backdrop-filter: blur(10px);
        padding: 3rem 2rem;
        border-left: 1px solid rgba(149, 55, 53, 0.1);
        position: relative;
    }

        .login-external-section::before {
            content: '';
            position: absolute;
            top: 20%;
            left: 0;
            width: 1px;
            height: 60%;
            background: linear-gradient(180deg,
                transparent 0%,
                rgba(149, 55, 53, 0.2) 50%,
                transparent 100%);
        }

    /* Enhanced Form Inputs with Advanced Effects */
    .form-input {
        margin-bottom: 1.5rem;
        position: relative;
        animation: inputSlideIn 0.6s ease-out;
    }

        .form-input .mud-input-control {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
        }

            .form-input .mud-input-control:hover {
                transform: translateY(-2px);
                box-shadow:
                    0 8px 25px rgba(0, 0, 0, 0.1),
                    0 4px 12px rgba(149, 55, 53, 0.05);
                background: rgba(255, 255, 255, 0.95);
            }

            .form-input .mud-input-control:focus-within {
                transform: translateY(-2px) scale(1.02);
                box-shadow:
                    0 12px 35px rgba(0, 0, 0, 0.15),
                    0 6px 16px rgba(149, 55, 53, 0.1),
                    0 0 0 3px rgba(149, 55, 53, 0.1);
                background: white;
            }

        .form-input .mud-input-adornment {
            color: var(--mud-palette-primary);
            transition: all 0.3s ease;
        }

            .form-input .mud-input-control:focus-within .mud-input-adornment {
                color: var(--mud-palette-primary-darken);
                transform: scale(1.1);
            }

    /* Enhanced Login Button with Advanced Effects */
    .login-button {
        height: 52px;
        border-radius: 16px;
        font-weight: 600;
        text-transform: none;
        background: linear-gradient(135deg,
            var(--mud-palette-primary) 0%,
            var(--mud-palette-primary-darken) 100%);
        box-shadow:
            0 6px 20px rgba(149, 55, 53, 0.3),
            0 2px 8px rgba(149, 55, 53, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        letter-spacing: 0.5px;
    }

        .login-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.2) 50%,
                transparent 100%);
            transition: left 0.6s ease;
        }

        .login-button:hover {
            box-shadow:
                0 8px 30px rgba(149, 55, 53, 0.4),
                0 4px 12px rgba(149, 55, 53, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transform: translateY(-3px) scale(1.02);
            background: linear-gradient(135deg,
                var(--mud-palette-primary-darken) 0%,
                #7a3f3d 100%);
        }

            .login-button:hover::before {
                left: 100%;
            }

        .login-button:active {
            transform: translateY(-1px) scale(0.98);
            box-shadow:
                0 4px 15px rgba(149, 55, 53, 0.3),
                0 2px 6px rgba(149, 55, 53, 0.2);
        }

    /* Enhanced Login Links with Micro-interactions */
    .login-links {
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid rgba(149, 55, 53, 0.1);
        position: relative;
    }

        .login-links::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 1px;
            background: linear-gradient(90deg,
                transparent 0%,
                var(--mud-palette-primary) 50%,
                transparent 100%);
        }

        .login-links .mud-link {
            display: inline-flex;
            align-items: center;
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 8px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

            .login-links .mud-link::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg,
                    transparent 0%,
                    rgba(149, 55, 53, 0.05) 50%,
                    transparent 100%);
                transition: left 0.5s ease;
            }

            .login-links .mud-link:hover {
                transform: translateX(8px);
                color: var(--mud-palette-primary-darken);
                background: rgba(149, 55, 53, 0.03);
                box-shadow: 0 2px 8px rgba(149, 55, 53, 0.1);
            }

                .login-links .mud-link:hover::before {
                    left: 100%;
                }

    /* Enhanced Welcome Icon with Pulsing Effect */
    .welcome-icon {
        font-size: 4.5rem;
        margin-bottom: 1rem;
        opacity: 0.95;
        animation: iconPulse 3s ease-in-out infinite;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    }

    /* Additional Animation Keyframes */
    @@keyframes headerContentFade {
        0% {
            opacity: 0;
            transform: translateY(20px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes inputSlideIn {
        0% {
            opacity: 0;
            transform: translateX(-20px);
        }
        100% {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @@keyframes iconPulse {
        0%, 100% {
            transform: scale(1);
            opacity: 0.95;
        }
        50% {
            transform: scale(1.05);
            opacity: 1;
        }
    }

    /* Enhanced Responsive Design */
    @@media (max-width: 960px) {
        .login-external-section {
            border-left: none;
            border-top: 1px solid rgba(149, 55, 53, 0.1);
        }

            .login-external-section::before {
                top: 0;
                left: 20%;
                width: 60%;
                height: 1px;
                background: linear-gradient(90deg,
                    transparent 0%,
                    rgba(149, 55, 53, 0.2) 50%,
                    transparent 100%);
            }

        .login-container {
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .login-form-section,
        .login-external-section {
            padding: 2rem 1.5rem;
        }

        .login-header {
            padding: 2.5rem 1.5rem;
        }

        .welcome-icon {
            font-size: 3.5rem;
        }

        .login-card {
            border-radius: 20px;
            margin: 0.5rem;
        }

        .form-input .mud-input-control:focus-within {
            transform: translateY(-1px) scale(1.01);
        }

        .login-button {
            height: 48px;
        }
    }

    @@media (max-width: 600px) {
        .login-container {
            padding: 0.5rem;
        }

        .login-header {
            padding: 2rem 1rem;
        }

        .login-form-section,
        .login-external-section {
            padding: 1.5rem 1rem;
        }

        .welcome-icon {
            font-size: 3rem;
        }

        .login-card {
            border-radius: 16px;
        }
    }

    /* Advanced Visual Enhancements */
    .mud-static-checkbox .mud-checkbox-input:checked + .mud-checkbox-box {
        background: linear-gradient(135deg,
            var(--mud-palette-primary) 0%,
            var(--mud-palette-primary-darken) 100%);
        box-shadow: 0 2px 8px rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .mud-static-checkbox .mud-checkbox-box {
        border-radius: 6px;
        transition: all 0.3s ease;
        border: 2px solid #e0e0e0;
    }

        .mud-static-checkbox .mud-checkbox-box:hover {
            border-color: var(--mud-palette-primary);
            box-shadow: 0 0 0 3px rgba(149, 55, 53, 0.1);
        }

    /* Enhanced Text Field Styling */
    .mud-input-outlined .mud-input-outlined-border {
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .mud-input-outlined:hover .mud-input-outlined-border {
        border-color: rgba(149, 55, 53, 0.3);
    }

    .mud-input-outlined.mud-input-focused .mud-input-outlined-border {
        border-color: var(--mud-palette-primary);
        border-width: 2px;
        box-shadow: 0 0 0 3px rgba(149, 55, 53, 0.1);
    }

    /* Enhanced Status Message Styling */
    .mud-alert {
        border-radius: 12px;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Smooth Page Transitions */
    * {
        box-sizing: border-box;
    }

    .login-container * {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Enhanced Focus Management */
    .mud-input-control:focus-within {
        z-index: 2;
        position: relative;
    }

    /* Loading State Animation */
    @@keyframes buttonLoading {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }

    .login-button.loading {
        background: linear-gradient(90deg,
            var(--mud-palette-primary) 25%,
            var(--mud-palette-primary-lighten) 50%,
            var(--mud-palette-primary) 75%);
        background-size: 200% 100%;
        animation: buttonLoading 1.5s infinite;
    }

    /* Accessibility Enhancements */
    @@media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }

    /* High Contrast Mode Support */
    @@media (prefers-contrast: high) {
        .login-card {
            border: 2px solid;
        }

        .login-button {
            border: 2px solid;
        }
    }
</style>
