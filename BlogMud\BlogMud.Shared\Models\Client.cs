using System;
using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    public class Client
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم العميل مطلوب")]
        [StringLength(100, ErrorMessage = "يجب أن يكون اسم العميل بين {2} و {1} حرفًا", MinimumLength = 2)]
        public string Name { get; set; }

        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صالح")]
        [StringLength(100, ErrorMessage = "يجب أن يكون البريد الإلكتروني أقل من {1} حرفًا")]
        public string Email { get; set; }

        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        [StringLength(12, ErrorMessage = "يجب أن يكون رقم الهاتف أقل من {1} حرفًا")]
        public string Phone { get; set; }

        [StringLength(200, ErrorMessage = "يجب أن يكون العنوان أقل من {1} حرفًا")]
        public string? Address { get; set; }

        [StringLength(500, ErrorMessage = "يجب أن تكون الملاحظات أقل من {1} حرفًا")]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;
    }
}
