using BlogMud.Shared.DTOs;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Net.Http.Json;

namespace BlogMud.Client.Pages.Admin.Posts.AboutPage
{
    /// <summary>
    /// صفحة إدارة محتوى "من نحن"
    /// </summary>
    public partial class AboutUs : ComponentBase
    {
        #region الخصائص والمتغيرات

        /// <summary>
        /// قائمة محتوى صفحة "من نحن" المعروضة في الصفحة
        /// </summary>
        private List<AboutUsDto> _aboutUsContent = new();

        /// <summary>
        /// مؤشر على حالة تحميل البيانات
        /// </summary>
        private bool _loading = true;

        #endregion

        #region الحقن والخدمات

        // الخدمات متاحة عالمياً من خلال _Imports.razor

        #endregion

        #region دوال دورة الحياة

        /// <summary>
        /// تهيئة مكونات الصفحة عند بدء التشغيل
        /// </summary>
        /// <returns>مهمة غير متزامنة</returns>
        protected override async Task OnInitializedAsync()
        {
            await LoadAboutUsContent();
        }

        #endregion

        #region دوال تحميل البيانات

        /// <summary>
        /// تحميل قائمة محتوى صفحة "من نحن" من الخادم
        /// </summary>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task LoadAboutUsContent()
        {
            try
            {
                _loading = true;
                StateHasChanged();

                Console.WriteLine("Loading AboutUs content from API: api/AboutUs");
                var response = await Http.GetAsync("api/AboutUs");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadFromJsonAsync<List<AboutUsDto>>();
                    _aboutUsContent = content ?? new List<AboutUsDto>();
                    Console.WriteLine($"Loaded {_aboutUsContent.Count} AboutUs items with total {_aboutUsContent.Sum(x => x.ProductionCapacityItems.Count)} production items");
                }
                else
                {
                    _Snackbar.Add("فشل في تحميل محتوى صفحة 'من نحن'", Severity.Error);
                    _aboutUsContent = new List<AboutUsDto>();
                }
            }
            catch (Exception ex)
            {
                _Snackbar.Add($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", Severity.Error);
                _aboutUsContent = new List<AboutUsDto>();
            }
            finally
            {
                _loading = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// تحميل محتوى صفحة "من نحن" بواسطة المعرف مع جميع العلاقات
        /// </summary>
        /// <param name="id">معرف المحتوى</param>
        /// <returns>محتوى صفحة "من نحن" أو null إذا لم يوجد</returns>
        private async Task<AboutUsDto?> LoadAboutUsById(int id)
        {
            try
            {
                Console.WriteLine($"Requesting AboutUs data from API: api/AboutUs/{id}");
                var response = await Http.GetAsync($"api/AboutUs/{id}");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadFromJsonAsync<AboutUsDto>();
                    Console.WriteLine($"API Response: AboutUs ID {content?.Id} with {content?.ProductionCapacityItems?.Count ?? 0} production items");
                    return content;
                }
                else
                {
                    Console.WriteLine($"API Error: {response.StatusCode} - {response.ReasonPhrase}");
                    _Snackbar.Add($"فشل في تحميل محتوى صفحة 'من نحن' بالمعرف {id}", Severity.Error);
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in LoadAboutUsById: {ex.Message}");
                _Snackbar.Add($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", Severity.Error);
                return null;
            }
        }

        #endregion

        #region دوال إدارة المحتوى

        /// <summary>
        /// فتح نافذة حوار لإضافة أو تعديل محتوى صفحة "من نحن"
        /// </summary>
        /// <param name="aboutUs">المحتوى المراد تعديله، أو null لإضافة محتوى جديد</param>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task OpenAboutUsDialog(AboutUsDto? aboutUs)
        {
            // إذا كان في وضع التعديل، جلب البيانات الكاملة من الخادم
            AboutUsDto? fullAboutUsData = null;
            if (aboutUs != null)
            {
                Console.WriteLine($"Loading AboutUs with ID: {aboutUs.Id} for editing");
                fullAboutUsData = await LoadAboutUsById(aboutUs.Id);
                if (fullAboutUsData != null)
                {
                    Console.WriteLine($"Loaded AboutUs with {fullAboutUsData.ProductionCapacityItems.Count} production capacity items");
                }
                else
                {
                    Console.WriteLine("Failed to load AboutUs data");
                }
            }

            var parameters = new DialogParameters<AboutUsEditForm>
            {
                { x => x.AboutUs, fullAboutUsData ?? aboutUs },
                { x => x.IsCreateMode, aboutUs == null }
            };

            var options = new DialogOptions()
            {
                MaxWidth = MaxWidth.Large,
                FullWidth = true,
                CloseButton = true,
                CloseOnEscapeKey = false
            };

            var dialog = await DialogService.ShowAsync<AboutUsEditForm>(
                aboutUs == null ? "إضافة محتوى جديد" : "تعديل المحتوى",
                parameters,
                options);

            var result = await dialog.Result;

            if (!result.Canceled && result.Data is AboutUsDto updatedAboutUs)
            {
                await SaveAboutUs(updatedAboutUs, aboutUs == null);
            }
        }

        /// <summary>
        /// حفظ محتوى صفحة "من نحن" (إضافة أو تعديل)
        /// </summary>
        /// <param name="aboutUs">المحتوى المراد حفظه</param>
        /// <param name="isCreate">هل هي عملية إضافة جديدة أم تعديل</param>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task SaveAboutUs(AboutUsDto aboutUs, bool isCreate)
        {
            try
            {
                HttpResponseMessage response;

                if (isCreate)
                {
                    response = await Http.PostAsJsonAsync("api/AboutUs", aboutUs);
                }
                else
                {
                    response = await Http.PutAsJsonAsync($"api/AboutUs/{aboutUs.Id}", aboutUs);
                }

                if (response.IsSuccessStatusCode)
                {
                    _Snackbar.Add(isCreate ? "تم إضافة المحتوى بنجاح" : "تم تحديث المحتوى بنجاح", Severity.Success);
                    await LoadAboutUsContent();
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _Snackbar.Add($"فشل في حفظ المحتوى: {error}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                _Snackbar.Add($"حدث خطأ أثناء حفظ المحتوى: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// عرض تفاصيل محتوى صفحة "من نحن"
        /// </summary>
        /// <param name="aboutUs">المحتوى المراد عرض تفاصيله</param>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task ViewAboutUsDetails(AboutUsDto aboutUs)
        {
            var parameters = new DialogParameters<AboutUsEditForm>
            {
                { x => x.AboutUs, aboutUs },
                { x => x.IsCreateMode, false },
                { x => x.IsViewMode, true }
            };

            var options = new DialogOptions()
            {
                MaxWidth = MaxWidth.Large,
                FullWidth = true,
                CloseButton = true
            };

            await DialogService.ShowAsync<AboutUsEditForm>("تفاصيل المحتوى", parameters, options);
        }

        /// <summary>
        /// عرض مربع حوار تأكيد حذف محتوى صفحة "من نحن"
        /// </summary>
        /// <param name="aboutUs">المحتوى المراد حذفه</param>
        /// <returns>مهمة غير متزامنة</returns>
        /// <remarks>
        /// يعرض مربع حوار تأكيد قبل حذف المحتوى
        /// إذا تم تأكيد الحذف، يتم استدعاء دالة DeleteAboutUs لحذف المحتوى
        /// </remarks>
        private async Task ConfirmDelete(AboutUsDto aboutUs)
        {
            var parameters = new DialogParameters
            {
                { "ContentText", $"هل أنت متأكد من حذف هذا المحتوى؟ هذا الإجراء لا يمكن التراجع عنه." },
                { "ButtonText", "حذف" },
                { "Color", Color.Error }
            };

            var dialog = await DialogService.ShowAsync<BlogMud.Client.Components.ConfirmationDialog>("تأكيد الحذف", parameters);
            var result = await dialog.Result;

            if (!result.Canceled)
            {
                await DeleteAboutUs(aboutUs);
            }
        }

        /// <summary>
        /// حذف محتوى صفحة "من نحن" من قاعدة البيانات
        /// </summary>
        /// <param name="aboutUs">المحتوى المراد حذفه</param>
        /// <returns>مهمة غير متزامنة</returns>
        /// <remarks>
        /// يقوم بإرسال طلب حذف إلى الخادم
        /// إذا نجحت العملية، يتم إزالة المحتوى من القائمة المحلية وعرض رسالة نجاح
        /// وإلا يتم عرض رسالة الخطأ المستلمة من الخادم
        /// </remarks>
        private async Task DeleteAboutUs(AboutUsDto aboutUs)
        {
            try
            {
                var response = await Http.DeleteAsync($"api/AboutUs/{aboutUs.Id}");

                if (response.IsSuccessStatusCode)
                {
                    _aboutUsContent.Remove(aboutUs);
                    _Snackbar.Add("تم حذف المحتوى بنجاح", Severity.Success);
                }
                else
                {
                    var errorMessage = await response.Content.ReadAsStringAsync();
                    _Snackbar.Add(errorMessage, Severity.Error);
                }
            }
            catch (Exception ex)
            {
                _Snackbar.Add($"حدث خطأ أثناء حذف المحتوى: {ex.Message}", Severity.Error);
            }
        }

        #endregion
    }
}
