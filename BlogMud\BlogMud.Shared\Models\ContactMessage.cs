using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    public class ContactMessage
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; }
        
        [StringLength(15)]
        public string? Phone { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Subject { get; set; }
        
        [Required]
        [StringLength(2000)]
        public string Message { get; set; }
        
        public DateTime SubmissionDate { get; set; } = DateTime.Now;
        
        public bool IsRead { get; set; } = false;
        
        public bool IsResponded { get; set; } = false;
    }
}
