using System;

namespace BlogMud.Shared.DTOs
{
    /// <summary>
    /// كائن نقل البيانات للصور المتحركة
    /// يستخدم لنقل بيانات الصور المتحركة بين واجهة المستخدم والخادم
    /// </summary>
    public class AnimatedGifDto
    {
        public int Id { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string ImageUrl { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
    }
}
