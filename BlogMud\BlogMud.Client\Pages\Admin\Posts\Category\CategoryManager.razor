@page "/admin/categories"

@attribute [Authorize(Roles = "Admin")]
@layout BlogMud.Client.Layout.AdminLayout

<PageTitle>إدارة الأقسام</PageTitle>

<style>
    /* إخفاء الجدول العادي في الشاشات الصغيرة */
    .regular-table { display: block; }
    .mobile-cards { display: none; }

    @@media (max-width: 768px) {
        .regular-table { display: none; }
        .mobile-cards { display: block; }
    }

    /* تصميم البطاقات المصغرة */
    .mobile-cards { padding: 0 2px; }
    .mobile-card { 
        border-radius: 6px; 
        transition: all 0.2s ease;
        border: 1px solid #e0e0e0;
        margin-bottom: 6px;
    }
    .mobile-card:hover { 
        transform: translateY(-1px); 
        box-shadow: 0 2px 8px rgba(0,0,0,0.1); 
    }
    
    /* تصغير المحتوى */
    .compact-content { padding: 8px !important; }
    .category-header { 
        display: flex; 
        justify-content: space-between; 
        align-items: center; 
        margin-bottom: 6px; 
    }
    .category-number { 
        font-size: 0.7rem; 
        padding: 1px 6px; 
        min-height: 20px;
    }
    .category-actions { display: flex; gap: 2px; }
    .category-title { 
        font-size: 0.9rem; 
        font-weight: 600; 
        margin-bottom: 3px; 
        line-height: 1.2;
    }
    .category-description { 
        font-size: 0.8rem; 
        color: #666; 
        margin-bottom: 6px; 
        line-height: 1.3;
    }
    .category-info { 
        display: flex; 
        align-items: center; 
        gap: 4px; 
        font-size: 0.8rem;
    }
    .info-icon { color: #1976d2; }
    .info-label { color: #666; font-weight: 500; }
    .info-chip { 
        font-size: 0.7rem; 
        padding: 1px 4px; 
        min-height: 18px;
    }
</style>
<MudContainer MaxWidth="MaxWidth.Large" Class="mt-6">
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h4">إدارة الأقسام</MudText>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
            OnClick="@(() => OpenCategoryDialog(null))">
            إضافة قسم جديد
        </MudButton>
    </div>

    @if (isLoading)
    {
        <div class="d-flex justify-center my-8">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
        </div>
    }
    else if (categories.Count == 0)
    {
        <MudAlert Severity="Severity.Info" Class="my-4">لا توجد أقسام متاحة. أضف قسماً جديداً للبدء.</MudAlert>
    }
    else
    {
        <!-- الجدول العادي للشاشات الكبيرة -->
        <div class="regular-table">
            <MudTable Items="@categories" Hover="true" Striped="true" Bordered="true" Class="mb-8">
                <HeaderContent>
                    <MudTh>ت</MudTh>
                    <MudTh>اسم القسم</MudTh>
                    <MudTh>الوصف</MudTh>
                    <MudTh>عدد المنشورات</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="ت">@(categories.IndexOf(context) + 1)</MudTd>
                    <MudTd DataLabel="اسم القسم">@context.Name</MudTd>
                    <MudTd DataLabel="الوصف" Class="category-description">@context.Description</MudTd>
                    <MudTd DataLabel="عدد المنشورات">
                        <MudChip T="string" Color="Color.Primary" Size="Size.Small" Class="category-count-chip">
                            @context.ArticleCount
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="الإجراءات">
                        <MudIconButton Icon="@Icons.Material.Filled.Edit" Color="Color.Primary"
                            OnClick="@(() => OpenCategoryDialog(context))" />
                        <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error"
                            OnClick="@(() => ConfirmDelete(context))" />
                    </MudTd>
                </RowTemplate>
                <PagerContent>
                    <MudTablePager PageSizeOptions="new int[] { 5, 10, 15 }" />
                </PagerContent>
            </MudTable>
        </div>

        <!-- تخطيط البطاقات المصغرة للشاشات الصغيرة (التابلت والهاتف) -->
        <div class="mobile-cards">
            @foreach (var category in categories)
            {
                <MudCard Class="mobile-card" Elevation="1">
                    <MudCardContent Class="compact-content">
                        <!-- رقم القسم والإجراءات -->
                        <div class="category-header">
                            <MudChip T="string" Color="Color.Primary" Size="Size.Small" Class="category-number">
                                @(categories.IndexOf(category) + 1)
                            </MudChip>
                            <div class="category-actions">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                             Color="Color.Primary"
                                             Size="Size.Small"
                                             OnClick="@(() => OpenCategoryDialog(category))" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                             Color="Color.Error"
                                             Size="Size.Small"
                                             OnClick="@(() => ConfirmDelete(category))" />
                            </div>
                        </div>

                        <!-- اسم القسم -->
                        <div class="category-title">
                            @(category.Name ?? "غير محدد")
                        </div>

                        <!-- وصف القسم (مختصر) -->
                        @if (!string.IsNullOrEmpty(category.Description))
                        {
                            <div class="category-description">
                                @(category.Description.Length > 60 ? category.Description.Substring(0, 60) + "..." : category.Description)
                            </div>
                        }

                        <!-- معلومات القسم -->
                        <div class="category-info">
                            <MudIcon Icon="@Icons.Material.Filled.Article" Size="Size.Small" Class="info-icon" />
                            <span class="info-label">المنشورات:</span>
                            <MudChip T="string" Color="Color.Primary" Size="Size.Small" Class="info-chip">
                                @category.ArticleCount
                            </MudChip>
                        </div>
                    </MudCardContent>
                </MudCard>
            }
        </div>
    }
</MudContainer>

@code {
    #region الخصائص والمتغيرات

    /// <summary>
    /// قائمة الأقسام المعروضة في الصفحة
    /// </summary>
    private List<CategoryDto> categories = new List<CategoryDto>();

    /// <summary>
    /// مؤشر على حالة تحميل البيانات
    /// </summary>
    private bool isLoading = true;
    #endregion

    #region دوال دورة الحياة

    /// <summary>
    /// تهيئة مكونات الصفحة عند بدء التشغيل
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بتحميل قائمة الأقسام من الخادم عند تحميل الصفحة
    /// </remarks>
    protected override async Task OnInitializedAsync()
    {
        await LoadCategories();
    }
    #endregion

    #region طلبات البيانات

    /// <summary>
    /// تحميل قائمة الأقسام من الخادم
    /// </summary>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بتعيين مؤشر التحميل إلى true قبل البدء في التحميل
    /// وإعادة تعيينه إلى false بعد الانتهاء من التحميل بغض النظر عن النتيجة
    /// </remarks>
    private async Task LoadCategories()
    {
        try
        {
            isLoading = true;
            categories = await Http.GetFromJsonAsync<List<CategoryDto>>("api/Categories") ?? new List<CategoryDto>();
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء تحميل الأقسام: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }
    #endregion

    #region الأحداث والتفاعلات

    /// <summary>
    /// فتح مربع حوار إضافة أو تعديل قسم
    /// </summary>
    /// <param name="category">القسم المراد تعديله، أو null لإضافة قسم جديد</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// إذا تم تمرير قسم موجود، يتم فتح مربع الحوار في وضع التعديل
    /// وإلا يتم فتحه في وضع الإضافة
    /// بعد إغلاق مربع الحوار، إذا لم يتم إلغاء العملية، يتم إعادة تحميل قائمة الأقسام
    /// </remarks>
    private async Task OpenCategoryDialog(CategoryDto category)
    {
        var parameters = new DialogParameters();

        if (category != null)
        {
            parameters.Add("CategoryDto", new CategoryDto
            {
                Id = category.Id,
                Name = category.Name,
                Description = category.Description,
                ArticleCount = category.ArticleCount
            });
        }

        var dialog = await DialogService.ShowAsync<CategoryDialog>(
        category == null ? "إضافة قسم جديد" : "تعديل القسم",
        parameters
        );

        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadCategories();
        }
    }

    /// <summary>
    /// عرض مربع حوار تأكيد حذف قسم
    /// </summary>
    /// <param name="category">القسم المراد حذفه</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يعرض مربع حوار تأكيد قبل حذف القسم
    /// إذا تم تأكيد الحذف، يتم استدعاء دالة DeleteCategory لحذف القسم
    /// </remarks>
    private async Task ConfirmDelete(CategoryDto category)
    {
        var parameters = new DialogParameters
{
{ "ContentText", $"هل أنت متأكد من حذف القسم '{category.Name}'؟" },
{ "ButtonText", "حذف" },
{ "Color", Color.Error }
};

        var dialog = await DialogService.ShowAsync<BlogMud.Client.Components.ConfirmationDialog>("تأكيد الحذف", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await DeleteCategory(category);
        }
    }

    /// <summary>
    /// حذف قسم من قاعدة البيانات
    /// </summary>
    /// <param name="category">القسم المراد حذفه</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// يقوم بإرسال طلب حذف إلى الخادم
    /// إذا نجحت العملية، يتم إزالة القسم من القائمة المحلية وعرض رسالة نجاح
    /// وإلا يتم عرض رسالة الخطأ المستلمة من الخادم
    /// </remarks>
    private async Task DeleteCategory(CategoryDto category)
    {
        try
        {
            var response = await Http.DeleteAsync($"api/Categories/{category.Id}");

            if (response.IsSuccessStatusCode)
            {
                categories.Remove(category);
                _Snackbar.Add("تم حذف القسم بنجاح", Severity.Success);
            }
            else
            {
                var errorMessage = await response.Content.ReadAsStringAsync();
                _Snackbar.Add(errorMessage, Severity.Error);
            }
        }
        catch (Exception ex)
        {
            _Snackbar.Add($"حدث خطأ أثناء حذف القسم: {ex.Message}", Severity.Error);
        }
    }
    #endregion
}
