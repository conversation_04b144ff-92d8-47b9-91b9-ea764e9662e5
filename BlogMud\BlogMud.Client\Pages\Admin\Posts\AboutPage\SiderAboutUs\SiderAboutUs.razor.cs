using BlogMud.Shared.DTOs;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Net.Http.Json;

namespace BlogMud.Client.Pages.Admin.Posts.AboutPage.SiderAboutUs
{
    /// <summary>
    /// صفحة إدارة الشرائح المتحركة لصفحة "من نحن"
    /// </summary>
    public partial class SiderAboutUs : ComponentBase
    {
        #region الحقول والخصائص

        private List<SiderAboutUsDto> _siderAboutUs = new();
        private bool _loading = false;
        private string _searchString = "";
        private string _statusFilter = "all";

        // إعدادات معاينة الشرائح
        private bool arrows = true;
        private bool bullets = true;
        private bool enableSwipeGesture = true;
        private bool autocycle = true;
        private Transition transition = Transition.Slide;

        // متغيرات عرض الصورة الكاملة
        private bool _showImageModal = false;
        private string _modalImageUrl = "";
        private string _modalImageTitle = "";

        #endregion

        #region دورة حياة المكون

        /// <summary>
        /// تهيئة المكون عند التحميل
        /// </summary>
        /// <returns>مهمة غير متزامنة</returns>
        protected override async Task OnInitializedAsync()
        {
            await LoadSiderAboutUs();
        }

        #endregion

        #region دوال تحميل البيانات

        /// <summary>
        /// تحميل جميع الشرائح المتحركة من الخادم
        /// </summary>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task LoadSiderAboutUs()
        {
            try
            {
                _loading = true;
                StateHasChanged();

                var response = await Http.GetAsync("api/SiderAboutUs");
                if (response.IsSuccessStatusCode)
                {
                    var slideshows = await response.Content.ReadFromJsonAsync<List<SiderAboutUsDto>>();
                    _siderAboutUs = slideshows ?? new List<SiderAboutUsDto>();
                }
                else
                {
                    _Snackbar.Add("فشل في تحميل الشرائح المتحركة", Severity.Error);
                    _siderAboutUs = new List<SiderAboutUsDto>();
                }
            }
            catch (Exception ex)
            {
                _Snackbar.Add($"حدث خطأ أثناء تحميل الشرائح المتحركة: {ex.Message}", Severity.Error);
                _siderAboutUs = new List<SiderAboutUsDto>();
            }
            finally
            {
                _loading = false;
                StateHasChanged();
            }
        }

        #endregion

        #region دوال الفلترة والبحث

        /// <summary>
        /// فلترة الشرائح المتحركة حسب النص المدخل وحالة النشاط
        /// </summary>
        /// <param name="element">عنصر الشريحة المتحركة</param>
        /// <returns>true إذا كان العنصر يطابق معايير البحث</returns>
        private bool FilterFunc(SiderAboutUsDto element)
        {
            // فلترة حسب النص
            bool matchesSearch = string.IsNullOrWhiteSpace(_searchString) ||
                                element.Title.Contains(_searchString, StringComparison.OrdinalIgnoreCase) ||
                                element.Description.Contains(_searchString, StringComparison.OrdinalIgnoreCase);

            // فلترة حسب الحالة
            bool matchesStatus = _statusFilter switch
            {
                "active" => element.IsActive,
                "inactive" => !element.IsActive,
                _ => true
            };

            return matchesSearch && matchesStatus;
        }

        #endregion

        #region دوال إدارة الشرائح المتحركة

        /// <summary>
        /// فتح نافذة حوار لإضافة شريحة متحركة جديدة
        /// </summary>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task OpenCreateDialog()
        {
            try
            {
                var newSiderAboutUs = new SiderAboutUsDto
                {
                    Title = "",
                    Description = "",
                    ImageUrls = "",
                    Duration = 5,
                    DisplayOrder = 0,
                    IsActive = true,
                    LinkUrl = "",
                    LinkText = "",
                    Notes = ""
                };

                var parameters = new DialogParameters<SiderAboutUsEditForm>
                {
                    { x => x.SiderAboutUs, newSiderAboutUs },
                    { x => x.IsCreateMode, true }
                };

                var options = new DialogOptions()
                {
                    MaxWidth = MaxWidth.Large,
                    FullWidth = true,
                    CloseButton = true,
                    BackdropClick = false,
                    CloseOnEscapeKey = false
                };

                var dialog = await DialogService.ShowAsync<SiderAboutUsEditForm>(
                    "إضافة شريحة متحركة جديدة",
                    parameters,
                    options);

                var result = await dialog.Result;
                if (!result.Canceled && result.Data is SiderAboutUsDto siderAboutUsDto)
                {
                    await HandleSaveSiderAboutUs(siderAboutUsDto);
                }
            }
            catch (Exception ex)
            {
                _Snackbar.Add($"حدث خطأ أثناء فتح نافذة الإضافة: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// فتح نافذة حوار لتعديل شريحة متحركة موجودة
        /// </summary>
        /// <param name="siderAboutUs">الشريحة المتحركة المراد تعديلها</param>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task OpenEditDialog(SiderAboutUsDto siderAboutUs)
        {
            try
            {
                // إنشاء نسخة من الشريحة المتحركة للتعديل
                var editSiderAboutUs = new SiderAboutUsDto
                {
                    Id = siderAboutUs.Id,
                    Title = siderAboutUs.Title,
                    Description = siderAboutUs.Description,
                    ImageUrls = siderAboutUs.ImageUrls,
                    Duration = siderAboutUs.Duration,
                    DisplayOrder = siderAboutUs.DisplayOrder,
                    IsActive = siderAboutUs.IsActive,
                    LinkUrl = siderAboutUs.LinkUrl,
                    LinkText = siderAboutUs.LinkText,
                    Notes = siderAboutUs.Notes,
                    CreatedAt = siderAboutUs.CreatedAt,
                    LastModifiedAt = siderAboutUs.LastModifiedAt
                };

                var parameters = new DialogParameters<SiderAboutUsEditForm>
                {
                    { x => x.SiderAboutUs, editSiderAboutUs },
                    { x => x.IsCreateMode, false }
                };

                var options = new DialogOptions()
                {
                    MaxWidth = MaxWidth.Large,
                    FullWidth = true,
                    CloseButton = true,
                    BackdropClick = false,
                    CloseOnEscapeKey = false
                };

                var dialog = await DialogService.ShowAsync<SiderAboutUsEditForm>(
                    "تعديل الشريحة المتحركة",
                    parameters,
                    options);

                var result = await dialog.Result;
                if (!result.Canceled && result.Data is SiderAboutUsDto updatedSiderAboutUs)
                {
                    await HandleSaveSiderAboutUs(updatedSiderAboutUs);
                }
            }
            catch (Exception ex)
            {
                _Snackbar.Add($"حدث خطأ أثناء فتح نافذة التعديل: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// معالجة حفظ الشريحة المتحركة (إضافة أو تعديل)
        /// </summary>
        /// <param name="updatedSiderAboutUs">بيانات الشريحة المتحركة المحدثة</param>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task HandleSaveSiderAboutUs(SiderAboutUsDto updatedSiderAboutUs)
        {
            try
            {
                // Ensure we're not sending null values
                updatedSiderAboutUs.Title ??= "";
                updatedSiderAboutUs.Description ??= "";
                updatedSiderAboutUs.ImageUrls ??= "";
                updatedSiderAboutUs.LinkUrl ??= "";
                updatedSiderAboutUs.LinkText ??= "";
                updatedSiderAboutUs.Notes ??= "";

                // Add the current timestamp for LastModifiedAt
                updatedSiderAboutUs.LastModifiedAt = DateTime.Now;

                HttpResponseMessage response;
                if (updatedSiderAboutUs.Id == 0)
                {
                    // إضافة شريحة متحركة جديدة
                    response = await Http.PostAsJsonAsync("api/SiderAboutUs", updatedSiderAboutUs);
                }
                else
                {
                    // تحديث شريحة متحركة موجودة
                    response = await Http.PutAsJsonAsync($"api/SiderAboutUs/{updatedSiderAboutUs.Id}", updatedSiderAboutUs);
                }

                if (response.IsSuccessStatusCode)
                {
                    _Snackbar.Add(updatedSiderAboutUs.Id == 0 ? "تم إضافة الشريحة المتحركة بنجاح" : "تم تحديث الشريحة المتحركة بنجاح", Severity.Success);
                    await LoadSiderAboutUs(); // إعادة تحميل البيانات
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _Snackbar.Add($"فشل في حفظ الشريحة المتحركة: {errorContent}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                _Snackbar.Add($"حدث خطأ أثناء حفظ الشريحة المتحركة: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// حذف شريحة متحركة
        /// </summary>
        /// <param name="id">معرف الشريحة المتحركة المراد حذفها</param>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task DeleteSiderAboutUs(int id)
        {
            try
            {
                var siderAboutUs = _siderAboutUs.FirstOrDefault(s => s.Id == id);
                if (siderAboutUs == null)
                {
                    _Snackbar.Add("الشريحة المتحركة غير موجودة", Severity.Warning);
                    return;
                }

                var parameters = new DialogParameters
                {
                    { "ContentText", $"هل أنت متأكد من حذف الشريحة المتحركة '{siderAboutUs.Title}'؟ سيتم حذف جميع الصور المرتبطة بها أيضاً." },
                    { "ButtonText", "حذف" },
                    { "Color", Color.Error }
                };

                var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.ExtraSmall };
                var dialog = await DialogService.ShowAsync<BlogMud.Client.Components.ConfirmationDialog>("تأكيد الحذف", parameters, options);
                var result = await dialog.Result;

                if (!result.Canceled)
                {
                    var response = await Http.DeleteAsync($"api/SiderAboutUs/{id}");
                    if (response.IsSuccessStatusCode)
                    {
                        _Snackbar.Add("تم حذف الشريحة المتحركة بنجاح", Severity.Success);
                        await LoadSiderAboutUs(); // إعادة تحميل البيانات
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        _Snackbar.Add($"فشل في حذف الشريحة المتحركة: {errorContent}", Severity.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                _Snackbar.Add($"حدث خطأ أثناء حذف الشريحة المتحركة: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// عرض تفاصيل الشريحة المتحركة
        /// </summary>
        /// <param name="siderAboutUs">الشريحة المتحركة المراد عرض تفاصيلها</param>
        /// <returns>مهمة غير متزامنة</returns>
        private async Task ViewSiderAboutUsDetails(SiderAboutUsDto siderAboutUs)
        {
            try
            {
                var parameters = new DialogParameters<SiderAboutUsEditForm>
                {
                    { x => x.SiderAboutUs, siderAboutUs },
                    { x => x.IsCreateMode, false },
                    { x => x.IsViewMode, true }
                };

                var options = new DialogOptions()
                {
                    MaxWidth = MaxWidth.Large,
                    FullWidth = true,
                    CloseButton = true
                };

                await DialogService.ShowAsync<SiderAboutUsEditForm>("تفاصيل الشريحة المتحركة", parameters, options);
            }
            catch (Exception ex)
            {
                _Snackbar.Add($"حدث خطأ أثناء عرض التفاصيل: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// عرض الصورة في نافذة منبثقة بحجم كامل
        /// </summary>
        /// <param name="imageUrl">رابط الصورة</param>
        /// <param name="title">عنوان الصورة</param>
        private void ShowImageModal(string imageUrl, string title)
        {
            _modalImageUrl = imageUrl;
            _modalImageTitle = title;
            _showImageModal = true;
            StateHasChanged();
        }

        /// <summary>
        /// إخفاء نافذة عرض الصورة
        /// </summary>
        private void HideImageModal()
        {
            _showImageModal = false;
            _modalImageUrl = "";
            _modalImageTitle = "";
            StateHasChanged();
        }

        #endregion
    }
}
