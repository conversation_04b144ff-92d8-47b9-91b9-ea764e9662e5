using System;

namespace BlogMud.Shared.DTOs
{
    /// <summary>
    /// كائن نقل البيانات لصفحة "من نحن"
    /// يستخدم لنقل بيانات محتوى الشركة بين واجهة المستخدم والخادم
    /// </summary>
    public class AboutUsDto
    {
        public int Id { get; set; }
        public string CompanyLogoUrl { get; set; }
        public string CompanyDescription { get; set; }
        public string CompanyCapabilities { get; set; }
        public List<ProductionCapacityItemDto> ProductionCapacityItems { get; set; } = new List<ProductionCapacityItemDto>();
        public int DisplayOrder { get; set; } = 0;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? LastModifiedAt { get; set; }
    }
}
