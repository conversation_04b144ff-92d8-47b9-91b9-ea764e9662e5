@using BlogMud.Shared.DTOs
@using System.Net.Http.Json
@inject HttpClient Http
@inject ISnackbar Snackbar

<MudDialog @bind-IsVisible="IsVisible" Options="new DialogOptions { CloseOnEscapeKey = true, MaxWidth = MaxWidth.Medium, FullWidth = true }" @key="AnimatedGif?.Id">
    <TitleContent>
        <MudText Typo="Typo.h6">
            تعديل الصورة المتحركة @if (AnimatedGif != null && AnimatedGif.Id > 0) { <span class="ml-2">(معرف: @AnimatedGif.Id)</span> }
        </MudText>
    </TitleContent>
    <DialogContent>
        @if (AnimatedGif != null)
        {
            <!-- Debug information -->
            <MudAlert Severity="Severity.Info" Class="mb-3">
                <strong>Debug Info:</strong><br />
                ID: @AnimatedGif.Id<br />
                Title: @AnimatedGif.Title<br />
                Local Title: @localTitle<br />
                Description: @AnimatedGif.Description<br />
                ImageUrl: @AnimatedGif.ImageUrl<br />
                DisplayOrder: @AnimatedGif.DisplayOrder<br />
                IsActive: @AnimatedGif.IsActive
            </MudAlert>

            <MudGrid>
                <MudItem xs="12">
                    <MudTextField Value="@localTitle" ValueChanged="@((string val) => localTitle = val)"
                                 Label="العنوان" Required="true" Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField Value="@localDescription" ValueChanged="@((string val) => localDescription = val)"
                                 Label="الوصف" Lines="3" Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField Value="@localImageUrl" ValueChanged="@((string val) => localImageUrl = val)"
                                 Label="رابط الصورة" Required="true" Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudNumericField Value="@localDisplayOrder" ValueChanged="@((int val) => localDisplayOrder = val)"
                                    Label="ترتيب العرض" Min="0" Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudSwitch T="bool" Checked="@localIsActive" CheckedChanged="@((bool val) => localIsActive = val)"
                              Label="نشط" Color="Color.Primary" Immediate="true" />
                </MudItem>
            </MudGrid>

            <MudDivider Class="my-3" />

            <MudText Typo="Typo.subtitle1" Class="mt-4 mb-2">تحميل صورة جديدة</MudText>
            <MudFileUpload T="IReadOnlyList<IBrowserFile>" Accept=".png, .jpg, .gif" FilesChanged="@UploadFile" MaximumFileCount="1">
                <ActivatorContent>
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.CloudUpload">
                        تحميل صورة
                    </MudButton>
                </ActivatorContent>
            </MudFileUpload>

            @if (!string.IsNullOrEmpty(localImageUrl))
            {
                <MudPaper Class="mt-3 pa-2" Elevation="0">
                    <MudText Typo="Typo.subtitle2">معاينة الصورة الحالية:</MudText>
                    <img src="@localImageUrl" style="max-height: 150px; max-width: 100%; object-fit: contain;" alt="@localTitle" />
                </MudPaper>
            }
        }
        else
        {
            <div class="d-flex flex-column align-center justify-center" style="min-height: 200px;">
                <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
                <MudText Class="mt-4">جاري تحميل البيانات...</MudText>
            </div>
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@Cancel" Variant="Variant.Outlined" Color="Color.Default">إلغاء</MudButton>
        <MudButton OnClick="@Submit" Variant="Variant.Filled" Color="Color.Primary"
                  Disabled="@(AnimatedGif == null || AnimatedGif.Id <= 0)">حفظ</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [Parameter]
    public bool IsVisible { get; set; }

    [Parameter]
    public EventCallback<bool> IsVisibleChanged { get; set; }

    [Parameter]
    public AnimatedGifDto AnimatedGif { get; set; }

    [Parameter]
    public EventCallback<AnimatedGifDto> OnSave { get; set; }

    [Parameter]
    public EventCallback OnCancel { get; set; }

    // Local variables to store form values
    private string localTitle;
    private string localDescription;
    private string localImageUrl;
    private int localDisplayOrder;
    private bool localIsActive;

    // This method is called when the component is first initialized
    protected override void OnInitialized()
    {
        InitializeLocalVariables();
    }

    // This method is called when parameters change
    protected override void OnParametersSet()
    {
        InitializeLocalVariables();
    }

    // This method is called when the component is rendered
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            InitializeLocalVariables();
            await InvokeAsync(StateHasChanged);
        }
    }

    // Public method that can be called from the parent component
    public void UpdateFormFields()
    {
        InitializeLocalVariables();
        StateHasChanged();
    }

    private void InitializeLocalVariables()
    {
        // Initialize local variables from the AnimatedGif parameter
        if (AnimatedGif != null)
        {
            localTitle = AnimatedGif.Title ?? "";
            localDescription = AnimatedGif.Description ?? "";
            localImageUrl = AnimatedGif.ImageUrl ?? "";
            localDisplayOrder = AnimatedGif.DisplayOrder;
            localIsActive = AnimatedGif.IsActive;

            Console.WriteLine($"Dialog initialized with ID: {AnimatedGif.Id}");
            Console.WriteLine($"Title: {localTitle}");
            Console.WriteLine($"Description: {localDescription}");
            Console.WriteLine($"ImageUrl: {localImageUrl}");
            Console.WriteLine($"DisplayOrder: {localDisplayOrder}");
            Console.WriteLine($"IsActive: {localIsActive}");
        }
    }

    private async Task Submit()
    {
        if (string.IsNullOrWhiteSpace(localImageUrl))
        {
            Snackbar.Add("رابط الصورة مطلوب", Severity.Error);
            return;
        }

        // Update the AnimatedGif object with local values
        AnimatedGif.Title = localTitle ?? "";
        AnimatedGif.Description = localDescription ?? "";
        AnimatedGif.ImageUrl = localImageUrl;
        AnimatedGif.DisplayOrder = localDisplayOrder;
        AnimatedGif.IsActive = localIsActive;
        AnimatedGif.LastModifiedAt = DateTime.Now;

        // Call the save callback
        await OnSave.InvokeAsync(AnimatedGif);

        // Close the dialog
        await CloseDialog();
    }

    private async Task Cancel()
    {
        await OnCancel.InvokeAsync();
        await CloseDialog();
    }

    private async Task CloseDialog()
    {
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(false);
    }

    private async Task UploadFile(IReadOnlyList<IBrowserFile> files)
    {
        if (files == null || files.Count == 0)
            return;

        try
        {
            var file = files[0];

            // Check file size (max 5MB)
            if (file.Size > 5 * 1024 * 1024)
            {
                Snackbar.Add("حجم الملف يتجاوز الحد الأقصى (5 ميجابايت)", Severity.Error);
                return;
            }

            // Check file type
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
            var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();

            if (!allowedExtensions.Contains(fileExtension))
            {
                Snackbar.Add("يُسمح فقط بملفات الصور (jpg, jpeg, png, gif)", Severity.Error);
                return;
            }

            // Store the old image URL for potential deletion
            string oldImageUrl = localImageUrl;

            // Generate a unique filename
            var fileName = $"{Guid.NewGuid()}{fileExtension}";

            // Create the form data for the file upload
            using var content = new MultipartFormDataContent();

            // Add the old image URL if it exists
            if (!string.IsNullOrEmpty(oldImageUrl))
            {
                content.Add(new StringContent(oldImageUrl), "oldImageUrl");
            }

            // Convert the browser file to a stream content
            using var fileStream = file.OpenReadStream(maxAllowedSize: 5 * 1024 * 1024);
            using var streamContent = new StreamContent(fileStream);

            // Add the file to the form data
            content.Add(streamContent, "file", fileName);

            // Send the file to the server
            var response = await Http.PostAsync("api/upload/animatedgif", content);

            if (response.IsSuccessStatusCode)
            {
                // Get the file path from the response
                var uploadedFilePath = await response.Content.ReadAsStringAsync();

                // Update the image URL in the form
                localImageUrl = uploadedFilePath;

                Snackbar.Add("تم رفع الملف بنجاح", Severity.Success);
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                Snackbar.Add($"خطأ في رفع الملف: {error}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في رفع الملفات: {ex.Message}", Severity.Error);
        }
    }
}
