@page "/admin"

@attribute [Authorize(Roles = "Admin")]
@layout BlogMud.Client.Layout.AdminLayout

@if (isRedirecting)
{
    <MudContainer MaxWidth="MaxWidth.Large" Class="d-flex justify-center align-center" Style="height: 50vh;">
        <div class="text-center">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
            <MudText Typo="Typo.h6" Class="mt-4">جاري التوجيه إلى لوحة التحكم...</MudText>
        </div>
    </MudContainer>
}

@code {
    #region المتغيرات والخصائص

    /// <summary>
    /// متغير لتتبع حالة إعادة التوجيه
    /// </summary>
    private bool isRedirecting = true;

    /// <summary>
    /// متغير لمنع إعادة التوجيه المتعددة
    /// </summary>
    private bool hasNavigated = false;

    #endregion

    #region دوال دورة الحياة

    /// <summary>
    /// تهيئة مكونات الصفحة عند بدء التشغيل
    /// </summary>
    /// <remarks>
    /// تقوم هذه الدالة بإعداد الصفحة للتوجيه إلى لوحة التحكم
    /// </remarks>
    protected override void OnInitialized()
    {
        // تعيين حالة التوجيه
        isRedirecting = true;
    }

    /// <summary>
    /// تنفيذ إعادة التوجيه بعد اكتمال عرض المكون
    /// </summary>
    /// <param name="firstRender">ما إذا كان هذا أول عرض للمكون</param>
    /// <returns>مهمة غير متزامنة</returns>
    /// <remarks>
    /// استخدام OnAfterRenderAsync يضمن أن المكون قد تم عرضه بالكامل
    /// قبل محاولة التنقل، مما يقلل من مشاكل التوقيت خاصة على الأجهزة المحمولة
    /// </remarks>
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && !hasNavigated)
        {
            try
            {
                hasNavigated = true;

                // إضافة تأخير قصير للتأكد من اكتمال التهيئة على الأجهزة المحمولة
                await Task.Delay(100);

                // التحقق من توفر مدير التنقل
                if (_Navigation != null)
                {
                    // إعادة توجيه إلى صفحة لوحة التحكم
                    _Navigation.NavigateTo("/admin/dashboard", forceLoad: false);
                }
                else
                {
                    // في حالة عدم توفر مدير التنقل، محاولة مرة أخرى بعد تأخير أطول
                    await Task.Delay(500);
                    _Navigation?.NavigateTo("/admin/dashboard", forceLoad: true);
                }
            }
            catch (Exception)
            {
                // في حالة حدوث خطأ، محاولة التوجيه مع إعادة التحميل الكامل
                try
                {
                    await Task.Delay(200);
                    _Navigation?.NavigateTo("/admin/dashboard", forceLoad: true);
                }
                catch
                {
                    // كحل أخير، إعادة تعيين حالة التوجيه للسماح بالمحاولة مرة أخرى
                    isRedirecting = false;
                    hasNavigated = false;
                    await InvokeAsync(StateHasChanged);
                }
            }
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    #endregion
}
