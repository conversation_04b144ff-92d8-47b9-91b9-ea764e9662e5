﻿@page "/admin/aboutus"
@attribute [Authorize(Roles = "Admin")]
@layout BlogMud.Client.Layout.AdminLayout

<PageTitle>إدارة صفحة "من نحن"</PageTitle>

<AboutUsCSS />

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-6">
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h4">إدارة صفحة "من نحن"</MudText>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                   OnClick="@(() => OpenAboutUsDialog(null))">
            إضافة محتوى جديد
        </MudButton>
    </div>

    @if (_loading)
    {
        <div class="d-flex justify-center my-8">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
        </div>
    }
    else if (_aboutUsContent.Count == 0)
    {
        <MudAlert Severity="Severity.Info" Class="my-4">لا يوجد محتوى متاح. أضف محتوى جديد للبدء.</MudAlert>
    }
    else
    {
        <!-- الجدول العادي للشاشات الكبيرة -->
        <div class="regular-table">
            <MudTable Items="@_aboutUsContent" Hover="true" Striped="true" Bordered="true" Class="mb-8">
                <HeaderContent>
                    <MudTh>ت</MudTh>
                    <MudTh>شعار الشركة</MudTh>
                    <MudTh>وصف الشركة</MudTh>
                    <MudTh>قدرات الشركة</MudTh>
                    <MudTh>عناصر الطاقة الإنتاجية</MudTh>
                    <MudTh>الترتيب</MudTh>
                    <MudTh>نشط</MudTh>
                    <MudTh>تاريخ الإنشاء</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="ت">@(_aboutUsContent.IndexOf(context) + 1)</MudTd>
                    <MudTd DataLabel="شعار الشركة">
                        @if (!string.IsNullOrEmpty(context.CompanyLogoUrl))
                        {
                            <MudImage Src="@context.CompanyLogoUrl" Alt="شعار الشركة"
                                     Width="50" Height="50" ObjectFit="ObjectFit.Cover" Class="rounded" />
                        }
                        else
                        {
                            <MudText Typo="Typo.caption" Color="Color.Secondary">لا توجد صورة</MudText>
                        }
                    </MudTd>
                    <MudTd DataLabel="وصف الشركة">
                        <MudText Typo="Typo.body2" Style="max-width: 200px;">
                            @(context.CompanyDescription?.Length > 50 ?
                              context.CompanyDescription.Substring(0, 50) + "..." :
                              context.CompanyDescription)
                        </MudText>
                    </MudTd>
                    <MudTd DataLabel="قدرات الشركة">
                        <MudText Typo="Typo.body2" Style="max-width: 200px;">
                            @(context.CompanyCapabilities?.Length > 50 ?
                              context.CompanyCapabilities.Substring(0, 50) + "..." :
                              context.CompanyCapabilities)
                        </MudText>
                    </MudTd>
                    <MudTd DataLabel="عناصر الطاقة الإنتاجية">
                        <MudText Typo="Typo.body2" Style="max-width: 150px;">
                            @(context.ProductionCapacityItems?.Count > 0 ?
                              $"{context.ProductionCapacityItems.Count} عنصر" :
                              "لا توجد عناصر")
                        </MudText>
                    </MudTd>
                    <MudTd DataLabel="الترتيب">
                        <MudChip T="int" Size="Size.Small" Color="Color.Info">
                            @context.DisplayOrder
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="نشط">
                        <MudIcon Icon="@(context.IsActive ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)"
                                 Color="@(context.IsActive ? Color.Success : Color.Error)" />
                    </MudTd>
                    <MudTd DataLabel="تاريخ الإنشاء">@context.CreatedAt.ToString("yyyy-MM-dd")</MudTd>
                    <MudTd DataLabel="الإجراءات">
                        <MudIconButton Icon="@Icons.Material.Filled.Visibility" Color="Color.Info"
                                       OnClick="@(() => ViewAboutUsDetails(context))" />
                        <MudIconButton Icon="@Icons.Material.Filled.Edit" Color="Color.Primary"
                                       OnClick="@(() => OpenAboutUsDialog(context))" />
                        <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error"
                                       OnClick="@(() => ConfirmDelete(context))" />
                    </MudTd>
                </RowTemplate>
                <PagerContent>
                    <MudTablePager PageSizeOptions="new int[] { 5, 10, 15 }" />
                </PagerContent>
            </MudTable>
        </div>

        <!-- تخطيط البطاقات المصغرة للشاشات الصغيرة (التابلت والهاتف) -->
        <div class="mobile-cards">
            @foreach (var content in _aboutUsContent)
            {
                <MudCard Class="mobile-card" Elevation="1">
                    <MudCardContent Class="pa-2">
                        <!-- رقم المحتوى والإجراءات -->
                        <div class="d-flex justify-space-between align-center mb-2">
                            <MudChip T="string" Color="Color.Primary" Size="Size.Small" Class="content-number">
                                @(_aboutUsContent.IndexOf(content) + 1)
                            </MudChip>
                            <div class="content-actions">
                                <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                             Color="Color.Info"
                                             Size="Size.Small"
                                             OnClick="@(() => ViewAboutUsDetails(content))" />
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                             Color="Color.Primary"
                                             Size="Size.Small"
                                             OnClick="@(() => OpenAboutUsDialog(content))" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                             Color="Color.Error"
                                             Size="Size.Small"
                                             OnClick="@(() => ConfirmDelete(content))" />
                            </div>
                        </div>

                        <!-- شعار الشركة -->
                        <div class="content-logo mb-2">
                            @if (!string.IsNullOrEmpty(content.CompanyLogoUrl))
                            {
                                <MudImage Src="@content.CompanyLogoUrl" Alt="شعار الشركة"
                                         Width="60" Height="60" ObjectFit="ObjectFit.Cover" Class="rounded" />
                            }
                            else
                            {
                                <MudIcon Icon="@Icons.Material.Filled.Business" Size="Size.Large" Color="Color.Secondary" />
                            }
                        </div>

                        <!-- معلومات المحتوى -->
                        <div class="content-info">
                            <div class="info-row">
                                <MudIcon Icon="@Icons.Material.Filled.Description" Size="Size.Small" Class="info-icon" />
                                <span class="info-label">الوصف:</span>
                                <span class="info-value">
                                    @(content.CompanyDescription?.Length > 30 ?
                                      content.CompanyDescription.Substring(0, 30) + "..." :
                                      content.CompanyDescription ?? "غير محدد")
                                </span>
                            </div>
                            <div class="info-row">
                                <MudIcon Icon="@Icons.Material.Filled.Star" Size="Size.Small" Class="info-icon" />
                                <span class="info-label">القدرات:</span>
                                <span class="info-value">
                                    @(content.CompanyCapabilities?.Length > 30 ?
                                      content.CompanyCapabilities.Substring(0, 30) + "..." :
                                      content.CompanyCapabilities ?? "غير محدد")
                                </span>
                            </div>
                            <div class="info-row">
                                <MudIcon Icon="@Icons.Material.Filled.Factory" Size="Size.Small" Class="info-icon" />
                                <span class="info-label">العناصر:</span>
                                <span class="info-value">
                                    @(content.ProductionCapacityItems?.Count > 0 ?
                                      $"{content.ProductionCapacityItems.Count} عنصر" :
                                      "لا توجد عناصر")
                                </span>
                            </div>
                            <div class="info-row">
                                <MudIcon Icon="@Icons.Material.Filled.Sort" Size="Size.Small" Class="info-icon" />
                                <span class="info-label">الترتيب:</span>
                                <span class="info-value">@content.DisplayOrder</span>
                            </div>
                            <div class="info-row">
                                <MudIcon Icon="@(content.IsActive ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)"
                                         Size="Size.Small" Class="info-icon"
                                         Color="@(content.IsActive ? Color.Success : Color.Error)" />
                                <span class="info-label">الحالة:</span>
                                <span class="info-value">@(content.IsActive ? "نشط" : "غير نشط")</span>
                            </div>
                            <div class="info-row">
                                <MudIcon Icon="@Icons.Material.Filled.DateRange" Size="Size.Small" Class="info-icon" />
                                <span class="info-label">تاريخ الإنشاء:</span>
                                <span class="info-value">@content.CreatedAt.ToString("yyyy-MM-dd")</span>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            }
        </div>
    }
</MudContainer>
