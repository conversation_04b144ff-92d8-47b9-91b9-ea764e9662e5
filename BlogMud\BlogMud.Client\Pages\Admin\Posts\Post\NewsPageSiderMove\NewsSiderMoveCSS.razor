<style>
    /* NewsSiderMove Component - Beautiful & Elegant Styles */

    /* Container Styling */
    .news-slider-container {
        margin-top: 2rem;
        padding: 0 1rem;
    }

    /* Page Header Section */
    .page-header-section {
        margin-bottom: 2rem;
    }

    .page-header-card {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        border-radius: 20px;
        padding: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(149, 55, 53, 0.3);
    }

    .page-header-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .header-content {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 1;
    }

    .header-icon-wrapper {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .header-icon {
        font-size: 2rem;
        color: white;
    }

    .header-title {
        color: white;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .header-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 400;
        line-height: 1.6;
    }

    .header-stats {
        display: flex;
        gap: 2rem;
        position: relative;
        z-index: 1;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        padding: 1rem 1.5rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
    }

    .stat-icon {
        font-size: 1.5rem;
        color: rgba(255, 255, 255, 0.9);
    }

    .stat-icon.active {
        color: #4caf50;
    }

    .stat-number {
        color: white;
        font-weight: 700;
        margin: 0;
    }

    .stat-label {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
    }

    /* Preview Section */
    .preview-section {
        margin-bottom: 2rem;
    }

    .preview-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .preview-card:hover {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(149, 55, 53, 0.1);
    }

    .preview-title-wrapper {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .preview-icon {
        color: #953735;
        font-size: 1.5rem;
    }

    .preview-title {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
    }

    .carousel-controls {
        display: flex;
        gap: 0.5rem;
    }

    .carousel-container {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        background: #f8f9fa;
        position: relative;
    }

    .carousel-container .mud-carousel {
        height: 400px;
        width: 100%;
    }

    .enhanced-carousel {
        height: 400px;
        border-radius: 16px;
        width: 100%;
    }

    .enhanced-carousel .mud-carousel-item {
        height: 400px;
        width: 100%;
    }

    .carousel-item {
        position: relative;
        height: 400px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .slide-content {
        position: relative;
        height: 100%;
        width: 100%;
        display: block;
    }

    .slide-image-wrapper {
        position: relative;
        height: 100%;
        width: 100%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .slide-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        transition: transform 0.3s ease;
    }

    .slide-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 40%;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        pointer-events: none;
        z-index: 1;
    }

    .slide-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 1.5rem 2rem;
        color: white;
        text-align: center;
        z-index: 2;
    }

    .slide-title {
        color: white;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    .slide-description {
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .slide-link-btn {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        font-weight: 600;
    }

    .slide-link-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }

    .no-active-slides {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .no-slides-icon {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }

    .no-slides-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .no-slides-subtitle {
        color: #6c757d;
    }

    /* Controls Section */
    .controls-section {
        margin-bottom: 2rem;
    }

    .controls-card {
        background: white;
        border-radius: 20px;
        padding: 1.5rem 2rem;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .controls-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 2rem;
    }

    .controls-left {
        flex: 0 0 auto;
    }

    .controls-right {
        flex: 1;
        display: flex;
        justify-content: flex-end;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
    }

    .primary-action-btn {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: none;
        box-shadow: 0 4px 16px rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .primary-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 24px rgba(149, 55, 53, 0.4);
    }

    .secondary-action-btn {
        border: 2px solid #953735;
        color: #953735;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: none;
        transition: all 0.3s ease;
    }

    .secondary-action-btn:hover {
        background: rgba(149, 55, 53, 0.1);
        transform: translateY(-2px);
    }

    .search-wrapper {
        position: relative;
        max-width: 400px;
        width: 100%;
    }

    .enhanced-search-field {
        border-radius: 12px;
    }

    .enhanced-search-field .mud-input-control {
        border-radius: 12px;
        border: 2px solid rgba(149, 55, 53, 0.2);
        transition: all 0.3s ease;
    }

    .enhanced-search-field .mud-input-control:hover {
        border-color: rgba(149, 55, 53, 0.4);
    }

    .enhanced-search-field .mud-input-control.mud-input-control-focused {
        border-color: #953735;
        box-shadow: 0 0 0 3px rgba(149, 55, 53, 0.1);
    }

    .search-clear-btn {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
    }

    .button-text-short {
        display: none;
    }

    .button-text-full {
        display: inline;
    }

    /* Loading Section */
    .loading-section {
        margin: 3rem 0;
    }

    .loading-card {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        text-align: center;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .loading-text {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
    }

    .loading-subtitle {
        color: #6c757d;
        margin: 0;
    }

    /* Empty State Section */
    .empty-state-section {
        margin: 3rem 0;
    }

    .empty-state-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 20px;
        padding: 4rem 2rem;
        text-align: center;
        border: 2px dashed rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .empty-state-card:hover {
        border-color: rgba(149, 55, 53, 0.5);
        transform: translateY(-2px);
    }

    .empty-state-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .empty-state-icon-wrapper {
        background: rgba(149, 55, 53, 0.1);
        border-radius: 50%;
        padding: 2rem;
        margin-bottom: 1rem;
    }

    .empty-state-icon {
        font-size: 4rem;
        color: #953735;
    }

    .empty-state-title {
        color: #2c3e50;
        font-weight: 700;
        margin: 0;
    }

    .empty-state-subtitle {
        color: #6c757d;
        max-width: 400px;
        line-height: 1.6;
        margin: 0;
    }

    .empty-state-action-btn {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        border-radius: 12px;
        padding: 1rem 2rem;
        font-weight: 600;
        text-transform: none;
        box-shadow: 0 4px 16px rgba(149, 55, 53, 0.3);
        transition: all 0.3s ease;
    }

    .empty-state-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 24px rgba(149, 55, 53, 0.4);
    }

    /* Table Enhancements */
    .regular-table {
        display: block;
    }

    .regular-table .mud-table {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        border: 1px solid rgba(149, 55, 53, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .regular-table .mud-table-head {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
    }

    .regular-table .mud-table-head .mud-table-cell {
        color: white;
        font-weight: 600;
        border-bottom: none;
    }

    .regular-table .mud-table-row:hover {
        background: rgba(149, 55, 53, 0.05);
    }

    .mud-table-cell:first-child {
        font-weight: 600;
        color: #953735;
        text-align: center;
        width: 60px;
        min-width: 60px;
    }

    .mobile-cards {
        display: none;
    }

    /* Enhanced Mobile Cards - Redesigned */
    .mobile-cards {
        display: none;
        gap: 1.5rem;
    }

    .mobile-card {
        background: #ffffff !important;
        border-radius: 20px;
        transition: all 0.3s ease;
        border: 1px solid rgba(149, 55, 53, 0.15);
        box-shadow: 0 4px 20px rgba(149, 55, 53, 0.1);
        overflow: hidden;
        position: relative;
        margin-bottom: 1.5rem;
    }

    .mobile-card .mud-card {
        background: #ffffff !important;
    }

    .mobile-card .mud-card-content {
        background: #ffffff !important;
    }

    .mobile-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        z-index: 1;
    }

    .mobile-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 32px rgba(149, 55, 53, 0.2);
        border-color: rgba(149, 55, 53, 0.3);
    }

    .card-content {
        padding: 1.5rem !important;
        background: #ffffff !important;
        position: relative;
        z-index: 2;
    }

    /* Card Header */
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(149, 55, 53, 0.15);
        background: #ffffff;
    }

    .card-number-wrapper {
        display: flex;
        align-items: center;
    }

    .slide-number {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        color: white;
        font-weight: 700;
        min-width: 45px;
        height: 45px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(149, 55, 53, 0.3);
        font-size: 1.1rem;
    }

    .card-actions {
        display: flex;
        gap: 0.5rem;
    }

    .action-btn {
        border-radius: 10px;
        transition: all 0.3s ease;
        width: 40px;
        height: 40px;
    }

    .action-btn:hover {
        transform: scale(1.1);
    }

    .edit-btn:hover {
        background: rgba(25, 118, 210, 0.1);
    }

    .delete-btn:hover {
        background: rgba(244, 67, 54, 0.1);
    }

    /* Card Main Content */
    .card-main-content {
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
        background: #ffffff;
    }

    /* Title Section */
    .card-title-section {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 1rem;
    }

    .card-title {
        color: #2c3e50;
        font-weight: 700;
        line-height: 1.4;
        word-break: break-word;
        margin: 0;
        flex: 1;
    }

    .status-chip {
        flex-shrink: 0;
        font-weight: 600;
    }

    /* Description */
    .card-description {
        margin: -0.5rem 0 0 0;
    }

    .description-text {
        color: #6c757d;
        line-height: 1.6;
        margin: 0;
    }

    /* Info Grid */
    .card-info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1rem;
        border: 1px solid rgba(149, 55, 53, 0.1);
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        direction: rtl;
    }

    .info-icon-wrapper {
        background: rgba(149, 55, 53, 0.15);
        border-radius: 8px;
        padding: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .info-icon {
        color: #953735;
        font-size: 1rem;
    }

    .info-content {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        flex: 1;
        text-align: right;
    }

    .info-label {
        color: #6c757d;
        font-weight: 500;
        margin: 0;
        font-size: 0.75rem;
    }

    .info-value {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
        font-size: 0.9rem;
    }

    /* Images Section */
    .card-images-section {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1rem;
        border: 1px solid rgba(149, 55, 53, 0.1);
    }

    .images-label {
        color: #6c757d;
        font-weight: 600;
        margin: 0 0 0.75rem 0;
        display: block;
    }

    .images-preview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
        gap: 0.75rem;
        max-width: 100%;
    }

    .preview-image-wrapper {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        overflow: hidden;
        border: 2px solid rgba(149, 55, 53, 0.2);
        background: #ffffff;
        transition: all 0.3s ease;
        position: relative;
    }

    .preview-image-wrapper:hover {
        transform: scale(1.05);
        border-color: #953735;
        box-shadow: 0 4px 12px rgba(149, 55, 53, 0.3);
    }

    .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .preview-image-wrapper:hover .preview-image {
        transform: scale(1.1);
    }

    .more-images-indicator {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        border-color: #953735;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .more-images-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
        color: white;
    }

    .more-icon {
        font-size: 1.2rem;
        color: white;
    }

    .more-text {
        font-size: 0.7rem;
        font-weight: 700;
        color: white;
        margin: 0;
    }

    /* Responsive Design - Mobile First */

    /* عرض البطاقات في الشاشات الصغيرة */
    @@media (max-width: 768px) {
        .regular-table {
            display: none;
        }

        .mobile-cards {
            display: block;
        }

        .page-header-card {
            padding: 1.5rem;
            border-radius: 16px;
        }

        .header-content {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .header-stats {
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .stat-item {
            flex: 1;
            min-width: 140px;
        }

        .preview-card,
        .controls-card {
            padding: 1.5rem;
            border-radius: 16px;
        }

        .enhanced-carousel {
            height: 300px;
        }

        .enhanced-carousel .mud-carousel {
            height: 300px;
        }

        .carousel-item {
            height: 300px;
        }

        .slide-image-wrapper {
            height: 300px;
        }

        .slide-image {
            max-height: 300px;
        }

        .controls-container {
            flex-direction: column;
            gap: 1.5rem;
        }

        .controls-left,
        .controls-right {
            width: 100%;
        }

        .action-buttons {
            justify-content: center;
        }

        .search-wrapper {
            max-width: none;
        }
    }

    /* تحسينات للشاشات الصغيرة جداً */
    @@media (max-width: 480px) {
        .news-slider-container {
            padding: 0 0.5rem;
        }

        .page-header-card {
            padding: 1rem;
            margin: 0 0.5rem 1.5rem;
        }

        .header-title {
            font-size: 1.5rem;
        }

        .stat-item {
            padding: 0.75rem 1rem;
            min-width: 120px;
        }

        .button-text-short {
            display: inline;
        }

        .button-text-full {
            display: none;
        }

        .action-buttons {
            flex-direction: column;
            width: 100%;
        }

        .primary-action-btn,
        .secondary-action-btn {
            width: 100%;
            justify-content: center;
        }

        .card-content {
            padding: 1rem !important;
        }

        .card-header {
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
        }

        .slide-number {
            min-width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .action-btn {
            width: 36px;
            height: 36px;
        }

        .card-title {
            font-size: 1.1rem;
        }

        .card-info-grid {
            grid-template-columns: 1fr;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .info-item {
            gap: 0.5rem;
        }

        .info-icon-wrapper {
            padding: 0.4rem;
        }

        .images-preview-grid {
            grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
            gap: 0.5rem;
        }

        .preview-image-wrapper {
            width: 50px;
            height: 50px;
        }

        .card-images-section {
            padding: 0.75rem;
        }

        .enhanced-carousel {
            height: 250px;
        }

        .enhanced-carousel .mud-carousel {
            height: 250px;
        }

        .carousel-item {
            height: 250px;
        }

        .slide-image-wrapper {
            height: 250px;
        }

        .slide-image {
            max-height: 250px;
        }

        .slide-info {
            padding: 1rem;
        }

        .preview-card,
        .controls-card,
        .loading-card,
        .empty-state-card {
            margin: 0 0.5rem 1.5rem;
        }
    }

    /* تحسينات للتابلت */
    @@media (min-width: 769px) and (max-width: 1024px) {
        .regular-table {
            display: none;
        }

        .mobile-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 1.5rem;
        }

        .card-info-grid {
            grid-template-columns: 1fr 1fr;
        }

        .images-preview-grid {
            grid-template-columns: repeat(auto-fit, minmax(55px, 1fr));
        }

        .preview-image-wrapper {
            width: 55px;
            height: 55px;
        }

        .controls-container {
            gap: 1.5rem;
        }

        .search-wrapper {
            max-width: 350px;
        }

        .enhanced-carousel {
            height: 350px;
        }

        .enhanced-carousel .mud-carousel {
            height: 350px;
        }

        .carousel-item {
            height: 350px;
        }

        .slide-image-wrapper {
            height: 350px;
        }

        .slide-image {
            max-height: 350px;
        }

        .page-header-card {
            padding: 2rem;
        }

        .header-stats {
            gap: 1.5rem;
        }

        .stat-item {
            padding: 1rem 1.5rem;
        }
    }

    /* Animations and Transitions */
    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @@keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }

    .page-header-section {
        animation: fadeInUp 0.6s ease-out;
    }

    .preview-section {
        animation: fadeInUp 0.6s ease-out 0.1s both;
    }

    .controls-section {
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }

    .mobile-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .mobile-card:nth-child(2n) {
        animation-delay: 0.1s;
    }

    .mobile-card:nth-child(2n+1) {
        animation-delay: 0.2s;
    }

    /* Additional Card Enhancements */
    .card-main-content > * {
        animation: slideInRight 0.4s ease-out;
    }

    .card-main-content > *:nth-child(2) {
        animation-delay: 0.1s;
    }

    .card-main-content > *:nth-child(3) {
        animation-delay: 0.2s;
    }

    .card-main-content > *:nth-child(4) {
        animation-delay: 0.3s;
    }

    .stat-item:hover {
        animation: pulse 0.6s ease-in-out;
    }

    /* دعم الوضع المظلم المحسن - اختياري فقط */
    .dark-theme,
    [data-theme="dark"] {
        .page-header-card {
            background: linear-gradient(135deg, #953735 0%, #6d2a28 100%);
        }

        .preview-card,
        .controls-card,
        .loading-card {
            background: #1e1e1e !important;
            border-color: rgba(149, 55, 53, 0.3);
        }

        .mobile-card,
        .mobile-card .mud-card,
        .mobile-card .mud-card-content,
        .mobile-card .card-content,
        .mobile-card .card-header,
        .mobile-card .card-main-content {
            background: #1e1e1e !important;
            border-color: rgba(149, 55, 53, 0.3);
        }

        .card-header {
            border-bottom-color: rgba(149, 55, 53, 0.2);
        }

        .card-title {
            color: #ffffff !important;
        }

        .description-text {
            color: #b0b0b0 !important;
        }

        .card-info-grid {
            background: rgba(149, 55, 53, 0.1) !important;
            border-color: rgba(149, 55, 53, 0.2);
        }

        .info-icon-wrapper {
            background: rgba(149, 55, 53, 0.2) !important;
        }

        .info-label {
            color: #b0b0b0 !important;
        }

        .info-value {
            color: #ffffff !important;
        }

        .card-images-section {
            background: rgba(149, 55, 53, 0.1) !important;
            border-color: rgba(149, 55, 53, 0.2);
        }

        .images-label {
            color: #b0b0b0 !important;
        }

        .preview-image-wrapper {
            border-color: rgba(149, 55, 53, 0.3);
            background: #2a2a2a !important;
        }

        .empty-state-card {
            background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%) !important;
            border-color: rgba(149, 55, 53, 0.3);
        }

        .empty-state-title {
            color: #ffffff !important;
        }

        .empty-state-subtitle {
            color: #b0b0b0 !important;
        }
    }

    /* تحسينات الحركة */
    @@media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }

        .mobile-card:hover,
        .preview-card:hover,
        .controls-card:hover,
        .primary-action-btn:hover,
        .secondary-action-btn:hover,
        .empty-state-action-btn:hover {
            transform: none;
        }
    }

    /* تحسينات للطباعة */
    @@media print {
        .mobile-cards {
            display: block !important;
        }

        .regular-table {
            display: none !important;
        }

        .page-header-card {
            background: #953735 !important;
            color: white !important;
            box-shadow: none !important;
        }

        .mobile-card {
            break-inside: avoid;
            box-shadow: none !important;
            border: 2px solid #953735 !important;
            margin-bottom: 1rem;
            background: white !important;
        }

        .card-actions,
        .carousel-controls,
        .action-buttons,
        .search-wrapper {
            display: none !important;
        }

        .card-images-section {
            display: none !important;
        }

        .mobile-card,
        .mobile-card .mud-card,
        .mobile-card .mud-card-content,
        .mobile-card .card-content,
        .mobile-card .card-header,
        .mobile-card .card-main-content {
            background: white !important;
        }

        .card-info-grid {
            background: #f8f9fa !important;
            border: 1px solid #953735 !important;
        }

        .card-images-section {
            background: #f8f9fa !important;
            border: 1px solid #953735 !important;
        }

        .info-icon-wrapper {
            background: #953735 !important;
        }

        .info-icon {
            color: white !important;
        }

        .preview-card,
        .controls-card {
            box-shadow: none !important;
            border: 1px solid #953735 !important;
        }
    }

    /* تحسينات إضافية للأداء */
    .enhanced-carousel,
    .slide-image,
    .mobile-card,
    .preview-card,
    .controls-card {
        will-change: transform;
    }

    /* تحسين التمرير */
    .news-slider-container {
        scroll-behavior: smooth;
    }

    /* تحسينات إضافية للكاروسيل */
    .enhanced-carousel .mud-carousel-item {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .enhanced-carousel .mud-carousel-item > div {
        width: 100% !important;
        height: 100% !important;
    }

    /* إصلاح مشاكل عرض الصور - ملء كامل المساحة */
    .slide-image {
        display: block !important;
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
    }

    /* إصلاح ألوان البطاقات */
    .mobile-card,
    .mobile-card *,
    .mobile-card .mud-card,
    .mobile-card .mud-card-content,
    .mobile-card .card-content,
    .mobile-card .card-header,
    .mobile-card .card-main-content {
        background-color: #ffffff !important;
        color: inherit !important;
    }

    /* تأكيد الألوان الصحيحة للنصوص */
    .mobile-card .card-title {
        color: #2c3e50 !important;
    }

    .mobile-card .description-text {
        color: #6c757d !important;
    }

    .mobile-card .info-label {
        color: #6c757d !important;
    }

    .mobile-card .info-value {
        color: #2c3e50 !important;
    }

    .mobile-card .images-label {
        color: #6c757d !important;
    }

    /* تحسين التركيز للوصولية */
    .primary-action-btn:focus,
    .secondary-action-btn:focus,
    .empty-state-action-btn:focus {
        outline: 3px solid rgba(149, 55, 53, 0.5);
        outline-offset: 2px;
    }

    .enhanced-search-field:focus-within {
        box-shadow: 0 0 0 3px rgba(149, 55, 53, 0.2);
    }
    /* ------------------------------------------------------------------ */
    /* Responsive Carousel Enhancements (MudBlazor)                      */
    /* ------------------------------------------------------------------ */

    /* Make sure navigation arrows are always visible and accessible */
    .carousel-container .mud-carousel-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10; /* keep above slides */
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        min-width: 48px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.45);
        color: #fff;
        transition: background 0.3s ease;
    }

    .carousel-container .mud-carousel-button:hover {
        background: rgba(0, 0, 0, 0.65);
    }

    /* Side-specific positioning – support multiple MudBlazor versions */
    .carousel-container .mud-carousel-button[data-side="Prev"],
    .carousel-container .mud-carousel-prev {
        left: 8px;
    }

    .carousel-container .mud-carousel-button[data-side="Next"],
    .carousel-container .mud-carousel-next {
        right: 8px;
    }

    /* RTL support: swap arrow positions for right-to-left layouts */
    [dir="rtl"] .carousel-container .mud-carousel-button[data-side="Prev"],
    [dir="rtl"] .carousel-container .mud-carousel-prev {
        right: 8px;
        left: auto;
    }

    [dir="rtl"] .carousel-container .mud-carousel-button[data-side="Next"],
    [dir="rtl"] .carousel-container .mud-carousel-next {
        left: 8px;
        right: auto;
    }

    /* Smooth arrow icon scaling */
    .carousel-container .mud-carousel-button svg {
        font-size: 24px;
    }

    /* Mobile optimisation */
    @@media (max-width: 768px) {
        /* Reduce overall carousel height for small screens */
        .carousel-container,
        .enhanced-carousel,
        .carousel-container .mud-carousel {
            height: 260px;
        }

        /* Enhanced arrow buttons on mobile - more visible */
        .carousel-container .mud-carousel-button {
            width: 44px !important;
            height: 44px !important;
            min-width: 44px !important;
            background: rgba(149, 55, 53, 0.8) !important;
            border: 2px solid rgba(255, 255, 255, 0.9) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            z-index: 1000 !important;
        }

        .carousel-container .mud-carousel-button:hover {
            background: rgba(149, 55, 53, 0.95) !important;
            transform: scale(1.1) !important;
        }

        .carousel-container .mud-carousel-button svg {
            font-size: 22px !important;
            color: white !important;
        }

        /* Ensure left arrow is visible */
        .carousel-container .mud-carousel-button[data-side="Prev"],
        .carousel-container .mud-carousel-prev {
            left: 12px !important;
            position: absolute !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
        }

        .carousel-container .mud-carousel-button[data-side="Next"],
        .carousel-container .mud-carousel-next {
            right: 12px !important;
            position: absolute !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
        }

        /* RTL support for mobile */
        [dir="rtl"] .carousel-container .mud-carousel-button[data-side="Prev"],
        [dir="rtl"] .carousel-container .mud-carousel-prev {
            right: 12px !important;
            left: auto !important;
        }

        [dir="rtl"] .carousel-container .mud-carousel-button[data-side="Next"],
        [dir="rtl"] .carousel-container .mud-carousel-next {
            left: 12px !important;
            right: auto !important;
        }
    }

    /* Extra small screens - enhanced arrow visibility */
    @@media (max-width: 480px) {
        .carousel-container .mud-carousel-button {
            width: 40px !important;
            height: 40px !important;
            min-width: 40px !important;
            background: rgba(149, 55, 53, 0.9) !important;
            border: 2px solid rgba(255, 255, 255, 1) !important;
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.4) !important;
        }

        .carousel-container .mud-carousel-button svg {
            font-size: 20px !important;
        }

        .carousel-container .mud-carousel-button[data-side="Prev"],
        .carousel-container .mud-carousel-prev {
            left: 8px !important;
        }

        .carousel-container .mud-carousel-button[data-side="Next"],
        .carousel-container .mud-carousel-next {
            right: 8px !important;
        }

        /* RTL support for extra small screens */
        [dir="rtl"] .carousel-container .mud-carousel-button[data-side="Prev"],
        [dir="rtl"] .carousel-container .mud-carousel-prev {
            right: 8px !important;
            left: auto !important;
        }

        [dir="rtl"] .carousel-container .mud-carousel-button[data-side="Next"],
        [dir="rtl"] .carousel-container .mud-carousel-next {
            left: 8px !important;
            right: auto !important;
        }
    }

    /* Force arrow visibility - fallback styles */
    .carousel-container .mud-carousel-button,
    .carousel-container .mud-carousel-prev,
    .carousel-container .mud-carousel-next {
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    /* Additional MudBlazor carousel arrow selectors */
    .enhanced-carousel .mud-button-root[aria-label*="Previous"],
    .enhanced-carousel .mud-button-root[aria-label*="Next"],
    .enhanced-carousel .mud-icon-button[aria-label*="Previous"],
    .enhanced-carousel .mud-icon-button[aria-label*="Next"] {
        position: absolute !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        z-index: 1000 !important;
        background: rgba(149, 55, 53, 0.8) !important;
        color: white !important;
        border: 2px solid rgba(255, 255, 255, 0.9) !important;
        border-radius: 50% !important;
        width: 48px !important;
        height: 48px !important;
        min-width: 48px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
    }

    .enhanced-carousel .mud-button-root[aria-label*="Previous"],
    .enhanced-carousel .mud-icon-button[aria-label*="Previous"] {
        left: 8px !important;
    }

    .enhanced-carousel .mud-button-root[aria-label*="Next"],
    .enhanced-carousel .mud-icon-button[aria-label*="Next"] {
        right: 8px !important;
    }

    /* Mobile specific arrow styles */
    @@media (max-width: 768px) {
        .enhanced-carousel .mud-button-root[aria-label*="Previous"],
        .enhanced-carousel .mud-button-root[aria-label*="Next"],
        .enhanced-carousel .mud-icon-button[aria-label*="Previous"],
        .enhanced-carousel .mud-icon-button[aria-label*="Next"] {
            width: 44px !important;
            height: 44px !important;
            min-width: 44px !important;
        }
    }

    @@media (max-width: 480px) {
        .enhanced-carousel .mud-button-root[aria-label*="Previous"],
        .enhanced-carousel .mud-button-root[aria-label*="Next"],
        .enhanced-carousel .mud-icon-button[aria-label*="Previous"],
        .enhanced-carousel .mud-icon-button[aria-label*="Next"] {
            width: 40px !important;
            height: 40px !important;
            min-width: 40px !important;
        }
    }

    /* Image Modal Styles */
    .image-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
        animation: fadeIn 0.3s ease-in-out;
        overflow: auto;
        padding: 20px;
        box-sizing: border-box;
    }

    .image-modal-close {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        z-index: 10000;
    }

    .image-modal-close:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    .image-modal-close .mud-icon-root {
        color: white;
        font-size: 1.5rem;
    }

    .image-modal-content-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        max-width: 90%;
        max-height: 90%;
    }

    .image-modal-content {
        max-width: 100%;
        max-height: calc(100% - 60px);
        object-fit: contain;
        border-radius: 12px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        animation: zoomIn 0.3s ease-in-out;
    }

    .image-modal-title {
        color: white;
        background: rgba(0, 0, 0, 0.7);
        padding: 10px 20px;
        border-radius: 20px;
        margin-top: 15px;
        font-size: 1.1rem;
        font-weight: 500;
        text-align: center;
        backdrop-filter: blur(10px);
        max-width: 100%;
        word-wrap: break-word;
    }

    @@keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @@keyframes zoomIn {
        from {
            transform: scale(0.8);
            opacity: 0;
        }
        to {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* Cursor pointer for images that can be clicked */
    .slide-image[title*="انقر لعرض"],
    .preview-image[title*="انقر لعرض"] {
        cursor: pointer;
    }

    /* AOS Animation Support for NewsSiderMove */
    [data-aos] {
        pointer-events: none;
    }

    [data-aos].aos-animate {
        pointer-events: auto;
    }

    /* Enhanced AOS animations for specific elements */
    .page-header-card[data-aos],
    .preview-card[data-aos],
    .controls-card[data-aos],
    .mobile-card[data-aos] {
        transition: transform 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
    }

    .page-header-card[data-aos]:hover,
    .preview-card[data-aos]:hover,
    .controls-card[data-aos]:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .mobile-card[data-aos]:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    }

    /* Smooth transitions for interactive elements */
    .primary-action-btn[data-aos],
    .secondary-action-btn[data-aos] {
        transition: all 0.3s ease;
    }

    .primary-action-btn[data-aos]:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 20px rgba(149, 55, 53, 0.3);
    }

    .secondary-action-btn[data-aos]:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    }

    /* Enhanced carousel animations */
    .carousel-container[data-aos] {
        transition: all 0.4s ease;
    }

    .carousel-container[data-aos]:hover {
        transform: scale(1.01);
    }

    /* Accessibility - Respect user preferences */
    @@media (prefers-reduced-motion: reduce) {
        [data-aos] {
            animation: none !important;
            transition: none !important;
        }
    }

    /* Performance optimization for animations */
    .page-header-section,
    .preview-section,
    .controls-section,
    .mobile-card,
    .carousel-container {
        will-change: transform, opacity;
    }

    .slide-image:hover,
    .preview-image:hover {
        transform: scale(1.02);
        transition: transform 0.3s ease;
    }

    /* Mobile responsive for modal */
    @@media (max-width: 768px) {
        .image-modal-overlay {
            padding: 10px;
        }

        .image-modal-close {
            top: 10px;
            right: 10px;
            width: 40px;
            height: 40px;
        }

        .image-modal-close .mud-icon-root {
            font-size: 1.2rem;
        }

        .image-modal-content-wrapper {
            max-width: 100%;
            max-height: 100%;
        }

        .image-modal-title {
            font-size: 1rem;
            padding: 8px 16px;
            margin-top: 10px;
        }
    }

    /* Mobile Cards - تخطيط البطاقات المصغرة للشاشات الصغيرة */
    .regular-table { display: block; }
    .mobile-cards { display: none; }

    @@media (max-width: 768px) {
        .regular-table { display: none; }
        .mobile-cards { display: block; }
    }

    /* تصميم البطاقات المصغرة */
    .mobile-cards {
        padding: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .mobile-card {
        background: white !important;
        border-radius: 12px !important;
        transition: all 0.3s ease !important;
        border: 1px solid #e0e0e0 !important;
        margin-bottom: 12px !important;
        overflow: hidden !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05) !important;
    }

    .mobile-card:hover {
        transform: translateY(-3px) !important;
        box-shadow: 0 4px 12px rgba(149, 55, 53, 0.15) !important;
        border-color: rgba(149, 55, 53, 0.3) !important;
    }

    /* تصميم محتوى البطاقة */
    .compact-content {
        padding: 12px !important;
        background: white !important;
    }

    /* رأس البطاقة */
    .gif-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 12px !important;
        padding-bottom: 8px !important;
        border-bottom: 1px solid rgba(149, 55, 53, 0.1) !important;
    }

    /* تعديل لون وحجم الرقم في الأجهزة المختلفة */
    @@media (max-width: 480px) {
    .gif-number {
            font-size: 0.75rem !important;
            padding: 0 6px !important;
            height: 22px !important;
            min-height: 22px !important;
    }
    }

    @@media (min-width: 481px) and (max-width: 768px) {
        .gif-number {
            font-size: 0.8rem !important;
            padding: 0 8px !important;
        }
    }

    /* تصميم رقم البطاقة */
    .card-number {
        background: linear-gradient(135deg, #953735 0%, #7a2d2b 100%);
        color: white;
        font-weight: 700;
        min-width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(149, 55, 53, 0.3);
        font-size: 1rem;
        padding: 0 10px;
    }
</style>
<script>
    // ضمان عرض أسهم الكاروسيل على الهاتف المحمول
    document.addEventListener('DOMContentLoaded', function() {
        function ensureCarouselArrowsVisible() {
            const carouselContainer = document.querySelector('.carousel-container');
            if (carouselContainer) {
                // البحث عن أزرار الأسهم بطرق مختلفة
                const arrowButtons = carouselContainer.querySelectorAll(
                    '.mud-carousel-button, .mud-carousel-prev, .mud-carousel-next, ' +
                    '[aria-label*="Previous"], [aria-label*="Next"], ' +
                    '.mud-button-root[aria-label*="Previous"], .mud-button-root[aria-label*="Next"]'
                );

                arrowButtons.forEach(button => {
                    button.style.display = 'flex';
                    button.style.visibility = 'visible';
                    button.style.opacity = '1';
                    button.style.pointerEvents = 'auto';
                    button.style.zIndex = '1000';
                });

                // إضافة كلاسات CSS مخصصة للأسهم
                const prevButtons = carouselContainer.querySelectorAll(
                    '.mud-carousel-prev, [aria-label*="Previous"], .mud-button-root[aria-label*="Previous"]'
                );
                const nextButtons = carouselContainer.querySelectorAll(
                    '.mud-carousel-next, [aria-label*="Next"], .mud-button-root[aria-label*="Next"]'
                );

                prevButtons.forEach(btn => {
                    btn.classList.add('custom-carousel-prev');
                    btn.style.left = '8px';
                    btn.style.position = 'absolute';
                    btn.style.top = '50%';
                    btn.style.transform = 'translateY(-50%)';
                });

                nextButtons.forEach(btn => {
                    btn.classList.add('custom-carousel-next');
                    btn.style.right = '8px';
                    btn.style.position = 'absolute';
                    btn.style.top = '50%';
                    btn.style.transform = 'translateY(-50%)';
                });
            }
        }

        // تشغيل الدالة عند تحميل الصفحة
        ensureCarouselArrowsVisible();

        // تشغيل الدالة عند تغيير حجم الشاشة
        window.addEventListener('resize', ensureCarouselArrowsVisible);

        // تشغيل الدالة بعد تحديث المحتوى (للتأكد من عمل Blazor)
        setTimeout(ensureCarouselArrowsVisible, 1000);
        setTimeout(ensureCarouselArrowsVisible, 2000);

        // مراقبة التغييرات في DOM للكاروسيل
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    ensureCarouselArrowsVisible();
                }
            });
        });

        const carouselContainer = document.querySelector('.carousel-container');
        if (carouselContainer) {
            observer.observe(carouselContainer, {
                childList: true,
                subtree: true
            });
        }
    });
</script>