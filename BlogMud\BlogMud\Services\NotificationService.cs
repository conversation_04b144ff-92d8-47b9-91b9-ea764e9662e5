using BlogMud.Data;
using BlogMud.Shared.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace BlogMud.Services
{
    public interface INotificationService
    {
        Task SendWelcomeNotificationAsync(string userId);
        Task SendNewPostNotificationAsync(Article article);
        Task SendSystemNotificationAsync(string title, string message, NotificationType type = NotificationType.General);
        Task<List<UserNotification>> GetUserNotificationsAsync(string userId, bool unreadOnly = false);
        Task MarkAsReadAsync(int notificationId, string userId);
        Task MarkAllAsReadAsync(string userId);
        Task<int> GetUnreadCountAsync(string userId);
        Task<int> GetActiveUsersCountAsync();
    }

    public class NotificationService : INotificationService
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly EmailService _emailService;
        private readonly ILogger<NotificationService> _logger;
        private readonly IConfiguration _configuration;

        public NotificationService(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            EmailService emailService,
            ILogger<NotificationService> logger,
            IConfiguration configuration)
        {
            _context = context;
            _userManager = userManager;
            _emailService = emailService;
            _logger = logger;
            _configuration = configuration;
        }

        public async Task SendWelcomeNotificationAsync(string userId)
        {
            var notification = new UserNotification
            {
                UserId = userId,
                Title = "مرحباً بك في BlogMud!",
                Message = "نرحب بك في موقع BlogMud. نتمنى لك تجربة ممتعة ومفيدة معنا.",
                Type = NotificationType.Welcome
            };

            _context.UserNotifications.Add(notification);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Welcome notification sent to user {UserId}", userId);
        }

        public async Task SendNewPostNotificationAsync(Article article)
        {
            try
            {
                // Get all active users
                var users = await _userManager.Users
                    .Where(u => u.IsActive && u.EmailConfirmed)
                    .ToListAsync();

                var notifications = new List<UserNotification>();

                foreach (var user in users)
                {
                    var notification = new UserNotification
                    {
                        UserId = user.Id,
                        Title = "منشور جديد",
                        Message = $"تم نشر مقال جديد: {article.Title}",
                        Type = NotificationType.NewPost,
                        RelatedEntityId = article.Id,
                        RelatedEntityType = "Article"
                    };

                    notifications.Add(notification);

                    // Send email notification
                    try
                    {
                        var emailBody = GetNewPostEmailTemplate(user.FullName, article);
                        await _emailService.SendEmailAsync(user.Email!, "منشور جديد - BlogMud", emailBody);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send email notification to user {UserId}", user.Id);
                    }
                }

                _context.UserNotifications.AddRange(notifications);
                await _context.SaveChangesAsync();

                _logger.LogInformation("New post notifications sent for article {ArticleId} to {UserCount} users",
                    article.Id, users.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send new post notifications for article {ArticleId}", article.Id);
            }
        }

        public async Task SendSystemNotificationAsync(string title, string message, NotificationType type = NotificationType.General)
        {
            try
            {
                var users = await _userManager.Users
                    .Where(u => u.IsActive)
                    .ToListAsync();

                var notifications = users.Select(user => new UserNotification
                {
                    UserId = user.Id,
                    Title = title,
                    Message = message,
                    Type = type
                }).ToList();

                _context.UserNotifications.AddRange(notifications);
                await _context.SaveChangesAsync();

                _logger.LogInformation("System notification sent to {UserCount} users", users.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send system notification");
            }
        }

        public async Task<List<UserNotification>> GetUserNotificationsAsync(string userId, bool unreadOnly = false)
        {
            var query = _context.UserNotifications
                .Where(n => n.UserId == userId);

            if (unreadOnly)
            {
                query = query.Where(n => !n.IsRead);
            }

            return await query
                .OrderByDescending(n => n.CreatedAt)
                .Take(50) // Limit to last 50 notifications
                .ToListAsync();
        }

        public async Task MarkAsReadAsync(int notificationId, string userId)
        {
            var notification = await _context.UserNotifications
                .FirstOrDefaultAsync(n => n.Id == notificationId && n.UserId == userId);

            if (notification != null && !notification.IsRead)
            {
                notification.IsRead = true;
                notification.ReadAt = DateTime.Now;
                await _context.SaveChangesAsync();
            }
        }

        public async Task MarkAllAsReadAsync(string userId)
        {
            var unreadNotifications = await _context.UserNotifications
                .Where(n => n.UserId == userId && !n.IsRead)
                .ToListAsync();

            foreach (var notification in unreadNotifications)
            {
                notification.IsRead = true;
                notification.ReadAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();
        }

        public async Task<int> GetUnreadCountAsync(string userId)
        {
            return await _context.UserNotifications
                .CountAsync(n => n.UserId == userId && !n.IsRead);
        }

        public async Task<int> GetActiveUsersCountAsync()
        {
            try
            {
                var count = await _userManager.Users
                    .Where(u => u.IsActive && u.EmailConfirmed)
                    .CountAsync();

                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get active users count");
                return 0;
            }
        }

        private string GetNewPostEmailTemplate(string fullName, Article article)
        {
            // Get base URL from configuration
            var baseUrl = _configuration["BaseAddress"] ?? "http://ahmedakear.runasp.net";
            var postUrl = $"{baseUrl}/post/{article.Id}";

            return $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>منشور جديد</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            direction: rtl;
            line-height: 1.6;
        }}
        .container {{
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            padding: 20px 0;
            border-bottom: 2px solid #28a745;
        }}
        .logo {{
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
        }}
        .content {{
            padding: 30px 0;
        }}
        .article-title {{
            color: #333;
            font-size: 20px;
            margin: 15px 0;
            font-weight: bold;
        }}
        .article-preview {{
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }}
        .button {{
            display: inline-block;
            padding: 12px 30px;
            background-color: #28a745;
            color: white !important;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
            transition: background-color 0.3s ease;
            min-height: 44px;
            line-height: 20px;
        }}
        .button:hover {{
            background-color: #218838;
        }}
        .button-container {{
            text-align: center;
            margin: 25px 0;
        }}
        .footer {{
            text-align: center;
            padding: 20px 0;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }}
        @media only screen and (max-width: 600px) {{
            .container {{
                margin: 10px;
                padding: 15px;
            }}
            .content {{
                padding: 20px 0;
            }}
            .button {{
                padding: 15px 25px;
                font-size: 16px;
            }}
        }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>BlogMud</div>
        </div>
        <div class='content'>
            <h2>مرحباً {fullName}!</h2>
            <p>تم نشر مقال جديد على موقع BlogMud:</p>
            <div class='article-title'>{article.Title}</div>
            <div class='article-preview'>{(string.IsNullOrEmpty(article.Content) ? "مقال جديد متاح للقراءة" : article.Content.Substring(0, Math.Min(200, article.Content.Length)))}...</div>
            <div class='button-container'>
                <a href='{postUrl}' class='button'>قراءة المقال كاملاً</a>
            </div>
            <p>مع أطيب التحيات،<br>فريق BlogMud</p>
        </div>
        <div class='footer'>
            <p>© 2024 BlogMud. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html>";
        }
    }
}
