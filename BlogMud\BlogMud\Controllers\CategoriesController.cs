using AutoMapper;
using BlogMud.Shared.DTOs;
using BlogMud.Shared.Models;
using BlogMud.Shared.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BlogMud.Controllers;

/// <summary>
/// وحدة التحكم بالتصنيفات - تدير عمليات إنشاء وقراءة وتحديث وحذف التصنيفات
/// </summary>
[Route("api/[controller]")]
[ApiController]
public class CategoriesController : ControllerBase
{
    #region المتغيرات والمنشئ

    /// <summary>
    /// وحدة العمل المسؤولة عن إدارة المستودعات وحفظ التغييرات
    /// </summary>
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// مستودع التصنيفات المسؤول عن عمليات قراءة وكتابة بيانات التصنيفات
    /// </summary>
    private readonly IRepository<Category> _categoryRepository;

    /// <summary>
    /// مسجل الأحداث لتسجيل معلومات التنفيذ والأخطاء
    /// </summary>
    private readonly ILogger<CategoriesController> _logger;

    /// <summary>
    /// أداة التحويل بين كائنات النموذج وكائنات نقل البيانات
    /// </summary>
    private readonly IMapper _mapper;

    /// <summary>
    /// منشئ وحدة التحكم بالتصنيفات
    /// </summary>
    /// <param name="unitOfWork">وحدة العمل لإدارة المستودعات</param>
    /// <param name="logger">مسجل الأحداث لتسجيل المعلومات والأخطاء</param>
    /// <param name="mapper">أداة التحويل بين النماذج وكائنات نقل البيانات</param>
    public CategoriesController(IUnitOfWork unitOfWork, ILogger<CategoriesController> logger, IMapper mapper)
    {
        _unitOfWork = unitOfWork;
        _categoryRepository = _unitOfWork.Repository<Category>();
        _logger = logger;
        _mapper = mapper;
    }

    #endregion

    #region عمليات القراءة (GET)

    /// <summary>
    /// استرجاع جميع التصنيفات
    /// </summary>
    /// <returns>قائمة بجميع التصنيفات</returns>
    /// <response code="200">تم استرجاع التصنيفات بنجاح</response>
    /// <response code="500">حدث خطأ أثناء استرجاع التصنيفات</response>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<CategoryDto>>> GetCategories()
    {
        try
        {
            _logger.LogInformation("Fetching all categories");
            // استرجاع جميع التصنيفات من قاعدة البيانات
            var categories = await _categoryRepository.GetAllAsync();
            // تحويل كائنات النموذج إلى كائنات نقل البيانات
            var categoryDtos = _mapper.Map<IEnumerable<CategoryDto>>(categories);
            _logger.LogInformation("Retrieved {Count} categories", categoryDtos.Count());
            return Ok(categoryDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving categories");
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    /// <summary>
    /// استرجاع تصنيف محدد بواسطة المعرف
    /// </summary>
    /// <param name="id">معرف التصنيف المطلوب</param>
    /// <returns>بيانات التصنيف المطلوب</returns>
    /// <response code="200">تم استرجاع التصنيف بنجاح</response>
    /// <response code="404">التصنيف غير موجود</response>
    /// <response code="500">حدث خطأ أثناء استرجاع التصنيف</response>
    [HttpGet("{id}")]
    public async Task<ActionResult<CategoryDto>> GetCategory(int id)
    {
        try
        {
            _logger.LogInformation("Fetching category with ID: {CategoryId}", id);
            // استرجاع التصنيف من قاعدة البيانات باستخدام المعرف
            var category = await _categoryRepository.GetByIdAsync(id);

            // التحقق من وجود التصنيف
            if (category == null)
            {
                _logger.LogWarning("Category not found with ID: {CategoryId}", id);
                return NotFound();
            }

            // تحويل كائن النموذج إلى كائن نقل البيانات
            var categoryDto = _mapper.Map<CategoryDto>(category);
            return Ok(categoryDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category with ID: {CategoryId}", id);
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    #endregion

    #region عمليات التعديل (PUT/POST/DELETE)

    /// <summary>
    /// تحديث تصنيف موجود بواسطة المعرف
    /// </summary>
    /// <param name="id">معرف التصنيف المراد تحديثه</param>
    /// <param name="categoryDto">بيانات التصنيف المحدثة</param>
    /// <returns>استجابة بدون محتوى في حالة نجاح التحديث</returns>
    /// <response code="204">تم تحديث التصنيف بنجاح</response>
    /// <response code="400">بيانات التصنيف غير صالحة</response>
    /// <response code="401">المستخدم غير مصرح له</response>
    /// <response code="404">التصنيف غير موجود</response>
    /// <response code="500">حدث خطأ أثناء تحديث التصنيف</response>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> PutCategory(int id, CategoryDto categoryDto)
    {
        // التحقق من تطابق المعرف في المسار مع المعرف في البيانات
        if (id != categoryDto.Id)
        {
            return BadRequest("Category ID mismatch");
        }

        try
        {
            _logger.LogInformation("Updating category with ID: {CategoryId}", id);

            // التحقق أولاً من وجود التصنيف
            var existingCategory = await _categoryRepository.GetByIdAsync(id);
            if (existingCategory == null)
            {
                _logger.LogWarning("Category not found with ID: {CategoryId}", id);
                return NotFound();
            }

            // استخدام AutoMapper لنسخ البيانات من كائن نقل البيانات إلى الكائن الموجود
            _mapper.Map(categoryDto, existingCategory);

            // تحديث التصنيف في المستودع
            _categoryRepository.Update(existingCategory);
            await _unitOfWork.SaveAsync();

            _logger.LogInformation("Category updated successfully: {CategoryId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating category: {CategoryId}", id);
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    /// <summary>
    /// إنشاء تصنيف جديد
    /// </summary>
    /// <param name="categoryDto">بيانات التصنيف الجديد</param>
    /// <returns>التصنيف الذي تم إنشاؤه مع معرفه الجديد</returns>
    /// <response code="201">تم إنشاء التصنيف بنجاح</response>
    /// <response code="400">بيانات التصنيف غير صالحة</response>
    /// <response code="401">المستخدم غير مصرح له</response>
    /// <response code="500">حدث خطأ أثناء إنشاء التصنيف</response>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<CategoryDto>> PostCategory(CategoryDto categoryDto)
    {
        try
        {
            _logger.LogInformation("Creating new category: {CategoryName}", categoryDto.Name);

            // تحويل كائن نقل البيانات إلى كائن نموذج باستخدام AutoMapper
            var category = _mapper.Map<Category>(categoryDto);

            // إضافة التصنيف الجديد إلى قاعدة البيانات
            await _categoryRepository.AddAsync(category);
            await _unitOfWork.SaveAsync();

            _logger.LogInformation("Category created successfully with ID: {CategoryId}", category.Id);

            // تحويل التصنيف المنشأ مرة أخرى إلى كائن نقل بيانات باستخدام AutoMapper
            var createdCategoryDto = _mapper.Map<CategoryDto>(category);

            // إرجاع استجابة بحالة 201 Created مع رابط للحصول على التصنيف الجديد
            return CreatedAtAction("GetCategory", new { id = createdCategoryDto.Id }, createdCategoryDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating category: {CategoryName}", categoryDto.Name);
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    /// <summary>
    /// حذف تصنيف بواسطة المعرف
    /// </summary>
    /// <param name="id">معرف التصنيف المراد حذفه</param>
    /// <returns>استجابة بدون محتوى في حالة نجاح الحذف</returns>
    /// <response code="204">تم حذف التصنيف بنجاح</response>
    /// <response code="401">المستخدم غير مصرح له</response>
    /// <response code="404">التصنيف غير موجود</response>
    /// <response code="500">حدث خطأ أثناء حذف التصنيف</response>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> DeleteCategory(int id)
    {
        try
        {
            _logger.LogInformation("Deleting category with ID: {CategoryId}", id);

            // التحقق من وجود التصنيف
            var category = await _categoryRepository.GetByIdAsync(id);
            if (category == null)
            {
                _logger.LogWarning("Category not found with ID: {CategoryId}", id);
                return NotFound();
            }

            // حذف التصنيف من قاعدة البيانات
            _categoryRepository.Remove(category);
            await _unitOfWork.SaveAsync();
            _logger.LogInformation("Category deleted successfully: {CategoryId}", id);

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting category with ID: {CategoryId}", id);
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    #endregion

    #region دوال مساعدة

    /// <summary>
    /// التحقق من وجود تصنيف بمعرف محدد
    /// </summary>
    /// <param name="id">معرف التصنيف المراد التحقق منه</param>
    /// <returns>قيمة منطقية تشير إلى وجود التصنيف (true) أو عدم وجوده (false)</returns>
    /// <exception cref="Exception">يتم إلقاء استثناء في حالة حدوث خطأ أثناء التحقق</exception>
    private async Task<bool> CategoryExists(int id)
    {
        try
        {
            _logger.LogInformation("Checking if category exists with ID: {CategoryId}", id);
            // البحث عن التصنيف باستخدام المعرف
            var category = await _categoryRepository.GetFirstOrDefaultAsync(c => c.Id == id);
            var exists = category != null;
            _logger.LogInformation("Category with ID: {CategoryId} exists: {Exists}", id, exists);
            return exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if category exists with ID: {CategoryId}", id);
            // إعادة إلقاء الاستثناء ليتم معالجته في المستوى الأعلى
            throw;
        }
    }

    #endregion
}
