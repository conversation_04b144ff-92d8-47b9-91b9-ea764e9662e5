@using BlogMud.Shared.DTOs
@using Microsoft.AspNetCore.Components.Forms
@using MudBlazor

<MudDialog Class="sider-about-us-management-dialog" MaxWidth="MaxWidth.Large">
    <TitleContent>
        <div class="dialog-title-container">
            <MudIcon Icon="@(IsCreateMode ? Icons.Material.Filled.Add : (IsViewMode ? Icons.Material.Filled.Visibility : Icons.Material.Filled.Edit))"
                     Class="dialog-title-icon" />
            <MudText Typo="Typo.h5" Class="dialog-title-text">
                @GetDialogTitle()
            </MudText>
        </div>
    </TitleContent>
    <DialogContent>
        <div class="dialog-content-container">
            <MudForm @ref="_form" Model="SiderAboutUs" @bind-IsValid="@_success" Class="sider-about-us-form">

                <!-- Tabbed Interface -->
                <MudTabs Elevation="1" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6" @bind-ActivePanelIndex="_activeTabIndex">

                    <!-- Tab 1: Basic Information -->
                    <MudTabPanel Text="المعلومات الأساسية" Icon="@Icons.Material.Filled.Info">
                        <MudGrid Spacing="3" Class="form-grid">
                            <!-- العنوان -->
                            <MudItem xs="12">
                                <MudTextField @bind-Value="SiderAboutUs.Title"
                                             Label="عنوان الشريحة"
                                             Required="true"
                                             RequiredError="العنوان مطلوب"
                                             MaxLength="100"
                                             Counter="100"
                                             HelperText="الحد الأقصى 100 حرف"
                                             Variant="Variant.Outlined"
                                             Class="form-field"
                                             ReadOnly="@IsViewMode"
                                             Immediate="true" />
                            </MudItem>

                            <!-- الوصف -->
                            <MudItem xs="12">
                                <MudTextField @bind-Value="SiderAboutUs.Description"
                                             Label="وصف الشريحة"
                                             Lines="3"
                                             MaxLength="500"
                                             Counter="500"
                                             HelperText="الحد الأقصى 500 حرف"
                                             Variant="Variant.Outlined"
                                             ReadOnly="@IsViewMode"
                                             Class="form-field" />
                            </MudItem>

                            <!-- ترتيب العرض ومدة العرض -->
                            <MudItem xs="12" sm="6">
                                <MudNumericField @bind-Value="SiderAboutUs.DisplayOrder"
                                                Label="ترتيب العرض"
                                                Min="0"
                                                Max="999"
                                                HideSpinButtons="false"
                                                Variant="Variant.Outlined"
                                                Class="form-field"
                                                ReadOnly="@IsViewMode"
                                                HelperText="ترتيب ظهور الشريحة" />
                            </MudItem>

                            <MudItem xs="12" sm="6">
                                <MudNumericField @bind-Value="SiderAboutUs.Duration"
                                                Label="مدة العرض (ثانية)"
                                                Min="1"
                                                Max="60"
                                                HideSpinButtons="false"
                                                Variant="Variant.Outlined"
                                                Class="form-field"
                                                ReadOnly="@IsViewMode"
                                                HelperText="مدة عرض الشريحة بالثواني" />
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                    <!-- Tab 2: Media -->
                    <MudTabPanel Text="الوسائط" Icon="@Icons.Material.Filled.PhotoLibrary">
                        <MudGrid Spacing="3" Class="media-grid">

                            <!-- Image Upload Section -->
                            <MudItem xs="12">
                                <MudPaper Class="upload-section image-upload" Elevation="0">
                                    <div class="upload-header">
                                        <MudIcon Icon="@Icons.Material.Filled.PhotoLibrary" Class="upload-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="upload-title">صور الشريحة</MudText>
                                    </div>

                                    <div class="upload-content">
                                        @if (!IsViewMode)
                                        {
                                            <MudFileUpload T="IReadOnlyList<IBrowserFile>"
                                                          Accept=".png, .jpg, .jpeg, .gif, .webp"
                                                          FilesChanged="UploadMultipleImages"
                                                          MaximumFileCount="10"
                                                          Class="file-upload">
                                                <ActivatorContent>
                                                    <MudButton Variant="Variant.Outlined"
                                                               Color="Color.Primary"
                                                               StartIcon="@Icons.Material.Filled.CloudUpload"
                                                               FullWidth="true"
                                                               Class="upload-button">
                                                        رفع صور متعددة (حد أقصى 10)
                                                    </MudButton>
                                                </ActivatorContent>
                                            </MudFileUpload>

                                            @if (_imageUploading)
                                            {
                                                <div class="upload-progress">
                                                    <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="mt-2" />
                                                    <MudText Typo="Typo.caption" Class="mt-1 upload-status">جاري رفع الصور...</MudText>
                                                </div>
                                            }
                                        }

                                        <!-- عرض الصور المرفوعة -->
                                        @if (SiderAboutUs.Images.Any())
                                        {
                                            <div class="uploaded-images-section">
                                                <MudText Typo="Typo.subtitle2" Class="mb-2 images-count">
                                                    الصور المرفوعة (@SiderAboutUs.Images.Count صورة)
                                                </MudText>
                                                <MudGrid Spacing="2">
                                                    @foreach (var (imageUrl, index) in SiderAboutUs.Images.Select((url, i) => (url, i)))
                                                    {
                                                        <MudItem xs="6" sm="4" md="3">
                                                            <MudCard Class="image-card" Elevation="2">
                                                                <MudCardMedia Image="@imageUrl" Height="120" Class="image-preview" />
                                                                @if (!IsViewMode)
                                                                {
                                                                    <MudCardActions Class="image-actions">
                                                                        <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                                                      Color="Color.Error"
                                                                                      Size="Size.Small"
                                                                                      OnClick="@(() => RemoveImage(index))"
                                                                                      Title="حذف الصورة"
                                                                                      Class="delete-image-btn" />
                                                                    </MudCardActions>
                                                                }
                                                            </MudCard>
                                                        </MudItem>
                                                    }
                                                </MudGrid>
                                            </div>
                                        }
                                    </div>
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                    <!-- Tab 3: Settings -->
                    <MudTabPanel Text="الإعدادات" Icon="@Icons.Material.Filled.Settings">
                        <MudGrid Spacing="3" Class="settings-grid">

                            <!-- رابط اختياري -->
                            <MudItem xs="12" md="8">
                                <MudTextField @bind-Value="SiderAboutUs.LinkUrl"
                                             Label="رابط اختياري (URL)"
                                             MaxLength="500"
                                             Counter="500"
                                             Placeholder="https://example.com"
                                             Variant="Variant.Outlined"
                                             Class="form-field"
                                             ReadOnly="@IsViewMode"
                                             HelperText="رابط يتم الانتقال إليه عند النقر على الشريحة" />
                            </MudItem>

                            <!-- نص الرابط -->
                            <MudItem xs="12" md="4">
                                <MudTextField @bind-Value="SiderAboutUs.LinkText"
                                             Label="نص الرابط"
                                             MaxLength="100"
                                             Counter="100"
                                             Placeholder="اقرأ المزيد"
                                             Variant="Variant.Outlined"
                                             Class="form-field"
                                             ReadOnly="@IsViewMode"
                                             HelperText="النص الذي يظهر على زر الرابط" />
                            </MudItem>

                            <!-- ملاحظات -->
                            <MudItem xs="12">
                                <MudTextField @bind-Value="SiderAboutUs.Notes"
                                             Label="ملاحظات إضافية"
                                             Lines="3"
                                             MaxLength="1000"
                                             Counter="1000"
                                             Variant="Variant.Outlined"
                                             Class="form-field"
                                             ReadOnly="@IsViewMode"
                                             HelperText="ملاحظات داخلية للمراجعة" />
                            </MudItem>

                            <!-- حالة النشاط -->
                            <MudItem xs="12">
                                <MudPaper Class="form-section status-section" Elevation="0">
                                    <div class="status-header">
                                        <MudIcon Icon="@Icons.Material.Filled.Visibility" Class="status-icon" />
                                        <MudText Typo="Typo.subtitle1" Class="status-title">حالة النشر</MudText>
                                    </div>
                                    <div class="status-content">
                                        <MudSwitch @bind-Value="SiderAboutUs.IsActive"
                                                  Label="نشط"
                                                  Color="Color.Primary"
                                                  ReadOnly="@IsViewMode"
                                                  Class="status-switch" />
                                        <MudText Typo="Typo.caption" Color="Color.Secondary" Class="status-description">
                                            عند التفعيل، ستظهر هذه الشريحة في صفحة "من نحن"
                                        </MudText>
                                    </div>
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    </MudTabPanel>

                </MudTabs>

                @if (!IsCreateMode)
                {
                    <!-- Metadata Section -->
                    <MudPaper Class="form-section metadata-section" Elevation="1">
                        <div class="section-header">
                            <MudIcon Icon="@Icons.Material.Filled.Info" Class="section-icon" />
                            <MudText Typo="Typo.h6" Class="section-title">معلومات إضافية</MudText>
                        </div>

                        <MudGrid Spacing="3">
                            <MudItem xs="12" md="6">
                                <div class="metadata-item">
                                    <MudText Typo="Typo.caption" Class="metadata-label">تاريخ الإنشاء:</MudText>
                                    <MudText Typo="Typo.body2" Class="metadata-value">@SiderAboutUs.CreatedAt.ToString("yyyy/MM/dd HH:mm")</MudText>
                                </div>
                            </MudItem>
                            @if (SiderAboutUs.LastModifiedAt.HasValue)
                            {
                                <MudItem xs="12" md="6">
                                    <div class="metadata-item">
                                        <MudText Typo="Typo.caption" Class="metadata-label">آخر تعديل:</MudText>
                                        <MudText Typo="Typo.body2" Class="metadata-value">@SiderAboutUs.LastModifiedAt.Value.ToString("yyyy/MM/dd HH:mm")</MudText>
                                    </div>
                                </MudItem>
                            }
                        </MudGrid>
                    </MudPaper>
                }
            </MudForm>
        </div>
    </DialogContent>
    <DialogActions>
        <div class="dialog-actions">
            <div class="action-buttons">
                @if (!IsViewMode)
                {
                    <MudButton Variant="Variant.Text"
                               Color="Color.Default"
                               OnClick="Cancel"
                               StartIcon="@Icons.Material.Filled.Cancel"
                               Class="cancel-button">
                        إلغاء
                    </MudButton>
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               OnClick="Submit"
                               Disabled="@(!_success)"
                               StartIcon="@(IsCreateMode ? Icons.Material.Filled.Add : Icons.Material.Filled.Save)"
                               Class="submit-button">
                        @(IsCreateMode ? "إضافة الشريحة" : "حفظ التعديلات")
                    </MudButton>
                }
                else
                {
                    <MudButton Variant="Variant.Text"
                               Color="Color.Default"
                               OnClick="Cancel"
                               StartIcon="@Icons.Material.Filled.Close"
                               Class="cancel-button">
                        إغلاق
                    </MudButton>
                }
            </div>
        </div>
    </DialogActions>
</MudDialog>

<SiderAboutUsEditFormCSS />
