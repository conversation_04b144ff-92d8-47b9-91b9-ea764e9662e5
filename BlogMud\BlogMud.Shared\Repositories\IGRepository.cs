using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace BlogMud.Shared.Repositories
{
    /// <summary>
    /// Generic repository interface for domain-specific repositories
    /// Extends the base IRepository with additional domain-specific operations
    /// </summary>
    /// <typeparam name="T">The entity type</typeparam>
    public interface IGRepository<T> : IRepository<T> where T : class
    {
        /// <summary>
        /// Gets entities with pagination support
        /// </summary>
        /// <param name="filter">Filter expression</param>
        /// <param name="orderBy">Order by expression</param>
        /// <param name="includeProperties">Comma-separated list of properties to include</param>
        /// <param name="page">Page number (1-based)</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="tracked">Whether to track the entities</param>
        /// <returns>Paged list of entities</returns>
        IEnumerable<T> GetPaged(
            Expression<Func<T, bool>> filter = null,
            Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
            string includeProperties = "",
            int page = 1,
            int pageSize = 10,
            bool tracked = true);

        /// <summary>
        /// Gets entities with pagination support asynchronously
        /// </summary>
        /// <param name="filter">Filter expression</param>
        /// <param name="orderBy">Order by expression</param>
        /// <param name="includeProperties">Comma-separated list of properties to include</param>
        /// <param name="page">Page number (1-based)</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="tracked">Whether to track the entities</param>
        /// <returns>Paged list of entities</returns>
        Task<IEnumerable<T>> GetPagedAsync(
            Expression<Func<T, bool>> filter = null,
            Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
            string includeProperties = "",
            int page = 1,
            int pageSize = 10,
            bool tracked = true);

        /// <summary>
        /// Gets the total count of entities matching the filter
        /// </summary>
        /// <param name="filter">Filter expression</param>
        /// <returns>Total count</returns>
        int Count(Expression<Func<T, bool>> filter = null);

        /// <summary>
        /// Gets the total count of entities matching the filter asynchronously
        /// </summary>
        /// <param name="filter">Filter expression</param>
        /// <returns>Total count</returns>
        Task<int> CountAsync(Expression<Func<T, bool>> filter = null);

        /// <summary>
        /// Checks if any entity matches the filter
        /// </summary>
        /// <param name="filter">Filter expression</param>
        /// <returns>True if any entity matches the filter, otherwise false</returns>
        bool Any(Expression<Func<T, bool>> filter = null);

        /// <summary>
        /// Checks if any entity matches the filter asynchronously
        /// </summary>
        /// <param name="filter">Filter expression</param>
        /// <returns>True if any entity matches the filter, otherwise false</returns>
        Task<bool> AnyAsync(Expression<Func<T, bool>> filter = null);
    }
}
