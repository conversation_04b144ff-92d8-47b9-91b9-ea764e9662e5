@inherits LayoutComponentBase
@inject NavigationManager NavigationManager
@inject IJSRuntime _JSRuntime
@implements IDisposable

<MudRTLProvider RightToLeft="true">
    <MudThemeProvider Theme="_theme" />
    <MudDialogProvider />
    <MudSnackbarProvider />
    <MudPopoverProvider />

    <MudLayout>
        <MudAppBar Elevation="2" Color="Color.Primary" Fixed="true" Dense="@_isMobile" Class="@GetAppBarClass()">
            <MudContainer MaxWidth="MaxWidth.False" Class="d-flex align-center justify-space-between pa-0">
                <div class="d-flex align-center">
                    <MudIconButton Icon="@Icons.Material.Filled.Menu"
                                   Color="Color.Inherit"
                                   Edge="Edge.Start"
                                   Size="@(_isMobile ? Size.Medium : Size.Large)"
                                   Class="@GetMenuButtonClass()"
                                   OnClick="@DrawerToggle"
                                   aria-label="فتح/إغلاق القائمة الجانبية"
                                   title="فتح/إغلاق القائمة الجانبية" />

                    <MudText Typo="@(_isMobile ? Typo.subtitle1 : Typo.h6)"
                             Class="@GetTitleClass()"
                             Style="font-weight: 600;">
                        لوحة تحكم المدير
                    </MudText>
                </div>

                <div class="d-flex align-center gap-2">
                    <!-- Breadcrumb for larger screens -->
                    <MudHidden Breakpoint="Breakpoint.SmAndDown">
                        <MudBreadcrumbs Items="_breadcrumbItems"
                                        Separator="/"
                                        Class="ma-0 pa-0"
                                        Style="color: rgba(255,255,255,0.8);" />
                    </MudHidden>

                    <!-- Action buttons -->
                    <MudTooltip Text="العودة للموقع الرئيسي">
                        <MudIconButton Icon="@Icons.Material.Filled.Home"
                                       Color="Color.Inherit"
                                       Size="@(_isMobile ? Size.Medium : Size.Large)"
                                       Class="@GetActionButtonClass()"
                                       OnClick="@(() => NavigateTo("/", true))"
                                       aria-label="العودة للموقع الرئيسي" />
                    </MudTooltip>

                    <!-- User menu for larger screens -->
                    <MudHidden Breakpoint="Breakpoint.SmAndDown">
                        <MudMenu Icon="@Icons.Material.Filled.AccountCircle"
                                 Color="Color.Inherit"
                                 Size="Size.Large"
                                 Class="@GetActionButtonClass()"
                                 AnchorOrigin="Origin.BottomCenter"
                                 TransformOrigin="Origin.TopCenter"
                                 aria-label="قائمة المستخدم">
                            <MudMenuItem OnClick="@(() => NavigateTo("/admin/profile"))">
                                <div class="d-flex align-center gap-3">
                                    <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" />
                                    <MudText>الملف الشخصي</MudText>
                                </div>
                            </MudMenuItem>
                            <MudMenuItem OnClick="@(() => NavigateTo("/admin/settings"))">
                                <div class="d-flex align-center gap-3">
                                    <MudIcon Icon="@Icons.Material.Filled.Settings" Size="Size.Small" />
                                    <MudText>الإعدادات</MudText>
                                </div>
                            </MudMenuItem>
                            <MudDivider />
                            <MudMenuItem OnClick="@(() => NavigateTo("/Account/Logout", true))">
                                <div class="d-flex align-center gap-3">
                                    <MudIcon Icon="@Icons.Material.Filled.Logout" Size="Size.Small" />
                                    <MudText>تسجيل الخروج</MudText>
                                </div>
                            </MudMenuItem>
                        </MudMenu>
                    </MudHidden>
                </div>
            </MudContainer>
        </MudAppBar>
        <MudDrawer @bind-Open="_drawerOpen"
                   ClipMode="DrawerClipMode.Always"
                   Elevation="@GetDrawerElevation()"
                   Variant="@GetDrawerVariant()"
                   Breakpoint="Breakpoint.Md"
                   DisableOverlay="false"
                   Width="@GetDrawerWidth()"
                   Class="@GetDrawerClass()"
                   Style="@GetDrawerStyle()"
                   Anchor="Anchor.Start">

            <!-- Drawer Header -->
            <MudDrawerHeader Class="@GetDrawerHeaderClass()">
                <div class="d-flex align-center justify-space-between w-100">
                    <div class="d-flex align-center gap-3">
                        <MudAvatar Color="Color.Primary" Size="Size.Medium">
                            <MudIcon Icon="@Icons.Material.Filled.AdminPanelSettings" />
                        </MudAvatar>
                        <div class="d-flex flex-column">
                            <MudText Typo="Typo.subtitle1" Style="font-weight: 600; line-height: 1.2;">
                                لوحة التحكم
                            </MudText>
                            <MudText Typo="Typo.caption" Style="opacity: 0.7;">
                                إدارة النظام
                            </MudText>
                        </div>
                    </div>

                    <!-- Close button for mobile -->
                    <MudHidden Breakpoint="Breakpoint.MdAndUp">
                        <MudIconButton Icon="@Icons.Material.Filled.Close"
                                       Size="Size.Small"
                                       OnClick="@DrawerToggle"
                                       aria-label="إغلاق القائمة"
                                       Class="ml-2" />
                    </MudHidden>
                </div>
            </MudDrawerHeader>

            <!-- Navigation Menu -->
            <MudNavMenu Class="@GetNavMenuClass()" Color="Color.Inherit">
                <!-- Dashboard -->
                <MudNavLink OnClick="@(() => NavigateTo("/admin/dashboard", true))"
                            Icon="@Icons.Material.Filled.Dashboard"
                            Match="NavLinkMatch.All"
                            Class="@GetNavLinkClass()">
                    <div class="d-flex align-center gap-3 w-100">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">
                            لوحة التحكم
                        </MudText>
                    </div>
                </MudNavLink>

                <!-- Posts Management -->
                <MudNavGroup Title="إدارة المناشير"
                             Icon="@Icons.Material.Filled.Article"
                             Expanded="@_expandedGroups.Contains("posts")"
                             Class="@GetNavGroupClass()">
                    <MudNavLink OnClick="@(() => NavigateTo("/admin/posts/postManagement", true))"
                                Icon="@Icons.Material.Filled.Article"
                                Class="@GetSubNavLinkClass()">
                        <MudText Typo="Typo.body2">منشور الأخبار</MudText>
                    </MudNavLink>
                    <MudNavLink OnClick="@(() => NavigateTo("/admin/newsSiderMove", true))"
                                Icon="@Icons.Material.Filled.Image"
                                Class="@GetSubNavLinkClass()">
                        <MudText Typo="Typo.body2">الصور شريط متحركة الأخبار</MudText>
                    </MudNavLink>
                </MudNavGroup>

                <!-- Categories -->
                <MudNavLink OnClick="@(() => NavigateTo("/admin/categories", true))"
                            Icon="@Icons.Material.Filled.Category"
                            Class="@GetNavLinkClass()">
                    <div class="d-flex align-center gap-3 w-100">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">
                            الأقسام
                        </MudText>
                    </div>
                </MudNavLink>

                <!-- Clients -->
                <MudNavLink OnClick="@(() => NavigateTo("/admin/client", true))"
                            Icon="@Icons.Material.Filled.People"
                            Class="@GetNavLinkClass()">
                    <div class="d-flex align-center gap-3 w-100">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">
                            العملاء
                        </MudText>
                    </div>
                </MudNavLink>

                <!-- Animated Images Management -->
                <MudNavGroup Title="إدارة الصور المتحركة"
                             Icon="@Icons.Material.Filled.Animation"
                             Expanded="@_expandedGroups.Contains("animations")"
                             Class="@GetNavGroupClass()">
                    <MudNavLink OnClick="@(() => NavigateTo("/admin/animatedgifs", true))"
                                Icon="@Icons.Material.Filled.Image"
                                Class="@GetSubNavLinkClass()">
                        <MudText Typo="Typo.body2">الصور شريط متحركة الرئيسية</MudText>
                    </MudNavLink>
                </MudNavGroup>

                <!-- Services Management -->
                <MudNavGroup Title="إدارة خدماتنا"
                             Icon="@Icons.Material.Filled.HomeRepairService"
                             Expanded="@_expandedGroups.Contains("services")"
                             Class="@GetNavGroupClass()">
                    <MudNavLink OnClick="@(() => NavigateTo("/admin/ourServicesManager", true))"
                                Icon="@Icons.Material.Filled.HomeRepairService"
                                Class="@GetSubNavLinkClass()">
                        <MudText Typo="Typo.body2">خدماتنا</MudText>
                    </MudNavLink>
                    <MudNavLink OnClick="@(() => NavigateTo("/admin/sidertMoveServices", true))"
                                Icon="@Icons.Material.Filled.Image"
                                Class="@GetSubNavLinkClass()">
                        <MudText Typo="Typo.body2">إدارة صور شرائح خدماتنا</MudText>
                    </MudNavLink>
                </MudNavGroup>

                <!-- About Us Management -->
                <MudNavGroup Title="إدارة من نحن"
                             Icon="@Icons.Material.Filled.Info"
                             Expanded="@_expandedGroups.Contains("about")"
                             Class="@GetNavGroupClass()">
                    <MudNavLink OnClick="@(() => NavigateTo("/admin/aboutus", true))"
                                Icon="@Icons.Material.Filled.Info"
                                Class="@GetSubNavLinkClass()">
                        <MudText Typo="Typo.body2">من نحن</MudText>
                    </MudNavLink>
                    <MudNavLink OnClick="@(() => NavigateTo("/admin/siderAboutUs", true))"
                                Icon="@Icons.Material.Filled.Image"
                                Class="@GetSubNavLinkClass()">
                        <MudText Typo="Typo.body2">إدارة صور شرائح من نحن</MudText>
                    </MudNavLink>
                </MudNavGroup>

                <!-- Mobile-only user menu -->
                <MudHidden Breakpoint="Breakpoint.MdAndUp">
                    <MudDivider Class="my-3" />
                    <MudNavLink OnClick="@(() => NavigateTo("/admin/profile"))"
                                Icon="@Icons.Material.Filled.Person"
                                Class="@GetNavLinkClass()">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">
                            الملف الشخصي
                        </MudText>
                    </MudNavLink>
                    <MudNavLink OnClick="@(() => NavigateTo("/admin/settings"))"
                                Icon="@Icons.Material.Filled.Settings"
                                Class="@GetNavLinkClass()">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">
                            الإعدادات
                        </MudText>
                    </MudNavLink>
                </MudHidden>

                <MudDivider Class="my-3" />

                <!-- Return to website -->
                <MudNavLink OnClick="@(() => NavigateTo("/", true))"
                            Icon="@Icons.Material.Filled.ArrowBack"
                            Class="@GetNavLinkClass()">
                    <div class="d-flex align-center gap-3 w-100">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">
                            العودة للموقع
                        </MudText>
                    </div>
                </MudNavLink>

                <!-- Mobile logout -->
                <MudHidden Breakpoint="Breakpoint.MdAndUp">
                    <MudNavLink OnClick="@(() => NavigateTo("/Account/Logout", true))"
                                Icon="@Icons.Material.Filled.Logout"
                                Class="@GetNavLinkClass()">
                        <MudText Typo="Typo.body2" Style="font-weight: 500; color: var(--mud-palette-error);">
                            تسجيل الخروج
                        </MudText>
                    </MudNavLink>
                </MudHidden>
            </MudNavMenu>
        </MudDrawer>
        <MudMainContent Class="@GetMainContentClass()" Style="@GetMainContentStyle()">
            <MudContainer MaxWidth="MaxWidth.False" Class="@GetContentContainerClass()">
                @Body
            </MudContainer>
        </MudMainContent>
    </MudLayout>
</MudRTLProvider>

<style>
    /* Custom CSS for enhanced responsive design */
    .admin-appbar {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10px);
        background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-primary-darken) 100%);
    }

    .admin-appbar-mobile {
        height: 56px !important;
    }

    .admin-appbar-desktop {
        height: 64px !important;
    }

    .admin-drawer {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .admin-drawer-mobile {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    }

    .admin-drawer-desktop {
        border-right: 1px solid rgba(0, 0, 0, 0.08);
    }

    .admin-drawer-header {
        background: linear-gradient(135deg, rgba(149, 55, 53, 0.05) 0%, rgba(149, 55, 53, 0.1) 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        padding: 16px 20px;
        min-height: 80px;
    }

    .admin-nav-menu {
        padding: 8px 12px;
    }

    .admin-nav-link {
        margin: 2px 0;
        border-radius: 8px;
        min-height: 44px;
        transition: all 0.2s ease-in-out;
    }

    .admin-nav-link:hover {
        background-color: rgba(149, 55, 53, 0.08);
        transform: translateX(-2px);
    }

    .admin-nav-group {
        margin: 4px 0;
    }

    .admin-sub-nav-link {
        margin: 1px 0;
        margin-right: 16px;
        border-radius: 6px;
        min-height: 40px;
        transition: all 0.2s ease-in-out;
    }

    .admin-sub-nav-link:hover {
        background-color: rgba(149, 55, 53, 0.06);
        transform: translateX(-2px);
    }

    .admin-menu-button {
        transition: all 0.2s ease-in-out;
    }

    .admin-menu-button:hover {
        background-color: rgba(255, 255, 255, 0.1);
        transform: scale(1.05);
    }

    .admin-action-button {
        transition: all 0.2s ease-in-out;
    }

    .admin-action-button:hover {
        background-color: rgba(255, 255, 255, 0.1);
        transform: scale(1.05);
    }

    .admin-title {
        transition: all 0.3s ease-in-out;
    }

    .admin-main-content {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
        min-height: calc(100vh - 64px);
        width: 100% !important;
        margin-left: 0 !important;
    }

    .admin-main-content-mobile {
        padding-top: 56px;
        min-height: calc(100vh - 56px);
    }

    .admin-content-container {
        padding: 24px;
        max-width: 100%;
        width: 100%;
        box-sizing: border-box;
    }

    .admin-content-container-mobile {
        padding: 16px 12px;
    }

    /* Focus styles for accessibility */
    .admin-nav-link:focus,
    .admin-sub-nav-link:focus,
    .admin-menu-button:focus,
    .admin-action-button:focus {
        outline: 2px solid var(--mud-palette-primary);
        outline-offset: 2px;
    }

    /* Prevent layout shifts when dialogs are opened */
    .mud-layout {
        overflow-x: hidden;
    }

    .mud-main-content {
        width: 100% !important;
        margin-left: 0 !important;
        transition: none !important;
    }

    /* Ensure drawer doesn't affect main content layout */
    .mud-drawer-temporary {
        position: fixed !important;
        z-index: 1300;
    }
</style>

@code {
    private bool _drawerOpen = false;
    private bool _isMobile = false;
    private bool _isTablet = false;
    private string _currentPath = "";
    private readonly HashSet<string> _expandedGroups = new();
    private List<BreadcrumbItem> _breadcrumbItems = new();

    private MudTheme _theme = new MudTheme()
    {
        PaletteDark = new PaletteDark(),
        PaletteLight = new PaletteLight()
        {
            Primary = "#953735",
            Secondary = "#595959",
            AppbarBackground = "#953735",
            Background = "#FFFFFF",
            DrawerBackground = "#FFF",
            DrawerText = "rgba(0,0,0, 0.7)",
            Surface = "#FFF",
            PrimaryDarken = "#7a2d2b",
            Error = "#f44336",
        },
        Typography = new Typography()
        {
            Default = new MudBlazor.DefaultTypography()
            {
                FontFamily = new[] { "Tajawal", "Roboto", "Helvetica", "Arial", "sans-serif" },
                FontSize = "0.9rem",
                FontWeight = "400",
                LineHeight = "1.43",
                LetterSpacing = ".01071em"
            }
        }
    };

    protected override void OnInitialized()
    {
        _currentPath = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
        UpdateBreadcrumbs();
        base.OnInitialized();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await DetectScreenSize();
        }
        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task DetectScreenSize()
    {
        try
        {
            // Enhanced device detection with multiple breakpoints
            var isMobile = await _JSRuntime.InvokeAsync<bool>("window.matchMedia", "(max-width: 767px)").AsTask();
            var isTablet = await _JSRuntime.InvokeAsync<bool>("window.matchMedia", "(min-width: 768px) and (max-width: 1023px)").AsTask();

            _isMobile = isMobile;
            _isTablet = isTablet;

            // Set initial drawer state based on screen size
            // Always start with drawer closed to prevent layout issues
            _drawerOpen = false;

            // Expand relevant nav groups based on current path
            UpdateExpandedGroups();

            StateHasChanged();
        }
        catch
        {
            // Fallback: assume mobile if detection fails
            _isMobile = true;
            _isTablet = false;
            _drawerOpen = false;
            StateHasChanged();
        }
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        _currentPath = NavigationManager.ToBaseRelativePath(e.Location);
        UpdateBreadcrumbs();
        UpdateExpandedGroups();

        // Auto-close drawer on mobile after navigation
        if (_isMobile && _drawerOpen)
        {
            _drawerOpen = false;
        }

        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }

    private void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }

    private void NavigateTo(string url, bool forceLoad = false)
    {
        try
        {
            NavigationManager.NavigateTo(url, forceLoad);

            // Close drawer on mobile after navigation
            if (_isMobile)
            {
                _drawerOpen = false;
                StateHasChanged();
            }
        }
        catch (Exception)
        {
            // Fallback navigation with force reload
            NavigationManager.NavigateTo(url, true);
        }
    }

    private void UpdateBreadcrumbs()
    {
        _breadcrumbItems.Clear();

        var pathSegments = _currentPath.Split('/', StringSplitOptions.RemoveEmptyEntries);

        if (pathSegments.Length > 0 && pathSegments[0] == "admin")
        {
            _breadcrumbItems.Add(new BreadcrumbItem("الرئيسية", "/admin/dashboard"));

            if (pathSegments.Length > 1)
            {
                var section = pathSegments[1];
                var breadcrumbText = section switch
                {
                    "dashboard" => "لوحة التحكم",
                    "posts" => "المناشير",
                    "categories" => "الأقسام",
                    "client" => "العملاء",
                    "animatedgifs" => "الصور المتحركة",
                    "ourServicesManager" => "خدماتنا",
                    "aboutus" => "من نحن",
                    "profile" => "الملف الشخصي",
                    "settings" => "الإعدادات",
                    _ => section
                };

                _breadcrumbItems.Add(new BreadcrumbItem(breadcrumbText, null, true));
            }
        }
    }

    private void UpdateExpandedGroups()
    {
        _expandedGroups.Clear();

        if (_currentPath.Contains("/admin/posts") || _currentPath.Contains("/admin/newsSiderMove"))
        {
            _expandedGroups.Add("posts");
        }
        else if (_currentPath.Contains("/admin/animatedgifs"))
        {
            _expandedGroups.Add("animations");
        }
        else if (_currentPath.Contains("/admin/ourServicesManager") || _currentPath.Contains("/admin/sidertMoveServices"))
        {
            _expandedGroups.Add("services");
        }
        else if (_currentPath.Contains("/admin/aboutus") || _currentPath.Contains("/admin/siderAboutUs"))
        {
            _expandedGroups.Add("about");
        }
    }

    // CSS Class Helper Methods
    private string GetAppBarClass()
    {
        var classes = new List<string> { "admin-appbar" };

        if (_isMobile)
            classes.Add("admin-appbar-mobile");
        else
            classes.Add("admin-appbar-desktop");

        return string.Join(" ", classes);
    }

    private string GetMenuButtonClass()
    {
        return "admin-menu-button";
    }

    private string GetTitleClass()
    {
        var classes = new List<string> { "admin-title" };

        if (_isMobile)
            classes.Add("ml-2");
        else
            classes.Add("ml-3");

        return string.Join(" ", classes);
    }

    private string GetActionButtonClass()
    {
        return "admin-action-button";
    }

    private int GetDrawerElevation()
    {
        return _isMobile ? 8 : 2;
    }

    private DrawerVariant GetDrawerVariant()
    {
        // Use Temporary variant for all screen sizes to prevent layout issues with dialogs
        return DrawerVariant.Temporary;
    }

    private string GetDrawerWidth()
    {
        if (_isMobile) return "280px";
        if (_isTablet) return "260px";
        return "280px";
    }

    private string GetDrawerClass()
    {
        var classes = new List<string> { "admin-drawer" };

        if (_isMobile)
            classes.Add("admin-drawer-mobile");
        else
            classes.Add("admin-drawer-desktop");

        return string.Join(" ", classes);
    }

    private string GetDrawerStyle()
    {
        return $"width: {GetDrawerWidth()};";
    }

    private string GetDrawerHeaderClass()
    {
        return "admin-drawer-header";
    }

    private string GetNavMenuClass()
    {
        return "admin-nav-menu";
    }

    private string GetNavLinkClass()
    {
        return "admin-nav-link";
    }

    private string GetNavGroupClass()
    {
        return "admin-nav-group";
    }

    private string GetSubNavLinkClass()
    {
        return "admin-sub-nav-link";
    }

    private string GetMainContentClass()
    {
        var classes = new List<string> { "admin-main-content" };

        if (_isMobile)
            classes.Add("admin-main-content-mobile");

        return string.Join(" ", classes);
    }

    private string GetMainContentStyle()
    {
        var paddingTop = _isMobile ? "56px" : "64px";
        return $"padding-top: {paddingTop}; min-height: calc(100vh - {paddingTop});";
    }

    private string GetContentContainerClass()
    {
        var classes = new List<string> { "admin-content-container" };

        if (_isMobile)
            classes.Add("admin-content-container-mobile");

        return string.Join(" ", classes);
    }
}
