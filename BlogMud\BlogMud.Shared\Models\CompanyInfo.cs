using System.ComponentModel.DataAnnotations;

namespace BlogMud.Shared.Models
{
    public class CompanyInfo
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        [StringLength(500)]
        public string Vision { get; set; }
        
        [StringLength(500)]
        public string Mission { get; set; }
        
        [StringLength(2000)]
        public string AboutUs { get; set; }
        
        [StringLength(100)]
        public string Phone { get; set; }
        
        [StringLength(100)]
        public string Email { get; set; }
        
        [StringLength(200)]
        public string Address { get; set; }
        
        [StringLength(100)]
        public string? FacebookUrl { get; set; }
        
        [StringLength(100)]
        public string? TwitterUrl { get; set; }
        
        [StringLength(100)]
        public string? InstagramUrl { get; set; }
        
        [StringLength(100)]
        public string? LinkedInUrl { get; set; }
        
        public string? LogoUrl { get; set; }
    }
}
