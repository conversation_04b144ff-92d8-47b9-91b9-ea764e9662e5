@using BlogMud.Shared.DTOs
@using System.Net.Http.Headers

<AboutUsEditFormCSS />
<MudDialog Class="about-us-management-dialog" MaxWidth="MaxWidth.Large">
  
    <TitleContent>
        <div class="dialog-title-container">
            <MudIcon Icon="@(AboutUs.Id > 0 ? Icons.Material.Filled.Edit : Icons.Material.Filled.Add)"
                     Class="dialog-title-icon" />
            <MudText Typo="Typo.h5" Class="dialog-title-text">
                @GetFormTitle()
            </MudText>
        </div>
    </TitleContent>
    <DialogContent>
     
        <div class="dialog-content-container">
            <MudForm @ref="_form" Model="@AboutUs" Validation="@(new Func<object, IEnumerable<string>>(ValidateModel))" Class="about-us-form">

                <!-- Tabbed Interface -->
                <MudTabs Elevation="1" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6" @bind-ActivePanelIndex="activeTabIndex">

                    <!-- Basic Information Tab -->
                    <MudTabPanel Text="المعلومات الأساسية" Icon="@Icons.Material.Filled.Business">
                        <div class="form-section-container">
                            <!-- Company Logo Section -->
                            <MudPaper Class="form-section" Elevation="1">
                                <div class="section-title">
                                    <MudIcon Icon="@Icons.Material.Filled.Image" Class="section-icon" />
                                    <MudText Typo="Typo.h6" Class="section-text">شعار الشركة</MudText>
                                </div>

                                <MudGrid>
                                    <MudItem xs="12">
                                        <MudPaper Class="upload-section" Elevation="0">
                                            <div class="upload-header">
                                                <MudIcon Icon="@Icons.Material.Filled.CloudUpload" Class="upload-icon" />
                                                <div class="upload-text">
                                                    <MudText Typo="Typo.subtitle1">تحميل شعار الشركة</MudText>
                                                    <MudText Typo="Typo.body2" Color="Color.Secondary">اختر صورة شعار الشركة (PNG, JPG, SVG)</MudText>
                                                </div>
                                            </div>

                                            <div class="upload-content">
                                                @if (!IsViewMode)
                                                {
                                                    <MudFileUpload T="IBrowserFile" Accept=".png, .jpg, .jpeg, .gif, .svg" FilesChanged="@UploadCompanyLogo" Class="file-upload">
                                                        <ActivatorContent>
                                                            <MudButton Variant="Variant.Outlined"
                                                                       Color="Color.Primary"
                                                                       StartIcon="@Icons.Material.Filled.CloudUpload"
                                                                       FullWidth="true"
                                                                       Class="upload-button">
                                                                تحميل شعار الشركة
                                                            </MudButton>
                                                        </ActivatorContent>
                                                    </MudFileUpload>
                                                }

                                                @if (!string.IsNullOrEmpty(AboutUs.CompanyLogoUrl))
                                                {
                                                    <div class="image-preview">
                                                        <MudImage Src="@AboutUs.CompanyLogoUrl"
                                                                  Alt="شعار الشركة"
                                                                  Class="preview-image"
                                                                  ObjectFit="ObjectFit.Cover" />
                                                        <MudText Typo="Typo.caption" Class="image-caption">
                                                            شعار الشركة
                                                        </MudText>
                                                        @if (!IsViewMode)
                                                        {
                                                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                                          Color="Color.Error"
                                                                          Size="Size.Small"
                                                                          OnClick="RemoveCompanyLogo"
                                                                          Class="delete-button"
                                                                          Title="حذف الشعار" />
                                                        }
                                                    </div>
                                                }
                                            </div>
                                        </MudPaper>
                                    </MudItem>
                                </MudGrid>
                            </MudPaper>


                            <!-- Production Capacity Items Summary -->
                            <MudPaper Class="form-section" Elevation="1">
                                <div class="section-title">
                                    <MudIcon Icon="@Icons.Material.Filled.Inventory" Class="section-icon" />
                                    <MudText Typo="Typo.h6" Class="section-text">عناصر الطاقة الإنتاجية</MudText>
                                    <MudChip T="bool" Size="Size.Small" Color="Color.Info" Class="ml-2">
                                        @AboutUs.ProductionCapacityItems.Count / 20
                                    </MudChip>
                                </div>

                              @*   <MudGrid>
                                    <MudItem xs="12">
                                        @if (AboutUs.ProductionCapacityItems.Count > 0)
                                        {
                                            <div class="production-items-preview mb-3">
                                                @foreach (var item in AboutUs.ProductionCapacityItems.Take(3))
                                                {
                                                    <MudChip T="bool" Size="Size.Small"
                                                            Color="@(item.IsActive ? Color.Success : Color.Default)"
                                                            Class="ma-1">
                                                        @(item.Title?.Length > 15 ? item.Title.Substring(0, 15) + "..." : item.Title)
                                                    </MudChip>
                                                }
                                                @if (AboutUs.ProductionCapacityItems.Count > 3)
                                                {
                                                    <MudChip T="bool" Size="Size.Small" Color="Color.Secondary" Class="ma-1">
                                                        +@(AboutUs.ProductionCapacityItems.Count - 3) أخرى
                                                    </MudChip>
                                                }
                                            </div>
                                        }

                                        @if (AboutUs.ProductionCapacityItems.Count < 20 && !IsViewMode)
                                        {
                                            <MudButton Variant="Variant.Outlined"
                                                      Color="Color.Primary"
                                                      StartIcon="@Icons.Material.Filled.Add"
                                                      OnClick="ToggleProductionItemsSection"
                                                      FullWidth="true"
                                                      Class="upload-button">
                                                إدارة العناصر
                                            </MudButton>
                                        }
                                        else if (!IsViewMode)
                                        {
                                            <MudAlert Severity="Severity.Warning" Dense="true">
                                                تم الوصول للحد الأقصى (20 عنصر)
                                            </MudAlert>
                                        }
                                    </MudItem>
                                </MudGrid> *@
                            </MudPaper>
                        </div>
                    </MudTabPanel>

                    <!-- Company Description Tab -->
                    <MudTabPanel Text="وصف الشركة" Icon="@Icons.Material.Filled.Description">
                        <div class="form-section-container">
                            <!-- Company Description Section -->
                            <MudPaper Class="form-section" Elevation="1">
                                <div class="section-title">
                                    <MudIcon Icon="@Icons.Material.Filled.Description" Class="section-icon" />
                                    <MudText Typo="Typo.h6" Class="section-text">وصف الشركة</MudText>
                                </div>

                                <MudGrid>
                                    <MudItem xs="12">
                                        <MudTextField @bind-Value="AboutUs.CompanyDescription"
                                                     Label="وصف الشركة (مطلوب)"
                                                     Lines="8"
                                                     Variant="Variant.Outlined"
                                                     Required="true"
                                                     ReadOnly="IsViewMode"
                                                     Class="content-field"
                                                     HelperText="نظرة عامة على الشركة وأنشطتها"
                                                     MaxLength="2000"
                                                     Counter="2000"
                                                     Immediate="true"
                                                     OnBlur="UpdateFormProgressAsync" />

                                        @if (!string.IsNullOrEmpty(AboutUs.CompanyDescription))
                                        {
                                            <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mt-2">
                                                عدد الكلمات: @(AboutUs.CompanyDescription.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length)
                                            </MudText>
                                        }
                                    </MudItem>
                                </MudGrid>
                            </MudPaper>

                            <!-- Company Capabilities Section -->
                            <MudPaper Class="form-section" Elevation="1">
                                <div class="section-title">
                                    <MudIcon Icon="@Icons.Material.Filled.Star" Class="section-icon" />
                                    <MudText Typo="Typo.h6" Class="section-text">قدرات ومميزات الشركة</MudText>
                                </div>

                                <MudGrid>
                                    <MudItem xs="12">
                                        <MudTextField @bind-Value="AboutUs.CompanyCapabilities"
                                                     Label="قدرات ومميزات الشركة (مطلوب)"
                                                     Lines="8"
                                                     Variant="Variant.Outlined"
                                                     Required="true"
                                                     ReadOnly="IsViewMode"
                                                     Class="content-field"
                                                     HelperText="المميزات والقدرات التي تتمتع بها الشركة"
                                                     MaxLength="2000"
                                                     Counter="2000"
                                                     Immediate="true"
                                                     OnBlur="UpdateFormProgressAsync" />

                                        @if (!string.IsNullOrEmpty(AboutUs.CompanyCapabilities))
                                        {
                                            <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mt-2">
                                                عدد الكلمات: @(AboutUs.CompanyCapabilities.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length)
                                            </MudText>
                                        }
                                    </MudItem>
                                </MudGrid>
                            </MudPaper>
                        </div>
                    </MudTabPanel>

                    <!-- Production Capacity Management Tab -->
                    <MudTabPanel Text="إدارة العناصر" Icon="@Icons.Material.Filled.Inventory">
                        <div class="form-section-container">
                            <!-- Add/Edit Item Form Section -->
                            @if (!IsViewMode && AboutUs.ProductionCapacityItems.Count < 20)
                            {
                                <MudPaper Class="form-section" Elevation="1" Style="border: 2px dashed var(--mud-palette-primary);">
                                    <div class="section-title">
                                        <MudIcon Icon="@Icons.Material.Filled.Add" Class="section-icon" />
                                        <MudText Typo="Typo.h6" Class="section-text">إضافة عنصر طاقة إنتاجية جديد</MudText>
                                        <MudButton Variant="@(_showAddItemForm ? Variant.Text : Variant.Filled)"
                                                  Color="Color.Primary"
                                                  StartIcon="@(_showAddItemForm ? Icons.Material.Filled.ExpandLess : Icons.Material.Filled.Add)"
                                                  OnClick="ToggleAddItemForm"
                                                  Class="upload-button ml-auto">
                                            @(_showAddItemForm ? "إخفاء النموذج" : "إضافة عنصر جديد")
                                        </MudButton>
                                    </div>

                                    <!-- Inline Add/Edit Form -->
                                    @if (_showAddItemForm)
                                    {
                                        <MudGrid>
                                            <!-- Basic Information Section -->
                                            <MudItem xs="12" md="6">
                                                <MudPaper Class="form-section" Elevation="1">
                                                    <div class="section-title">
                                                        <MudIcon Icon="@Icons.Material.Filled.Info" Class="section-icon" />
                                                        <MudText Typo="Typo.h6" Class="section-text">معلومات العنصر الأساسية</MudText>
                                                    </div>

                                                    <MudGrid>
                                                        <MudItem xs="12">
                                                            <MudTextField @bind-Value="_currentProductionItem.Title"
                                                                         Label="عنوان العنصر (مطلوب)"
                                                                         Variant="Variant.Outlined"
                                                                         Required="true"
                                                                         MaxLength="200"
                                                                         Counter="200"
                                                                         Immediate="true"
                                                                         Class="content-field mb-3"
                                                                         HelperText="عنوان مختصر وواضح لعنصر الطاقة الإنتاجية" />
                                                        </MudItem>
                                                        <MudItem xs="12">
                                                            <MudTextField @bind-Value="_currentProductionItem.Description"
                                                                         Label="وصف العنصر (مطلوب)"
                                                                         Lines="4"
                                                                         Variant="Variant.Outlined"
                                                                         Required="true"
                                                                         MaxLength="1000"
                                                                         Counter="1000"
                                                                         Immediate="true"
                                                                         Class="content-field"
                                                                         HelperText="وصف تفصيلي ومفيد لعنصر الطاقة الإنتاجية" />
                                                        </MudItem>
                                                    </MudGrid>
                                                </MudPaper>
                                            </MudItem>

                                            <!-- Image Upload and Status -->
                                            <MudItem xs="12" md="6">
                                                <MudPaper Class="form-section" Elevation="1">
                                                    <div class="section-title">
                                                        <MudIcon Icon="@Icons.Material.Filled.Image" Class="section-icon" />
                                                        <MudText Typo="Typo.h6" Class="section-text">صورة العنصر</MudText>
                                                    </div>

                                                    <MudGrid>
                                                        <MudItem xs="12">
                                                            <MudPaper Class="upload-section" Elevation="0">
                                                                <div class="upload-header">
                                                                    <MudIcon Icon="@Icons.Material.Filled.CloudUpload" Class="upload-icon" />
                                                                    <div class="upload-text">
                                                                        <MudText Typo="Typo.subtitle1">تحميل صورة العنصر</MudText>
                                                                        <MudText Typo="Typo.body2" Color="Color.Secondary">اختيارية - PNG, JPG, GIF</MudText>
                                                                    </div>
                                                                </div>

                                                                <div class="upload-content">
                                                                    <MudFileUpload T="IBrowserFile" Accept=".png, .jpg, .jpeg, .gif" FilesChanged="@UploadProductionItemImage" Class="file-upload">
                                                                        <ActivatorContent>
                                                                            <MudButton Variant="Variant.Outlined"
                                                                                       Color="Color.Secondary"
                                                                                       StartIcon="@Icons.Material.Filled.CloudUpload"
                                                                                       FullWidth="true"
                                                                                       Class="upload-button">
                                                                                تحميل صورة العنصر
                                                                            </MudButton>
                                                                        </ActivatorContent>
                                                                    </MudFileUpload>

                                                                    @if (!string.IsNullOrEmpty(_currentProductionItem.ImageUrl))
                                                                    {
                                                                        <div class="image-preview">
                                                                            <MudImage Src="@_currentProductionItem.ImageUrl"
                                                                                      Alt="صورة العنصر"
                                                                                      Class="preview-image"
                                                                                      ObjectFit="ObjectFit.Cover" />
                                                                            <MudText Typo="Typo.caption" Class="image-caption">
                                                                                صورة العنصر
                                                                            </MudText>
                                                                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                                                          Color="Color.Error"
                                                                                          Size="Size.Small"
                                                                                          OnClick="RemoveProductionItemImage"
                                                                                          Class="delete-button"
                                                                                          Title="حذف الصورة" />
                                                                        </div>
                                                                    }
                                                                </div>
                                                            </MudPaper>
                                                        </MudItem>
                                                        <MudItem xs="12">
                                                            <MudSwitch T="bool" @bind-Checked="_currentProductionItem.IsActive"
                                                                      Label="العنصر نشط"
                                                                      Color="Color.Primary"
                                                                      Class="mt-3" />
                                                            <MudText Typo="Typo.body2" Color="@(_currentProductionItem.IsActive ? Color.Success : Color.Secondary)" Class="mt-2">
                                                                @(_currentProductionItem.IsActive ? "العنصر نشط وسيظهر للزوار" : "العنصر غير نشط ولن يظهر للزوار")
                                                            </MudText>
                                                        </MudItem>
                                                    </MudGrid>
                                                </MudPaper>
                                            </MudItem>

                                            <!-- Action Buttons -->
                                            <MudItem xs="12">
                                                <MudDivider Class="my-4" />
                                                <div class="action-buttons">
                                                    <MudButton OnClick="CancelAddItemForm"
                                                              Color="Color.Secondary"
                                                              Variant="Variant.Outlined"
                                                              StartIcon="@Icons.Material.Filled.Cancel"
                                                              Class="cancel-button">
                                                        إلغاء
                                                    </MudButton>
                                                    <MudButton OnClick="SaveProductionItem"
                                                              Color="Color.Primary"
                                                              Variant="Variant.Filled"
                                                              StartIcon="@(_isEditingProductionItem ? Icons.Material.Filled.Update : Icons.Material.Filled.Add)"
                                                              Disabled="_isSubmittingProductionItem"
                                                              Class="submit-button">
                                                        @if (_isSubmittingProductionItem)
                                                        {
                                                            <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                                            <span class="mr-2">جاري المعالجة...</span>
                                                        }
                                                        else
                                                        {
                                                            @(_isEditingProductionItem ? "تحديث العنصر" : "إضافة العنصر")
                                                        }
                                                    </MudButton>
                                                </div>
                                            </MudItem>
                                        </MudGrid>
                                    }
                                </MudPaper>
                            }

                            <!-- Existing Items Display -->
                            @if (AboutUs.ProductionCapacityItems.Count > 0)
                            {
                                <MudPaper Class="form-section" Elevation="1">
                                    <div class="section-title">
                                        <MudIcon Icon="@Icons.Material.Filled.List" Class="section-icon" />
                                        <MudText Typo="Typo.h6" Class="section-text">العناصر المضافة (@AboutUs.ProductionCapacityItems.Count)</MudText>
                                    </div>

                                    <MudGrid>
                                        @foreach (var item in AboutUs.ProductionCapacityItems.OrderBy(x => x.DisplayOrder))
                                        {
                                            <MudItem xs="12" md="6" lg="4">
                                                <MudCard Class="media-card" Elevation="2" Style="height: 100%;">
                                                    @if (!string.IsNullOrEmpty(item.ImageUrl))
                                                    {
                                                        <MudCardMedia Image="@item.ImageUrl" Height="150" />
                                                    }
                                                    else
                                                    {
                                                        <MudPaper Class="d-flex align-center justify-center" Height="150" Elevation="0"
                                                                 Style="background-color: var(--mud-palette-grey-lighten-4);">
                                                            <MudIcon Icon="@Icons.Material.Filled.Image" Size="Size.Large" Color="Color.Secondary" />
                                                        </MudPaper>
                                                    }

                                                    <MudCardContent>
                                                        <div class="d-flex align-center justify-space-between mb-2">
                                                            <MudText Typo="Typo.h6" Class="font-weight-bold">
                                                                @item.Title
                                                            </MudText>
                                                            <MudIcon Icon="@(item.IsActive ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)"
                                                                     Color="@(item.IsActive ? Color.Success : Color.Error)" />
                                                        </div>
                                                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                                                            @(item.Description?.Length > 120 ? item.Description.Substring(0, 120) + "..." : item.Description)
                                                        </MudText>
                                                    </MudCardContent>

                                                    @if (!IsViewMode)
                                                    {
                                                        <MudCardActions>
                                                            <MudTooltip Text="تعديل العنصر">
                                                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                                                              Color="Color.Primary"
                                                                              OnClick="@(() => EditProductionCapacityItemInline(item))"
                                                                              Class="action-icon-button" />
                                                            </MudTooltip>
                                                            <MudTooltip Text="حذف العنصر">
                                                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                                              Color="Color.Error"
                                                                              OnClick="@(() => DeleteProductionCapacityItem(item))"
                                                                              Class="action-icon-button" />
                                                            </MudTooltip>
                                                        </MudCardActions>
                                                    }
                                                </MudCard>
                                            </MudItem>
                                        }
                                    </MudGrid>
                                </MudPaper>
                            }
                            else if (!IsViewMode)
                            {
                                <MudAlert Severity="Severity.Info" Dense="true" Class="mt-4">
                                    لم يتم إضافة أي عناصر للطاقة الإنتاجية بعد. انقر على "إضافة عنصر جديد" لبدء الإضافة.
                                </MudAlert>
                            }
                        </div>
                    </MudTabPanel>

                    <!-- Settings and Metadata Tab -->
                    <MudTabPanel Text="الإعدادات" Icon="@Icons.Material.Filled.Settings">
                        <div class="form-section-container">
                            <!-- Display Settings Section -->
                            <MudPaper Class="form-section" Elevation="1">
                                <div class="section-title">
                                    <MudIcon Icon="@Icons.Material.Filled.Settings" Class="section-icon" />
                                    <MudText Typo="Typo.h6" Class="section-text">إعدادات العرض</MudText>
                                </div>

                                <MudGrid>
                                    <MudItem xs="12" md="6">
                                        <MudNumericField @bind-Value="AboutUs.DisplayOrder"
                                                        Label="ترتيب العرض"
                                                        Variant="Variant.Outlined"
                                                        Min="0"
                                                        ReadOnly="IsViewMode"
                                                        Class="content-field"
                                                        HelperText="ترتيب ظهور المحتوى (0 = الأول)"
                                                        OnBlur="UpdateFormProgressAsync" />
                                    </MudItem>
                                    <MudItem xs="12" md="6">
                                        <div class="d-flex align-center gap-3 mt-4">
                                            <MudSwitch T="bool" @bind-Checked="AboutUs.IsActive"
                                                      Label="المحتوى نشط"
                                                      Color="Color.Primary"
                                                      ReadOnly="IsViewMode" />
                                            <MudText Typo="Typo.body2" Color="@(AboutUs.IsActive ? Color.Success : Color.Secondary)">
                                                @(AboutUs.IsActive ? "مرئي للزوار" : "مخفي عن الزوار")
                                            </MudText>
                                        </div>
                                    </MudItem>
                                </MudGrid>
                            </MudPaper>

                            <!-- Metadata Information Section -->
                            @if (!IsCreateMode && AboutUs.Id > 0)
                            {
                                <MudPaper Class="form-section" Elevation="1">
                                    <div class="section-title">
                                        <MudIcon Icon="@Icons.Material.Filled.Info" Class="section-icon" />
                                        <MudText Typo="Typo.h6" Class="section-text">معلومات النظام</MudText>
                                    </div>

                                    <MudGrid>
                                        <MudItem xs="12" md="4">
                                            <div class="metadata-item">
                                                <MudIcon Icon="@Icons.Material.Filled.CalendarToday" Size="Size.Small" Color="Color.Primary" />
                                                <div>
                                                    <MudText Typo="Typo.caption" Color="Color.Secondary">تاريخ الإنشاء:</MudText>
                                                    <MudText Typo="Typo.body2">@AboutUs.CreatedAt.ToString("yyyy-MM-dd HH:mm")</MudText>
                                                </div>
                                            </div>
                                        </MudItem>
                                        @if (AboutUs.LastModifiedAt.HasValue)
                                        {
                                            <MudItem xs="12" md="4">
                                                <div class="metadata-item">
                                                    <MudIcon Icon="@Icons.Material.Filled.Update" Size="Size.Small" Color="Color.Secondary" />
                                                    <div>
                                                        <MudText Typo="Typo.caption" Color="Color.Secondary">آخر تعديل:</MudText>
                                                        <MudText Typo="Typo.body2">@AboutUs.LastModifiedAt.Value.ToString("yyyy-MM-dd HH:mm")</MudText>
                                                    </div>
                                                </div>
                                            </MudItem>
                                        }
                                        <MudItem xs="12" md="4">
                                            <div class="metadata-item">
                                                <MudIcon Icon="@Icons.Material.Filled.Tag" Size="Size.Small" Color="Color.Info" />
                                                <div>
                                                    <MudText Typo="Typo.caption" Color="Color.Secondary">معرف المحتوى:</MudText>
                                                    <MudText Typo="Typo.body2">#@AboutUs.Id</MudText>
                                                </div>
                                            </div>
                                        </MudItem>
                                    </MudGrid>
                                </MudPaper>
                            }
                            else
                            {
                                <MudAlert Severity="Severity.Info" Dense="true" Class="mt-4">
                                    <div class="d-flex align-center gap-2">
                                        <MudIcon Icon="@Icons.Material.Filled.Info" />
                                        <MudText>سيتم إنشاء معلومات النظام تلقائياً عند الحفظ</MudText>
                                    </div>
                                </MudAlert>
                            }
                        </div>
                    </MudTabPanel>
                </MudTabs>
            </MudForm>
        </div>
    </DialogContent>
    <DialogActions>
        <div class="dialog-actions">
            <div class="action-buttons">
                @if (!IsViewMode)
                {
                    <MudButton Variant="Variant.Text"
                               Color="Color.Default"
                               OnClick="Cancel"
                               StartIcon="@Icons.Material.Filled.Cancel"
                               Class="cancel-button">
                        إلغاء
                    </MudButton>
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               OnClick="Submit"
                               Disabled="_isSubmitting"
                               StartIcon="@(IsCreateMode ? Icons.Material.Filled.Save : Icons.Material.Filled.Update)"
                               Class="submit-button">
                        @if (_isSubmitting)
                        {
                            <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                            <span class="mr-2">جاري المعالجة...</span>
                        }
                        else
                        {
                            @(IsCreateMode ? "حفظ المحتوى" : "تحديث المحتوى")
                        }
                    </MudButton>
                }
                else
                {
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               OnClick="Cancel"
                               StartIcon="@Icons.Material.Filled.Close"
                               Class="submit-button">
                        إغلاق
                    </MudButton>
                }
            </div>
        </div>
    </DialogActions>
</MudDialog>




