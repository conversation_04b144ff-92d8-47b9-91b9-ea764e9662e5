@page "/branches"


<PageTitle>الفروع | @(_companyInfo?.Name ?? "شركتنا")</PageTitle>

<!-- Hero Section -->
<div class="position-relative" style="background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url('/images/branches-hero.jpg') no-repeat center center; background-size: cover; height: 300px;">
    <div class="d-flex flex-column justify-center align-center h-100">
        <MudContainer Class="text-center">
            <MudText Typo="Typo.h2" Color="Color.Surface">فروعنا</MudText>
            <MudText Typo="Typo.subtitle1" Color="Color.Surface">تواصل معنا في أقرب فرع إليك</MudText>
        </MudContainer>
    </div>
</div>

<MudContainer MaxWidth="MaxWidth.Large" Class="py-16">
    @if (_loading)
    {
        <div class="d-flex justify-center my-8">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
        </div>
    }
    else if (_branches != null && _branches.Any())
    {
        <!-- Branches Map -->
        <MudPaper Elevation="2" Class="mb-12 overflow-hidden">
            <div id="map" style="height: 500px;"></div>
        </MudPaper>
        
        <!-- Branches List -->
        <MudGrid>
            @foreach (var branch in _branches)
            {
                <MudItem xs="12" sm="6" md="6" lg="4">
                    <MudCard Elevation="2" Class="h-100 branch-card">
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h5">@branch.Name</MudText>
                            </CardHeaderContent>
                            <CardHeaderActions>
                                <MudIconButton Icon="@Icons.Material.Filled.LocationOn" Color="Color.Primary" Size="Size.Small" 
                                             OnClick="@(() => ShowBranchOnMap(branch))" />
                            </CardHeaderActions>
                        </MudCardHeader>
                        <MudCardContent>
                            <div class="d-flex flex-column gap-2">
                                <div class="d-flex gap-3 align-center">
                                    <MudIcon Icon="@Icons.Material.Filled.Place" Color="Color.Primary" Size="Size.Small" />
                                    <MudText Typo="Typo.body1">@branch.Address</MudText>
                                </div>
                                
                                @if (!string.IsNullOrEmpty(branch.Phone))
                                {
                                    <div class="d-flex gap-3 align-center">
                                        <MudIcon Icon="@Icons.Material.Filled.Phone" Color="Color.Primary" Size="Size.Small" />
                                        <MudText Typo="Typo.body1">@branch.Phone</MudText>
                                    </div>
                                }
                                
                                @if (!string.IsNullOrEmpty(branch.Email))
                                {
                                    <div class="d-flex gap-3 align-center">
                                        <MudIcon Icon="@Icons.Material.Filled.Email" Color="Color.Primary" Size="Size.Small" />
                                        <MudText Typo="Typo.body1">@branch.Email</MudText>
                                    </div>
                                }
                                
                                @if (!string.IsNullOrEmpty(branch.Description))
                                {
                                    <MudDivider Class="my-2" />
                                    <MudText Typo="Typo.body2">@branch.Description</MudText>
                                }
                            </div>
                        </MudCardContent>
                        <MudCardActions>
                            <MudButton Variant="Variant.Text" Color="Color.Primary" Href="@GetGoogleMapsDirectionsUrl(branch)">
                                الاتجاهات
                            </MudButton>
                        </MudCardActions>
                    </MudCard>
                </MudItem>
            }
        </MudGrid>
    }
    else
    {
        <MudAlert Severity="Severity.Info" Class="my-8">
            لم يتم العثور على فروع حالياً.
        </MudAlert>
    }
</MudContainer>

<!-- Call to Action Section -->
<div class="py-12" style="background-color: var(--mud-palette-primary); color: white;">
    <MudContainer MaxWidth="MaxWidth.Large">
        <MudGrid Justify="Justify.Center" Class="text-center">
            <MudItem xs="12" md="8">
                <MudText Typo="Typo.h4" Class="mb-4">هل لديك أي استفسارات؟</MudText>
                <MudText Typo="Typo.body1" Class="mb-6">يمكنك التواصل معنا مباشرة من خلال نموذج الاتصال أو زيارة أحد فروعنا.</MudText>
                <MudButton Variant="Variant.Filled" Color="Color.Secondary" Size="Size.Large" Href="/contact">اتصل بنا</MudButton>
            </MudItem>
        </MudGrid>
    </MudContainer>
</div>

<style>
    .branch-card {
        transition: all 0.3s ease;
    }
    
    .branch-card:hover {
        transform: translateY(-5px);
    }
</style>

@code {
    private CompanyInfo _companyInfo;
    private IEnumerable<Branch> _branches = new List<Branch>();
    private bool _loading = true;
    
    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Intentar obtener datos de la empresa usando una API
            try
            {
                _companyInfo = await Http.GetFromJsonAsync<CompanyInfo>("/api/CompanyInfo/1");
            }
            catch
            {
                // Si no hay un endpoint para CompanyInfo, usar datos por defecto
                _companyInfo = new CompanyInfo
                {
                    Name = "شركتنا",
                    Vision = "أن نكون الشركة الرائدة في مجال عملنا",
                    Mission = "توفير خدمات عالية الجودة لعملائنا",
                    AboutUs = "نحن شركة رائدة في مجال تكنولوجيا المعلومات.",
                    Phone = "+966123456789",
                    Email = "<EMAIL>",
                    Address = "الرياض، المملكة العربية السعودية"
                };
            }
            
            // Intentar obtener sucursales usando una API
            try
            {
                _branches = await Http.GetFromJsonAsync<List<Branch>>("/api/Branches");
                
                // Filtrar sucursales activas
                _branches = _branches.Where(b => b.IsActive).ToList();
            }
            catch
            {
                // Si no hay un endpoint para Branches, usar datos de muestra
                _branches = new List<Branch>
                {
                    new Branch
                    {
                        Id = 1,
                        Name = "فرع الرياض",
                        Address = "الرياض، حي الملقا، شارع الأمير محمد بن سلمان",
                        Phone = "+966112345678",
                        Email = "<EMAIL>",
                        Latitude = 24.7136,
                        Longitude = 46.6753,
                        IsActive = true,
                        Description = "الفرع الرئيسي للشركة في الرياض"
                    },
                    new Branch
                    {
                        Id = 2,
                        Name = "فرع جدة",
                        Address = "جدة، حي الشاطىء، شارع الأمير فيصل",
                        Phone = "+966122345678",
                        Email = "<EMAIL>",
                        Latitude = 21.5433,
                        Longitude = 39.1728,
                        IsActive = true,
                        Description = "فرع الشركة في جدة"
                    },
                    new Branch
                    {
                        Id = 3,
                        Name = "فرع الدمام",
                        Address = "الدمام، حي الشاطىء، شارع الملك سعود",
                        Phone = "+966132345678",
                        Email = "<EMAIL>",
                        Latitude = 26.4207,
                        Longitude = 50.0907,
                        IsActive = true,
                        Description = "فرع الشركة في الدمام"
                    }
                };
            }
        }
        catch (Exception ex)
        {
            // Handle error if needed
            Console.WriteLine($"Error loading branches: {ex.Message}");
            
            // Crear algunas sucursales por defecto en caso de error
            _branches = new List<Branch>
            {
                new Branch
                {
                    Id = 1,
                    Name = "فرع الرياض",
                    Address = "الرياض، حي الملقا، شارع الرئيسي",
                    Phone = "+966112345678",
                    Email = "<EMAIL>",
                    Latitude = 24.7136,
                    Longitude = 46.6753,
                    IsActive = true
                }
            };
        }
        finally
        {
            _loading = false;
        }
        
        await base.OnInitializedAsync();
    }
    
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && _branches != null && _branches.Any())
        {
            // Initialize Google Maps
            await InitializeMap();
        }
        
        await base.OnAfterRenderAsync(firstRender);
    }
    
    private async Task InitializeMap()
    {
        try
        {
            // In a real application, you would use Google Maps JS API
            // Here we'll just inject a dummy script for demonstration
            // await JSRuntime.InvokeVoidAsync("initMap", _branches.Select(b => new { b.Name, b.Latitude, b.Longitude, b.Address }));
            
            // For now, we'll just indicate this would be where the map is initialized
            Console.WriteLine("Map would be initialized here with branches data");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error initializing map: {ex.Message}");
        }
    }
    
    private async Task ShowBranchOnMap(Branch branch)
    {
        try
        {
            // In a real application, you would center the map on the branch
            // await JSRuntime.InvokeVoidAsync("centerMapOn", branch.Latitude, branch.Longitude);
            
            // For now, we'll just indicate this would be where the map is centered
            Console.WriteLine($"Map would center on branch: {branch.Name} at {branch.Latitude}, {branch.Longitude}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error showing branch on map: {ex.Message}");
        }
    }
    
    private string GetGoogleMapsDirectionsUrl(Branch branch)
    {
        // Create Google Maps directions URL
        return $"https://www.google.com/maps/dir/?api=1&destination={branch.Latitude},{branch.Longitude}";
    }
}
